{"incrementalBuildIgnoredGlobs": ["temp/**", "lib/**", "**/*.svelte", ".build/**", ".validate/**", ".format/**", "types"], "disableBuildCacheForProject": false, "operationSettings": [{"operationName": "build", "outputFolderNames": ["lib", ".build"]}, {"operationName": "test", "outputFolderNames": ["coverage"]}, {"operationName": "format", "outputFolderNames": [".format"]}, {"operationName": "_phase:build", "outputFolderNames": ["lib", ".build"]}, {"operationName": "_phase:validate", "outputFolderNames": ["types", ".validate"]}, {"operationName": "_phase:bundle", "outputFolderNames": ["bundle"], "disableBuildCacheForOperation": true}, {"operationName": "_phase:test", "outputFolderNames": ["coverage"]}, {"operationName": "_phase:docker-build", "outputFolderNames": []}, {"operationName": "_phase:docker-staging", "outputFolderNames": []}, {"operationName": "docker:build", "outputFolderNames": []}]}