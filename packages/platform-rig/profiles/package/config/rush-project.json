{"$schema": "https://developer.microsoft.com/json-schemas/rush/v5/rush-project.schema.json", "incrementalBuildIgnoredGlobs": ["lib/**", "rush-logs/**", "coverage/**", ".rush/**", ".build/**", ".validate/**", ".format/**", "types", "dist"], "disableBuildCacheForProject": false, "operationSettings": [{"operationName": "build", "outputFolderNames": ["lib", ".build"]}, {"operationName": "package", "outputFolderNames": ["dist", ".build"]}, {"operationName": "test", "outputFolderNames": ["coverage"]}, {"operationName": "format", "outputFolderNames": [".rush/temp/.format"]}, {"operationName": "_phase:build", "outputFolderNames": ["lib", ".build"]}, {"operationName": "_phase:validate", "outputFolderNames": ["types", ".validate"]}, {"operationName": "_phase:bundle", "outputFolderNames": ["bundle"], "disableBuildCacheForOperation": true}, {"operationName": "_phase:package", "outputFolderNames": ["dist"]}, {"operationName": "_phase:docker-build", "outputFolderNames": []}, {"operationName": "_phase:docker-staging", "outputFolderNames": []}, {"operationName": "docker:build", "outputFolderNames": []}]}