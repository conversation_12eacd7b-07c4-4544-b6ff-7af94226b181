//
// Copyright © 2020, 2021 Anticrm Platform Contributors.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import activity, { type ActivityMessageControl } from '@hcengineering/activity'
import { dailyPrioritiesId, type ChunterSpace } from '@hcengineering/dailypriorities'
import { type Builder } from '@hcengineering/model'
import core from '@hcengineering/model-core'
import presentation from '@hcengineering/model-presentation'
import view from '@hcengineering/model-view'
import workbench from '@hcengineering/model-workbench'
import { WidgetType } from '@hcengineering/workbench'

import { defineNotifications } from './notifications'
import dailyPriorities from './plugin'
import {
  DOMAIN_DAILY_PRIORITIES,
  TChannel,
  TChatMessage,
  TChatMessageViewlet,
  TChatSyncInfo,
  TChunterExtension,
  TChunterSpace,
  TInlineButton,
  TObjectChatPanel,
  TThreadMessage,
  TTypingInfo
} from './types'
export { dailyPrioritiesId } from '@hcengineering/dailypriorities'
export { chunterOperation } from './migration'
export * from './types'

export function createModel (builder: Builder): void {
  builder.createModel(
    TChunterSpace,
    TChannel,
    TChatMessage,
    TThreadMessage,
    TChatMessageViewlet,
    TObjectChatPanel,
    TChatSyncInfo,
    TInlineButton,
    TTypingInfo,
    TChunterExtension
  )

  builder.createDoc(
    workbench.class.Application,
    core.space.Model,
    {
      label: dailyPriorities.string.ApplicationLabelDailyPriorities,
      locationDataResolver: dailyPriorities.function.LocationDataResolver,
      icon: dailyPriorities.icon.DailyPriorities,
      alias: dailyPrioritiesId,
      hidden: false,
      component: dailyPriorities.component.Chat
    },
    dailyPriorities.app.Chunter
  )

  builder.createDoc(
    workbench.class.Application,
    core.space.Model,
    {
      label: dailyPriorities.string.ApplicationLabelDailyPriorities,
      locationDataResolver: dailyPriorities.function.LocationDataResolver,
      icon: dailyPriorities.icon.DailyPriorities,
      alias: dailyPrioritiesId,
      hidden: false,
      component: dailyPriorities.component.Chat
    },
    dailyPriorities.app.Chunter
  )

  builder.createDoc(
    workbench.class.Widget,
    core.space.Model,
    {
      label: dailyPriorities.string.Chat,
      type: WidgetType.Flexible,
      icon: dailyPriorities.icon.DailyPriorities,
      closeIfNoTabs: true,
      onTabClose: dailyPriorities.function.CloseChatWidgetTab,
      component: dailyPriorities.component.ChatWidget,
      tabComponent: dailyPriorities.component.ChatWidgetTab
    },
    dailyPriorities.ids.ChatWidget
  )

  builder.createDoc(
    workbench.class.Widget,
    core.space.Model,
    {
      label: dailyPriorities.string.WhosOut,
      type: WidgetType.Fixed,
      icon: dailyPriorities.icon.DailyPriorities,
      component: dailyPriorities.component.WhoIsOutWidget
    },
    dailyPriorities.ids.WhoIsOutWidget
  )

  builder.createDoc(presentation.class.ComponentPointExtension, core.space.Model, {
    extension: workbench.extensions.WorkbenchTabExtensions,
    component: dailyPriorities.component.WorkbenchTabExtension
  })

  const spaceClasses = [dailyPriorities.class.Channel]

  spaceClasses.forEach((spaceClass) => {
    builder.mixin(spaceClass, core.class.Class, activity.mixin.ActivityDoc, {})

    builder.mixin(spaceClass, core.class.Class, view.mixin.LinkProvider, {
      encode: dailyPriorities.function.GetChunterSpaceLinkFragment
    })

    builder.mixin(spaceClass, core.class.Class, view.mixin.ObjectPanel, {
      component: dailyPriorities.component.ChannelPanel
    })
  })

  // Presenters
  builder.mixin(dailyPriorities.class.Channel, core.class.Class, view.mixin.ObjectIcon, {
    component: dailyPriorities.component.ChannelIcon
  })

  builder.mixin(dailyPriorities.class.Channel, core.class.Class, view.mixin.ObjectTitle, {
    titleProvider: dailyPriorities.function.ChannelTitleProvider
  })

  builder.mixin(dailyPriorities.class.Channel, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: dailyPriorities.component.ChannelPresenter
  })

  builder.mixin(dailyPriorities.class.Channel, core.class.Class, view.mixin.SpaceHeader, {
    header: dailyPriorities.component.ChannelHeader
  })

  builder.mixin(dailyPriorities.class.ChatMessage, core.class.Class, view.mixin.CollectionPresenter, {
    presenter: dailyPriorities.component.ChatMessagesPresenter
  })

  builder.mixin(dailyPriorities.class.ChatMessage, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: dailyPriorities.component.ChatMessagePresenter
  })

  builder.mixin(dailyPriorities.class.ThreadMessage, core.class.Class, view.mixin.ObjectPresenter, {
    presenter: dailyPriorities.component.ThreadMessagePresenter
  })

  builder.mixin(dailyPriorities.class.TypingInfo, core.class.Class, core.mixin.TransientConfiguration, {
    broadcastOnly: true
  })

  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: dailyPriorities.class.Channel,
      descriptor: view.viewlet.Table,
      configOptions: {
        strict: true
      },
      config: ['', 'topic', 'private', 'archived', 'members'],
      props: { enableChecking: false }
    },
    dailyPriorities.viewlet.Channels
  )

  builder.createDoc(
    dailyPriorities.class.ChatMessageViewlet,
    core.space.Model,
    {
      messageClass: dailyPriorities.class.ThreadMessage,
      objectClass: dailyPriorities.class.ChatMessage,
      label: dailyPriorities.string.RepliedToThread
    },
    dailyPriorities.ids.ThreadMessageViewlet
  )

  builder.mixin(dailyPriorities.class.Channel, core.class.Class, dailyPriorities.mixin.ObjectChatPanel, {
    ignoreKeys: ['archived', 'collaborators', 'lastMessage', 'pinned', 'topic', 'description', 'members', 'owners']
  })

  builder.createDoc(activity.class.ReplyProvider, core.space.Model, {
    function: dailyPriorities.function.ReplyToThread
  })

  builder.mixin(dailyPriorities.class.Channel, core.class.Class, view.mixin.ClassFilters, {
    filters: ['name', 'topic', 'private', 'archived', 'members'],
    strict: true
  })

  builder.mixin(dailyPriorities.class.ChatMessage, core.class.Class, presentation.mixin.InstantTransactions, {
    txClasses: [core.class.TxCreateDoc]
  })

  // Activity
  builder.createDoc<ActivityMessageControl<ChunterSpace>>(activity.class.ActivityMessageControl, core.space.Model, {
    objectClass: dailyPriorities.class.Channel,
    skip: [
      { _class: core.class.TxMixin },
      { _class: core.class.TxCreateDoc, objectClass: { $ne: dailyPriorities.class.Channel } },
      { _class: core.class.TxRemoveDoc }
    ],
    allowedFields: ['members']
  })

  builder.mixin(dailyPriorities.class.ChatMessage, core.class.Class, activity.mixin.ActivityMessagePreview, {
    presenter: dailyPriorities.component.ChatMessagePreview
  })

  builder.mixin(dailyPriorities.class.ThreadMessage, core.class.Class, activity.mixin.ActivityMessagePreview, {
    presenter: dailyPriorities.component.ThreadMessagePreview
  })

  builder.mixin(activity.class.ActivityMessage, core.class.Class, view.mixin.LinkProvider, {
    encode: dailyPriorities.function.GetMessageLink
  })

  builder.mixin(dailyPriorities.class.ThreadMessage, core.class.Class, view.mixin.LinkProvider, {
    encode: dailyPriorities.function.GetThreadLink
  })

  builder.createDoc(activity.class.ActivityMessagesFilter, core.space.Model, {
    label: dailyPriorities.string.Comments,
    position: 60,
    filter: dailyPriorities.filter.ChatMessagesFilter
  })

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: dailyPriorities.class.Channel,
    components: { input: { component: dailyPriorities.component.ChatMessageInput } }
  })

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: activity.class.DocUpdateMessage,
    components: { input: { component: dailyPriorities.component.ChatMessageInput } }
  })

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: dailyPriorities.class.ChatMessage,
    components: { input: { component: dailyPriorities.component.ChatMessageInput } }
  })

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: activity.class.ActivityReference,
    components: { input: { component: dailyPriorities.component.ChatMessageInput } }
  })

  // Indexing
  builder.createDoc(core.class.DomainIndexConfiguration, core.space.Model, {
    domain: DOMAIN_DAILY_PRIORITIES,
    disabled: [{ _class: 1 }, { space: 1 }, { modifiedBy: 1 }, { createdBy: 1 }, { createdOn: -1 }]
  })

  defineNotifications(builder)

  builder.mixin(dailyPriorities.class.InlineButton, core.class.Class, core.mixin.IndexConfiguration, {
    indexes: [],
    searchDisabled: true
  })

  builder.mixin(dailyPriorities.class.ChatSyncInfo, core.class.Class, core.mixin.IndexConfiguration, {
    indexes: [],
    searchDisabled: true
  })
}

export default dailyPriorities
