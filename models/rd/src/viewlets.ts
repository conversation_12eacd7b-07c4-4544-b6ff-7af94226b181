import { Builder } from '@hcengineering/model'
import core from '@hcengineering/model-core'
import { SortingOrder } from '@hcengineering/core'
import view from '@hcengineering/model-view'
import drive from '@hcengineering/model-drive'
import rd from '@hcengineering/rd'

export function defineViewlets(builder: Builder): void {
  
  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: rd.class.DatasetVersion,
      descriptor: view.viewlet.List,
      viewOptions: {
        groupBy: ['datasetName'],
        orderBy: [
          ['lastUpdated', SortingOrder.Descending]
        ],
        other: []
      },
      config: [
        {
          key: '',  
          presenter: rd.component.DatasetPresenter,
          label: drive.string.Version,
          displayProps: { fixed: 'left' }
        },
        {
          key: 'sizeBytes',
          presenter: view.component.FileSizePresenter,
          label: drive.string.Size,
          displayProps: { dividerBefore: true }
        },
        {
          key: 'lastUpdated', 
          presenter: rd.component.DatasetDatePresenter,
          label: drive.string.LastModified,
          displayProps: { dividerBefore: true }  
        }
      ]
    },
    rd.viewlet.DatasetList
  )
  
  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: rd.class.Experiment,
      descriptor: view.viewlet.List,
      viewOptions: {
        groupBy: ['annotator'],
        orderBy: [
          ['creationTimestamp', SortingOrder.Ascending]
        ],
        other: []
      },
      config: [
        {
          key: '',  
          presenter: rd.component.ExperimentPresenter,
          label: rd.string.Experimentation,
          displayProps: { fixed: 'left' }
        },
        {
          key: 'status', 
          presenter: rd.component.ExperimentStatusPresenter,
          label: rd.string.Status,
          displayProps: { key: 'status' }  
        },
        {
          key: 'creationTimestamp', 
          presenter: rd.component.DatasetDatePresenter,
          label: rd.string.CreationTimestamp,
          displayProps: { key: 'timestamp', dividerBefore: true }  
        },
        {
          key: 'url', 
          presenter: rd.component.ConnectButton,
          label: rd.string.Connect,
          displayProps: { key: 'connect', fixed: 'right' }  
        }
      ]
    },
    rd.viewlet.ExperimentList
  )

  builder.createDoc(
    view.class.Viewlet,
    core.space.Model,
    {
      attachTo: rd.class.ModelRegistryVersions,
      descriptor: view.viewlet.List,
      viewOptions: {
        groupBy: ['modelRegistryName'],
        orderBy: [
          ['lastUpdated', SortingOrder.Descending]
        ],
        other: []
      },
      config: [
        {
          key: '',  
          presenter: rd.component.ModelRegistryPresenter,
          label: drive.string.Version,
          displayProps: { fixed: 'left' }
        },
        {
          key: 'lastUpdated', 
          presenter: rd.component.ModelRegistryDatePresenter,
          label: drive.string.LastModified,
          displayProps: { dividerBefore: true }  
        }
      ]
    },
    rd.viewlet.ModelRegistryList
  )
} 