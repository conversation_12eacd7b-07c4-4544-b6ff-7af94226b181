//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import {
  IndexKind,
  type Ref,
  type Space,
  type Doc,
  type Timestamp
} from '@hcengineering/core'
import { type RdDoc, DOMAIN_RD } from '@hcengineering/rd'
import {
  Builder,
  Index,
  Model,
  Prop,
  TypeString,
  TypeRef,
  UX,
  TypeDate,
  TypeNumber,
  Collection
} from '@hcengineering/model'
import core, { TAttachedDoc, TDoc } from '@hcengineering/model-core'
import view from '@hcengineering/model-view'
import workbench, { createNavigateAction } from '@hcengineering/model-workbench'
import activity from '@hcengineering/activity'
import chunter from '@hcengineering/chunter'

import rd, { rdId } from '@hcengineering/rd'
import _tracker from '@hcengineering/tracker'
import { defineViewlets } from './viewlets'

const tracker: any = _tracker

export { rdId } from '@hcengineering/rd'
export { default } from './plugin'

@Model(rd.class.RdDoc, core.class.Doc, DOMAIN_RD)
@UX(rd.string.Rd, rd.icon.Rd)
export class TRdDoc extends TDoc implements RdDoc {
  @Prop(TypeString(), rd.string.Name)
  @Index(IndexKind.FullText)
    name!: string

  @Prop(TypeString(), rd.string.Description)
  @Index(IndexKind.FullText)
    description?: string

  @Prop(TypeRef(core.class.Space), core.string.Space)
  declare space: Ref<Space>
}

@Model(rd.class.Dataset, core.class.Doc, DOMAIN_RD)
@UX(rd.string.Datasets, rd.icon.Dataset)
export class TDataset extends TDoc {
  @Prop(TypeString(), rd.string.Name)
  @Index(IndexKind.FullText)
    name!: string

  @Prop(TypeString(), rd.string.Name)
    project!: string

  @Prop(TypeString(), rd.string.Name)
    environment!: string

  @Prop(TypeString(), rd.string.Name)
    path!: string

  @Prop(TypeString(), rd.string.Description)
  @Index(IndexKind.FullText)
    description?: string

  @Prop(TypeDate(), rd.string.Name)
    createdAt!: Timestamp

  @Prop(TypeDate(), rd.string.Name)
    updatedAt!: Timestamp

  @Prop(TypeRef(core.class.Space), core.string.Space)
  declare space: Ref<Space>
}

@Model(rd.class.DatasetVersion, core.class.Doc, DOMAIN_RD)
@UX(rd.string.Datasets, rd.icon.Dataset)
export class TDatasetVersion extends TDoc {
  @Prop(TypeRef(rd.class.Dataset), rd.string.Datasets)
    dataset!: Ref<TDataset>

  @Prop(TypeString(), rd.string.Name)
    datasetName!: string

  @Prop(TypeString(), rd.string.Name)
    version!: string

  @Prop(TypeString(), rd.string.Name)
    path!: string

  @Prop(TypeDate(), rd.string.Name)
    lastUpdated!: Timestamp

  @Prop(TypeNumber(), rd.string.Name)
    sizeBytes!: number

  @Prop(TypeRef(core.class.Space), core.string.Space)
  declare space: Ref<Space>
}

@Model(rd.class.ModelRegistry, core.class.Doc, DOMAIN_RD)
@UX(rd.string.ModelRegistry, rd.icon.Model)
export class TModelRegistry extends TDoc {
  @Prop(TypeString(), rd.string.Name)
  @Index(IndexKind.FullText)
    name!: string

  @Prop(TypeString(), rd.string.Name)
    project!: string

  @Prop(TypeString(), rd.string.Name)
    environment!: string

  @Prop(TypeString(), rd.string.Name)
    path!: string

  @Prop(TypeString(), rd.string.Description)
  @Index(IndexKind.FullText)
    description?: string

  @Prop(TypeDate(), rd.string.Name)
    createdAt!: Timestamp

  @Prop(TypeDate(), rd.string.Name)
    updatedAt!: Timestamp

  @Prop(TypeRef(core.class.Space), core.string.Space)
  declare space: Ref<Space>
}

@Model(rd.class.ModelRegistryVersions, core.class.Doc, DOMAIN_RD)
@UX(rd.string.ModelRegistry, rd.icon.Model)
export class TModelRegistryVersions extends TDoc {
  @Prop(TypeRef(rd.class.ModelRegistry), rd.string.ModelRegistry)
    modelRegistry!: Ref<TModelRegistry>

  @Prop(TypeString(), rd.string.Name)
  @Index(IndexKind.FullText)
    modelRegistryName!: string

  @Prop(TypeString(), rd.string.Name)
  @Index(IndexKind.FullText)
    version!: string

  @Prop(TypeString(), rd.string.Name)
  @Index(IndexKind.FullText)
    path!: string

  @Prop(TypeDate(), rd.string.Name)
    lastUpdated!: Timestamp

  @Prop(TypeNumber(), rd.string.Name)
    sizeBytes!: number

  @Prop(TypeRef(core.class.Space), core.string.Space)
  declare space: Ref<Space>
}

@Model(rd.class.AnnotationTask, core.class.Doc, DOMAIN_RD)
export class TAnnotationTask extends TDoc {}

@Model(rd.class.Experiment, core.class.Doc, DOMAIN_RD)
@UX(rd.string.Experimentation, rd.icon.Experiment)
export class TExperiment extends TDoc {

  @Prop(TypeString(), rd.string.Name)
    id !: string

  @Prop(TypeString(), rd.string.Name)
    name!: string
  
  @Prop(TypeString(), rd.string.Name)
    nameSpace !: string

  @Prop(TypeString(), rd.string.Name)
    creationTimestamp !: string

  @Prop(TypeString(), rd.string.Name)
    annotator !: string

  @Prop(TypeString(), rd.string.Name)
    template !: string

  @Prop(TypeString(), rd.string.Name)
    status !: string

  @Prop(TypeString(), rd.string.Name)
    cpuResource !: string

  @Prop(TypeString(), rd.string.Name)
    memoryResource !: string
  
  @Prop(TypeString(), rd.string.Name)
    url !: string
    
  @Prop(Collection(chunter.class.ChatMessage), chunter.string.Comments)
    comments?: number
    
  @Prop(TypeRef(core.class.Space), core.string.Space)
  declare space: Ref<Space>
}

@Model(rd.class.Model, core.class.Doc, DOMAIN_RD)
export class TModel extends TDoc {}

@Model(rd.class.ModelMonitoring, core.class.Doc, DOMAIN_RD)
export class TModelMonitoring extends TDoc {}

export function createModel (builder: Builder): void {
  builder.createDoc(
    workbench.class.Application,
    core.space.Model,
    {
      label: rd.string.Rd,
      icon: rd.icon.Rd,
      alias: rdId,
      hidden: false
    },
    rd.app.Rd
  )

  builder.createModel(TDataset, TDatasetVersion, TModelRegistry, TModelRegistryVersions, TAnnotationTask, TExperiment, TModel, TModelMonitoring)

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: rd.class.Dataset,
    components: { input: { component: chunter.component.ChatMessageInput } }
  })

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: rd.class.DatasetVersion,
    components: { input: { component: chunter.component.ChatMessageInput } }
  })

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: rd.class.Experiment,
    components: { input: { component: chunter.component.ChatMessageInput } }
  })
    
   builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: rd.class.ModelRegistry,
    components: { input: { component: chunter.component.ChatMessageInput } }
  })

  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: rd.class.ModelRegistryVersions,
    components: { input: { component: chunter.component.ChatMessageInput } }
  })

  builder.mixin(rd.class.Dataset, core.class.Class, view.mixin.ObjectPanel, {
    component: rd.component.EditDataset
  })

  builder.mixin(rd.class.DatasetVersion, core.class.Class, view.mixin.ObjectPanel, {
    component: rd.component.EditDataset
  })

  builder.mixin(rd.class.Experiment, core.class.Class, view.mixin.ObjectPanel, {
    component: rd.component.ExperimentExpanded
  })
  
  builder.mixin(rd.class.ModelRegistry, core.class.Class, view.mixin.ObjectPanel, {
    component: rd.component.EditModelRegistry
  })

  builder.mixin(rd.class.ModelRegistryVersions, core.class.Class, view.mixin.ObjectPanel, {
    component: rd.component.EditModelRegistry
  })

  defineViewlets(builder)

  builder.createDoc(core.class.DomainIndexConfiguration, core.space.Model, {
    domain: DOMAIN_RD,
    disabled: [
      { space: 1 },
      { modifiedOn: 1 },
      { modifiedBy: 1 },
      { createdBy: 1 },
      { createdOn: -1 }
    ]
  })

  builder.createDoc(
    workbench.class.ApplicationNavModel,
    core.space.Model,
    {
      extends: rd.app.Rd,
      spaces: [
        {
          id: 'projects',
          label: (tracker.string?.Projects ?? tracker.string?.Project ?? 'Your projects') as any,
          icon: tracker.icon.Home,
          spaceClass: tracker.class.Project,
          addSpaceLabel: tracker.string?.CreateProject ?? 'Create project',
          createComponent: tracker.component?.CreateProject ?? undefined,
          visibleIf: (rd as any).function.IsRnDProject,
          specials: [
            {
              id: 'datasets',
              label: rd.string.Datasets,
              icon: rd.icon.Dataset,
              component: (rd.component as any).DatasetsView,
              componentProps: { 
                _class: rd.class.DatasetVersion,
                icon: rd.icon.Dataset,
                title: rd.string.Datasets 
              }
            },
            {
              id: 'annotation',
              label: rd.string.Annotation,
              icon: rd.icon.Annotation,
              component: (rd.component as any).AnnotationView,
              componentProps: {
                _class: rd.class.AnnotationTask,
                icon: rd.icon.Annotation,
                label: rd.string.Annotation
              }
            },
            {
              id: 'experimentation',
              label: rd.string.Experimentation,
              icon: rd.icon.Experiment,
              component: (rd.component as any).ExperimentationView,
              componentProps: {
                _class: rd.class.Experiment,
                icon: rd.icon.Experiment,
                title: rd.string.Experimentation
              }
            },
            {
              id: 'model-registry',
              label: rd.string.ModelRegistry,
              icon: rd.icon.Model,
              component: (rd.component as any).ModelRegistryView,
              componentProps: {
                _class: rd.class.ModelRegistryVersions,
                icon: rd.icon.Model,
                title: rd.string.ModelRegistry
              }
            },
            {
              id: 'model-monitoring',
              label: rd.string.ModelMonitoring,
              icon: rd.icon.Monitor,
              component: (rd.component as any).RdBlank,
              componentProps: {
                _class: rd.class.ModelMonitoring,
                icon: rd.icon.Monitor,
                label: rd.string.ModelMonitoring
              }
            }
          ]
        }
      ],
      specials: []
    },
    (rd.app.Rd + '-nav') as Ref<Doc>
  )
}