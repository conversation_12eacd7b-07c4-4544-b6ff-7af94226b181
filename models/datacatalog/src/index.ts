import { Asset, Plugin } from "@hcengineering/platform";
import { Builder } from "@hcengineering/model";
import { TDatacatalogDoc, TDataAsset } from "./types";
import workbench from "@hcengineering/model-workbench";
import activity from '@hcengineering/activity'
import chunter from '@hcengineering/chunter'
import core from "@hcengineering/model-core";

import datacatalog from "./plugin";
import { AnyComponent } from "@hcengineering/ui";
import { Ref, Doc, Domain, Class } from "@hcengineering/core";

export const DOMAIN_DATACATALOG = 'datacatalog' as Domain

export const datacatalogId = 'datacatalog' as Plugin

export function createModel (builder: Builder): void {
  builder.createModel(TDatacatalogDoc, TDataAsset)
 
  builder.createDoc(
    workbench.class.Application,
    core.space.Model,
    {
      label: datacatalog.string.Datacatalog,
      // locationDataResolver: datacatalog.function.LocationDataResolver,
      icon: datacatalog.icon.Datacatalog as Asset,
      alias: datacatalogId,
      hidden: false,
      component: datacatalog.component.DatacatalogComponent as AnyComponent
    },
    datacatalog.app.DatacatalogApp as Ref<Doc>
  )

  // Enable activity input for DataAsset documents
  builder.createDoc(activity.class.ActivityExtension, core.space.Model, {
    ofClass: (datacatalog.class.DataAsset as unknown as Ref<Class<Doc>>),
    components: { input: { component: (chunter as any).component.ChatMessageInput } }
  })

}

export default datacatalog