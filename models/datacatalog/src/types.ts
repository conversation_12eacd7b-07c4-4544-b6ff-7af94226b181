//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

  import {IndexKind, Ref, Class, Doc } from '@hcengineering/core'
  import { DOMAIN_DATACATALOG } from '@hcengineering/datacatalog'
  import datacatalog from './plugin'
  import { DatacatalogDoc } from '@hcengineering/datacatalog/src/types'
  import {
    Index,
    Model,
    Prop,
    TypeString,
    UX
  } from '@hcengineering/model'
  import core, { TDoc } from '@hcengineering/model-core'
  import { IntlString, Asset } from '@hcengineering/platform'
  
  // Assuming pluginName could have hyphens, a safer import name might be needed if used directly as variable
  // For now, using pluginName as per the context provided for this file.
  import pluginDefaultExport from './plugin' // Changed import variable name
  
  export { datacatalogId } from './index'
  export { default } from './plugin'
  
  @Model(pluginDefaultExport.class.DatacatalogDoc as Ref<Class<DatacatalogDoc>>, core.class.Doc, DOMAIN_DATACATALOG)
  @UX(pluginDefaultExport.string.Datacatalog as IntlString, pluginDefaultExport.icon.Datacatalog as Asset)
  export class TDatacatalogDoc extends TDoc implements DatacatalogDoc {
    @Prop(TypeString(), pluginDefaultExport.string.Name)
    @Index(IndexKind.FullText)
      name!: string
  
    @Prop(TypeString(), pluginDefaultExport.string.Description)
    @Index(IndexKind.FullText)
      description?: string
  }

  export interface DataAsset extends Doc {
    assetId: string
    name?: string
    description?: string
  }

  @Model(pluginDefaultExport.class.DataAsset as Ref<Class<DataAsset>>, core.class.Doc, DOMAIN_DATACATALOG)
  @UX(pluginDefaultExport.string.Datacatalog as IntlString, pluginDefaultExport.icon.Datacatalog as Asset)
  export class TDataAsset extends TDoc implements DataAsset {
    @Prop(TypeString(), pluginDefaultExport.string.Name)
    @Index(IndexKind.FullText)
      assetId!: string

    @Prop(TypeString(), pluginDefaultExport.string.Name)
    @Index(IndexKind.FullText)
      name?: string

    @Prop(TypeString(), pluginDefaultExport.string.Description)
    @Index(IndexKind.FullText)
      description?: string
  }