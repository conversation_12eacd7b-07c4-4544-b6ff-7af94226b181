import { datacatalogId } from '@hcengineering/datacatalog'
import datacatalog from '@hcengineering/datacatalog'
import { IntlString, mergeIds, Resource } from '@hcengineering/platform'
import type { Ref, Class, Doc } from '@hcengineering/core'
import { type LocationData } from '@hcengineering/workbench'

export default mergeIds(datacatalogId, datacatalog, {
  string: {
    Name: '' as IntlString,
    Description: '' as IntlString,
    Datacatalog: '' as IntlString,
  },
  class: {
    DataAsset: '' as Ref<Class<Doc>>
  },
  function: {
    LocationDataResolver: '' as Resource<(loc: Location) => Promise<LocationData>>
  }
})