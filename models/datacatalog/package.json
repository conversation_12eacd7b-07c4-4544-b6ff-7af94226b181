{"name": "@hcengineering/model-datacatalog", "version": "0.1.0", "main": "lib/index.js", "types": "types/index.d.ts", "files": ["lib/**/*", "types/**/*", "tsconfig.json"], "author": "Hardcore Engineering Inc.", "license": "EPL-2.0", "scripts": {"build": "compile", "build:watch": "compile", "format": "format src", "test": "jest --passWithNoTests --silent", "_phase:build": "compile transpile src", "_phase:test": "jest --passWithNoTests --silent", "_phase:format": "format src", "_phase:validate": "compile validate"}, "dependencies": {"@hcengineering/core": "^0.6.32", "@hcengineering/datacatalog": "^0.1.0", "@hcengineering/model": "^0.6.11", "@hcengineering/model-core": "^0.6.0", "@hcengineering/model-workbench": "^0.6.1", "@hcengineering/activity": "^0.6.32", "@hcengineering/chunter": "^0.6.20", "@hcengineering/platform": "^0.6.11", "@hcengineering/workbench": "^0.6.16", "@hcengineering/ui": "^0.6.15", "@hcengineering/view": "^0.6.13", "@hcengineering/model-view": "^0.6.0", "@hcengineering/setting": "^0.6.17"}, "devDependencies": {"@hcengineering/platform-rig": "^0.6.0", "@types/jest": "^29.5.5", "jest": "^29.7.0", "typescript": "^5.3.3"}}