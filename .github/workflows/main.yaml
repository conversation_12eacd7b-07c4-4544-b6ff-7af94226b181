# This is a basic workflow to help you get started with Actions

name: CI

# Add explicit permissions for GitHub Actions
permissions:
  contents: read
  actions: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    tags:
      # # develop tags
      - d*
      # production tags
      - v*
      # staging tags
      - s*
  pull_request:
    branches: [develop, staging, staging-new, main]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

env:
  CacheFolders: |
    common
    desktop
    desktop-package
    dev
    models
    packages
    plugins
    pods
    server
    server-plugins
    templates
    services
    workers
    tests
    qms-tests
    rush.json
    .prettierrc
    tools
    workers
    ws-tests
  PublishTempFolder: publish_artifacts
  INIT_SCRIPTS_BRANCH: 'unified-init-scripts'

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    runs-on: ${{ vars.DEFAULT_GITHUB_RUNNER }}-medium
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          filter: tree:0

      - uses: actions/setup-node@v4
        with:
          node-version: '22'
          
      - uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Set rush cache environment...
        uses: gigara/rush-cache@v2.2.0

      # - name: Cheking model is updated...
      #   run: node common/scripts/check_model_version.js

      - name: Checking for mis-matching dependencies...
        run: node common/scripts/install-run-rush.js check

      - name: Installing...
        run: node common/scripts/install-run-rush.js install

      - name: Model version from git tags
        run: node common/scripts/install-run-rush.js model-version

      - name: Validate...
        run: node common/scripts/install-run-rush.js validate

      - name: Building...
        run: node common/scripts/install-run-rush.js build

      - name: Bundle...
        run: node common/scripts/install-run-rush.js bundle

      - name: Checkout init repository
        run: |
          curl -L -o ${{env.INIT_SCRIPTS_BRANCH}}.zip https://github.com/hcengineering/init/archive/refs/heads/${{env.INIT_SCRIPTS_BRANCH}}.zip
          unzip ${{env.INIT_SCRIPTS_BRANCH}}.zip -d pods/workspace
          mv pods/workspace/init-${{env.INIT_SCRIPTS_BRANCH}} pods/workspace/init
          rm -rf ${{env.INIT_SCRIPTS_BRANCH}}.zip


      - name: Docker Login to GCP Artifact Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.GOOGLE_REGION }}-docker.pkg.dev
          username: _json_key
          password: ${{ secrets.GOOGLE_CREDENTIALS }}

      - name: Docker build
        run: node common/scripts/install-run-rush.js docker:build -v
        env:
          # Do not fail the build if there are warnings
          RUSH_ALLOW_WARNINGS_IN_SUCCESSFUL_BUILD: "1"
          DOCKER_CLI_HINTS: false
          DOCKER_EXTRA: --platform=linux/amd64
      - name: Docker build love-agent
        run: |
          cd ./services/ai-bot/love-agent
          pnpm install && pnpm build
          pnpm docker:build -v
        env:
          DOCKER_CLI_HINTS: false
          DOCKER_EXTRA: --platform=linux/amd64
      - name: Docker push tag
        if: ${{ startsWith(github.ref, 'refs/tags/d') || startsWith(github.ref, 'refs/tags/v') || startsWith(github.ref, 'refs/tags/s') }} 
        run: |
          echo Pushing release of tag ${{ github.ref }}
          node common/scripts/install-run-rush.js docker:push -v
      - name: Docker push love-agent
        if: ${{ startsWith(github.ref, 'refs/tags/d') || startsWith(github.ref, 'refs/tags/v') || startsWith(github.ref, 'refs/tags/s') }}  
        run: |
          echo Pushing love-agent release of tag ${{ github.ref }}
          cd ./services/ai-bot/love-agent
          pnpm docker:push
  
  deploy:
    name: Deploy to ${{ matrix.environment.name }}
    needs:
      - build
    if: ${{ github.ref_type == 'tag' && github.event_name == 'push'}}
    runs-on: ${{  vars.DEFAULT_GITHUB_RUNNER }}
    strategy:
      matrix:
        exclude:
          - environment:
              hasToRun: false
        environment:
          # Make sure to add the condition in the steps above aswell for tags that start with d, s, v
          - name: develop
            hasToRun: ${{ startsWith(github.ref, 'refs/tags/d') }}
            kustomizationPath: argocd/clusters/hplus-develop-cluster/deployments/huly
          
          # Staging is not deployed yet, leaving for future use
          - name: staging
            hasToRun: ${{ startsWith(github.ref, 'refs/tags/s') }}
            kustomizationPath: argocd/clusters/hplus-develop-cluster/deployments/huly-staging

          - name: production
            hasToRun: ${{ startsWith(github.ref, 'refs/tags/v') }}
            kustomizationPath: argocd/clusters/hplus-prod-cluster/deployments/huly
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Update kustomization
        uses: Matrics-io/github-composites/update-kustomize-image-tag@main
        with:
          repository: Matrics-io/platform-infra
          kustomization-path: ${{ matrix.environment.kustomizationPath }}
          images: |
            account: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/account:${{ github.ref_name }}
            collaborator: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/collaborator:${{ github.ref_name }}
            front: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/front:${{ github.ref_name }}
            fulltext: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/fulltext:${{ github.ref_name }}
            rekoni-service: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/rekoni-service:${{ github.ref_name }}
            stats: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/stats:${{ github.ref_name }}
            transactor: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/transactor:${{ github.ref_name }}
            workspace: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/workspace:${{ github.ref_name }}
            go-annotationtool-microservice: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-annotationtool-microservice:${{ github.ref_name }} 
            go-webhook-microservice: us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-webhook-microservice:${{ github.ref_name }} 
          token: ${{ secrets.GH_PAT }}