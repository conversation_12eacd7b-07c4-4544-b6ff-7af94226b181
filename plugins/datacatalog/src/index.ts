//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import { Class, Doc, Ref } from '@hcengineering/core'
import { Asset, Plugin } from '@hcengineering/platform'
import { plugin } from '@hcengineering/platform'
import { DatacatalogDoc } from './types'
import { AnyComponent } from '@hcengineering/ui/src/types'


/**
 * @public
*/
export const datacatalogId = 'datacatalog' as Plugin

export * from './types'

export default plugin(datacatalogId, {
  icon: {
    Datacatalog: '' as Asset,
    ChevronLeft: '' as Asset,
    ChevronRight: '' as Asset,
  },
  class: {
    DatacatalogDoc: '' as Ref<Class<DatacatalogDoc>>,
  },
  component: {
    DatacatalogComponent: '' as AnyComponent,
  },
  app: {
    DatacatalogApp: '' as Ref<Doc>
  },
})