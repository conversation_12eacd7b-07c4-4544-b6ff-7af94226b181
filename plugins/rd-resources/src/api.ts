import rd from "./plugin"
import { getMetadata } from "@hcengineering/platform"
import presentation from '@hcengineering/presentation'
import type { DatasetApiResponse, Notebook, DatasetStats } from './types'

const rdUrl = getMetadata(rd.metadata.RdUrl)

type EnvName = 'dev' | 'staging' | 'prod'

const detectEnvironmentFromUrl = (urlString?: string): EnvName => {
  try {
    const urlObj = new URL(urlString ?? (typeof window !== 'undefined' ? window.location.href : ''))
    const host = urlObj.hostname.toLowerCase()

    if (host.includes('intra.matrics.io')) return 'prod'
    if (host.includes('huly-staging') || host.includes('staging')) return 'staging'
    if (host.includes('huly-dev') || host.includes('dev')) return 'dev'

    return 'prod'
  } catch {
    return 'prod'
  }
}

export const getDetectedEnvironment = (): EnvName => detectEnvironmentFromUrl(rdUrl)

const defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}

const getAuthHeaders = (): Record<string, string> => {
    const token = getMetadata(presentation.metadata.Token)
  
    if (!token) {
      throw new Error('No authentication token found. Please login first.')
    }
  
    return {
      ...defaultHeaders,
      Authorization: `Bearer ${token}`
    }
}

// Normalize different possible API response envelopes into a consistent shape
function normalizeDatasetsResponse(data: any, pageSize: number): DatasetApiResponse {
  const inner = (data != null && typeof data === 'object' && 'data' in data) ? (data as any).data : data

  // Primary expected shape
  if (inner && typeof inner === 'object' && Array.isArray((inner as any).datasets)) {
    const ds = (inner as any)
    return {
      total: typeof ds.total === 'number' ? ds.total : ds.datasets.length,
      page: typeof ds.page === 'number' ? ds.page : 1,
      page_size: typeof ds.page_size === 'number' ? ds.page_size : pageSize,
      datasets: ds.datasets
    }
  }

  // Fallbacks: some backends may return items/results instead of datasets
  const items = inner?.items ?? inner?.results ?? []
  if (Array.isArray(items)) {
    return {
      total: typeof inner?.total === 'number' ? inner.total : items.length,
      page: typeof inner?.page === 'number' ? inner.page : 1,
      page_size: typeof inner?.page_size === 'number' ? inner.page_size : pageSize,
      datasets: items
    }
  }

  // Empty default
  return { total: 0, page: 1, page_size: pageSize, datasets: [] }
}

export const fetchDatasets = async (
  project: string,
  environment: EnvName = getDetectedEnvironment(),
  page = 1,
  pageSize = 50
): Promise<DatasetApiResponse> => {
  try {
    const endpoint = `${rdUrl}api/v1/datasets/projects/${project}/environments/${environment}/datasets?page=${page}&page_size=${pageSize}`

    const response = await fetch(endpoint, {
      headers: getAuthHeaders()
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const raw = await response.json()
    if (!raw || typeof raw !== 'object') {
      throw new Error('Invalid response format: not an object')
    }
    return normalizeDatasetsResponse(raw, pageSize)
  } catch (error) {
    console.error('fetchDatasets failed', error)
    // Return empty response structure on error
    return {
      total: 0,
      page: 1,
      page_size: pageSize,
      datasets: []
    }
  }
}

export const fetchNotebooks = async (
  nameSpace: string,
): Promise<Notebook[]> => {
  try {
    const endpoint = `${rdUrl}api/v1/notebooks/namespaces/${nameSpace}/notebooks`

    const response = await fetch(endpoint)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const data = await response.json()
    
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid response format: not an object')
    }
    
    if (data.success && data.data) {
      return Object.values(data.data.notebooks || {})
    }
    
    return []

  } catch (error) {
    console.error('fetchNotebooks failed', error)
    return []
  }
}

export const fetchDatasetStats = async (catalog: string): Promise<DatasetStats | null> => {
  try {
    const endpoint = `${rdUrl}api/v1/datasets/${encodeURIComponent(catalog)}/stats`
    const response = await fetch(endpoint, { headers: getAuthHeaders() })
    if (!response.ok) {
      const errorText = await response.text()
      console.error('API error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }
    const raw = await response.json()
    const data = raw?.data ?? raw
    if (!data || typeof data !== 'object') return null
    return data as DatasetStats
  } catch (e) {
    console.error('fetchDatasetStats failed', e)
    return null
  }
}
