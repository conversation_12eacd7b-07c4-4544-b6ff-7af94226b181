export interface DatasetVersion {
  version: string
  path: string
  last_modified: string
  size_bytes: number
}

export interface ModelRegistryVersion {
  version: string
  path: string
  last_modified: string
  size_bytes: number
}

export interface Dataset {
  id: string
  name: string
  project: string
  environment: string
  path: string
  description: string
  created_at: string
  updated_at: string
  versions: DatasetVersion[]
  latest_version: DatasetVersion
}

export interface ModelRegistry {
  id: string
  name: string
  project: string
  environment: string
  versions: ModelRegistryVersion[]
  latest_version: ModelRegistryVersion
}

export interface DatasetApiResponse {
  total: number
  page: number
  page_size: number
  datasets: Dataset[]
}
export interface Notebook {
  age: string
  cpu: string
  gpus: {
    count: number
    message: string
  }
  image: string
  last_activity: string
  memory: string
  metadata: {
    annotations: {
      "notebooks.kubeflow.org/creator": string
      "notebooks.kubeflow.org/http-rewrite-uri": string
      "notebooks.kubeflow.org/server-type": string
      [key: string]: string
    }
    creationTimestamp: string
    generation: number
    labels: {
      app: string
      [key: string]: string
    }
    managedFields: Array<{
      apiVersion: string
      fieldsType: string
      fieldsV1: any
      manager: string
      operation: string
      time: string
      subresource?: string
    }>
    name: string
    namespace: string
    resourceVersion: string
    uid: string
  }
  name: string
  namespace: string
  serverType: string
  shortImage: string
  status: {
    message: string
    phase: string
    state: string
  }
  volumes: string[]
}

export interface NotebooksApiResponse {
  total: number
  notebooks: Record<string, Notebook>
}

export interface DatasetStats {
  dataset_id: string
  total_annotations: number
  unique_labellers: number
  labelled_strips: number
  episodes_touched: number
  labelled_episodes: number
  finished_episodes: number
  unfinished_episodes: number
  unsure_episodes: number
  last_activity_at?: string
}
