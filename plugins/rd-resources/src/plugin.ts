import rd, { rdId } from '@hcengineering/rd'
import { mergeIds } from '@hcengineering/platform'
import type { IntlString } from '@hcengineering/platform'

export default mergeIds(rdId, rd, {
  string: {
    Activity: '' as IntlString,
    Priority: '' as IntlString,
    DataSource: '' as IntlString,
    Format: '' as IntlString,
    CreatedDate: '' as IntlString,
    ParallelCoordinates: '' as IntlString,
    MSE: '' as IntlString
  }
})