import { type Resources } from '@hcengineering/platform'
import { type Space } from '@hcengineering/core'
import tracker from '@hcengineering/tracker'
import RdBlank from './components/RdBlank.svelte'
import DatasetsView from './components/datasets/DatasetsView.svelte'
import ModelRegistryView from './components/modelregistry/ModelRegistryView.svelte'
import EditDataset from './components/datasets/EditDataset.svelte'
import EditModelRegistry from './components/modelregistry/EditModelRegistry.svelte'
import DatasetControlPanel from './components/datasets/DatasetControlPanel.svelte'
import DatasetActivity from './components/datasets/DatasetActivity.svelte'
import DatasetPresenter from './components/datasets/DatasetPresenter.svelte'
import ModelRegistryPresenter from './components/modelregistry/ModelRegistryPresenter.svelte'
import DatasetDatePresenter from './components/datasets/DatasetDatePresenter.svelte'
import ExperimentationView from './components/experimentation/ExperimentationView.svelte'
import AnnotationView from './components/annotation/AnnotationView.svelte'
import ExperimentPresenter from './components/experimentation/ExperimentPresenter.svelte'
import ExperimentStatusPresenter from './components/experimentation/ExperimentStatusPresenter.svelte'
import ConnectButton from './components/experimentation/ConnectButton.svelte'
import ExperimentExpanded from './components/experimentation/ExperimentExpanded.svelte'
import ExperimentControlPanel from './components/experimentation/ExperimentControlPanel.svelte'
import ExperimentActivity from './components/experimentation/ExperimentActivity.svelte'
import ModelRegistryDatePresenter from './components/modelregistry/ModelRegistryDatePresenter.svelte'
import ModelRegistryCharts from './components/modelregistry/ModelRegistryCharts.svelte'
import ModelRegistryChartsPanel from './components/modelregistry/ModelRegistryChartsPanel.svelte'
import ModelRegistryHeader from './components/modelregistry/ModelRegistryHeader.svelte'
import ModelRegistryDetails from './components/modelregistry/ModelRegistryDetails.svelte'
import ExperimentsView from './components/experiments/ExperimentsView.svelte'
import NotebooksView from './components/notebooks/NotebooksView.svelte'

export default async (): Promise<Resources> => ({
  component: {
    RdBlank,
    RdView: DatasetsView,
    DatasetsView,
    ModelRegistryView,
    EditDataset,
    EditModelRegistry,
    DatasetControlPanel,
    DatasetActivity,
    DatasetPresenter,
    DatasetDatePresenter,
    ExperimentationView: ExperimentationView,
    AnnotationView,
    ExperimentPresenter,
    ExperimentStatusPresenter,
    ConnectButton,
    ExperimentExpanded,
    ExperimentControlPanel,
    ExperimentActivity,
    ModelRegistryPresenter,
    ModelRegistryCharts,
    ModelRegistryChartsPanel,
    ModelRegistryDatePresenter,
    ModelRegistryHeader,
    ModelRegistryDetails
  },
  function: {
    IsRnDProject: async (space: Space) => (space as any).type === tracker.ids.RnDProjectType
  }
})
