<script lang="ts">
  import { Class, Doc, DocumentQuery, Ref, Space, WithLookup } from '@hcengineering/core'
  import { Header, Breadcrumb } from '@hcengineering/ui'
  import type { Asset } from '@hcengineering/platform'
  import { Viewlet } from '@hcengineering/view'
  import { ViewletSelector } from '@hcengineering/view-resources'

  export let space: Ref<Space> | undefined = undefined
  export let _class: Ref<Class<Doc>>
  export let icon: Asset | undefined = undefined
  export let viewlet: WithLookup<Viewlet> | undefined
  export let viewletQuery: DocumentQuery<Viewlet> | undefined = undefined
  export let viewlets: Array<WithLookup<Viewlet>> = []
  export let label: string
</script>

<Header
  hideSearch={true}
  hideActions={true}
  hideExtra={true}
>
  <svelte:fragment slot="beforeTitle">
    <ViewletSelector bind:viewlet bind:viewlets ignoreFragment viewletQuery={viewletQuery ?? { attachTo: _class }} />
  </svelte:fragment>

  <Breadcrumb {icon} title={label} size={'large'} isCurrent />
</Header> 