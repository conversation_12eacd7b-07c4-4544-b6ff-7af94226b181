<script lang="ts">
  import rd from '../../plugin'
  import { getClient } from '@hcengineering/presentation'
  import { onMount } from 'svelte'
  import ExperimentStatusPresenter from '../experimentation/ExperimentStatusPresenter.svelte'

  export let modelRegistry: any  
  export let readonly: boolean = false

  let parentModelRegistry: any = null  

  
  onMount(async () => {
    if (modelRegistry?.modelRegistry) {
      const client = getClient()
      try {
        parentModelRegistry = await client.findOne(rd.class.ModelRegistry, { _id: modelRegistry.modelRegistry })
      } catch (err) {
        console.error('Failed to fetch parent modelRegistry:', err)
      }
    }
  })


  function formatDate(timestamp: number): string {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
</script>

<div class="popupPanel-body__aside-grid">
  <span class="labelOnPanel">
    Version
  </span>
  <div class="value-display">
    {modelRegistry?.version || 'N/A'}
  </div>

  <span class="labelOnPanel">
    Path
  </span>
  <a class="value-display" href={modelRegistry?.path} target="_blank">
    {modelRegistry?.path || 'N/A'}
  </a>

  <span class="labelOnPanel">
    Size
  </span>
  <div class="size-display">
    {modelRegistry?.sizeBytes ? formatBytes(modelRegistry.sizeBytes) : 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Status
  </span>
  <div class="value-display">
    <ExperimentStatusPresenter value={modelRegistry?.status || 'Pending'} inline />
  </div>

  <span class="labelOnPanel">
    Started
  </span>
  <div class="date-display">
    {modelRegistry?.startedAt ? formatDate(modelRegistry.startedAt) : 'N/A'}
  </div>

  <span class="labelOnPanel">
    Finished
  </span>
  <div class="date-display">
    {modelRegistry?.finishedAt ? formatDate(modelRegistry.finishedAt) : (modelRegistry?.status === 'Running' ? 'In progress' : 'N/A')}
  </div>

  <span class="labelOnPanel">
    Last Modified
  </span>
  <div class="date-display">
    {modelRegistry?.lastUpdated ? formatDate(modelRegistry.lastUpdated) : 'N/A'}
  </div>

  <div class="divider" />

  <!-- ModelRegistry DATA (from parent ModelRegistry) -->
  <span class="labelOnPanel">
    ModelRegistry
  </span>
  <div class="value-display">
    {parentModelRegistry?.name || modelRegistry?.ModelRegistryName || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Project
  </span>
  <div class="value-display">
    {parentModelRegistry?.project || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Environment
  </span>
  <div class="value-display">
    {parentModelRegistry?.environment || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    ModelRegistry Path
  </span>
  <a class="value-display" href={parentModelRegistry?.path} target="_blank">
    {parentModelRegistry?.path || 'N/A'}
  </a>
  
  <span class="labelOnPanel">
    Description
  </span>
  <div class="value-display">
    {parentModelRegistry?.description || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Created on
  </span>
  <div class="value-display">
    {parentModelRegistry?.createdAt ? formatDate(parentModelRegistry.createdAt) : 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Modified on
  </span>
  <div class="value-display">
    {parentModelRegistry?.updatedAt ? formatDate(parentModelRegistry.updatedAt) : 'N/A'}
  </div>
</div>

<style>
  .labelOnPanel {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--theme-content-color);
    margin-bottom: 0.25rem;
  }

  .divider {
    height: 1px;
    background: var(--theme-divider-color);
    margin: 1rem 0;
    grid-column: span 2;
  }

  .value-display,
  .size-display,
  .date-display {
    padding: 0.5rem;
    border-radius: 0.25rem;
    color: var(--theme-content-dark-color);
    font-size: 0.875rem;
  }

  .popupPanel-body__aside-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 1rem;
  }
</style> 