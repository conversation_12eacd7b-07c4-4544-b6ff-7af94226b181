export interface ModelVersionData {
  version: string
  timestamp: string
  size_mb: number
  runStatus?: 'Running' | 'Succeeded' | 'Failed' | 'Pending'
  started_at?: string
  finished_at?: string
}

export interface ModelData {
  id: string
  name: string
  parameters: {
    learning_rate: number
    max_depth: number
    reg_lambda: number
  }
  metrics: {
    accuracy: number
    precision: number
    recall: number
    f1_score: number
  }
  status: 'DEPLOYED' | 'STAGING' | 'REGISTERED' | 'DEPRECATED'
  framework: string
  versions: ModelVersionData[]
}

export const mockModelData: ModelData[] = [
  {
    id: 'model-001',
    name: 'ECG Classification Model',
    parameters: {
      learning_rate: 0.1,
      max_depth: 20,
      reg_lambda: 0.05
    },
    metrics: {
      accuracy: 0.925,
      precision: 0.918,
      recall: 0.912,
      f1_score: 0.915
    },
    status: 'DEPLOYED',
    framework: 'XGBoost',
    versions: [
      {
        version: '0.1.1',
        timestamp: '2024-01-15T10:30:00Z',
        size_mb: 42.1,
        runStatus: 'Succeeded',
        started_at: '2024-01-15T10:00:00Z',
        finished_at: '2024-01-15T10:30:00Z'
      },
      {
        version: '0.2.1',
        timestamp: '2024-02-05T09:20:00Z',
        size_mb: 48.3,
        runStatus: 'Running',
        started_at: '2024-02-05T09:00:00Z'
      }
    ]
  },
  {
    id: 'model-002', 
    name: 'Rexha bossi Model',
    parameters: {
      learning_rate: 0.05,
      max_depth: 40,
      reg_lambda: 0.10
    },
    metrics: {
      accuracy: 0.918,
      precision: 0.922,
      recall: 0.905,
      f1_score: 0.913
    },
    status: 'DEPLOYED',
    framework: 'RandomForest',
    versions: [
      {
        version: '2.0.0',
        timestamp: '2024-01-15T11:15:00Z',
        size_mb: 67.2,
        runStatus: 'Failed',
        started_at: '2024-01-15T10:45:00Z',
        finished_at: '2024-01-15T11:10:00Z'
      },
      {
        version: '2.1.0',
        timestamp: '2024-01-25T14:10:00Z',
        size_mb: 68.9,
        runStatus: 'Succeeded',
        started_at: '2024-01-25T13:50:00Z',
        finished_at: '2024-01-25T14:05:00Z'
      }
    ]
  },
  {
    id: 'model-003',
    name: 'Sentiment Analysis Model',
    parameters: {
      learning_rate: 0.2,
      max_depth: 15,
      reg_lambda: 0.01
    },
    metrics: {
      accuracy: 0.891,
      precision: 0.885,
      recall: 0.897,
      f1_score: 0.891
    },
    status: 'STAGING',
    framework: 'TensorFlow',
    versions: [
      {
        version: '1.5.0',
        timestamp: '2024-01-15T12:00:00Z',
        size_mb: 128.5,
        runStatus: 'Running',
        started_at: '2024-01-15T11:40:00Z'
      }
    ]
  }
]

export interface ModelPerformanceData {
  modelName: string
  normalizedValues: {
    accuracy: number
    precision: number
    recall: number
    f1_score: number
  }
  accuracy: number
}

export interface ModelSizeData {
  modelName: string
  size_mb: number
  framework: string
}

export function getModelPerformanceData(): ModelPerformanceData[] {
  return mockModelData.map(model => ({
    modelName: model.name,
    normalizedValues: {
      accuracy: model.metrics.accuracy,
      precision: model.metrics.precision,
      recall: model.metrics.recall,
      f1_score: model.metrics.f1_score
    },
    accuracy: model.metrics.accuracy
  }))
}

export function getModelSizeData(): ModelSizeData[] {
  return mockModelData.map(model => ({
    modelName: model.name,
    size_mb: model.versions?.[0]?.size_mb ?? 0,
    framework: model.framework
  }))
} 