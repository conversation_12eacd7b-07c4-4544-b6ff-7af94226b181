<script lang="ts">
  import { WithLookup } from '@hcengineering/core'
  import { showPanel } from '@hcengineering/ui'
  import rd from '../../plugin'
  import ExperimentStatusPresenter from '../experimentation/ExperimentStatusPresenter.svelte'
  

  export let value: WithLookup<any> | undefined
  export let disabled: boolean = false
  export let onClick: (() => void) | undefined = undefined

  function handleClick() {
    if (disabled || !value) return
    
    if (onClick) {
      onClick()
    } else {
      showPanel(rd.component.EditModelRegistry, value._id, value._class, 'content', undefined, false)
    }
  }
</script>

{#if value}
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <div class="listGrid">
    <div class="left">
      <span 
        class="overflow-label cursor-pointer name" 
        class:disabled
        title={value?.modelRegistryName || 'Model Registry'}
        on:click={handleClick}
      >
        {value.modelRegistryName || 'Unknown'}
      </span>
    </div>

    <div class="right">
      <ExperimentStatusPresenter value={value?.status || 'Pending'} inline />
    </div>
  </div>
{/if}

<style lang="scss">
  .listGrid {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
  }

  .left {
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .right {
    display: flex;
    justify-content: center;
  }

  .overflow-label.name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--theme-content-color);
  }
  
  .cursor-pointer {
    cursor: pointer;
  }
  
  .cursor-pointer:hover:not(.disabled) {
    color: var(--theme-accent-color);
    text-decoration: underline;
  }

  .disabled {
    cursor: default;
    opacity: 0.7;
  }
</style> 