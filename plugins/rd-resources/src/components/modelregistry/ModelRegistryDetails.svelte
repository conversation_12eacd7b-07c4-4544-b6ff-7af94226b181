<script lang="ts">
  import { onMount } from 'svelte'
  import { getClient } from '@hcengineering/presentation'
  import type { Ref } from '@hcengineering/core'
  import { location } from '@hcengineering/ui'
  import rd from '../../plugin'
  import ModelRegistryCharts from './ModelRegistryCharts.svelte'

  let modelId: Ref<any> | undefined = undefined
  let modelName: string = ''
  let version: string = ''
  let size: number = 0
  let loading = true
  let error: string | null = null

  $: if ($location.path.length >= 4) {
    modelId = $location.path[3] as Ref<any>
  }

  $: if ($location.query) {
    modelName = $location.query.modelName ?? ''
    version = $location.query.version ?? ''
    size = parseInt($location.query.size ?? '0')
  }

  onMount(async () => {
    if (!modelId) {
      error = 'Model ID not found'
      loading = false
      return
    }

    try {
      const client = getClient()
      const model = await client.findOne(rd.class.ModelRegistry, { _id: modelId })
      if (!model) {
        error = 'Model not found'
        return
      }

      // Here you would typically fetch additional model details and metrics
      loading = false
    } catch (err) {
      console.error('Failed to load model details:', err)
      error = err instanceof Error ? err.message : 'Failed to load model details'
      loading = false
    }
  })
</script>

<div class="model-details">
  <div class="header">
    <h1>{modelName}</h1>
    <div class="version">Version {version}</div>
  </div>

  {#if loading}
    <div class="loading">Loading model details...</div>
  {:else if error}
    <div class="error">{error}</div>
  {:else if modelId}
    <ModelRegistryCharts modelId={modelId} {modelName} {version} {size} />
  {/if}
</div>

<style lang="scss">
  .model-details {
    padding: 2rem;
    height: 100%;
    overflow-y: auto;
  }

  .header {
    margin-bottom: 2rem;

    h1 {
      margin: 0;
      color: var(--theme-caption-color);
      font-size: 1.5rem;
      font-weight: 600;
    }

    .version {
      margin-top: 0.5rem;
      color: var(--theme-content-accent-color);
      font-size: 0.875rem;
    }
  }

  .loading {
    color: var(--theme-content-dark-color);
    font-size: 0.875rem;
    text-align: center;
    padding: 2rem;
  }

  .error {
    color: var(--theme-error-color);
    font-size: 0.875rem;
    text-align: center;
    padding: 2rem;
  }
</style> 