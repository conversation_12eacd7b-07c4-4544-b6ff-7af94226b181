<script lang="ts">
  export let value: number | undefined // Now expecting timestamp directly

  function formatDateTime(timestamp: number): string {
    const date = new Date(timestamp)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
</script>

{#if value}
  <span class="date-text">
    {formatDateTime(value)}
  </span>
{:else}
  <span class="date-text-empty">
    No date
  </span>
{/if}

<style>
  .date-text {
    color: var(--theme-content-color);
    font-size: 0.875rem;
  }
  
  .date-text-empty {
    color: var(--theme-content-trans-color);
    font-size: 0.875rem;
    font-style: italic;
  }
</style> 