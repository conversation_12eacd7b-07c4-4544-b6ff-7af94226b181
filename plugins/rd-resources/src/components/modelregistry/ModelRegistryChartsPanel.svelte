<script lang="ts">
  import { Class, Doc, Ref, WithLookup } from '@hcengineering/core'
  import { Panel } from '@hcengineering/panel'
  import { createQuery, getClient } from '@hcengineering/presentation'
  import { DocNavLink } from '@hcengineering/view-resources'
  import { createEventDispatcher } from 'svelte'
  import rd from '../../plugin'
  import ModelRegistryControlPanel from './ModelRegistryControlPanel.svelte'
  import ModelRegistryCharts from './ModelRegistryCharts.svelte'

  export let _id: Ref<any> | string
  export let _class: Ref<Class<any>>
  export let embedded: boolean = false

  const queryClient = createQuery()
  const dispatch = createEventDispatcher()
  const client = getClient()

  let ModelRegistryVersion: WithLookup<any> | undefined  
  let parentModelRegistry: any = null  
  let innerWidth: number

  $: if (_id !== undefined && _class !== undefined) {
    queryClient.query<any>(
      _class,
      { _id },
      async (result) => {
        ;[ModelRegistryVersion] = result
        if (ModelRegistryVersion !== undefined) {
          // Fetch the parent ModelRegistry
          if (ModelRegistryVersion.ModelRegistry) {
            try {
              parentModelRegistry = await client.findOne(rd.class.ModelRegistry, { _id: ModelRegistryVersion.ModelRegistry })
            } catch (err) {
              console.error('Failed to fetch parent ModelRegistry:', err)
            }
          }
        }
      },
      {
        limit: 1
      }
    )
  }

  $: displayTitle = ModelRegistryVersion 
    ? ModelRegistryVersion.modelRegistryName + (ModelRegistryVersion.version ? ` - ${ModelRegistryVersion.version}` : '')
    : ''

</script>

{#if ModelRegistryVersion !== undefined}
  <Panel
    object={ModelRegistryVersion}
    isHeader={false}
    withoutInput={true}
    allowClose={!embedded}
    isAside={true}
    isSub={false}
    {embedded}
    withoutActivity={true}
    printAside={true}
    adaptive={'default'}
    bind:innerWidth
    on:open
    on:close={() => dispatch('close')}
    on:select
  >
    <svelte:fragment slot="title">
      {#if embedded}
        <DocNavLink noUnderline object={ModelRegistryVersion}>
          <div class="title">{displayTitle}</div>
        </DocNavLink>
      {:else}
        <div class="title not-active">{displayTitle}</div>
      {/if}
    </svelte:fragment>

    <!-- Main content area -->
    <ModelRegistryCharts
      modelId={ModelRegistryVersion._id}
      modelName={ModelRegistryVersion.modelRegistryName}
      version={ModelRegistryVersion.version}
      size={ModelRegistryVersion.sizeBytes}
    />

    <svelte:fragment slot="aside">
      {#if ModelRegistryVersion !== undefined}
        <ModelRegistryControlPanel modelRegistry={ModelRegistryVersion} readonly={true} />
      {/if}
    </svelte:fragment>
  </Panel>
{/if}

<style>
  .title {
    font-size: 1.2rem;
    font-weight: 600;
  }

  .title.not-active {
    color: var(--theme-content-color);
  }
</style> 