<script lang="ts">
  import { onMount } from 'svelte'
  import { getClient } from '@hcengineering/presentation'
  import type { Ref } from '@hcengineering/core'
  import rd from '../../plugin'

  export let modelId: Ref<any>
  export let modelName: string
  export let version: string
  export let size: number

  let performanceData = {
    accuracy: 0.95,
    precision: 0.92,
    recall: 0.94,
    f1_score: 0.93
  }

  let trainingHistory = [
    { epoch: 1, loss: 0.5, accuracy: 0.85 },
    { epoch: 2, loss: 0.3, accuracy: 0.89 },
    { epoch: 3, loss: 0.2, accuracy: 0.92 },
    { epoch: 4, loss: 0.15, accuracy: 0.94 },
    { epoch: 5, loss: 0.12, accuracy: 0.95 }
  ]

  let resourceUsage = {
    cpu: 45,
    memory: 75,
    gpu: 85
  }

  function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  onMount(() => {
    // Here you would typically fetch real metrics for this specific model
    console.log(`Loading metrics for model: ${modelId}`)
  })
</script>

<div class="model-charts">
  <div class="metrics-grid">
    <div class="metric-card">
      <h3>Model Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">Name:</span>
          <span class="value">{modelName}</span>
        </div>
        <div class="info-item">
          <span class="label">Version:</span>
          <span class="value">{version}</span>
        </div>
        <div class="info-item">
          <span class="label">Size:</span>
          <span class="value">{formatBytes(size)}</span>
        </div>
      </div>
    </div>

    <div class="metric-card">
      <h3>Performance Metrics</h3>
      <div class="metrics-list">
        <div class="metric-item">
          <span class="label">Accuracy:</span>
          <div class="progress-bar">
            <div class="progress" style="width: {performanceData.accuracy * 100}%"></div>
          </div>
          <span class="value">{(performanceData.accuracy * 100).toFixed(1)}%</span>
        </div>
        <div class="metric-item">
          <span class="label">Precision:</span>
          <div class="progress-bar">
            <div class="progress" style="width: {performanceData.precision * 100}%"></div>
          </div>
          <span class="value">{(performanceData.precision * 100).toFixed(1)}%</span>
        </div>
        <div class="metric-item">
          <span class="label">Recall:</span>
          <div class="progress-bar">
            <div class="progress" style="width: {performanceData.recall * 100}%"></div>
          </div>
          <span class="value">{(performanceData.recall * 100).toFixed(1)}%</span>
        </div>
        <div class="metric-item">
          <span class="label">F1 Score:</span>
          <div class="progress-bar">
            <div class="progress" style="width: {performanceData.f1_score * 100}%"></div>
          </div>
          <span class="value">{(performanceData.f1_score * 100).toFixed(1)}%</span>
        </div>
      </div>
    </div>

    <div class="metric-card">
      <h3>Resource Usage</h3>
      <div class="metrics-list">
        <div class="metric-item">
          <span class="label">CPU:</span>
          <div class="progress-bar">
            <div class="progress" style="width: {resourceUsage.cpu}%"></div>
          </div>
          <span class="value">{resourceUsage.cpu}%</span>
        </div>
        <div class="metric-item">
          <span class="label">Memory:</span>
          <div class="progress-bar">
            <div class="progress" style="width: {resourceUsage.memory}%"></div>
          </div>
          <span class="value">{resourceUsage.memory}%</span>
        </div>
        <div class="metric-item">
          <span class="label">GPU:</span>
          <div class="progress-bar">
            <div class="progress" style="width: {resourceUsage.gpu}%"></div>
          </div>
          <span class="value">{resourceUsage.gpu}%</span>
        </div>
      </div>
    </div>

    <div class="metric-card">
      <h3>Training History</h3>
      <div class="chart">
        <div class="chart-grid">
          {#each trainingHistory as point}
            <div 
              class="chart-bar" 
              style="height: {point.accuracy * 100}%"
              title="Epoch {point.epoch}: {(point.accuracy * 100).toFixed(1)}%"
            ></div>
          {/each}
        </div>
        <div class="chart-labels">
          {#each trainingHistory as point}
            <div class="chart-label">{point.epoch}</div>
          {/each}
        </div>
      </div>
    </div>
  </div>
</div>

<style lang="scss">
  .model-charts {
    padding: 1rem;
    height: 100%;
    overflow-y: auto;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
  }

  .metric-card {
    background: var(--theme-bg-accent);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 1rem;
      color: var(--theme-caption-color);
      font-size: 1rem;
      font-weight: 500;
    }
  }

  .info-grid {
    display: grid;
    gap: 0.5rem;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--theme-bg-accent);
    border-radius: 4px;

    .label {
      color: var(--theme-content-dark-color);
      font-size: 0.875rem;
    }

    .value {
      color: var(--theme-content-color);
      font-weight: 500;
    }
  }

  .metrics-list {
    display: grid;
    gap: 0.75rem;
  }

  .metric-item {
    display: grid;
    grid-template-columns: 80px 1fr 50px;
    align-items: center;
    gap: 0.5rem;

    .label {
      color: var(--theme-content-dark-color);
      font-size: 0.875rem;
    }

    .value {
      color: var(--theme-content-color);
      font-size: 0.875rem;
      text-align: right;
    }
  }

  .progress-bar {
    height: 8px;
    background: var(--theme-bg-dark);
    border-radius: 4px;
    overflow: hidden;

    .progress {
      height: 100%;
      background: var(--theme-accent-color);
      border-radius: 4px;
      transition: width 0.3s ease;
    }
  }

  .chart {
    height: 200px;
    padding: 1rem 0;
    position: relative;
  }

  .chart-grid {
    height: 100%;
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
    padding-bottom: 1.5rem;
  }

  .chart-bar {
    flex: 1;
    background: var(--theme-accent-color);
    border-radius: 4px 4px 0 0;
    min-height: 4px;
    transition: height 0.3s ease;
    cursor: pointer;

    &:hover {
      background: var(--theme-accent-hover-color);
    }
  }

  .chart-labels {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;

    .chart-label {
      color: var(--theme-content-dark-color);
      font-size: 0.75rem;
    }
  }
</style> 