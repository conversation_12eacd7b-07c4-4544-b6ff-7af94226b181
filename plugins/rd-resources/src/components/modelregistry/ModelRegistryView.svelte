<script lang="ts">
  import { DocumentQuery, Ref, Space, WithLookup, SortingOrder } from '@hcengineering/core'
  import { ViewletContentView, ViewletSelector } from '@hcengineering/view-resources'
  import { Asset, IntlString, translateCB } from '@hcengineering/platform'
  import { themeStore, Header, Breadcrumb } from '@hcengineering/ui'
  import { getClient } from '@hcengineering/presentation'
  import rd from '../../plugin'
  import { mockModelData } from './mockData'

  export let space: Ref<Space> | undefined = undefined
  export let query: DocumentQuery<any> = {}
  export let title: IntlString | undefined = rd.string.ModelRegistry
  export let label: string = ''
  export let icon: Asset | undefined = undefined

  let viewlet: WithLookup<any> | undefined = undefined
  let viewlets: WithLookup<any>[] = []

  const viewOptions: any = {
    groupBy: ['modelRegistryName'],
    orderBy: ['lastUpdated', SortingOrder.Descending]
  }

  let resultQuery: DocumentQuery<any> = { ...query }

  $: if (title) {
    translateCB(title, {}, $themeStore.language, (res) => {
      label = res
    })
  }

  const client = getClient()
  let loading = false
  let error: string | null = null
  let modelsFetched = false

  $: if (space) {
    modelsFetched = false
    error = null
  }

  async function loadMockData() {
    if (modelsFetched || !space || loading) return
    
    loading = true
    error = null
    
    try {
      // Clear existing data
      const existingModels = await client.findAll(rd.class.ModelRegistry, { space })
      const existingVersions = await client.findAll(rd.class.ModelRegistryVersions, { space })
      
      for (const model of existingModels) {
        await client.remove(model)
      }
      for (const version of existingVersions) {
        await client.remove(version)
      }

      // Seed from provided mock data
      for (const model of mockModelData) {
        const modelName = model.name
        const modelDoc = await client.createDoc(rd.class.ModelRegistry, space, {
          name: modelName,
          project: 'Default Project',
          environment: model.status,
          path: `/models/${modelName.toLowerCase().replace(/ /g, '-')}`,
          description: `${model.framework} model (${model.status})`,
          createdAt: Date.now(),
          updatedAt: Date.now()
        })

        for (const v of model.versions) {
          const lastUpdated = new Date(v.timestamp).getTime() || Date.now()
          const sizeBytes = Math.round(v.size_mb * 1024 * 1024)

          await client.createDoc(rd.class.ModelRegistryVersions, space, {
            modelRegistry: modelDoc,
            modelRegistryName: modelName,
            version: v.version,
            path: `/models/${modelName.toLowerCase().replace(/ /g, '-')}/v${v.version}`,
            lastUpdated,
            sizeBytes,
            // Additional fields to mimic experimentation statuses
            status: v.runStatus ?? 'Pending',
            startedAt: v.started_at ? new Date(v.started_at).getTime() : undefined,
            finishedAt: v.finished_at ? new Date(v.finished_at).getTime() : undefined,
            url: `/models/${modelName.toLowerCase().replace(/ /g, '-')}/v${v.version}`
          })
        }
      }
      
      modelsFetched = true
      
    } catch (err) {
      console.error('Failed to load mock data:', err)
      error = err instanceof Error ? err.message : 'Failed to load models'
      
    } finally {
      loading = false
    }
  }

  $: if (space && !modelsFetched && !loading) {
    loadMockData()
  }
</script>

<Header>
  <svelte:fragment slot="beforeTitle">
    <ViewletSelector bind:viewlet bind:viewlets viewletQuery={{ attachTo: rd.class.ModelRegistryVersions }} />
  </svelte:fragment>

  {#if label}
    <Breadcrumb {icon} title={label} size={'large'} isCurrent />
  {/if}
</Header>

{#if loading}
  <div class="loading-indicator">Loading models...</div>
{:else if error}
  <div class="error-indicator" title={error}>Error loading models</div>
{:else if viewlet}
  <ViewletContentView
    _class={rd.class.ModelRegistryVersions}
    {viewlet}
    query={resultQuery}
    {space}
    {viewOptions}
  />
{/if}

<style lang="scss">
  .loading-indicator {
    color: var(--theme-content-dark-color);
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
    text-align: center;
  }
  
  .error-indicator {
    color: var(--theme-error-color);
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
    text-align: center;
    cursor: pointer;
  }

  :global(.listGrid) {
    gap: 1rem;
    padding: 0.75rem 1rem 0.75rem 1rem;
    width: 100%;
    display: flex;
    align-items: center;
  }
  :global(.listGrid.compactMode) {
    gap: 0.75rem;
    padding: 0.5rem 1rem 0.5rem 1rem;
  }
  :global(.listGrid .antiButton),
  :global(.listGrid button[data-id="rd:string:Connect"]),
  :global(.listGrid .button) {
    margin-left: auto !important;
  }
  :global(.listGrid > :last-child) {
    margin-left: auto !important;
  }
</style> 