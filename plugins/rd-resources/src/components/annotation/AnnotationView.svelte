<script lang="ts">
  import { DocumentQuery, Ref, Space, WithLookup, SortingOrder } from '@hcengineering/core'
  import core from '@hcengineering/core'
  import { Asset, IntlString, translateCB } from '@hcengineering/platform'
  import { getClient } from '@hcengineering/presentation'
  import { themeStore } from '@hcengineering/ui'
  import { SpaceHeader, ViewletContentView } from '@hcengineering/view-resources'
  import rd from '../../plugin'
  import { fetchDatasetStats } from '../../api'
  import type { DatasetStats } from '../../types'

  export let space: Ref<Space> | undefined = undefined
  export let query: DocumentQuery<any> = {}
  export let title: IntlString | undefined = undefined
  export let label: string = ''
  export let icon: Asset | undefined = undefined

  let viewlet: WithLookup<any> | undefined = undefined
  const viewlets: WithLookup<any>[] | undefined = undefined

  const viewOptions: any = {
    groupBy: ['datasetName'],
    orderBy: ['lastUpdated', SortingOrder.Descending]
  }

  const client = getClient()
  let loading = false
  let error: string | null = null
  let stats: DatasetStats | null = null
  let search = ''

  $: if (title) {
    translateCB(title, {}, $themeStore.language, (res) => {
      label = res
    })
  }

  async function loadStatsForProject () {
    if (!space || loading) return
    loading = true
    error = null
    stats = null
    try {
      const spaceDoc = await client.findOne(core.class.Space, { _id: space })
      // Use project identifier as catalog/dataset id for stats
      const projectIdentifier = ((spaceDoc as any)?.identifier ?? '').toString()
      if (!projectIdentifier) {
        throw new Error('Project identifier not found')
      }
      const res = await fetchDatasetStats(projectIdentifier)
      stats = res
    } catch (e: any) {
      error = e?.message ?? 'Failed to load stats'
    } finally {
      loading = false
    }
  }

  $: if (space) {
    loadStatsForProject()
  }
</script>

<SpaceHeader
  _class={rd.class.DatasetVersion}
  {icon}
  bind:viewlet
  bind:search
  viewletQuery={{ attachTo: rd.class.DatasetVersion }}
  {viewlets}
  {label}
  {space}
>
  <svelte:fragment slot="extra">
    {#if loading}
      <span class="loading-indicator">Loading stats…</span>
    {:else if error}
      <span class="error-indicator" title={error}>Failed to load stats</span>
    {:else if stats}
      <div class="stats">
        <div class="stat"><span class="k">Annotations</span><span class="v">{stats.total_annotations}</span></div>
        <div class="stat"><span class="k">Labellers</span><span class="v">{stats.unique_labellers}</span></div>
        <div class="stat"><span class="k">Labelled strips</span><span class="v">{stats.labelled_strips}</span></div>
        <div class="stat"><span class="k">Episodes touched</span><span class="v">{stats.episodes_touched}</span></div>
        <div class="stat"><span class="k">Labelled episodes</span><span class="v">{stats.labelled_episodes}</span></div>
        <div class="stat"><span class="k">Finished</span><span class="v">{stats.finished_episodes}</span></div>
        <div class="stat"><span class="k">Unfinished</span><span class="v">{stats.unfinished_episodes}</span></div>
        <div class="stat"><span class="k">Unsure</span><span class="v">{stats.unsure_episodes}</span></div>
      </div>
    {/if}
  </svelte:fragment>
</SpaceHeader>

{#if viewlet}
  <ViewletContentView
    _class={rd.class.DatasetVersion}
    {viewlet}
    {query}
    {space}
    {viewOptions}
  />
{/if}

<style>
  .stats {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
  }
  .stat { display: flex; gap: 0.25rem; align-items: baseline; }
  .k { color: var(--theme-content-trans-color); font-size: 0.75rem; }
  .v { color: var(--theme-content-color); font-weight: 600; }
  .loading-indicator { color: var(--theme-content-trans-color); font-size: 0.8rem; }
  .error-indicator { color: var(--theme-error-color); font-size: 0.8rem; }
  :global(.listGrid) { gap: 1rem; padding: 0.75rem 1rem 0.75rem 1rem; width: 100%; display: flex; align-items: center; }
  :global(.listGrid.compactMode) { gap: 0.75rem; padding: 0.5rem 1rem 0.5rem 1rem; }
</style>


