<script lang="ts">
    import { Label } from '@hcengineering/ui'
    import { Component } from '@hcengineering/ui'
    import activity from '@hcengineering/activity'
  
    export let experiment: any
    export let accentHeader: boolean = false
  </script>
  
  <div class="activity-container">
    <div class="activity-header" class:accent={accentHeader}>
      <Label label={activity.string.Activity} />
    </div>
  
    <Component
      is={activity.component.ActivityExtension}
      props={{
        object: experiment,
        showCommenInput: true
      }}
    />
  </div>
  
  <style>
    .activity-container {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 1rem;
    }
  
    .activity-header {
      font-weight: 600;
      font-size: 0.9rem;
      color: var(--theme-content-color);
      border-bottom: 1px solid var(--theme-divider-color);
      padding-bottom: 0.5rem;
    }
  
    .activity-header.accent {
      color: var(--theme-accent-color);
    }
  </style> 