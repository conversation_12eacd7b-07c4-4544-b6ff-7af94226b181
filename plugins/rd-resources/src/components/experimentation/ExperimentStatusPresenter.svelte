<script lang="ts">
  import { WithLookup } from '@hcengineering/core'
  import { IconSize, tooltip } from '@hcengineering/ui'
  import ExperimentStatusIcon from './ExperimentStatusIcon.svelte'

  export let value: WithLookup<any> | undefined
  export let size: IconSize = 'small'
  export let inline: boolean = false

  $: status = typeof value === 'string' ? value : (value?.status || 'Pending')
</script>

{#if value}
  <div 
    class="flex-row-center experiment-status-icon" 
    class:inline
    use:tooltip={{ label: status }}
  >
    <ExperimentStatusIcon 
      {status} 
      {size} 
    />
  </div>
{:else}
  <div class="experiment-status-icon">
    <span class="overflow-label">-</span>
  </div>
{/if}

<style lang="scss">
  .experiment-status-icon {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  
  .overflow-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .inline {
    display: inline-flex;
  }
</style> 