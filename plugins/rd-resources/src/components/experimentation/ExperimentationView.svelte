<script lang="ts">
  import { DocumentQuery, Ref, Space, WithLookup, SortingOrder } from '@hcengineering/core'
  import { Asset, IntlString, translateCB } from '@hcengineering/platform'
  import { getClient } from '@hcengineering/presentation'
  import { themeS<PERSON>, Header, Breadcrumb } from '@hcengineering/ui'
  import { ViewletContentView, ViewletSelector } from '@hcengineering/view-resources'
  import rd from '../../plugin'
  import tracker from '@hcengineering/tracker'
  import { fetchNotebooks } from '../../api'
  import type { Notebook } from '../../types'

  export let space: Ref<Space> | undefined = undefined
  export let query: DocumentQuery<any> = {}
  export let title: IntlString | undefined = undefined
  export let label: string = ''
  export let icon: Asset | undefined = undefined

  let viewlet: WithLookup<any> | undefined = undefined
  let viewlets: WithLookup<any>[] = []

  const viewOptions: any = {
    groupBy: ['annotator'],
    orderBy: ['creationTimestamp', SortingOrder.Ascending]
  }

  $: if (title) {
    translateCB(title, {}, $themeStore.language, (res) => {
      label = res
    })
  }

  const client = getClient()
  let loading = false
  let error: string | null = null
  let experimentsFetched = false
  
  $: if (space) {
    experimentsFetched = false
    error = null
  }

  async function loadNotebooksAsExperiments() {
    loading = true
    error = null
    
    try {
      const currentSpace = await client.findOne(tracker.class.Project, { _id: space as any })
      const projectNamespace = currentSpace?.nameSpace || ''
      
      const existingExperiments = await client.findAll(rd.class.Experiment, { space })
      const notebooks = await fetchNotebooks(projectNamespace)
      
      for (const notebook of notebooks) {
        const existingExperiment = existingExperiments.find((exp: any) => exp.id === notebook.metadata.uid)
        
        if (!existingExperiment) {
          await client.createDoc(rd.class.Experiment, space!, {
            id: notebook.metadata.uid,
            name: notebook.metadata.name,
            nameSpace: notebook.metadata.namespace,
            creationTimestamp: notebook.metadata.creationTimestamp,
            annotator: notebook.metadata.annotations?.["notebooks.kubeflow.org/creator"] || 'Unknown',
            team: notebook.metadata.labels?.team || 'Unknown',
            cpuResource: notebook.cpu || 'Unknown',
            memoryResource: notebook.memory || 'Unknown',
            status: notebook.status?.message || 'Unknown',
            url: `http://**************/notebook/${projectNamespace}/${notebook.metadata.name}/?folder=/home/<USER>
            template: notebook.image || 'Unknown'
          })
        }
      }
    } catch (err) {
      console.error('Failed to load notebooks:', err)
      error = err instanceof Error ? err.message : 'Failed to load notebooks'
    } finally {
      loading = false
      experimentsFetched = true  
    }
  }

  $: if (space && !experimentsFetched && !loading) {
    loadNotebooksAsExperiments()
  }
</script>

<Header>
  <svelte:fragment slot="beforeTitle">
    <ViewletSelector bind:viewlet bind:viewlets viewletQuery={{ attachTo: rd.class.Experiment }} />
  </svelte:fragment>
  
  {#if label}
    <Breadcrumb {icon} title={label} size={'large'} isCurrent />
  {/if}
</Header>

{#if viewlet}
  <ViewletContentView
    _class={rd.class.Experiment}
    {viewlet}
    query={query}
    {space}
    {viewOptions}
  />
{/if}

<style>
  .loading-indicator {
    color: #888;
    font-size: 12px;
    margin-left: 8px;
  }
  
  .error-indicator {
    color: #ff6b6b;
    font-size: 12px;
    margin-left: 8px;
    cursor: pointer;
  }
  :global(.listGrid) {
    gap: 1rem;
    padding: 0.75rem 1rem 0.75rem 1rem;
    width: 100%;
    display: flex;
    align-items: center;
  }
  :global(.listGrid.compactMode) {
    gap: 0.75rem;
    padding: 0.5rem 1rem 0.5rem 1rem;
  }
  :global(.listGrid .antiButton),
  :global(.listGrid button[data-id="rd:string:Connect"]),
  :global(.listGrid .button) {
    margin-left: auto !important;
  }
  :global(.listGrid > :last-child) {
    margin-left: auto !important;
  }
</style>