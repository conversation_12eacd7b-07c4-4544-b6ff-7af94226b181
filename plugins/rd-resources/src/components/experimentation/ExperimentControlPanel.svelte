<script lang="ts">
  import rd from '../../plugin'
  import { getClient } from '@hcengineering/presentation'
  import { onMount } from 'svelte'
  import { DatePresenter } from '@hcengineering/ui'
  import { DateRangeMode } from '@hcengineering/core'

  export let experiment: any  
  export let readonly: boolean = false

  let parentExperiment: any = null  

  
  onMount(async () => {
    if (experiment?.experiment) {
      const client = getClient()
      try {
        parentExperiment = await client.findOne(rd.class.Experiment, { _id: experiment.experiment })
      } catch (err) {
        console.error('Failed to fetch experiment:', err)
      }
    }
  })

</script>

<div class="popupPanel-body__aside-grid " style:margin-top="0">
  <span class="labelOnPanel">
    Name
  </span>
  <div class="value-display">
    {experiment?.name || 'N/A'}
  </div>

  <span class="labelOnPanel">
    Name Space
  </span>
  <div class="value-display">
    {experiment?.nameSpace || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Annotator
  </span>
  <div class="date-display">
    {experiment?.annotator || 'N/A'}
  </div>

  <span class="labelOnPanel">
    Team
  </span>
  <div class="value-display">
    { experiment?.team || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Status
  </span>
  <div class="value-display">
    {experiment?.status || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Link
  </span>
  <a class="value-display" href={experiment?.url}>
    {experiment?.url || 'N/A'}
  </a>

  <div class="divider" />

  <span class="labelOnPanel">
    Created At
  </span>
  <div class="size-display">
    {#if experiment?.creationTimestamp}
      <DatePresenter value={experiment.creationTimestamp} mode={DateRangeMode.DATETIME} kind="list" />
    {:else}
      N/A
    {/if}
  </div>

  <span class="labelOnPanel">
    CPU Resource
  </span>
  <div class="size-display">
    {experiment?.cpuResource || 'N/A'}
  </div>

  <span class="labelOnPanel">
    Memory Resource
  </span>
  <div class="size-display">
    {experiment?.memoryResource || 'N/A'}
  </div>



</div>

<style>
  .labelOnPanel {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--theme-content-color);
    margin-bottom: 0.25rem;
  }

  .divider {
    height: 1px;
    background: var(--theme-divider-color);
    margin: 1rem 0;
    grid-column: span 2;
  }

  .value-display,
  .size-display,
  .date-display {
    padding: 0.5rem;
    border-radius: 0.25rem;
    color: var(--theme-content-dark-color);
    font-size: 0.875rem;
  }

  .popupPanel-body__aside-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 1rem;
  }
</style> 