<script lang="ts">
  import { WithLookup } from '@hcengineering/core'
  import { showPanel } from '@hcengineering/ui'
  import rd from '../../plugin'

  export let value: WithLookup<any> | undefined
  export let disabled: boolean = false
  export let onClick: (() => void) | undefined = undefined

  function handleClick() {
    if (disabled || !value) return
    
    if (onClick) {
      onClick()
    } else {
      showPanel(rd.component.ExperimentExpanded, value._id, value._class, 'content')
    }
  }
</script>

{#if value}
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <!-- svelte-ignore a11y-no-static-element-interactions -->
  <span 
    class="overflow-label cursor-pointer" 
    class:disabled
    title={value?.name || value?.nameSpace || 'Experiment'}
    on:click={handleClick}
  >
    {value.name || value.nameSpace || 'Unknown'}
  </span>
{/if}

<style lang="scss">
  .overflow-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--theme-content-color);
  }
  
  .cursor-pointer {
    cursor: pointer;
  }
  
  .cursor-pointer:hover:not(.disabled) {
    color: var(--theme-accent-color);
    text-decoration: underline;
  }

  .disabled {
    cursor: default;
    opacity: 0.7;
  }
</style> 