<script lang="ts">
  import { WithLookup } from '@hcengineering/core'
  import { Button } from '@hcengineering/ui'
  import rd from '../../plugin'

  export let value: WithLookup<any> | undefined
  export let disabled: boolean = false

  $: url = typeof value === 'string' ? value : value?.url

  function handleConnect() {
    if (url) {
      window.open(url, '_blank')
    }
  }
</script>

{#if url}
  <Button 
    label={rd.string.Connect}
    size="small"
    on:click={handleConnect}
    {disabled}
  />
{/if} 