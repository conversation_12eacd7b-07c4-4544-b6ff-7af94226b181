<script lang="ts">
  import { Class, Doc, Ref, WithLookup } from '@hcengineering/core'
  import { Panel } from '@hcengineering/panel'
  import presentation, { createQuery, getClient } from '@hcengineering/presentation'
  import { EditBox, Label } from '@hcengineering/ui'
  import { DocNavLink } from '@hcengineering/view-resources'
  import { createEventDispatcher, onDestroy } from 'svelte'
  import rd from '../../plugin'
  import ExperimentControlPanel from './ExperimentControlPanel.svelte'

  export let _id: Ref<any> | string
  export let _class: Ref<Class<any>>
  export let embedded: boolean = false
  export let readonly: boolean = false

  const queryClient = createQuery()
  const dispatch = createEventDispatcher()
  const client = getClient()

  let experiment: WithLookup<any> | undefined  
  let title = ''
  let innerWidth: number
  let saved = false

  $: if (_id !== undefined && _class !== undefined) {
    queryClient.query<any>(
      _class,
      { _id },
      async (result) => {
        ;[experiment] = result
        if (experiment !== undefined) {
          title = experiment.name || experiment.experimentName || 'Unknown Experiment'
        }
      },
      {
        limit: 1
      }
    )
  }

  async function save(): Promise<void> {
    if (experiment === undefined || readonly) return
    
    await client.update(experiment, { name: title })
    saved = true
    setTimeout(() => { saved = false }, 2000)
  }

  $: displayTitle = experiment 
    ? `${experiment.name || experiment.experimentName || 'Unknown'}`
    : 'Loading...'

</script>

{#if experiment !== undefined}
  <Panel
    object={experiment}
    isHeader={false}
    withoutInput={readonly}
    allowClose={!embedded}
    isAside={true}
    isSub={false}
    {embedded}
    withoutActivity={false}
    printAside={true}
    adaptive={'default'}
    bind:innerWidth
    on:open
    on:close={() => dispatch('close')}
    on:select
  >
    <svelte:fragment slot="title">
      {#if embedded}
        <DocNavLink noUnderline object={experiment}>
          <div class="title">{displayTitle}</div>
        </DocNavLink>
      {:else}
        <div class="title not-active">{displayTitle}</div>
      {/if}
    </svelte:fragment>

    <svelte:fragment slot="pre-utils">
      {#if saved}
        <Label label={presentation.string.Saved} />
      {/if}
    </svelte:fragment>

    <!-- Main content area -->
    <div class="w-full mt-6">
      <EditBox
        bind:value={title}
        placeholder={rd.string.Name}
        kind={'large-style'}
        disabled={readonly}
        on:change={save}
      />
    </div>

    <svelte:fragment slot="aside">
      {#if experiment !== undefined}
        <ExperimentControlPanel experiment={experiment} {readonly} />
      {/if}
    </svelte:fragment>
  </Panel>
{/if}

<style>
  .title {
    font-size: 1.2rem;
    font-weight: 600;
  }

  .title.not-active {
    color: var(--theme-content-color);
  }

  .space-divider {
    height: 1px;
    background: var(--theme-divider-color);
    margin: 1rem 0;
  }
</style> 