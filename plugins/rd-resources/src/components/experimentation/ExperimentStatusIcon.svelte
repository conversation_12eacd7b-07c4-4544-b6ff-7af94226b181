<script lang="ts">
  import { Icon, IconSize } from '@hcengineering/ui'
  import tracker from '@hcengineering/tracker'

  export let status: string | undefined
  export let size: IconSize = 'small'

  type ExperimentStatus = 'Running' | 'Succeeded' | 'Failed' | 'Pending'

  const statusIcons: Record<ExperimentStatus, any> = {
    'Running': tracker.icon.CategoryStarted,
    'Succeeded': tracker.icon.CategoryCompleted,
    'Failed': tracker.icon.CategoryCanceled,
    'Pending': tracker.icon.CategoryStarted  
  }

  $: iconAsset = status && status in statusIcons ? statusIcons[status as ExperimentStatus] : tracker.icon.CategoryStarted
</script>

{#if status}
  <Icon icon={iconAsset} {size} />
{:else}
  <Icon icon={tracker.icon.CategoryStarted} {size} />
{/if} 