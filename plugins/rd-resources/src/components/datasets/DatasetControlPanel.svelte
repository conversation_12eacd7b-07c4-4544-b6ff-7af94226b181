<script lang="ts">
  import rd from '../../plugin'
  import { getClient } from '@hcengineering/presentation'
  import { onMount } from 'svelte'
  import { DatePresenter } from '@hcengineering/ui'
  import { DateRangeMode } from '@hcengineering/core'
  import FileSizePresenter from '@hcengineering/view-resources/src/components/FileSizePresenter.svelte'
  
  export let dataset: any  
  export let readonly: boolean = false

  let parentDataset: any = null  

  
  onMount(async () => {
    if (dataset?.dataset) {
      const client = getClient()
      try {
        parentDataset = await client.findOne(rd.class.Dataset, { _id: dataset.dataset })
      } catch (err) {
        console.error('Failed to fetch parent dataset:', err)
      }
    }
  })

</script>

<div class="popupPanel-body__aside-grid">
  <span class="labelOnPanel">
    Version
  </span>
  <div class="value-display">
    {dataset?.version || 'N/A'}
  </div>

  <span class="labelOnPanel">
    Path
  </span>
  <a class="value-display" href={dataset?.path} target="_blank">
    {dataset?.path || 'N/A'}
  </a>

  <span class="labelOnPanel">
    Size
  </span>
  <div class="size-display">
    <FileSizePresenter value={dataset?.sizeBytes} />
  </div>
  
  <span class="labelOnPanel">
    Last Modified
  </span>
  <div class="size-display">
    {#if dataset?.lastUpdated}
      <DatePresenter value={dataset.lastUpdated} mode={DateRangeMode.DATETIME} kind="list" />
    {:else}
      N/A
    {/if}
  </div>

  <div class="divider" />

  <!-- DATASET DATA (from parent Dataset) -->
  <span class="labelOnPanel">
    Dataset
  </span>
  <div class="value-display">
    {parentDataset?.name || dataset?.datasetName || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Project
  </span>
  <div class="value-display">
    {parentDataset?.project || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Environment
  </span>
  <div class="value-display">
    {parentDataset?.environment || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Dataset Path
  </span>
  <a class="value-display" href={parentDataset?.path} target="_blank">
    {parentDataset?.path || 'N/A'}
  </a>
  
  <span class="labelOnPanel">
    Description
  </span>
  <div class="value-display">
    {parentDataset?.description || 'N/A'}
  </div>
  
  <span class="labelOnPanel">
    Created on
  </span>
  <div class="size-display">
    {#if parentDataset?.createdAt}
      <DatePresenter value={parentDataset.createdAt} mode={DateRangeMode.DATETIME} kind="list" />
    {:else}
      N/A
    {/if}
  </div>
  
  <span class="labelOnPanel">
    Modified on
  </span>
  <div class="size-display">
    {#if parentDataset?.updatedAt}
      <DatePresenter value={parentDataset.updatedAt} mode={DateRangeMode.DATETIME} kind="list" />
    {:else}
      N/A
    {/if}
  </div>
</div>

<style>
  .labelOnPanel {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--theme-content-color);
    margin-bottom: 0.25rem;
  }

  .divider {
    height: 1px;
    background: var(--theme-divider-color);
    margin: 1rem 0;
    grid-column: span 2;
  }

  .value-display,
  .size-display,
  .date-display {
    padding: 0.5rem;
    border-radius: 0.25rem;
    color: var(--theme-content-dark-color);
    font-size: 0.875rem;
  }

  .popupPanel-body__aside-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 1rem;
  }
</style> 