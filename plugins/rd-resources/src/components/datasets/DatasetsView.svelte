<script lang="ts">
  import { DocumentQuery, Ref, Space, WithLookup, SortingOrder } from '@hcengineering/core'
  import core from '@hcengineering/core'
  import { Asset, IntlString, translateCB } from '@hcengineering/platform'
  import { getClient } from '@hcengineering/presentation'
  import { themeStore, Header, Breadcrumb } from '@hcengineering/ui'
  import { ViewletContentView, ViewletSelector } from '@hcengineering/view-resources'
  import rd from '../../plugin'
  import { fetchDatasets } from '../../api'
  import type { Doc } from '@hcengineering/core'

  export let space: Ref<Space> | undefined = undefined
  export let query: DocumentQuery<any> = {}
  export let title: IntlString | undefined = undefined
  export let label: string = ''
  export let icon: Asset | undefined = undefined

  let viewlet: WithLookup<any> | undefined = undefined
  let viewlets: WithLookup<any>[] | undefined = undefined

  const viewOptions: any = {
    groupBy: ['datasetName'],
    orderBy: ['lastUpdated', SortingOrder.Descending]
  }

  let search = ''
  let searchQuery: DocumentQuery<any> = { ...query }
  function updateSearchQuery (search: string): void {
    searchQuery = search === '' ? { ...query } : { ...query, $search: search }
  }
  $: if (query) updateSearchQuery(search)
  $: resultQuery = space ? { ...searchQuery, space } : searchQuery

  $: if (title) {
    translateCB(title, {}, $themeStore.language, (res) => {
      label = res
    })
  }

  const client = getClient()
  let loading = false
  let error: string | null = null
  let datasetsFetched = false
  
  $: if (space) {
    datasetsFetched = false
    error = null
  }

  async function loadDatasetsFromAPI() {
    if (datasetsFetched || !space || loading) return
    
    loading = true
    error = null
    
    try {

      let projectId = 'default'

      try {
        const spaceDoc = await client.findOne(core.class.Space, { _id: space })
        if (spaceDoc) {

          projectId = ((spaceDoc as any).identifier).toLowerCase() ||  'default'
        }
      } catch (spaceError) {
        console.warn('Could not find space document, using default project id:', spaceError)
        projectId = space!.toString().substring(0, 12) 
      }
      
      const apiResponse = await fetchDatasets(projectId)

      if (!apiResponse || !apiResponse.datasets || !Array.isArray(apiResponse.datasets)) {
        throw new Error('Invalid API response structure')
      }
      
      if (apiResponse.datasets.length === 0) {
        datasetsFetched = true
        loading = false
        return
      }
      
      const allDatasets = await client.findAll(rd.class.Dataset, { space }) as Array<Doc>
      const projectDatasets = allDatasets.filter(d => (d as any).project === projectId)
      const allVersions = await client.findAll(rd.class.DatasetVersion, { space }) as Array<Doc>
      const projectDatasetIds = new Set(projectDatasets.map(d => d._id))
      const projectVersions = allVersions.filter(v => projectDatasetIds.has((v as any).dataset))
      
      const versionsToRemove = allVersions.filter(v => !projectDatasetIds.has((v as any).dataset))
      for (const version of versionsToRemove) {
        await client.remove(version)
      }

      const datasetsToRemove = allDatasets.filter(d => (d as any).project !== projectId)
      for (const dataset of datasetsToRemove) {
        await client.remove(dataset)
      }

      // Remove datasets that exist in db but not in API response
      const apiDatasetNames = new Set(apiResponse.datasets.map(d => d.name))
      const staleDatasets = projectDatasets.filter(d => !apiDatasetNames.has((d as any).name))
      for (const dataset of staleDatasets) {
        await client.remove(dataset)
      }

      // Remove versions that exist in db but not in API response
      const apiVersions = new Set()
      apiResponse.datasets.forEach(dataset => {
        dataset.versions.forEach(version => {
          apiVersions.add(`${dataset.name}-${version.version}`)
        })
      })
      const staleVersions = projectVersions.filter(v => {
        const versionKey = `${(v as any).datasetName}-${(v as any).version}`
        return !apiVersions.has(versionKey)
      })
      for (const version of staleVersions) {
        await client.remove(version)
      }

      for (const apiDataset of apiResponse.datasets) {
        // Check if dataset exists by name and project
        const existingDataset = projectDatasets.find(d => 
          (d as any).name === apiDataset.name && (d as any).project === apiDataset.project
        )
        
        let dataset: Doc
        if (existingDataset) {
          // Update existing dataset 
          await client.updateDoc(rd.class.Dataset, space, existingDataset._id, {
            name: apiDataset.name,
            project: apiDataset.project,
            environment: apiDataset.environment,
            path: apiDataset.path,
            description: apiDataset.description || '',
            createdAt: new Date(apiDataset.created_at).getTime(),
            updatedAt: new Date(apiDataset.updated_at).getTime()
          } as any)
          dataset = existingDataset
          
          // Get existing versions for this specific dataset
          const datasetVersions = projectVersions.filter(v => (v as any).dataset === dataset._id)
          
          for (const apiVersion of apiDataset.versions) {

            const existingVersion = datasetVersions.find(v => (v as any).version === apiVersion.version)
            
            if (existingVersion) {
              // Update existing version 
              await client.updateDoc(rd.class.DatasetVersion, space, existingVersion._id, {
                version: apiVersion.version,
                path: apiVersion.path,
                lastUpdated: new Date(apiVersion.last_modified).getTime(),
                sizeBytes: apiVersion.size_bytes
              } as any)
            } else {
              // Create new version that didn't exist before
              await client.createDoc(rd.class.DatasetVersion, space, {
                dataset: dataset._id,
                datasetName: apiDataset.name,
                version: apiVersion.version,
                path: apiVersion.path,
                lastUpdated: new Date(apiVersion.last_modified).getTime(),
                sizeBytes: apiVersion.size_bytes
              } as any)
            }
          }
        } else {
          // Create new dataset 
          dataset = await client.createDoc(rd.class.Dataset, space, {
            name: apiDataset.name,
            project: apiDataset.project,
            environment: apiDataset.environment,
            path: apiDataset.path,
            description: apiDataset.description || '',
            createdAt: new Date(apiDataset.created_at).getTime(),
            updatedAt: new Date(apiDataset.updated_at).getTime()
          } as any)
          
          // Create all versions for new dataset
          for (const apiVersion of apiDataset.versions) {
            await client.createDoc(rd.class.DatasetVersion, space, {
              dataset: dataset._id,
              datasetName: apiDataset.name,
              version: apiVersion.version,
              path: apiVersion.path,
              lastUpdated: new Date(apiVersion.last_modified).getTime(),
              sizeBytes: apiVersion.size_bytes
            } as any)
          }
        }
      }
      
      datasetsFetched = true
      
    } catch (err) {
      console.error('Failed to load datasets from API:', err)
      error = err instanceof Error ? err.message : 'Failed to load datasets'
    } finally {
      loading = false
    }
  }

  $: if (space && !datasetsFetched && !loading) {
    loadDatasetsFromAPI()
  }
</script>

<Header>
  <svelte:fragment slot="beforeTitle">
    <ViewletSelector bind:viewlet bind:viewlets viewletQuery={{ attachTo: rd.class.DatasetVersion }} />
  </svelte:fragment>
  
  {#if label}
    <Breadcrumb {icon} title={label} size={'large'} isCurrent />
  {/if}
</Header>

{#if viewlet}
  <ViewletContentView
    _class={rd.class.DatasetVersion}
    {viewlet}
    query={resultQuery}
    {space}
    {viewOptions}
  />
{/if}