import DataAssets from './components/data-assets/DataAssets.svelte'
import DataLineage from './components/data-lineage/DataLineage.svelte'
import Domains from './components/domains/Domains.svelte'
import DomainDetail from './components/domains/DomainDetail.svelte'
import Observability from './components/observability/Observability.svelte'
import DataGovernance from './components/data-governance/DataGovernance.svelte'
import Glossary from './components/data-governance/Glossary.svelte'
import Classification from './components/data-governance/Classification.svelte'
import plugin from './plugin'
import {
    IconObjects,
    IconDetails,
    IconShare,
    IconThread,
    IconActivity,
    IconFile,
    IconSettings,
    IconFolder
  } from '@hcengineering/ui'

export const assetCategories = [
    {
      id: 'databases',
      label: plugin.string.Databases,
      endpoint: 'databases',
      icon: IconObjects,
      childLevels: [
        { entityType: 'databaseService', parentFilter: null },
        { entityType: 'database', parentFilter: 'service.fullyQualifiedName' },
        { entityType: 'databaseSchema', parentFilter: 'database.fullyQualifiedName' },
        { entityType: 'table', parentFilter: 'databaseSchema.fullyQualifiedName' }
      ]
    },
    {
      id: 'dashboards',
      label: plugin.string.Dashboards,
      endpoint: 'dashboards',
      icon: IconDetails,
      childLevels: [
        { entityType: 'dashboardService', parentFilter: null },
        { entityType: 'dashboard', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'pipelines',
      label: plugin.string.Pipelines,
      endpoint: 'pipelines',
      icon: IconShare,
      childLevels: [
        { entityType: 'pipelineService', parentFilter: null },
        { entityType: 'pipeline', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'topics',
      label: plugin.string.Topics,
      endpoint: 'topics',
      icon: IconThread,
      childLevels: [
        { entityType: 'messagingService', parentFilter: null },
        { entityType: 'topic', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'charts',
      label: plugin.string.Charts,
      endpoint: 'charts',
      icon: IconDetails,
      childLevels: [
        { entityType: 'chartService', parentFilter: null },
        { entityType: 'chart', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'metrics',
      label: plugin.string.Metrics,
      endpoint: 'metrics',
      icon: IconActivity,
      childLevels: [
        { entityType: 'metricService', parentFilter: null },
        { entityType: 'metric', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'reports',
      label: plugin.string.Reports,
      endpoint: 'reports',
      icon: IconFile,
      childLevels: [
        { entityType: 'reportService', parentFilter: null },
        { entityType: 'report', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'mlmodels',
      label: plugin.string.MLModels,
      endpoint: 'mlmodels',
      icon: IconSettings,
      childLevels: [
        { entityType: 'mlmodelService', parentFilter: null },
        { entityType: 'mlmodel', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'containers',
      label: plugin.string.Containers,
      endpoint: 'containers',
      icon: IconFolder,
      childLevels: [
        { entityType: 'storageService', parentFilter: null },
        { entityType: 'container', parentFilter: 'service.fullyQualifiedName' }
      ]
    }
  ]

export const sidebarItems = [
    {
        id: 'nav-item-1',
        title: plugin.string.DataAssets,
        icon: IconObjects,
        path: 'data-assets',
        isParent: true,
        categoryChildren: assetCategories
    },
    {
        id: 'nav-item-2',
        title: plugin.string.DataLineage,
        icon: IconShare,
        path: 'data-lineage',
        isParent: true,
    },
    {
        id: 'nav-item-3',
        title: plugin.string.Observability,
        icon: IconActivity,
        path: 'observability',
        isParent: true,
    },
    {
        id: 'nav-item-4',
        title: plugin.string.Domains,
        icon: IconFolder,
        path: 'domains',
        isParent: true,
    },
    {
        id: 'nav-item-5',
        title: plugin.string.DataGovernance,
        icon: IconSettings,
        path: 'data-governance',
        defaultSubItem: 'glossary',
        subItems: [
            {
                id: 'glossary',
                title: plugin.string.Glossary,
                icon: IconDetails,
                path: 'data-governance/glossary'
            },
            {
                id: 'classification', 
                title: plugin.string.Classification,
                icon: IconActivity,
                path: 'data-governance/classification'
            }
        ]
    }
]

export const pathToComponentMap = {
    'data-assets': DataAssets,
    'data-lineage': DataLineage,
    'observability': Observability,
    'domains': Domains,
    'domains/detail': DomainDetail,
    'data-governance': DataGovernance,
    'data-governance/glossary': Glossary,
    'data-governance/classification': Classification
} 