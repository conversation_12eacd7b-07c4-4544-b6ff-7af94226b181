<script lang="ts">
  import { onMount } from 'svelte'
  import { SearchInput, Spinner, IconUp, IconDown, Button, IconBack, IconFilter, eventToHTMLElement, showPopup, Icon, mouseAttractor, Label, IconDetails, IconActivity, ButtonIcon, IconSettings } from '@hcengineering/ui'
  import type { IntlString } from '@hcengineering/platform'
  import TagFilterPopup from './TagFilterPopup.svelte'
  import GovernanceDetails from './GovernanceDetails.svelte'
  import plugin from '../../plugin'
  import type { GovernanceItem, GovernanceConfig } from './types'
  import datacatalog from '../../plugin'

  export let config: GovernanceConfig

  // Constants
  const FETCH_LIMIT = 1000
  
  // Create reactive labels for the template
  $: searchPlaceholderLabel = config.searchPlaceholder as IntlString
  $: filterLabel = selectedTagFilter ? plugin.string.Tag : plugin.string.Filter

  let loading = true
  let items: GovernanceItem[] = []
  let search = ''
  let selected: GovernanceItem | null = null
  let selectedTagFilter = ''
  let rowSelection: number | undefined = undefined

  // Sorting state
  let sortKey: keyof GovernanceItem = 'displayName'
  let sortAsc = true

  function normalizeArrayField(field: any[], fallbackField?: any[]): string[] {
    const sourceArray = field?.length ? field : (fallbackField ?? [])
    return sourceArray.map((item: any) => {
      if (typeof item === 'string') return item
      return item.displayName ?? item.name ?? item.tagFQN ?? item.fullyQualifiedName ?? ''
    }).filter(Boolean)
  }

  function cleanHtmlDescription(rawDesc: string): string {
    return rawDesc.replace(/<\/?p[^>]*>/gi, '').replace(/<[^>]+>/g, '').trim()
  }

  // Generic fetch function that works for both glossary and classification
  async function fetchGovernanceItems(): Promise<GovernanceItem[]> {
    try {
      let fields: string[] = []
      let queryParams: any = {
        limit: FETCH_LIMIT,
        include: 'non-deleted'
      }

      if (config.type === 'glossary') {
        // Request only fields known to be widely supported to avoid 400s
        fields = ['owners']
        if (config.hasTagsColumn) {
          fields.push('tags', 'reviewers')
        }
      } else if (config.type === 'classification') {
        // Classification API fields: usageCount,termCount (owners/domain not supported)
        fields = ['usageCount', 'termCount']
        // disabled is a query parameter, not a field
        queryParams.disabled = 'false'
      }

      queryParams.fields = fields

      const res = await config.fetchFunction(queryParams)

      return (res.data ?? []).map((t: any) => {
        // Handle different field names between APIs
        const domains = config.type === 'glossary' 
          ? normalizeArrayField(t.domain ? [t.domain] : [], t.dataProducts ?? [])
          : [] // Classifications don't have domain info
        
        const owners = config.type === 'glossary'
          ? normalizeArrayField(t.owners ?? [])
          : [] // Classifications don't have owner info
        
        const tags = config.hasTagsColumn ? normalizeArrayField(t.tags ?? [], t.relatedTerms ?? []) : []

        const rawDesc = t.description ?? t.definition ?? ''
        const cleanDesc = cleanHtmlDescription(rawDesc)

        const item: GovernanceItem = {
          id: t.id,
          name: t.name ?? t.id,
          displayName: t.displayName ?? t.term ?? t.name,
          description: cleanDesc,
          owners,
          domains
        }

        if (config.hasTagsColumn) {
          item.tags = tags
        }

        return item
      })
    } catch (err) {
      console.error(`Failed to fetch ${config.type}:`, err)
      return []
    }
  }

  onMount(async () => {
    items = await fetchGovernanceItems()
    loading = false
  })

  function toggleSort(key: keyof GovernanceItem): void {
    if (sortKey === key) {
      sortAsc = !sortAsc
    } else {
      sortKey = key
      sortAsc = true
    }
  }

  function compare(a: GovernanceItem, b: GovernanceItem): number {
    const av: any = a[sortKey]
    const bv: any = b[sortKey]
    return String(av).localeCompare(String(bv), undefined, { numeric: true })
  }

  // Get unique tags from all items (only for glossary)
  $: allTags = config.hasTagsColumn 
    ? [...new Set(items.flatMap(t => t.tags || []))].sort()
    : []

  $: filtered = items
    .filter((t) => {
      const matchesSearch = t.displayName.toLowerCase().includes(search.toLowerCase())
      const matchesTag = !config.hasTagFilter || !selectedTagFilter || (t.tags && t.tags.includes(selectedTagFilter))
      return matchesSearch && matchesTag
    })
    .sort((a, b) => (sortAsc ? compare(a, b) : compare(b, a)))

  function open(t: GovernanceItem): void {
    selected = t
  }

  function goBack(): void {
    selected = null
    rowSelection = undefined
  }

  function handleTagFilter(e: MouseEvent): void {
    const target = eventToHTMLElement(e)
    const onSelectTag = (tag: string): void => {
      selectedTagFilter = tag
    }
    showPopup(TagFilterPopup, { 
      allTags, 
      selectedTagFilter, 
      onSelect: onSelectTag
    }, target)
  }

  function onRowFocus(index: number): void {
    rowSelection = index
  }

  function onRowClick(item: GovernanceItem, index: number): void {
    rowSelection = index
    open(item)
  }
</script>

<div class="datacatalog-data-governance governance-container">
  <div class="hulyHeader-container flex w-full justify-between items-center">
    {#if selected}
      <Button kind="ghost" icon={IconBack} label={plugin.string.Back} on:click={goBack} />
      <div class="spacer"></div>
    {:else}
      <button class="hulyBreadcrumb-container large current">
        <ButtonIcon
            icon={IconSettings}
            kind="tertiary"
            size="large"
            pressed={false}
            disabled={true}
        />
        <span class="heading-medium-16 line-height-auto hulyBreadcrumb-label overflow-label">
            <Label label={datacatalog.string.DataGovernance} /> - <Label label={config.title} />
        </span>
      </button>
      
      <!-- Right side: search + optional tag filter -->
      <div class="right-controls">
        <SearchInput
          bind:value={search}
          placeholder={searchPlaceholderLabel}
          collapsed
        />
        {#if config.hasTagFilter}
          <Button
            icon={IconFilter}
            label={filterLabel}
            kind={'regular'}
            size={'medium'}
            pressed={!!selectedTagFilter}
            on:click={handleTagFilter}
          />
        {/if}
      </div>
    {/if}
  </div>
  
  {#if loading}
    <div class="loading">
      <Spinner size="large" />
    </div>
  {:else if !selected}
    <table class="antiTable highlightRows">
      <thead class="scroller-thead">
        <tr class="scroller-thead__tr">
          <th class="sortable" class:sorted={sortKey === 'displayName'} on:click={() => toggleSort('displayName')}>
            <div class="antiTable-cells">
              <span>
                <Label label={config.type === 'glossary' ? plugin.string.TermForTable : plugin.string.Classification} />
              </span>
              {#if sortKey === 'displayName'}
                <div class="icon">
                  {#if sortAsc}<IconUp size="small" />{:else}<IconDown size="small" />{/if}
                </div>
              {/if}
            </div>
          </th>
          <th><div class="antiTable-cells"><span><Label label={plugin.string.DescriptionForTable} /></span></div></th>
          <th><div class="antiTable-cells"><span><Label label={plugin.string.OwnersForTable} /></span></div></th>
          {#if config.hasTagsColumn}
            <th><div class="antiTable-cells"><span><Label label={plugin.string.TagsForTable} /></span></div></th>
          {/if}
          <th><div class="antiTable-cells"><span><Label label={plugin.string.DomainsForTable} /></span></div></th>
        </tr>
      </thead>
      <tbody>
        {#each filtered as item, index}
          <tr 
            class="antiTable-body__row" 
            class:selected={index === rowSelection}
            on:click={() => onRowClick(item, index)}
            on:mouseover={mouseAttractor(() => onRowFocus(index))}
            on:mouseenter={mouseAttractor(() => onRowFocus(index))}
            on:focus={() => {}}
          >
            <td><div class="antiTable-cells__firstCell">{item.displayName}</div></td>
            <td class="definition">{item.description}</td>
            <td><div class="antiTable-cells data-cell">{item.owners.join(', ') || 'N/A'}</div></td>
            {#if config.hasTagsColumn}
              <td><div class="antiTable-cells data-cell">{(item.tags || []).join(', ') || 'N/A'}</div></td>
            {/if}
            <td><div class="antiTable-cells data-cell">{item.domains.join(', ') || 'N/A'}</div></td>
          </tr>
        {/each}
      </tbody>
    </table>
  {:else}
    <GovernanceDetails 
      {config}
      item={selected}
    />
  {/if}
</div>

<style lang="scss">
  .governance-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  }

  .full-width-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    margin: 0;
    border-bottom: 1px solid var(--theme-divider-color, #e0e4e7);
    background-color: var(--theme-bg-color, #ffffff);
  }

  .page-title {
    font-size: 1rem;
    font-weight: 700;
    margin: 0;
    color: var(--theme-secondary-color);
  }

  .page-title-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
  }

  .right-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .left-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    min-width: 120px;
  }

  .spacer {
    flex: 1;
  }

  .definition {
    max-width: 40ch;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .data-cell {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9rem;
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }

  .antiTable-body__row {
    cursor: pointer;
  }
</style> 