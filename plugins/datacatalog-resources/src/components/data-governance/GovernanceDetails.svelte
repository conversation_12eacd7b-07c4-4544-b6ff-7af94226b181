<script lang="ts">
  import { Separator, showPop<PERSON>, Spinner, Component, Section, Label, Button } from '@hcengineering/ui'
  import { Panel } from '@hcengineering/panel'
  import activity from '@hcengineering/activity'
  import view from '@hcengineering/view'
  import type { IntlString } from '@hcengineering/platform'
  import { onMount } from 'svelte'
  import GovernanceTermDetail from './GovernanceTermDetail.svelte'
  import ClassificationTagDetail from './ClassificationTagDetail.svelte'
  import GovernanceControlPanel from './GovernanceControlPanel.svelte'
  import { listGlossaryTerms, type GlossaryTermEntity } from '../../openmetadata/glossary'
  import { listClassificationTags, type ClassificationTagEntity } from '../../openmetadata/classification'
  import type { GovernanceItem, GovernanceConfig } from './types'
  import plugin from '../../plugin'
  
  export let config: GovernanceConfig
  export let item: GovernanceItem

  // Constants
  const FETCH_LIMIT = 1000

  let terms: GlossaryTermEntity[] = []
  let tags: ClassificationTagEntity[] = []
  let loadingTerms = true
  let selectedTerm: GlossaryTermEntity | null = null
  let selectedTag: ClassificationTagEntity | null = null

  // Only fetch terms for glossary type
  async function fetchTerms(): Promise<void> {
    if (config.type !== 'glossary' || !item.id) return
    
    try {
      loadingTerms = true
      const response = await listGlossaryTerms({
        glossary: item.id,
        fields: ['owners', 'tags', 'reviewers', 'synonyms', 'relatedTerms'],
        limit: FETCH_LIMIT
      })
      terms = response.data || []
    } catch (error) {
      console.error('Failed to fetch glossary terms:', error)
      terms = []
    } finally {
      loadingTerms = false
    }
  }

  // Fetch tags for classification type
  async function fetchTags(): Promise<void> {
    if (config.type !== 'classification' || !item.id) return
    
    try {
      loadingTerms = true
      const response = await listClassificationTags({
        parent: item.name || item.id, // Use classification name as parent
        fields: ['usageCount', 'children'],
        disabled: 'false',
        limit: FETCH_LIMIT
      })
      tags = response.data || []
    } catch (error) {
      console.error('Failed to fetch classification tags:', error)
      tags = []
    } finally {
      loadingTerms = false
    }
  }

  onMount(() => {
    if (config.type === 'glossary') {
      fetchTerms()
    } else if (config.type === 'classification') {
      fetchTags()
    } else {
      loadingTerms = false
    }
  })

  function selectTerm(term: GlossaryTermEntity): void {
    selectedTerm = term
  }

  function selectTag(tag: ClassificationTagEntity): void {
    selectedTag = tag
  }

  function goBackToTerms(): void {
    selectedTerm = null
    selectedTag = null
  }

  function formatTagDisplayName(tag: any): string {
    if (typeof tag === 'string') return tag
    return tag.displayName || tag.name || tag.tagFQN || tag
  }

  // Create a mock object that satisfies Panel's requirements
  const panelObject = {
    _id: item.id as any,
    _class: 'governance:class:Item' as any,
    space: 'governance:space:Main' as any,
    modifiedOn: Date.now(),
    modifiedBy: 'system' as any,
    ...item
  }
</script>

{#if selectedTerm}
  <GovernanceTermDetail term={selectedTerm} parentName={item.displayName} on:back={goBackToTerms} />
{:else if selectedTag}
  <ClassificationTagDetail tag={selectedTag} parentName={item.displayName} on:back={goBackToTerms} />
{:else}
  {#if item?.id}
    <Panel
      object={panelObject}
      isHeader={false}
      allowClose={false}
      isAside={true}
      isSub={false}
      embedded={false}
      withoutActivity={true}
      adaptive={'default'}
    >
      <svelte:fragment slot="title">
        <div class="title">{item.displayName}</div>
      </svelte:fragment>

      <!-- Main Content - Description and Custom Activity -->
      <div class="main-content">
        <div class="description-section">
          <div class="section-label"><Label label={plugin.string.DescriptionForTable} /></div>
          <p class="description-text">{item.description}</p>
        </div>

        <!-- Custom Activity Section for Terms -->
        <div class="step-tb-6">
          <Section label={plugin.string.Terms} icon={view.icon.List}>
            <svelte:fragment slot="header">
              {#if loadingTerms}
                <div class="ml-1">
                  <Spinner size="small" />
                </div>
              {/if}
              <div class="w-4 min-w-4 max-w-4" />
              <div class="antiSection-header__tag highlight">
                <Label label={plugin.string.All} />
              </div>
            </svelte:fragment>

            <svelte:fragment slot="content">
              {#if config.type === 'glossary'}
                {#if loadingTerms}
                  <div class="centered-content">
                    <Spinner size="medium" />
                  </div>
                {:else if terms.length > 0}
                  <div class="terms-activity">
                    {#each terms as term}
                      <div class="term-activity-item" on:click={() => selectTerm(term)}>
                        <div class="term-icon"></div>
                        <div class="term-content">
                          <div class="term-name">{term.displayName || term.name}</div>
                          {#if term.description}
                            <div class="term-description">{term.description}</div>
                          {/if}
                          {#if term.tags && term.tags.length > 0}
                            <div class="term-tags">
                              {#each term.tags.slice(0, 3) as tag}
                                <span class="tag-chip">{formatTagDisplayName(tag)}</span>
                              {/each}
                              {#if term.tags.length > 3}
                                <span class="more-tags">+{term.tags.length - 3}</span>
                              {/if}
                            </div>
                          {/if}
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <div class="centered-content">
                    <div class="placeholder-text">No terms found in this glossary</div>
                  </div>
                {/if}
              {:else if config.type === 'classification'}
                {#if loadingTerms}
                  <div class="centered-content">
                    <Spinner size="medium" />
                  </div>
                {:else if tags.length > 0}
                  <div class="terms-activity">
                    {#each tags as tag}
                      <div class="term-activity-item" on:click={() => selectTag(tag)}>
                        <div class="term-icon" style="background-color: #2563EB;"></div>
                        <div class="term-content">
                          <div class="term-name">{tag.displayName || tag.name}</div>
                          {#if tag.description}
                            <div class="term-description">{tag.description}</div>
                          {/if}
                          {#if tag.usageCount !== undefined}
                            <div class="term-tags">
                              <span class="tag-chip">Used {tag.usageCount} times</span>
                            </div>
                          {/if}
                        </div>
                      </div>
                    {/each}
                  </div>
                {:else}
                  <div class="centered-content">
                    <div class="placeholder-text">No tags found in this classification</div>
                  </div>
                {/if}
              {/if}
            </svelte:fragment>
          </Section>
        </div>
      </div>

      <svelte:fragment slot="custom-attributes">
        <div class="space-divider" />
        <GovernanceControlPanel 
          {item} 
          {config} 
          {tags}
        />
      </svelte:fragment>
    </Panel>
  {:else}
    <div class="main-content">
      <div class="description-section">
        <div class="section-label"><Label label={plugin.string.DescriptionForTable} /></div>
        <p class="description-text">Loading...</p>
      </div>
    </div>
  {/if}
{/if}

<style lang="scss">
  .main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    height: 100%;
    overflow-y: auto;
  }

  .description-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    flex-shrink: 0;
    padding: var(--spacing-3);
  }

  .section-label {
    color: var(--theme-dark-color);
    font-weight: 600;
    font-size: .75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
  }

  .description-text {
    color: var(--global-secondary-TextColor);
    font-size: 1rem;
    line-height: 1.4rem;
    margin: 0;
  }

  .title {
    font-weight: 500;
    font-size: 1.25rem;
    line-height: 1.5rem;
    color: var(--theme-caption-color);
  }

  .centered-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
    flex: 1;
  }

  .placeholder-text {
    color: var(--global-secondary-TextColor);
    font-style: italic;
    font-size: .875rem;
  }

  .terms-activity {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-2);
  }

  .term-activity-item {
    display: flex;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    border-radius: var(--small-BorderRadius);
    cursor: pointer;
    transition: background-color 0.15s ease;

    &:hover {
      background-color: var(--theme-link-color);
    }
  }

  .term-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #6B7280;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .term-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
  }

  .term-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--global-primary-TextColor);
    line-height: 1.2;
  }

  .term-description {
    font-size: 0.75rem;
    color: var(--global-secondary-TextColor);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .term-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-0_25);
    margin-top: var(--spacing-0_25);
  }

  .tag-chip {
    padding: 1px 4px;
    background-color: var(--global-ui-BackgroundColor);
    color: var(--global-secondary-TextColor);
    border: 1px solid var(--global-subtle-ui-BorderColor);
    border-radius: 2px;
    font-size: 0.625rem;
  }

  .more-tags {
    font-size: 0.625rem;
    color: var(--global-secondary-TextColor);
    font-style: italic;
  }

  .step-tb-6 {
    padding: 0 var(--spacing-3);
  }
</style> 