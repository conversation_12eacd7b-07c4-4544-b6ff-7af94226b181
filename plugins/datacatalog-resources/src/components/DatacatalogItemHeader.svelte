<script lang="ts">
    import { ButtonIcon, Label, type AnySvelteComponent, SearchInput } from '@hcengineering/ui'
    import { IntlString } from '@hcengineering/platform'

    export let icon: AnySvelteComponent
    export let label: IntlString
    export let sublabel: IntlString | undefined = undefined
    export let nonIntlSublabel: string = ''
    export let value: string | undefined = undefined
    export let placeholder: IntlString | undefined = undefined
    export let onSearchInput: any | undefined = undefined
</script>

<div class="hulyHeader-container flex w-full justify-between items-center">
    <button class="hulyBreadcrumb-container large current">
        <ButtonIcon
            icon={icon}
            kind="tertiary"
            size="large"
            pressed={false}
            disabled={true}
        />
        <span class="heading-medium-16 line-height-auto hulyBreadcrumb-label overflow-label">
            <Label label={label} /> 
            {#if sublabel}
            - <Label label={sublabel} />
            {:else if nonIntlSublabel}
            - {nonIntlSublabel}
            {/if}
        </span>
    </button>
    {#if onSearchInput}
        <div>
            <SearchInput bind:value {placeholder} collapsed on:input={onSearchInput} />
        </div>
    {/if}
</div>