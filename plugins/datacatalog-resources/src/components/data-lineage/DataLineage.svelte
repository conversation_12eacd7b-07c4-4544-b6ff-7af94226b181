<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { Spinner, Label } from '@hcengineering/ui'
  import { getEmbeddedLabel } from '@hcengineering/platform'
  import { lineage as omLineage } from '../../openmetadata'
  import DatacatalogItemHeader from '../DatacatalogItemHeader.svelte'
  import datacatalog from '../../plugin'
  import { IconShare } from '@hcengineering/ui'

  export let selectedCategory: string | undefined = undefined
  $: void selectedCategory

  interface LineageEntity {
    id?: string
    name?: string
    displayName?: string
    description?: string
    serviceType?: string
    fullyQualifiedName?: string
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any
  }

  interface NodeWrapper { entity: LineageEntity }

  // Raw maps from API
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let nodesMap: Record<string, NodeWrapper> = {}
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let upstreamEdgesRaw: any = {}
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let downstreamEdgesRaw: any = {}

  // UI state
  let loading = false
  let errorMessage: string | null = null
  let searchTerm = ''

  // Query params (defaults match user's example)
  let view: 'service' | 'entity' = 'service'
  let upstreamDepth = 2
  let downstreamDepth = 2
  let includeDeleted = false
  let size = 50

  // Derived
  let nodes: Array<{ key: string; entity: LineageEntity }> = []
  let edges: Array<{ source: string; target: string }> = []

  const intlTitle: any = getEmbeddedLabel('Data Lineage')
  const intlSearch: any = getEmbeddedLabel('Search…')
  const intlRefresh: any = getEmbeddedLabel('Refresh')
  const intlNoData: any = getEmbeddedLabel('No data')

  async function loadLineage(): Promise<void> {
    loading = true
    errorMessage = null
    try {
      const res = await omLineage.getPlatformLineage({
        view,
        upstreamDepth,
        downstreamDepth,
        includeDeleted,
        size
      })

      nodesMap = res.nodes ?? {}
      upstreamEdgesRaw = res.upstreamEdges ?? {}
      downstreamEdgesRaw = res.downstreamEdges ?? {}

      const entries = Object.entries(nodesMap)
      nodes = entries.map(([key, wrapper]) => ({ key, entity: wrapper.entity }))
        .sort((a, b) => {
          const an = (a.entity.displayName ?? a.entity.name ?? a.entity.fullyQualifiedName ?? '').toLowerCase()
          const bn = (b.entity.displayName ?? b.entity.name ?? b.entity.fullyQualifiedName ?? '').toLowerCase()
          return an.localeCompare(bn)
        })

      // Build helper lookup maps
      const keyByName = new Map<string, string>()
      const keyByFqn = new Map<string, string>()
      const keyById = new Map<string, string>()
      for (const { key, entity } of nodes) {
        if (entity.name) keyByName.set(entity.name, key)
        if (entity.displayName) keyByName.set(entity.displayName, key)
        if (entity.fullyQualifiedName) keyByFqn.set(entity.fullyQualifiedName, key)
        if (entity.id) keyById.set(entity.id, key)
      }

      function tryResolveNodeKey(o: any): string | undefined {
        if (typeof o === 'string') {
          // Could be a node key, name or FQN
          if (nodesMap[o]) return o
          if (keyByName.has(o)) return keyByName.get(o)
          if (keyByFqn.has(o)) return keyByFqn.get(o)
          if (keyById.has(o)) return keyById.get(o)
          return undefined
        }
        if (o && typeof o === 'object') {
          const cand = o.fqn ?? o.fullyQualifiedName ?? o.name ?? o.id
          return cand ? tryResolveNodeKey(String(cand)) : undefined
        }
        return undefined
      }

      const collected: Array<{ source: string; target: string }> = []
      const addEdge = (s?: string, t?: string): void => {
        if (!s || !t) return
        if (!nodesMap[s] || !nodesMap[t]) return
        collected.push({ source: s, target: t })
      }

      // Parse upstreamEdges as object maps: key -> array of upstream nodes
      if (upstreamEdgesRaw && typeof upstreamEdgesRaw === 'object') {
        for (const [toKey, arr] of Object.entries(upstreamEdgesRaw)) {
          const targetKey = tryResolveNodeKey(toKey)
          if (!Array.isArray(arr)) continue
          for (const item of arr) {
            const sourceKey = tryResolveNodeKey(item)
            addEdge(sourceKey, targetKey)
          }
        }
      }

      // Parse downstreamEdges: key -> array of downstream nodes
      if (downstreamEdgesRaw && typeof downstreamEdgesRaw === 'object') {
        for (const [fromKey, arr] of Object.entries(downstreamEdgesRaw)) {
          const sourceKey = tryResolveNodeKey(fromKey)
          if (!Array.isArray(arr)) continue
          for (const item of arr) {
            const targetKey = tryResolveNodeKey(item)
            addEdge(sourceKey, targetKey)
          }
        }
      }

      // Deduplicate edges
      const uniq = new Set<string>()
      edges = []
      for (const e of collected) {
        const k = `${e.source}->${e.target}`
        if (!uniq.has(k)) {
          uniq.add(k)
          edges.push(e)
        }
      }
    } catch (e: any) {
      errorMessage = String(e?.message ?? 'Failed to load lineage')
      nodes = []
      edges = []
    } finally {
      loading = false
    }
  }

  // Debounced search
  let searchHandle: number | null = null
  function onSearchInput(): void {
    if (searchHandle) window.clearTimeout(searchHandle)
    searchHandle = window.setTimeout(() => {}, 300)
  }

  // Filter
  $: filteredNodeKeys = new Set(
    nodes
      .filter(({ entity }) => {
        const q = searchTerm.trim().toLowerCase()
        if (!q) return true
        const name = (entity.displayName ?? entity.name ?? '').toLowerCase()
        const fqn = (entity.fullyQualifiedName ?? '').toLowerCase()
        const type = (entity.serviceType ?? '').toLowerCase()
        return name.includes(q) || fqn.includes(q) || type.includes(q)
      })
      .map((n) => n.key)
  )

  // Graph layout (simple layered left-to-right)
  interface PositionedNode { key: string; entity: LineageEntity; x: number; y: number; level: number }
  let positioned: PositionedNode[] = []
  let positionedEdges: Array<{ source: PositionedNode; target: PositionedNode }> = []

  function computeLayout(width: number, height: number): void {
    const visibleNodes = nodes.filter((n) => filteredNodeKeys.has(n.key))
    const visibleSet = new Set(visibleNodes.map((n) => n.key))

    const visEdges = edges.filter((e) => visibleSet.has(e.source) && visibleSet.has(e.target))

    // Compute in-degree for levels
    const indeg = new Map<string, number>()
    for (const n of visibleNodes) indeg.set(n.key, 0)
    for (const e of visEdges) indeg.set(e.target, (indeg.get(e.target) ?? 0) + 1)

    const queue: string[] = []
    for (const [k, d] of indeg.entries()) if (d === 0) queue.push(k)
    if (queue.length === 0 && visibleNodes.length > 0) {
      // Fallback: pick arbitrary sources
      queue.push(visibleNodes[0].key)
    }

    const level = new Map<string, number>()
    for (const k of indeg.keys()) level.set(k, 0)
    const out = new Map<string, string[]>()
    for (const e of visEdges) {
      const arr = out.get(e.source) ?? []
      arr.push(e.target)
      out.set(e.source, arr)
    }

    // BFS-like longest path layering
    const q: string[] = [...queue]
    while (q.length) {
      const u = q.shift()!
      const lu = level.get(u) ?? 0
      for (const v of out.get(u) ?? []) {
        const newLv = Math.max(level.get(v) ?? 0, lu + 1)
        if (newLv !== (level.get(v) ?? 0)) {
          level.set(v, newLv)
          q.push(v)
        }
      }
    }

    const byLevel = new Map<number, string[]>()
    for (const k of visibleSet) {
      const lv = level.get(k) ?? 0
      const arr = byLevel.get(lv) ?? []
      arr.push(k)
      byLevel.set(lv, arr)
    }

    // Sort nodes within each level by name for stability
    for (const [lv, arr] of byLevel.entries()) {
      arr.sort((ak, bk) => {
        const a = nodes.find((n) => n.key === ak)?.entity
        const b = nodes.find((n) => n.key === bk)?.entity
        const an = (a?.displayName ?? a?.name ?? a?.fullyQualifiedName ?? '').toLowerCase()
        const bn = (b?.displayName ?? b?.name ?? b?.fullyQualifiedName ?? '').toLowerCase()
        return an.localeCompare(bn)
      })
      byLevel.set(lv, arr)
    }

    const levels = Array.from(byLevel.keys()).sort((a, b) => a - b)
    const numLevels = Math.max(levels.length, 1)

    const nodeW = 180
    const nodeH = 46
    const xGap = 120
    const yGap = 24
    const totalW = Math.max(width, numLevels * (nodeW + xGap) + xGap)
    const colX: number[] = []
    for (let i = 0; i < numLevels; i++) {
      colX.push(xGap + i * (nodeW + xGap))
    }

    const posMap = new Map<string, PositionedNode>()
    for (const [idx, lv] of levels.entries()) {
      const keys = byLevel.get(lv) ?? []
      const nInCol = Math.max(keys.length, 1)
      const totalColH = nInCol * nodeH + (nInCol - 1) * yGap
      const startY = Math.max(yGap, (height - totalColH) / 2)
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        const ent = nodes.find((n) => n.key === key)!.entity
        posMap.set(key, {
          key,
          entity: ent,
          x: colX[idx],
          y: startY + i * (nodeH + yGap),
          level: lv
        })
      }
    }

    positioned = Array.from(posMap.values())
    positionedEdges = visEdges
      .map((e) => {
        const s = posMap.get(e.source)
        const t = posMap.get(e.target)
        return s && t ? { source: s, target: t } : undefined
      })
      .filter(Boolean) as Array<{ source: PositionedNode; target: PositionedNode }>
  }

  // Pan & zoom
  let graphContainer: HTMLDivElement | null = null
  let width = 800
  let height = 600
  let zoom = 1
  let offsetX = 0
  let offsetY = 0
  let panning = false
  let panStartX = 0
  let panStartY = 0
  let panOrigX = 0
  let panOrigY = 0

  function onWheel(e: WheelEvent): void {
    e.preventDefault()
    if (e.ctrlKey || e.metaKey) {
      const scaleBy = 1.05
      const delta = e.deltaY < 0 ? scaleBy : 1 / scaleBy
      const rect = graphContainer?.getBoundingClientRect()
      const cx = rect ? e.clientX - rect.left : 0
      const cy = rect ? e.clientY - rect.top : 0
      // Zoom towards cursor
      const newZoom = Math.max(0.2, Math.min(3, zoom * delta))
      const k = newZoom / zoom
      offsetX = cx - k * (cx - offsetX)
      offsetY = cy - k * (cy - offsetY)
      zoom = newZoom
    } else {
      offsetX -= e.deltaX
      offsetY -= e.deltaY
    }
  }
  function onMouseDown(e: MouseEvent): void {
    if (e.button !== 0) return
    panning = true
    panStartX = e.clientX
    panStartY = e.clientY
    panOrigX = offsetX
    panOrigY = offsetY
  }
  function onMouseMove(e: MouseEvent): void {
    if (!panning) return
    const dx = e.clientX - panStartX
    const dy = e.clientY - panStartY
    offsetX = panOrigX + dx
    offsetY = panOrigY + dy
  }
  function onMouseUp(): void {
    panning = false
  }

  let ro: ResizeObserver | null = null
  let lastObservedEl: HTMLDivElement | null = null
  onMount(() => {
    loadLineage()
  })
  // React to container mount/unmount
  $: {
    if (graphContainer !== lastObservedEl) {
      if (ro) {
        ro.disconnect()
        ro = null
      }
      lastObservedEl = graphContainer
      if (graphContainer) {
        const el = graphContainer
        const update = () => {
          if (!el || !document.body.contains(el)) return
          width = el.clientWidth
          height = el.clientHeight
          computeLayout(width / zoom, height / zoom)
        }
        ro = new ResizeObserver(() => update())
        ro.observe(el)
        update()
      }
    }
  }
  onDestroy(() => {
    if (ro) {
      ro.disconnect()
      ro = null
    }
    lastObservedEl = null
  })

  $: computeLayout(width / zoom, height / zoom)

  function refresh(): void { loadLineage() }

  // Helpers to draw edges
  function edgePath(sx: number, sy: number, tx: number, ty: number): string {
    const dx = Math.max(60, Math.abs(tx - sx) / 2)
    const c1x = sx + dx
    const c1y = sy
    const c2x = tx - dx
    const c2y = ty
    return `M ${sx} ${sy} C ${c1x} ${c1y}, ${c2x} ${c2y}, ${tx} ${ty}`
  }
  function nodeFill(entity: LineageEntity): string {
    const t = (entity.serviceType ?? '').toLowerCase()
    const map: Record<string, string> = {
      mysql: '#E3F2FD',
      bigquery: '#E8F5E9',
      kafka: '#FFF3E0',
      airflow: '#F3E5F5',
      looker: '#F1F8E9',
      elasticsearch: '#EDE7F6',
      superset: '#E0F7FA',
      glue: '#FFFDE7',
      default: '#F5F5F5'
    }
    const key = Object.keys(map).find(k => t.includes(k)) ?? 'default'
    return map[key]
  }
  function nodeStroke(entity: LineageEntity): string {
    const t = (entity.serviceType ?? '').toLowerCase()
    const map: Record<string, string> = {
      mysql: '#64B5F6',
      bigquery: '#81C784',
      kafka: '#FFB74D',
      airflow: '#BA68C8',
      looker: '#AED581',
      elasticsearch: '#9575CD',
      superset: '#4DD0E1',
      glue: '#FFF176',
      default: '#BDBDBD'
    }
    const key = Object.keys(map).find(k => t.includes(k)) ?? 'default'
    return map[key]
  }
</script>

<style lang="scss">
  .lineage-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  
  .graph-container {
    flex: 1 1 auto;
    position: relative;
    overflow: hidden;
    cursor: grab;
  }
  .graph-container:active { cursor: grabbing; }
  .node-label {
    font-size: 12px;
    fill: var(--theme-primary-color, #222);
  }
  .node-sub {
    font-size: 10px;
    fill: var(--theme-secondary-color, #666);
  }
  .edge {
    stroke: var(--theme-divider-color, #cfd8dc);
    stroke-width: 1.5;
    fill: none;
  }
  .node-rect {
    rx: 8;
    ry: 8;
    stroke-width: 1.5;
  }
</style>

<div class="lineage-container">
  <DatacatalogItemHeader 
    icon={IconShare} 
    label={datacatalog.string.DataLineage} 
    bind:value={searchTerm} placeholder={intlSearch} {onSearchInput} 
  />
  

  {#if loading}
    <div class="flex-row-center" style="padding: 1rem;"><Spinner size="large" /></div>
  {:else if errorMessage}
    <div style="padding: 0.75rem; color: var(--theme-danger-color, #c00);">{errorMessage}</div>
  {:else}
    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
    <div class="graph-container" bind:this={graphContainer} role="application" aria-label="Data Lineage graph" on:wheel|passive={onWheel} on:mousedown={onMouseDown} on:mousemove={onMouseMove} on:mouseup={onMouseUp} on:mouseleave={onMouseUp}>
      {#if positioned.length === 0}
        <div style="padding: 1rem;">
          <Label label={intlNoData} />
        </div>
      {:else}
        <svg width={width} height={height} style="position:absolute; inset:0;">
          <defs>
            <marker id="arrow" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto-start-reverse">
              <path d="M 0 0 L 10 5 L 0 10 z" fill="var(--theme-divider-color, #cfd8dc)" />
            </marker>
          </defs>
          <g transform={`translate(${offsetX},${offsetY}) scale(${zoom})`}>
            {#each positionedEdges as e}
              {@const sx = e.source.x + 180}
              {@const sy = e.source.y + 23}
              {@const tx = e.target.x}
              {@const ty = e.target.y + 23}
              <path class="edge" d={edgePath(sx, sy, tx, ty)} marker-end="url(#arrow)" />
            {/each}

            {#each positioned as n}
              <g transform={`translate(${n.x},${n.y})`}>
                <rect class="node-rect" width="180" height="46" fill={nodeFill(n.entity)} stroke={nodeStroke(n.entity)} />
                <text class="node-label" x="10" y="18">{n.entity.displayName ?? n.entity.name ?? n.entity.fullyQualifiedName ?? n.entity.id}</text>
                <text class="node-sub" x="10" y="34">{n.entity.serviceType || ''}</text>
              </g>
            {/each}
          </g>
        </svg>
      {/if}
    </div>
  {/if}
</div>