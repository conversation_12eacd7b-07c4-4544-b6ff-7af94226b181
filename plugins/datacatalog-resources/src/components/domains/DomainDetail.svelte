<script lang="ts">
  import { <PERSON><PERSON>, <PERSON>, Spinner, <PERSON>read<PERSON>rumbs, ModeSelector, mouseAttractor, Button, Icon } from '@hcengineering/ui'
  import type { BreadcrumbItem, IModeSelector } from '@hcengineering/ui'
  import type { IntlString } from '@hcengineering/platform'
  import { Panel } from '@hcengineering/panel'
  import { onMount } from 'svelte'
  import { getDomainById, type DomainEntity } from '../../openmetadata/domains'
  import { getDataProductsByDomain, type DataProductEntity } from '../../openmetadata/dataProducts'
  import { getCustomPropertiesForEntityType, type TypeDefinitionEntity } from '../../openmetadata/customProperties'
  import plugin from '../../plugin'
  import { IconFolder } from '@hcengineering/ui'

  export let domainId: string

  let domain: DomainEntity | null = null
  let dataProducts: DataProductEntity[] = []
  let customPropertiesTypeDef: TypeDefinitionEntity | null = null
  let loading = true
  let error: string | null = null

  function cleanHtmlDescription(description: string): string {
    if (!description) return 'No description available'
    return description.replace(/<\/?p[^>]*>/gi, '').replace(/<[^>]+>/g, '').trim()
  }

  // Map specific demo sub-domain names to requested labels
  const renameMap = new Map<string, string>([
    ['file', 'medina1'],
    ['document', 'medina2']
  ])

  const normalize = (s?: string) => (s ?? '').trim().toLowerCase()
  const mapDisplayName = (name?: string) => {
    const key = normalize(name)
    return renameMap.get(key) ?? name
  }

  // Mode selector configuration
  const config: Array<[string, IntlString, object]> = [
    ['documentation', plugin.string.Documentation, {}],
    ['sub-domains', plugin.string.SubDomains, {}],
    ['data-products', plugin.string.DataProducts, {}],
    ['assets', plugin.string.Assets, {}],
    ['custom-properties', plugin.string.CustomProperties, {}]
  ]

  let mode = config[0][0] // Default to first mode

  function changeMode(_mode: string): void {
    mode = _mode
  }

  $: modeSelectorProps = {
    mode,
    config,
    onChange: changeMode
  } as IModeSelector

  $: breadcrumbItems = domain ? [
    {
      icon: IconFolder,
      title: domain.displayName || domain.name
    }
  ] as BreadcrumbItem[] : []

  $: panelObject = domain ? {
    _id: domain.id as any,
    _class: 'datacatalog:class:Domain' as any,
    space: 'datacatalog:space:Main' as any,
    modifiedOn: domain.updatedAt || Date.now(),
    modifiedBy: domain.updatedBy || 'system' as any,
    ...domain
  } : null

  async function fetchDomain(): Promise<void> {
    if (!domainId) return

    try {
      loading = true
      error = null

      domain = await getDomainById(domainId.toString(), ['parent', 'children', 'owners', 'experts', 'assets', 'tags', 'extension'])

      // Fetch custom properties type definition for domains
      try {
        customPropertiesTypeDef = await getCustomPropertiesForEntityType('domain')
      } catch (cpErr) {
        console.error('Failed to fetch custom properties:', cpErr)
        customPropertiesTypeDef = null
      }

      // Fetch data products for this domain
      try {
        // Use domain name directly (API expects domain name, not ID)
        const domainName = domain?.name || domain?.displayName
        if (domainName) {
          const dataProductsResponse = await getDataProductsByDomain(domainName, {
            limit: 10  // Only pass limit, no extra fields
          })
          dataProducts = dataProductsResponse.data || []
        } else {
          dataProducts = []
        }
      } catch (dpErr) {
        console.error('Data products API call failed:', dpErr)
      }
    } catch (err) {
      console.error('Failed to fetch domain:', err)
      error = 'Failed to load domain details'
      domain = null
      dataProducts = []
    } finally {
      loading = false
    }
  }

  onMount(() => {
    fetchDomain()
  })

  $: if (domainId) {
    fetchDomain()
  }
</script>

<div class="domain-detail">
  {#if loading}
    <div class="loading-content">
      <Spinner size="large" />
    </div>
  {:else if error}
    <Header adaptive={'disabled'}>
      <Breadcrumbs
        items={[
          {
            icon: IconFolder,
            title: 'Error'
          }
        ]}
        currentOnly
      />
    </Header>
    <div class="error-content">
      <p>{error}</p>
    </div>
  {:else if domain}
    <Header adaptive={'disabled'}>
      <Breadcrumbs items={breadcrumbItems} currentOnly />

      <svelte:fragment slot="actions">
        <ModeSelector
          kind={'subtle'}
          props={modeSelectorProps}
        />
      </svelte:fragment>
    </Header>
    <div class="domain-content">
      {#if mode === 'documentation'}
        <!-- Documentation Tab using Panel component -->
        {#if panelObject}
          <Panel
            object={panelObject}
            isHeader={false}
            allowClose={false}
            isAside={true}
            isSub={false}
            embedded={false}
            withoutActivity={true}
            adaptive={'default'}
          >

            <!-- Main Content - Description Only -->
            <div class="panel-main-content">
              <div class="description-section">
                <div class="section-label"><Label label={plugin.string.DescriptionForTable} /></div>
                <p class="description-text">{cleanHtmlDescription(domain.description || '')}</p>
              </div>
            </div>

            <svelte:fragment slot="custom-attributes">
              <div class="popupPanel-body__aside-grid">
                <!-- Owners -->
                <span class="labelOnPanel">
                  <Label label={plugin.string.Owners} />
                </span>
                <div class="field-display">
                  {#if domain.owners && domain.owners.length > 0}
                    <div class="owners-list">
                      {#each domain.owners as owner}
                        <div class="owner-item">
                          <div class="avatar">{(owner.displayName || owner.name || owner.id).charAt(0).toUpperCase()}</div>
                          <span>{owner.displayName || owner.name || owner.id}</span>
                        </div>
                      {/each}
                    </div>
                  {:else}
                    <span class="none-text">None</span>
                  {/if}
                </div>

                <!-- Glossary Terms -->
                <span class="labelOnPanel">
                  <Label label={plugin.string.Terms} />
                </span>
                <div class="field-display terms-container">
                  <span class="none-text">None</span>
                </div>

                <!-- Experts -->
                <span class="labelOnPanel">
                  <Label label={plugin.string.Reviewers} />
                </span>
                <div class="field-display">
                  {#if domain.experts && domain.experts.length > 0}
                    <div class="owners-list">
                      {#each domain.experts as expert}
                        <div class="owner-item">
                          <div class="avatar expert">{(expert.displayName || expert.name || expert.id).charAt(0).toUpperCase()}</div>
                          <span>{expert.displayName || expert.name || expert.id}</span>
                        </div>
                      {/each}
                    </div>
                  {:else}
                    <span class="none-text">None</span>
                  {/if}
                </div>

                <!-- Domain Type -->
                <span class="labelOnPanel">
                  Domain Type
                </span>
                <div class="field-display">
                  {#if domain.domainType}
                    <div class="status-icon domain-type"></div>
                    <span>{domain.domainType}</span>
                  {:else}
                    <span class="none-text">None</span>
                  {/if}
                </div>

                <!-- Horizontal separator -->
                <div class="metadata-separator"></div>

                <!-- Tags using standard grid pattern (same line) -->
                <span class="labelOnPanel">
                  <Label label={plugin.string.Tags} />
                </span>
                <div class="field-display tags-container">
                  {#if domain.tags && domain.tags.length > 0}
                    <div class="tags-grid">
                      {#each domain.tags as tag}
                        <div class="tag-chip">
                          {tag.displayName || tag.name || tag.tagFQN || tag}
                        </div>
                      {/each}
                    </div>
                  {:else}
                    <span class="none-text">None</span>
                  {/if}
                </div>
              </div>
            </svelte:fragment>
          </Panel>
        {/if}
      {:else if mode === 'sub-domains'}
        <!-- Sub Domains Tab -->
        <div class="sub-domains-container">
          {#if domain.children && domain.children.length > 0}
            <table class="antiTable highlightRows">
              <thead class="scroller-thead">
                <tr class="scroller-thead__tr">
                  <th>
                    <div class="antiTable-cells">
                      <span>Sub Domains</span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span><Label label={plugin.string.DescriptionForTable} /></span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span><Label label={plugin.string.Owners} /></span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {#each domain.children as child}
                  <tr
                    class="antiTable-body__row"
                    on:mouseover={mouseAttractor(() => {})}
                    on:mouseenter={mouseAttractor(() => {})}
                    on:focus={() => {}}
                  >
                    <td>
                      <div class="antiTable-cells__firstCell">
                        {mapDisplayName(child.displayName || child.name) || child.id}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells definition">
                        {cleanHtmlDescription(child.description || '')}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells data-cell">
                        {#if child.owners && child.owners.length > 0}
                          {#each child.owners as owner, i}
                            {owner.displayName || owner.name || owner.id}{#if i < child.owners.length - 1}, {/if}
                          {/each}
                        {:else}
                          N/A
                        {/if}
                      </div>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          {:else}
            <div class="empty-state">
              <p>No sub-domains found.</p>
            </div>
          {/if}
        </div>
      {:else if mode === 'data-products'}
        <!-- Data Products Tab  -->
        <div class="sub-domains-container">
          {#if dataProducts && dataProducts.length > 0}
            <table class="antiTable highlightRows">
              <thead class="scroller-thead">
                <tr class="scroller-thead__tr">
                  <th>
                    <div class="antiTable-cells">
                      <span>Data Product</span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span><Label label={plugin.string.DescriptionForTable} /></span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span>Updated By</span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span>Version</span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {#each dataProducts as product}
                  <tr
                    class="antiTable-body__row"
                    on:mouseover={mouseAttractor(() => {})}
                    on:mouseenter={mouseAttractor(() => {})}
                    on:focus={() => {}}
                  >
                    <td>
                      <div class="antiTable-cells__firstCell">
                        <div class="product-name">
                          {product.displayName || product.name || 'Unnamed Product'}
                        </div>
                        {#if product.fullyQualifiedName && product.fullyQualifiedName !== (product.displayName || product.name)}
                          <div class="product-fqn">
                            {product.fullyQualifiedName}
                          </div>
                        {/if}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells definition">
                        {cleanHtmlDescription(product.description || 'No description available')}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells data-cell">
                        {product.updatedBy || 'N/A'}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells data-cell">
                        {#if product.version}
                          v{product.version}
                        {:else}
                          N/A
                        {/if}
                      </div>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          {:else}
            <div class="empty-state">
              <Icon icon={plugin.icon.Datacatalog} size={'large'} />
              <p>No data products found for this domain.</p>
              <p class="empty-state-subtitle">Data products will appear here when they are created and associated with this domain.</p>
            </div>
          {/if}
        </div>
      {:else if mode === 'assets'}
        <!-- Assets Tab -->
        <div class="sub-domains-container">
          {#if domain.assets && domain.assets.length > 0}
            <table class="antiTable highlightRows">
              <thead class="scroller-thead">
                <tr class="scroller-thead__tr">
                  <th>
                    <div class="antiTable-cells">
                      <span>Asset</span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span>Type</span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span><Label label={plugin.string.DescriptionForTable} /></span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span>FQN</span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {#each domain.assets as asset}
                  <tr
                    class="antiTable-body__row"
                    on:mouseover={mouseAttractor(() => {})}
                    on:mouseenter={mouseAttractor(() => {})}
                    on:focus={() => {}}
                  >
                    <td>
                      <div class="antiTable-cells__firstCell">
                        <div class="asset-name">
                          {asset.name || asset.displayName || 'Unnamed Asset'}
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells data-cell">
                        {asset.type || 'Data Asset'}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells definition">
                        {cleanHtmlDescription(asset.description || 'No description available')}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells data-cell">
                        {#if asset.fullyQualifiedName}
                          <div class="asset-fqn">
                            {asset.fullyQualifiedName}
                          </div>
                        {:else}
                          N/A
                        {/if}
                      </div>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          {:else}
            <div class="empty-state">
              <Icon icon={plugin.icon.Datacatalog} size={'large'} />
              <p>No data assets found for this domain.</p>
              <p class="empty-state-subtitle">Data assets will appear here when they are created and associated with this domain.</p>
            </div>
          {/if}
        </div>
      {:else if mode === 'custom-properties'}
        <!-- Custom Properties Tab -->
        <div class="sub-domains-container">
          {#if customPropertiesTypeDef && customPropertiesTypeDef.customProperties && customPropertiesTypeDef.customProperties.length > 0}
            <table class="antiTable highlightRows">
              <thead class="scroller-thead">
                <tr class="scroller-thead__tr">
                  <th>
                    <div class="antiTable-cells">
                      <span>Custom Property</span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span>Type</span>
                    </div>
                  </th>
                  <th>
                    <div class="antiTable-cells">
                      <span><Label label={plugin.string.DescriptionForTable} /></span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {#each customPropertiesTypeDef.customProperties as customProp}
                  <tr
                    class="antiTable-body__row"
                    on:mouseover={mouseAttractor(() => {})}
                    on:mouseenter={mouseAttractor(() => {})}
                    on:focus={() => {}}
                  >
                    <td>
                      <div class="antiTable-cells__firstCell">
                        {customProp.name}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells definition">
                        {customProp.propertyType?.type || customProp.propertyType?.name || 'Unknown'}
                      </div>
                    </td>
                    <td>
                      <div class="antiTable-cells data-cell">
                        {cleanHtmlDescription(customProp.description || 'No description available')}
                      </div>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          {:else}
            <div class="empty-state">
              <p>No custom properties defined for domains.</p>
              <p class="empty-state-subtitle">Custom properties will appear here when they are defined in OpenMetadata for the domain entity type.</p>
            </div>
          {/if}
        </div>
      {/if}
    </div>
  {:else}
    <Header adaptive={'disabled'}>
      <Breadcrumbs
        items={[
          {
            icon: IconFolder,
            title: 'Domain not found'
          }
        ]}
        currentOnly
      />
    </Header>
  {/if}
</div>

<style lang="scss">
  .domain-detail {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .loading-content,
  .error-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-large);
  }

  .domain-content {
    flex: 1;
    overflow-y: auto;
  }

  .panel-title {
    font-size: var(--font-size-large);
    font-weight: 600;
    color: var(--theme-caption-color);
  }

  .panel-main-content {
    padding: 0;
  }



  .description-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    flex-shrink: 0;
    padding: 0;
  }

  .section-label {
    color: var(--theme-dark-color);
    font-weight: 600;
    font-size: .75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
  }

  .description-text {
    color: var(--global-secondary-TextColor);
    font-size: 1rem;
    line-height: 1.4rem;
    margin: 0;
  }

  .description {
  font-size: 1.25rem;
  line-height: 1.4;
}






  .field-display {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--global-primary-TextColor);
    font-size: 0.875rem;
  }

  .status-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #2563EB;
    position: relative;
    flex-shrink: 0;

    &.domain-type {
      background-color: #6366F1;
    }



    &.domain-type::after {
      content: '';
      width: 6px;
      height: 6px;
      background-color: white;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #3B82F6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.625rem;
    font-weight: 600;
    flex-shrink: 0;

    &.expert {
      background-color: #8B5CF6;
    }
  }

  .owners-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
  }

  .owner-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: var(--global-primary-TextColor);
  }

  .tags-container {
    width: 100%;
  }

  .tags-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    width: 100%;
  }

  .tag-chip {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-0_25) var(--spacing-0_75);
    border-radius: var(--extra-small-BorderRadius);
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1.2;
    white-space: nowrap;
    background-color: var(--global-ui-BackgroundColor);
    color: var(--global-secondary-TextColor);
    border: 1px solid var(--global-subtle-ui-BorderColor);
  }

  .none-text {
    color: var(--global-secondary-TextColor);
    font-style: italic;
    font-size: 0.875rem;
  }


  .data-products-container,
  .sub-domains-container {
    padding: 0rem;
    height: 100%;
    overflow-y: auto;
  }

  .sub-domains-container .antiTable {
    width: 100%;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-8);
    text-align: center;
    color: var(--theme-content-color);
  }

  .empty-state-subtitle {
    font-size: var(--font-size-small);
    color: var(--theme-content-color);
    opacity: 0.7;
    margin-top: var(--spacing-2);
  }


  .cards-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    padding: var(--spacing-4);
  }


  .antiContactCard {
    display: flex;
    flex-direction: column;
    flex: 1 1 300px;
    min-width: 300px;
    min-height: 200px;
  }

  .card-content {
    flex: 1;
  }

  .card-bottom {
    margin-top: auto;
    display: flex;
    flex-direction: column;
  }

  .metadata-right {
    text-align: right;
    font-size: var(--font-size-x-small);
    color: var(--theme-content-accent-color);
    margin-bottom: var(--spacing-1);
  }

  .meta-item {
    margin-bottom: 2px;
  }

  .fqn-display {
    font-size: var(--font-size-small);
    color: var(--theme-content-accent-color);
    align-self: flex-start;
  }


  .card-bottom .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-1);
  }

  .tags-display {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }

  .tag-chip.small {
    font-size: 0.625rem;
    padding: var(--spacing-0_25) var(--spacing-0_5);
    background-color: var(--global-ui-BackgroundColor);
    color: var(--global-secondary-TextColor);
    border: 1px solid var(--global-subtle-ui-BorderColor);
    border-radius: var(--extra-small-BorderRadius);
  }

  .tag-chip.more {
    background-color: var(--theme-bg-accent-color);
    color: var(--theme-caption-color);
    font-weight: 600;
  }



  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-8);
    color: var(--global-secondary-TextColor);
    font-style: italic;
    text-align: center;
  }

  .empty-state-subtitle {
    font-size: var(--font-size-x-small);
    margin-top: var(--spacing-2);
    opacity: 0.8;
  }

  .metadata-separator {
    grid-column: 1 / -1;
    height: 1px;
    background-color: var(--theme-divider-color);
    margin: var(--spacing-3) 0;
  }

  .tags-container {
    width: 100%;
  }

  .tags-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
    width: 100%;
    justify-content: flex-start;
  }


  .assets-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
  }

  .assets-grid .antiContactCard {
    width: 100%;
    max-width: 100%;
    min-width: 100%;
  }

  .asset-type-badge {
    display: inline-block;
    background: var(--theme-button-bg-enabled);
    color: var(--theme-button-content-enabled);
    padding: 0;
    border-radius: var(--small-BorderRadius);
    font-size: var(--font-size-small);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-2);
  }

  .asset-title {
    font-size: var(--font-size-large);
    font-weight: 600;
    color: var(--theme-caption-color);
    margin: 0;
    line-height: 1.3;
  }

  .asset-body {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .asset-field {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .asset-fqn {
    font-family: var(--font-mono);
    font-size: var(--font-size-small);
    color: var(--theme-content-accent-color);
  }

  .asset-description {
    color: var(--theme-content-color);
    line-height: 1.5;
    margin: 0;
  }

  .footer {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .footer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .description.overflow-label {
    font-size: var(--font-size-small);
    color: var(--theme-content-accent-color);
    align-self: flex-start;
  }

  .name {
  font-size: 1.5rem;
  font-weight: 600; 
  }

  .label.uppercase {
  font-size: var(--font-size-medium); 
  font-weight: 600; 
  }

  .field-row {
    display: flex;
    gap: var(--spacing-1);
    margin-top: var(--spacing-1);
    font-size: var(--font-size-small);
  }

  .field-label {
    font-weight: 600;
    color: var(--theme-content-accent-color);
    min-width: 60px;
  }

  .field-value {
    color: var(--theme-content-color);
    flex: 1;
  }

  .tags-inline {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }

  .tag-chip.small {
    font-size: var(--font-size-x-small);
    padding: 2px 6px;
    background: var(--theme-button-default);
    border-radius: var(--border-radius-small);
    white-space: nowrap;
  }

  .tag-chip.more {
    background: var(--theme-button-pressed);
    color: var(--theme-content-accent-color);
  }

  .field-row.right-aligned {
    justify-content: flex-end;
    margin-left: auto;
    max-width: 150px;
  }

  .bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: var(--spacing-2);
  }

  .metadata-right {
    text-align: right;
    font-size: var(--font-size-x-small);
    color: var(--theme-content-accent-color);
  }

  .meta-item {
    margin-bottom: 2px;
  }

  .product-name {
    font-weight: 600;
    color: var(--theme-caption-color);
    font-size: var(--font-size-medium);
    line-height: 1.3;
  }

  .product-fqn {
    font-family: var(--font-mono);
    font-size: var(--font-size-x-small);
    color: var(--theme-content-accent-color);
    margin-top: 2px;
    opacity: 0.8;
  }

  .product-version {
    font-size: var(--font-size-x-small);
    color: var(--theme-content-accent-color);
    background: var(--theme-button-default);
    padding: 1px 4px;
    border-radius: var(--border-radius-small);
    display: inline-block;
    margin-top: 2px;
    font-weight: 500;
  }

  .update-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .update-date {
    font-size: var(--font-size-x-small);
    color: var(--theme-content-color);
  }

  .updated-by {
    font-size: var(--font-size-x-small);
    color: var(--theme-content-accent-color);
    opacity: 0.8;
  }

  .tags-table {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
  }

  .tag-chip.table-tag {
    font-size: var(--font-size-x-small);
    padding: 2px 6px;
    background: var(--theme-button-default);
    color: var(--theme-content-color);
    border-radius: var(--border-radius-small);
    white-space: nowrap;
    border: 1px solid var(--global-subtle-ui-BorderColor);
  }

  .tag-chip.table-tag.more {
    background: var(--theme-button-pressed);
    color: var(--theme-content-accent-color);
    font-weight: 600;
  }

  .asset-name {
    font-weight: 600;
    color: var(--theme-caption-color);
    font-size: var(--font-size-medium);
    line-height: 1.3;
  }

  .asset-fqn {
    font-family: var(--font-mono);
    font-size: var(--font-size-x-small);
    color: var(--theme-content-accent-color);
    word-break: break-all;
  }

</style>