import { getEntityByFqn, getEntityById } from '../../openmetadata/entities'
import { AssetTableItem } from './types'

export async function safeGetEntity<T>(
  endpoint: string,
  fqnOrId: { fqn?: string; id?: string },
  fields: string[]
): Promise<T> {
  try {
    return fqnOrId.fqn
      ? await getEntityByFqn<T>(endpoint, fqnOrId.fqn, fields)
      : await getEntityById<T>(endpoint, fqnOrId.id!, fields)
  } catch (err: any) {
    // Retry without fields on 4xx errors (invalid field etc.)
    const msg = String(err?.message ?? '')
    if (err?.code === 400 || err?.status === 400 || msg.includes('Invalid') || msg.includes('status: 400')) {
      return fqnOrId.fqn
        ? await getEntityByFqn<T>(endpoint, fqnOrId.fqn)
        : await getEntityById<T>(endpoint, fqnOrId.id!)
    }
    throw err
  }
}

export function normalizeArrayField(field: any[] | undefined, fallbackField?: any[]): string[] {
  const sourceArray = (field && field.length ? field : fallbackField ?? []) as any[]
  return sourceArray
    .map((item: any) => {
      if (typeof item === 'string') return item
      return item?.displayName ?? item?.name ?? item?.tagFQN ?? item?.fullyQualifiedName ?? ''
    })
    .filter(Boolean) as string[]
}

export function compareItems(a: AssetTableItem, b: AssetTableItem, sortKey: string): number {
  const av: any = a[sortKey as keyof AssetTableItem]
  const bv: any = b[sortKey as keyof AssetTableItem]
  const as = Array.isArray(av) ? av.join(', ') : String(av ?? '')
  const bs = Array.isArray(bv) ? bv.join(', ') : String(bv ?? '')
  return as.localeCompare(bs, undefined, { numeric: true })
}
