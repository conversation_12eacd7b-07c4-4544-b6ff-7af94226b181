import type { AnySvelteComponent } from '@hcengineering/ui'

// ---------------------------------------------------------------------------
// Types (minimal subset to keep the component typed yet flexible)
// ---------------------------------------------------------------------------
export interface BasicRef {
  name?: string
  displayName?: string
}

export interface AssetSearchHit {
  id?: string
  name?: string
  displayName?: string
  fullyQualifiedName: string
  entityType: string
  description?: string

  // Extra fields used for grouping / display
  service?: BasicRef
  database?: BasicRef
  databaseSchema?: BasicRef
  serviceType?: string[]
}

export interface Category {
  id: string
  label: string
  endpoint: string // OpenMetadata API endpoint
  icon?: AnySvelteComponent
  childLevels: { entityType: string; parentFilter: string | null }[]
}

export interface Row {
  type: string
  label: string
  category: Category
  level: number
  expanded: boolean
  childrenLoaded: boolean
  entity?: AssetSearchHit
}

export interface AssetDetail extends AssetSearchHit {
  owner?: any
  tags?: any[]
  columns?: Array<{ name: string; dataType: string }>
  usageSummary?: any
  charts?: any[]
  partitions?: number
  algorithm?: string
  target?: string
  mlFeatures?: Array<{
    name: string
    dataType?: string
    description?: string
    featureAlgorithm?: string
    featureSources?: any[]
  }>
  mlHyperParameters?: Array<{ name: string; value: string }>
  mlStore?: any
  server?: string
  numberOfObjects?: number
  size?: number
  tableConstraints?: any[]
  expression?: string
}

export interface AssetTableItem {
  displayName: string
  description: string
  serviceType: string[]
  entity: AssetSearchHit
}
