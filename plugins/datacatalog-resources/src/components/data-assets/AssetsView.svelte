<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Panel } from '@hcengineering/panel'
  import { Label, Section, Spinner } from '@hcengineering/ui'
  import view from '@hcengineering/view'
  import AssetsControlPanel from './AssetsControlPanel.svelte'
  import plugin from '../../plugin'

  export let asset: any
  export let embedded: boolean = false
  export let readonly: boolean = false
  export let loading: boolean = false

  const dispatch = createEventDispatcher()

  $: displayTitle = asset
    ? `${asset.displayName ?? asset.name ?? asset.fullyQualifiedName ?? 'Unknown'}`
    : 'Loading...'

  // Provide a safe, minimal object for Panel
  $: panelObject = (() => {
    if (!asset) return null
    const base: any = {
      _id: (asset.id ?? asset.fullyQualifiedName ?? asset.name ?? 'asset') as any,
      _class: 'datacatalog:class:Asset' as any,
      space: 'datacatalog:space:Main' as any,
      modifiedOn: (asset.updatedAt ?? Date.now()),
      displayName: (asset.displayName ?? asset.name ?? asset.fullyQualifiedName),
      name: (asset.name ?? asset.displayName ?? asset.fullyQualifiedName),
      description: asset.description ?? ''
    }
    if (asset.updatedBy) base.modifiedBy = asset.updatedBy as any
    return base
  })()

  function openChild(child: any): void {
    if (!child) return
    const fqn = child.fullyQualifiedName ?? child.name
    if (!fqn) return
    dispatch('openAsset', {
      ...child,
      fullyQualifiedName: fqn,
      entityType: 'databaseSchema'
    })
  }

  function formatTagDisplayName(tag: any): string {
    if (typeof tag === 'string') return tag
    return tag?.displayName || tag?.name || tag?.tagFQN || tag?.fullyQualifiedName || tag || ''
  }

  // Build the list of terms (children) to display, like glossary terms
  $: termsList = Array.isArray(asset?.databaseSchemas) ? asset.databaseSchemas : []
</script>

{#if asset && asset.id}
  <Panel
    object={panelObject}
    isHeader={false}
    withoutInput={readonly}
    allowClose={!embedded}
    isAside={true}
    isSub={false}
    {embedded}
    withoutActivity={true}
    adaptive={'default'}
    on:open
    on:close={() => dispatch('close')}
    on:select
  >
    <svelte:fragment slot="title">
      <div class="title">{displayTitle}</div>
    </svelte:fragment>

    <div class="main-content">
      <div class="description-section">
        <div class="section-label"><Label label={plugin.string.DescriptionForTable} /></div>
        {#if asset?.description}
          <div class="description-text">{@html asset.description}</div>
        {:else}
          <div class="description-text"><Label label={plugin.string.NoDescriptionAvailable} /></div>
        {/if}
      </div>

      <div class="step-tb-6">
        <Section label={"Related"} icon={view.icon.List}>
          <svelte:fragment slot="header">
            {#if loading}
              <div class="ml-1">
                <Spinner size="small" />
              </div>
            {/if}
            <div class="w-4 min-w-4 max-w-4" />
            <div class="antiSection-header__tag highlight">
              <Label label={plugin.string.All} />
            </div>
          </svelte:fragment>

          <svelte:fragment slot="content">
            {#if loading}
              <div class="centered-content">
                <Spinner size="medium" />
              </div>
            {:else if termsList.length > 0}
              <div class="terms-activity">
                {#each termsList as child}
                  <div class="term-activity-item" on:click={() => openChild(child)}>
                    <div class="term-icon"></div>
                    <div class="term-content">
                      <div class="term-name">{child.displayName ?? child.name}</div>
                      {#if child.description}
                        <div class="term-description">{child.description}</div>
                      {/if}
                      {#if child.tags && child.tags.length > 0}
                        <div class="term-tags">
                          {#each child.tags.slice(0, 3) as tag}
                            <span class="tag-chip">{formatTagDisplayName(tag)}</span>
                          {/each}
                          {#if child.tags.length > 3}
                            <span class="more-tags">+{child.tags.length - 3}</span>
                          {/if}
                        </div>
                      {/if}
                    </div>
                  </div>
                {/each}
              </div>
            {:else}
              <div class="centered-content">
                <div class="placeholder-text">No terms found</div>
              </div>
            {/if}
          </svelte:fragment>
        </Section>
      </div>
    </div>

    <svelte:fragment slot="aside">
      <AssetsControlPanel asset={asset} {readonly} />
    </svelte:fragment>
  </Panel>
{:else if asset}
  <div class="main-content">
    <div class="description-section">
      <div class="section-label"><Label label={plugin.string.DescriptionForTable} /></div>
      <div class="description-text">Loading...</div>
    </div>
  </div>
{/if}

<style>
  .main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    height: 100%;
    overflow-y: auto;
  }

  .description-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    flex-shrink: 0;
    padding: var(--spacing-3);
  }

  .section-label {
    color: var(--theme-dark-color);
    font-weight: 600;
    font-size: .75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
  }

  .description-text {
    color: var(--global-secondary-TextColor);
    font-size: 1rem;
    line-height: 1.4rem;
    margin: 0;
  }

  .title {
    font-weight: 500;
    font-size: 1.25rem;
    line-height: 1.5rem;
    color: var(--theme-caption-color);
  }

  .centered-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
    flex: 1;
  }

  .placeholder-text {
    color: var(--global-secondary-TextColor);
    font-style: italic;
    font-size: .875rem;
  }

  .terms-activity {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: var(--spacing-2);
  }

  .term-activity-item {
    display: flex;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    border-radius: var(--small-BorderRadius);
    cursor: pointer;
    transition: background-color 0.15s ease;
  }

  .term-activity-item:hover {
    background-color: var(--theme-link-color);
  }

  .term-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #6B7280;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .term-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-0_5);
  }

  .term-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--global-primary-TextColor);
    line-height: 1.2;
  }

  .term-description {
    font-size: 0.75rem;
    color: var(--global-secondary-TextColor);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .term-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-0_25);
    margin-top: var(--spacing-0_25);
  }

  .tag-chip {
    padding: 1px 4px;
    background-color: var(--global-ui-BackgroundColor);
    color: var(--global-secondary-TextColor);
    border: 1px solid var(--global-subtle-ui-BorderColor);
    border-radius: 2px;
    font-size: 0.625rem;
  }

  .more-tags {
    font-size: 0.625rem;
    color: var(--global-secondary-TextColor);
    font-style: italic;
  }

  .step-tb-6 { padding: 0 var(--spacing-3); }
</style>


