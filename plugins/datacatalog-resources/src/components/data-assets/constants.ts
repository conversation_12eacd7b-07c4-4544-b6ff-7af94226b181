import type { AnySvelteComponent } from '@hcengineering/ui'
import {
  IconObjects,
  IconDetails,
  IconShare,
  IconThread,
  IconActivity,
  IconFile,
  IconSettings,
  IconFolder
} from '@hcengineering/ui'
import type { Category } from './types'

// Map OpenMetadata entity types to icons used in the navigation list
export const entityTypeIcons: Record<string, AnySvelteComponent> = {
  database: IconObjects,
  table: IconObjects,
  dashboard: IconDetails,
  pipeline: IconShare,
  topic: IconThread,
  chart: IconDetails,
  metric: IconActivity,
  report: IconFile,
  mlmodel: IconSettings,
  container: IconFolder
}

export const categories: Category[] = [
  {
    id: 'databases',
    label: 'Databases',
    endpoint: 'databases',
    icon: IconObjects,
    childLevels: [
      { entityType: 'databaseService', parentFilter: null },
      { entityType: 'database', parentFilter: 'service.fullyQualifiedName' },
      { entityType: 'databaseSchema', parentFilter: 'database.fullyQualifiedName' },
      { entityType: 'table', parentFilter: 'databaseSchema.fullyQualifiedName' }
    ]
  },
  {
    id: 'dashboards',
    label: 'Dashboards',
    endpoint: 'dashboards',
    icon: IconDetails,
    childLevels: [
      { entityType: 'dashboardService', parentFilter: null },
      { entityType: 'dashboard', parentFilter: 'service.fullyQualifiedName' }
    ]
  },
  {
    id: 'pipelines',
    label: 'Pipelines',
    endpoint: 'pipelines',
    icon: IconShare,
    childLevels: [
      { entityType: 'pipelineService', parentFilter: null },
      { entityType: 'pipeline', parentFilter: 'service.fullyQualifiedName' }
    ]
  },
  {
    id: 'topics',
    label: 'Topics',
    endpoint: 'topics',
    icon: IconThread,
    childLevels: [
      { entityType: 'messagingService', parentFilter: null },
      { entityType: 'topic', parentFilter: 'service.fullyQualifiedName' }
    ]
  },
  {
    id: 'charts',
    label: 'Charts',
    endpoint: 'charts',
    icon: IconDetails,
    childLevels: [
      { entityType: 'chartService', parentFilter: null },
      { entityType: 'chart', parentFilter: 'service.fullyQualifiedName' }
    ]
  },
  {
    id: 'metrics',
    label: 'Metrics',
    endpoint: 'metrics',
    icon: IconActivity,
    childLevels: [
      { entityType: 'metricService', parentFilter: null },
      { entityType: 'metric', parentFilter: 'service.fullyQualifiedName' }
    ]
  },
  {
    id: 'reports',
    label: 'Reports',
    endpoint: 'reports',
    icon: IconFile,
    childLevels: [
      { entityType: 'reportService', parentFilter: null },
      { entityType: 'report', parentFilter: 'service.fullyQualifiedName' }
    ]
  },
  {
    id: 'mlmodels',
    label: 'ML Models',
    endpoint: 'mlmodels',
    icon: IconSettings,
    childLevels: [
      { entityType: 'mlmodelService', parentFilter: null },
      { entityType: 'mlmodel', parentFilter: 'service.fullyQualifiedName' }
    ]
  },
  {
    id: 'containers',
    label: 'Containers',
    endpoint: 'containers',
    icon: IconFolder,
    childLevels: [
      { entityType: 'storageService', parentFilter: null },
      { entityType: 'container', parentFilter: 'service.fullyQualifiedName' }
    ]
  }
]

export const commonFields = ['tags', 'usageSummary']
export const extraFields: Record<string, string[]> = {
  table: ['columns', 'tableConstraints'],
  dashboard: ['charts'],
  topic: ['partitions'],
  mlmodel: ['algorithm', 'target', 'mlFeatures', 'mlHyperParameters', 'mlStore', 'server'],
  container: ['numberOfObjects', 'size'],
  metric: ['expression']
}
