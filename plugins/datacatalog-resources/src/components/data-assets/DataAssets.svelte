<script lang="ts">
  import { onMount } from 'svelte'
  import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Label,
    Scroller,
    IconObjects,
    IconDetails,
    IconShare,
    IconThread,
    IconActivity,
    IconFile,
    IconSettings,
    IconFolder,
    Section,
    SearchInput,
    IconUp,
    IconDown,
    Icon,
    mouseAttractor,
    IconBack
  } from '@hcengineering/ui'
  import type { AnySvelteComponent } from '@hcengineering/ui'
  import { getEmbeddedLabel } from '@hcengineering/platform'
  import plugin from '../../plugin'
  import AssetsView from './AssetsView.svelte'
  import {
    getEntityByFqn,
    getEntityById,
    updateEntity,
    listEntities
  } from '../../openmetadata/entities'

  // Map OpenMetadata entity types to icons used in the navigation list
  const entityTypeIcons: Record<string, AnySvelteComponent> = {
    database: IconObjects,
    table: IconObjects,
    dashboard: IconDetails,
    pipeline: IconShare,
    topic: IconThread,
    chart: IconDetails,
    metric: IconActivity,
    report: IconFile,
    mlmodel: IconSettings,
    container: IconFolder
  }

  // Keep a large but safe limit similar to governance table prefetch
  const FETCH_LIMIT = 1000

  // ---------------------------------------------------------------------------
  // Types (minimal subset to keep the component typed yet flexible)
  // ---------------------------------------------------------------------------
  interface BasicRef {
    name?: string
    displayName?: string
  }

  interface AssetSearchHit {
    id?: string
    name?: string
    displayName?: string
    fullyQualifiedName: string
    entityType: string
    description?: string
    serviceType?: string

    // Extra fields used for grouping / display
    service?: BasicRef
    database?: BasicRef
    databaseSchema?: BasicRef
  }

  interface Category {
    id: string
    label: string
    endpoint: string // OpenMetadata API endpoint
    icon?: AnySvelteComponent
    childLevels: { entityType: string; parentFilter: string | null }[]
  }

  interface Row {
    type: string
    label: string
    category: Category
    level: number
    expanded: boolean
    childrenLoaded: boolean
    entity?: AssetSearchHit
  }

  interface AssetDetail extends AssetSearchHit {
    owner?: any
    tags?: any[]
    columns?: Array<{ name: string; dataType: string }>
    usageSummary?: any
    charts?: any[]
    partitions?: number
    algorithm?: string
    target?: string
    mlFeatures?: Array<{
      name: string
      dataType?: string
      description?: string
      featureAlgorithm?: string
      featureSources?: any[]
    }>
    mlHyperParameters?: Array<{ name: string; value: string }>
    mlStore?: any
    server?: string
    numberOfObjects?: number
    size?: number
    tableConstraints?: any[]
    expression?: string
  }

  // ---------------------------------------------------------------------------
  // Component state
  // ---------------------------------------------------------------------------
  // No exported props currently used
  export let selectedCategory: string | undefined = undefined

  // Intl placeholder/labels – temporary direct strings cast to any for IntlString compatibility
  const intlSearchAssets: any = getEmbeddedLabel('Search assets…')
  const intlNoAssets: any = getEmbeddedLabel('No assets found')
  const intlSelectAsset: any = getEmbeddedLabel('Select an asset to view details')
  const intlNextPage: any = getEmbeddedLabel('Next Page')
  const intlPrevPage: any = getEmbeddedLabel('Previous Page')
  const intlLoading: any = getEmbeddedLabel('Loading...')

  /**
   * Convert a plain string to the `IntlString` compatible type expected by
   * some UI components. This avoids the need for `as any` assertions directly
   * inside the Svelte markup, which are not allowed.
   */
  function toIntl (str: string): any {
    return getEmbeddedLabel(str)
  }

  function toSingular (endpoint: string): string {
    return endpoint.endsWith('s') ? endpoint.slice(0, -1) : endpoint
  }

  function isServiceType (entityType: string): boolean {
    return /service$/i.test(entityType)
  }

  function buildDetailsEndpoint (entityType: string): string {
    if (!entityType) return ''
    // Preserve case for service endpoints: services/databaseServices
    if (isServiceType(entityType)) {
      const plural = entityType.endsWith('s') ? entityType : `${entityType}s`
      return `services/${plural}`
    }
    const t = entityType.toLowerCase()
    return t.endsWith('s') ? t : `${t}s`
  }

  $: if (selectedCategory) {
    viewCategory(selectedCategory)
  }

  const categories: Category[] = [
    {
      id: 'databases',
      label: 'Databases',
      endpoint: 'databases',
      icon: IconObjects,
      childLevels: [
        { entityType: 'databaseService', parentFilter: null },
        { entityType: 'database', parentFilter: 'service.fullyQualifiedName' },
        { entityType: 'databaseSchema', parentFilter: 'database.fullyQualifiedName' },
        { entityType: 'table', parentFilter: 'databaseSchema.fullyQualifiedName' }
      ]
    },
    {
      id: 'dashboards',
      label: 'Dashboards',
      endpoint: 'dashboards',
      icon: IconDetails,
      childLevels: [
        { entityType: 'dashboardService', parentFilter: null },
        { entityType: 'dashboard', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'pipelines',
      label: 'Pipelines',
      endpoint: 'pipelines',
      icon: IconShare,
      childLevels: [
        { entityType: 'pipelineService', parentFilter: null },
        { entityType: 'pipeline', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'topics',
      label: 'Topics',
      endpoint: 'topics',
      icon: IconThread,
      childLevels: [
        { entityType: 'messagingService', parentFilter: null },
        { entityType: 'topic', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'charts',
      label: 'Charts',
      endpoint: 'charts',
      icon: IconDetails,
      childLevels: [
        { entityType: 'chartService', parentFilter: null },
        { entityType: 'chart', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'metrics',
      label: 'Metrics',
      endpoint: 'metrics',
      icon: IconActivity,
      childLevels: [
        { entityType: 'metricService', parentFilter: null },
        { entityType: 'metric', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'reports',
      label: 'Reports',
      endpoint: 'reports',
      icon: IconFile,
      childLevels: [
        { entityType: 'reportService', parentFilter: null },
        { entityType: 'report', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'mlmodels',
      label: 'ML Models',
      endpoint: 'mlmodels',
      icon: IconSettings,
      childLevels: [
        { entityType: 'mlmodelService', parentFilter: null },
        { entityType: 'mlmodel', parentFilter: 'service.fullyQualifiedName' }
      ]
    },
    {
      id: 'containers',
      label: 'Containers',
      endpoint: 'containers',
      icon: IconFolder,
      childLevels: [
        { entityType: 'storageService', parentFilter: null },
        { entityType: 'container', parentFilter: 'service.fullyQualifiedName' }
      ]
    }
  ]

  let searchTerm: string = ''
  let displayRows: Row[] = []
  let categoryEntities: Map<string, AssetSearchHit[]> = new Map()
  let assetsLoading = false
  let errorMessage: string | null = null

  let selectedFqn: string | null = null
  let selectedAsset: AssetDetail | null = null
  let detailsLoading = false

  // Cache for already fetched asset details to avoid refetching
  const detailsCache: Map<string, AssetDetail> = new Map()
  let detailsRequestId : string | null = null

  // description editing
  let editingDescription = false
  let descriptionDraft: string = ''

  // pagination
  let limit = 20
  let offset = 0
  let total = 0
  let currentPage = 1
  let paginationLoading = false

  type SortableKey = 'displayName' | 'description' | 'serviceType'
  let sortKey: SortableKey = 'displayName'
  let sortAsc = true
  let rowSelection: number | undefined = undefined

  // ---------------------------------------------------------------------------
  // Helper functions
  // ---------------------------------------------------------------------------
  onMount(async () => {
    if (selectedCategory == null) {
      await fetchAllAssets()
    }
  })

  async function fetchAllAssets (): Promise<void> {
    assetsLoading = true
    errorMessage = null
    const failedCategories: string[] = []
    
    try {
      const entries = await Promise.all(
        categories.map(async (c) => {
          try {
            const leafType = c.childLevels[c.childLevels.length - 1].entityType
            const leafEndpoint = leafType.endsWith('s') ? leafType : `${leafType}s`
            const res = await listEntities<any>(leafEndpoint, { limit: FETCH_LIMIT, include: 'non-deleted' })
            const assets = (res.data ?? []).map((entity: any) => ({
              ...entity,
              entityType: entity.entityType ?? leafType,
              fullyQualifiedName: entity.fullyQualifiedName ?? entity.name ?? entity.id
            })) as AssetSearchHit[]
            return [c.id, assets] as const
          } catch (err) {
            console.error(`Failed to fetch ${c.id}`, err)
            failedCategories.push(c.label)
            return [c.id, [] as AssetSearchHit[]] as const
          }
        })
      )

      categoryEntities = new Map(entries)
      if (failedCategories.length > 0 && failedCategories.length === categories.length) {
        errorMessage = 'Failed to load any data categories. Please check your connection and try again.'
      }
      
      // If user already typed something, recompute search from prefetched data
      if (searchTerm.trim()) {
        await loadAssets()
      }
    } finally {
      assetsLoading = false
    }
  }

  async function loadAssets (): Promise<void> {
    assetsLoading = true
    errorMessage = null
    try {
      const trimmedTerm = searchTerm.trim()
      if (!trimmedTerm) {
        // Clear transient search rows; table will show pre-fetched items
        displayRows = []
        selectedFqn = null
        selectedAsset = null
        return
      }

      // Local search over pre-fetched assets (like governance table)
      const allAssets: AssetSearchHit[] = Array.from(categoryEntities.values()).flat()
      const filtered = allAssets.filter((a) => {
        const name = (a.displayName ?? a.name ?? a.fullyQualifiedName ?? '').toLowerCase()
        const desc = (stripHtmlToText(a.description ?? '')).toLowerCase()
        const q = trimmedTerm.toLowerCase()
        return name.includes(q) || desc.includes(q)
      })

      const rows: Row[] = filtered
        .sort((a, b) => {
          const aName = (a.displayName ?? a.name ?? a.fullyQualifiedName).toLowerCase()
          const bName = (b.displayName ?? b.name ?? b.fullyQualifiedName).toLowerCase()
          return aName.localeCompare(bName)
        })
        .map((hit) => {
          const label = hit.displayName ?? hit.name ?? hit.fullyQualifiedName
          const serviceName = hit.service?.displayName ?? hit.service?.name
          return {
            type: hit.entityType ?? 'item',
            label: serviceName ? `${serviceName} / ${label}` : label,
            entity: hit,
            category:
              categories.find((c) =>
                c.childLevels.some((l) => l.entityType === hit.entityType)
              ) ?? categories[0],
            level: 0,
            expanded: false,
            childrenLoaded: false
          }
        })

      displayRows = rows
      selectedFqn = null
      selectedAsset = null
    } catch (err) {
      console.error('Failed to fetch assets', err)
    } finally {
      assetsLoading = false
    }
  }

  async function loadPaginatedList (categoryId: string): Promise<void> {
    paginationLoading = true
    try {
      const category = categories.find(c => c.id === categoryId)
      if (!category) return
      
      // Databases should list databases; others list their leaf entities
      const leafType = category.childLevels[category.childLevels.length - 1].entityType
      const endpoint = category.id === 'databases'
        ? 'databases'
        : (leafType.endsWith('s') ? leafType : `${leafType}s`)
      
      const params = {
        limit: FETCH_LIMIT,
        offset
      }
      
      const response = await listEntities(endpoint, params)
      total = response.paging?.total || 0
      
      const assets = response.data.map((entity: any) => ({
        ...entity,
        entityType: entity.entityType ?? (category.id === 'databases' ? 'database' : leafType),
        fullyQualifiedName: entity.fullyQualifiedName
      }))
      
      categoryEntities.set(categoryId, assets)
      categoryEntities = categoryEntities
    } catch (err) {
      console.error(`Failed to fetch ${categoryId}`, err)
    } finally {
      paginationLoading = false
    }
  }

  async function fetchAssetDetails (asset: AssetSearchHit, silent: boolean = false): Promise<void> {
    const requestId = ++detailsRequestId
    if (!silent) detailsLoading = true
    try {
      const type = asset.entityType
      const endpoint = buildDetailsEndpoint(type)
      const fields = [...commonFields, ...(extraFields[(type as string).toLowerCase()] ?? [])]

      const detail = await safeGetEntity<AssetDetail>(
        endpoint,
        { fqn: asset.fullyQualifiedName, id: asset.id },
        fields
      )

      // Ignore stale responses
      if (requestId !== detailsRequestId) return

      const merged: AssetDetail = { ...asset, ...detail }
      detailsCache.set(merged.fullyQualifiedName, merged)
      // Only update the UI for non-silent (user-initiated) requests
      if (!silent && selectedFqn === merged.fullyQualifiedName) {
        selectedAsset = merged
      }
    } catch (err) {
      console.error('Failed to fetch asset details', err)
    } finally {
      if (requestId === detailsRequestId && !silent) detailsLoading = false
    }
  }

  function prefetchAssetDetails(asset: AssetSearchHit): void {
    const fqn = asset.fullyQualifiedName
    if (!fqn || detailsCache.has(fqn)) return
    // fire-and-forget; do not toggle UI loading states
    void fetchAssetDetails(asset, true)
  }

  // Local reactive filtering (governance-like): no remote search
  $: allAssets = Array.from(categoryEntities.values()).flat()
  $: baseAssets = selectedCategory ? (categoryEntities.get(selectedCategory) ?? []) : allAssets
  $: filteredAssets = (() => {
    const trimmed = searchTerm.trim().toLowerCase()
    if (!trimmed) return baseAssets
    return baseAssets.filter((a) => {
      const name = (a.displayName ?? a.name ?? a.fullyQualifiedName ?? '').toLowerCase()
      const desc = (stripHtmlToText(a.description ?? '')).toLowerCase()
      return name.includes(trimmed) || desc.includes(trimmed)
    })
  })()
  
  /** Return readable label for asset, prefixed with service name if available */
  function assetLabel (asset: AssetSearchHit): string {
    const name = asset.displayName ?? asset.name ?? asset.fullyQualifiedName
    const serviceName = asset.service?.displayName ?? asset.service?.name
    return serviceName ? `${serviceName} / ${name}` : name
  }

  function toggleSort (key: SortableKey): void {
    if (sortKey === key) {
      sortAsc = !sortAsc
    } else {
      sortKey = key
      sortAsc = true
    }
  }

  function onRowFocus (index: number): void {
    rowSelection = index
    // Prefetch details for hovered row to improve perceived performance
    const entity = tableItemsSorted?.[index]?.entity as AssetSearchHit | undefined
    if (entity) prefetchAssetDetails(entity)
  }

  function onRowClick (asset: AssetSearchHit, index: number): void {
    rowSelection = index
    handleEntityClick(asset)
  }

  async function viewCategory(categoryId: string): Promise<void> {
    offset = 0
    currentPage = 1
    categoryEntities.clear()
    await loadPaginatedList(categoryId)
  }

  function handleCategoryClick(category: Category): void {
    selectedCategory = category.id
    viewCategory(category.id)
  }

  function handleBackToList (): void {
    selectedAsset = null
    selectedFqn = null
  }

  function handleEntityClick(asset: AssetSearchHit): void {
    selectedFqn = asset.fullyQualifiedName
    // Optimistically show basic info immediately
    const cached = detailsCache.get(selectedFqn)
    selectedAsset = cached ?? ({ ...asset } as AssetDetail)
    fetchAssetDetails(asset)
  }

  function handleOpenContained(event: CustomEvent): void {
    const asset = event.detail
    if (!asset) return
    handleEntityClick(asset)
  }

  function getCategoryLabel(categoryId: string): any {
    return categories.find(c => c.id === categoryId)?.label || ''
  }

  function goBack(): void {
    selectedAsset = null
    selectedFqn = null
    rowSelection = undefined
  }

  // ---------------------------------------------------------------------------
  // Generic helpers
  // ---------------------------------------------------------------------------

  const commonFields = ['tags', 'usageSummary', 'owners']
  const extraFields: Record<string, string[]> = {
    database: ['databaseSchemas'],
    table: ['columns', 'tableConstraints'],
    dashboard: ['charts'],
    topic: ['partitions'],
    mlmodel: ['algorithm', 'target', 'mlFeatures', 'mlHyperParameters', 'mlStore', 'server'],
    container: ['numberOfObjects', 'size'],
    metric: ['expression']
  }

  async function safeGetEntity<T> (
    endpoint: string,
    fqnOrId: { fqn?: string; id?: string },
    fields: string[]
  ): Promise<T> {
    try {
      return fqnOrId.fqn
        ? await getEntityByFqn<T>(endpoint, fqnOrId.fqn, fields)
        : await getEntityById<T>(endpoint, fqnOrId.id!, fields)
    } catch (err: any) {
      // Retry without fields on 4xx errors (invalid field etc.)
      const msg = String(err?.message ?? '')
      if (err?.code === 400 || err?.status === 400 || msg.includes('Invalid') || msg.includes('status: 400')) {
        return fqnOrId.fqn
          ? await getEntityByFqn<T>(endpoint, fqnOrId.fqn)
          : await getEntityById<T>(endpoint, fqnOrId.id!)
      }
      throw err
    }
  }

  interface AssetTableItem {
    displayName: string
    description: string
    serviceType: string
    entity: AssetSearchHit
  }

  function normalizeArrayField (field: any[] | undefined, fallbackField?: any[]): string[] {
    const sourceArray = (field && field.length ? field : (fallbackField ?? [])) as any[]
    return sourceArray
      .map((item: any) => {
        if (typeof item === 'string') return item
        return item?.displayName ?? item?.name ?? item?.tagFQN ?? item?.fullyQualifiedName ?? ''
      })
      .filter(Boolean) as string[]
  }

  function stripHtmlToText (html: string): string {
    if (!html) return ''
    const tmp = document.createElement('div')
    tmp.innerHTML = html
    return tmp.textContent || tmp.innerText || ''
  }

  $: tableItems = (filteredAssets || []).map((a): AssetTableItem => {
    const name = a?.displayName ?? a?.name ?? a?.fullyQualifiedName
    const serviceType = a?.serviceType ?? 'N/A'
    return {
      displayName: name ?? '',
      description: stripHtmlToText(a?.description ?? ''),
      serviceType,
      entity: a
    }
  })

  function compareItems (a: AssetTableItem, b: AssetTableItem): number {
    const av: any = a[sortKey]
    const bv: any = b[sortKey]
    const as = Array.isArray(av) ? av.join(', ') : String(av ?? '')
    const bs = Array.isArray(bv) ? bv.join(', ') : String(bv ?? '')
    return as.localeCompare(bs, undefined, { numeric: true })
  }

  $: tableItemsSorted = [...tableItems].sort((a, b) => (sortAsc ? compareItems(a, b) : compareItems(b, a)))
</script>

<style lang="scss">
  .data-assets-container {
    display: flex;
    flex-direction: column;
    min-height: 0;
    height: 100%;
    width: 100%;
  }
  .list-view-container {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .table-wrapper {
    width: 100%;
    flex: 1 1 auto;
    min-height: 0;
    overflow-x: auto;
    overflow-y: auto;
  }
  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-top: 1px solid var(--theme-popup-divider);
  }
  .controls {
    margin-bottom: 0.5rem;
    margin-left: 0.5rem;
    margin-top: 0.5rem;
  }
  .full-width-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    margin: 0;
    border-bottom: 1px solid var(--theme-divider-color, #e0e4e7);
    background-color: var(--theme-bg-color, #ffffff);
  }
  .page-title {
    font-size: 1rem;
    font-weight: 700;
    margin: 0;
    color: var(--theme-secondary-color);
  }
  .page-title-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
  }
  .right-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  .left-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    min-width: 120px;
  }
  .definition {
    max-width: 40ch;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .antiTable-cells__firstCell {
    max-width: 40ch;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .data-cell {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9rem;
  }
  .antiTable-body__row { cursor: pointer; }
  .loading{
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }
  .spacer { flex: 1; }
  .error-message {
    background-color: var(--theme-error-bg, #ffebee);
    color: var(--theme-error-color, #d32f2f);
    padding: 0.75rem 1rem;
    margin: 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid var(--theme-error-border, #ffcdd2);
    font-size: 0.9rem;
  }
</style>

<div class="data-assets-container">
  <div class="controls full-width-header">
    {#if selectedAsset}
      <Button kind="ghost" icon={IconBack} label={plugin.string.Back} on:click={goBack} />
      <div class="spacer"></div>
    {:else}
      <div class="left-controls">
        <div class="page-title-container">
          <Icon icon={IconObjects} size="small" />
          <h2 class="page-title"><Label label={toIntl('Data Assets')} /></h2>
        </div>
      </div>
      <div class="right-controls">
        <SearchInput bind:value={searchTerm} placeholder={intlSearchAssets} collapsed />
      </div>
    {/if}
  </div>

  {#if errorMessage}
    <div class="error-message">{errorMessage}</div>
  {/if}

  {#if assetsLoading || paginationLoading}
    <div class="loading"><Spinner size="large" /></div>
  {:else if !selectedAsset && (selectedCategory || searchTerm.trim())}
    <div class="table-wrapper">
      <table class="antiTable highlightRows">
        <thead class="scroller-thead">
          <tr class="scroller-thead__tr">
            <th class="sortable" class:sorted={sortKey === 'displayName'} on:click={() => toggleSort('displayName')}>
              <div class="antiTable-cells">
                <span><Label label={toIntl('Asset')} /></span>
                {#if sortKey === 'displayName'}
                  <div class="icon">{#if sortAsc}<IconUp size="small" />{:else}<IconDown size="small" />{/if}</div>
                {/if}
              </div>
            </th>
            <th><div class="antiTable-cells"><span><Label label={toIntl('Description')} /></span></div></th>
            <th><div class="antiTable-cells"><span><Label label={toIntl('Service Type')} /></span></div></th>
          </tr>
        </thead>
        <tbody>
          {#each tableItemsSorted as item, index}
            <tr 
              class="antiTable-body__row" 
              class:selected={index === rowSelection}
              on:click={() => onRowClick(item.entity, index)}
              on:mouseover={mouseAttractor(() => onRowFocus(index))}
              on:mouseenter={mouseAttractor(() => onRowFocus(index))}
              on:focus={() => {}}
            >
              <td><div class="antiTable-cells__firstCell">{item.displayName}</div></td>
              <td><div class="antiTable-cells definition">{item.description}</div></td>
              <td><div class="antiTable-cells data-cell">{item.serviceType}</div></td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {:else if selectedAsset}
    <AssetsView asset={selectedAsset} embedded={true} loading={detailsLoading} on:openAsset={handleOpenContained} />
  {/if}
</div>