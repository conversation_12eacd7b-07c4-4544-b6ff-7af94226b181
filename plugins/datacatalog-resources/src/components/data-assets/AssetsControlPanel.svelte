<script lang="ts">
  import { DatePresenter } from '@hcengineering/ui'
  import { DateRangeMode } from '@hcengineering/core'
  export let asset: any
  export let readonly: boolean = false

  // Helper to show dash for empty
  const show = (v: any): string => {
    if (v === undefined || v === null) return 'N/A'
    if (Array.isArray(v)) return v.filter(Boolean).join(', ') || 'N/A'
    return String(v)
  }

  $: service = asset?.service?.displayName ?? asset?.service?.name
  $: database = asset?.database?.displayName ?? asset?.database?.name
  $: schema = asset?.databaseSchema?.displayName ?? asset?.databaseSchema?.name
</script>

<div class="popupPanel-body__aside-grid" style="margin-top:0">
    <span class="labelOnPanel">Name</span>
    <div class="value-display">{asset?.name ?? 'N/A'}</div>

    <span class="labelOnPanel">Service Type</span>
    <div class="value-display">{asset?.serviceType ?? 'N/A'}</div>

    <span class="labelOnPanel">Service</span>
    <div class="value-display">{asset?.service?.name}</div>

    <span class="labelOnPanel">Owner</span>
    <div class="value-display">{asset?.updatedBy ?? 'N/A'}</div>

    <span class="labelOnPanel">Version</span>
    <div class="value-display">{asset?.version ?? 'N/A'}</div>

    <span class="labelOnPanel">Updated At</span>
    <div class="value-display">
      {#if asset?.updatedAt}
        <DatePresenter value={Number(asset.updatedAt)} mode={DateRangeMode.DATETIME} kind="list" />
      {:else}
        N/A
      {/if}
    </div>

</div>

<style>
    .labelOnPanel {
      font-size: 0.8rem;
      font-weight: 500;
      color: var(--theme-content-color);
      margin-bottom: 0.25rem;
    }
  
    .divider {
      height: 1px;
      background: var(--theme-divider-color);
      margin: 1rem 0;
      grid-column: span 2;
    }
  
    .value-display,
    .size-display,
    .date-display {
      padding: 0.5rem;
      border-radius: 0.25rem;
      color: var(--theme-content-dark-color);
      font-size: 0.875rem;
    }
  
  .popupPanel-body__aside-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    column-gap: 0.75rem;
    row-gap: 0.5rem;
    padding: 1rem;
  }
  </style> 


