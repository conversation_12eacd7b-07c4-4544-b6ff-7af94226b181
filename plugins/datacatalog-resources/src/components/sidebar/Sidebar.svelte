<script lang="ts">
    import { <PERSON><PERSON><PERSON>, Label } from '@hcengineering/ui'
    import { Nav<PERSON><PERSON><PERSON>, NavHeader, SpecialElement } from '@hcengineering/workbench-resources'
    import { TreeNode } from '@hcengineering/view-resources'
    import datacatalog from '../../plugin'
    import { sidebarItems } from '../../constants'
    import { onMount } from 'svelte'
    import { listDomains, type DomainEntity } from '../../openmetadata/domains'
    import SidebarItemIcon from './SidebarItemIcon.svelte'

  export let selectedApplication: string | undefined = undefined
  export let selectedCategory: string | undefined = undefined
  export let selectedDomain: string | undefined = undefined
  export let collapsedCategories: Map<string, boolean> = new Map()

  let domains: DomainEntity[] = []
  let domainsLoading = true
  let domainsError: string | null = null

  // Load domains on component mount
  onMount(async () => {
    try {
      const response = await listDomains({ limit: 50 })
      domains = response.data || []
    } catch (error) {
      console.error('Failed to load domains:', error)
      domainsError = error instanceof Error ? error.message : 'Failed to load domains'
    } finally {
      domainsLoading = false
    }
  })

  // Consolidated navigation handler
  function handleNavigate(
    type: 'nav' | 'sub' | 'category' | 'domain',
    item: any,
    parent?: any
  ): void {
    selectedCategory = undefined
    selectedDomain = undefined

    switch (type) {
      case 'nav':
        if (item.defaultSubItem) {
          selectedApplication = `${item.path}/${item.defaultSubItem}`
        } else {
          selectedApplication = item.path
          if (item.categoryChildren) {
            selectedCategory = selectedCategory // Keep current if has categories
          }
          if (item.path === 'domains') {
            selectedDomain = selectedDomain // Keep current for domains
          }
        }
        break
      case 'sub':
        selectedApplication = item.path
        break
      case 'category':
        selectedApplication = parent.path
        selectedCategory = item.id
        break
      case 'domain':
        selectedApplication = 'domains/detail'
        selectedDomain = item.id
        break
    }
  }

  // Keyboard navigation handler
  function handleKeyDown(event: KeyboardEvent, handler: () => void): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      handler()
    }
  }

  $: isTreeHighlighted = (item: any): boolean => {
    if (!selectedApplication) return false

    return (
      selectedApplication === item.path ||
      selectedApplication.startsWith(item.path + '/') ||
      (item.path === 'domains' && selectedApplication === 'domains/detail')
    )
  }

  $: isTreeOpen = (item: any): boolean => {
    if (!selectedApplication) return false
    if (item.subItems) {
      return isDropdownSelected(item)
    }
    if (item.categoryChildren) {
      return selectedApplication === item.path
    }
    if (item.path === 'domains') {
      return selectedApplication === 'domains' || selectedApplication === 'domains/detail'
    }
    return selectedApplication === item.path
  }

  $: isDropdownSelected = (item: any): boolean => {
    if (!selectedApplication) return false
    return selectedApplication.startsWith(item.path + '/') || selectedApplication === item.path
  }

  $: isSubItemSelected = (subItem: any): boolean => {
    return selectedApplication === subItem.path
  }

  $: isCategorySelected = (parent: any, category: any): boolean => {
    return selectedApplication === parent.path && selectedCategory === category.id
  }

  $: isDomainSelected = (domain: DomainEntity): boolean => {
    return selectedDomain === domain.id
  }

  $: console.log('collapsedCategories', collapsedCategories)
</script>

<NavHeader label={datacatalog.string.ApplicationTitle} />

<div class="sidebar-scroller-wrapper">
  <Scroller shrink>
    {#each sidebarItems as sidebarItem (sidebarItem.id)}
      <TreeNode
        _id={sidebarItem.id}
        icon={SidebarItemIcon}
        iconProps={{ icon: sidebarItem.icon, collapsed: collapsedCategories.get(sidebarItem.id) }}
        label={sidebarItem.title}
        type={'nested'}
        highlighted={isTreeHighlighted(sidebarItem)}
        visible={isTreeOpen(sidebarItem)}
        on:toggle={(ev) => {
          if (!sidebarItem.subItems && !sidebarItem.categoryChildren && sidebarItem.path !== 'domains') {
            handleNavigate('nav', sidebarItem)
          }

          collapsedCategories.set(sidebarItem.id, ev.detail.collapsed)
          collapsedCategories = new Map(collapsedCategories)
        }}
      >
        {#if sidebarItem.subItems}
          {#each sidebarItem.subItems as subItem (subItem.id)}
            <div 
              role="button"
              tabindex="0"
              aria-label="Navigate to {subItem.title}"
              on:click={() => handleNavigate('sub', subItem)}
              on:keydown={(e) => handleKeyDown(e, () => handleNavigate('sub', subItem))}
            >
              <SpecialElement 
                indent 
                label={subItem.title} 
                selected={isSubItemSelected(subItem)} 
              />
            </div>
          {/each}
        {:else if sidebarItem.categoryChildren}
          {#each sidebarItem.categoryChildren as category (category.id)}
            <div 
              role="button"
              tabindex="0"
              aria-label="View {category.label} category"
              on:click={() => handleNavigate('category', category, sidebarItem)}
              on:keydown={(e) => handleKeyDown(e, () => handleNavigate('category', category, sidebarItem))}
            >
              <SpecialElement 
                indent 
                label={category.label} 
                selected={isCategorySelected(sidebarItem, category)} 
              />
            </div>
          {/each}
        {:else if sidebarItem.path === 'domains'}
          {#if domainsLoading}
            <div class="loading-item" role="status" aria-live="polite">
              <Label label={datacatalog.string.LoadingDomains} />
            </div>
          {:else if domainsError}
            <div class="error-item" role="alert" aria-live="assertive">
              <span class="error-text">Error loading domains</span>
            </div>
          {:else if domains.length === 0}
            <div class="no-domains-item" role="status">
              <Label label={datacatalog.string.NoDomains} />
            </div>
          {:else}
            {#each domains as domain (domain.id)}
              <div 
                role="button"
                tabindex="0"
                aria-label="View domain {domain.displayName || domain.name}"
                on:click={() => handleNavigate('domain', domain)}
                on:keydown={(e) => handleKeyDown(e, () => handleNavigate('domain', domain))}
              >
                <SpecialElement 
                  indent 
                  nonIntlLabel={domain.displayName || domain.name}
                  selected={isDomainSelected(domain)} 
                />
              </div>
            {/each}
          {/if}
        {:else}
          <div 
            role="button"
            tabindex="0"
            aria-label="Navigate to {sidebarItem.title}"
            on:click={() => handleNavigate('nav', sidebarItem)}
            on:keydown={(e) => handleKeyDown(e, () => handleNavigate('nav', sidebarItem))}
          >
            <SpecialElement 
              indent 
              label={sidebarItem.title} 
              selected={selectedApplication === sidebarItem.path} 
            />
          </div>
        {/if}
      </TreeNode>
    {/each}
  </Scroller>
</div>
<NavFooter />

<style>
  .sidebar-scroller-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-width: 0;
    min-height: 0;
  }

  .error-text {
    color: var(--theme-danger-text, #ff6b6b);
  }

  [role="button"] {
    cursor: pointer;
  }

  [role="button"]:focus {
    outline: 2px solid var(--theme-primary-color, #007bff);
    outline-offset: 2px;
  }

  [role="button"]:focus:not(:focus-visible) {
    outline: none;
  }

  .loading-item,
  .error-item,
  .no-domains-item {
    padding: 8px 16px;
  }

  :global(.sidebar-scroller-wrapper .hulyNavItem-container) {
    width: 100% !important;
  }

  :global(.sidebar-scroller-wrapper .hulyNavGroup-header__label) {
    margin-left: 16px;
  }
</style>
