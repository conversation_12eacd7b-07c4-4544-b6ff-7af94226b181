<script lang="ts">
  import { Icon, IconDown, type AnySvelteComponent } from '@hcengineering/ui'

  export let icon: AnySvelteComponent
  export let collapsed: boolean

  console.log('collapsed', collapsed)
</script>

<div class="wrapper">
    <button class="hulyNavGroup-header__chevron" class:collapsed={collapsed}>
        <IconDown size={'small'} />
    </button>
    <Icon icon={icon} size={'small'} />
</div>

<style>
    .wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        margin-left: -28px;
    }
</style>