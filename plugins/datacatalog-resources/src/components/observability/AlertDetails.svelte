<script lang="ts">
  import { Label, Section } from '@hcengineering/ui'
  import { getEmbeddedLabel } from '@hcengineering/platform'
  import type { AlertEntity } from '../../openmetadata/observability'

  export let alert: AlertEntity

  const intlDetails: any = getEmbeddedLabel('Details')
  const intlConfiguration: any = getEmbeddedLabel('Configuration')
  const intlDestinations: any = getEmbeddedLabel('Destinations')

  $: updatedAtDisplay = alert?.updatedAt != null
    ? (typeof alert.updatedAt === 'number' ? new Date(alert.updatedAt).toLocaleString() : String(alert.updatedAt))
    : '—'
</script>

<style lang="scss">
  .details-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0.75rem;
  }
  .kv-row {
    display: grid;
    grid-template-columns: 180px 1fr;
    gap: 0.5rem;
    padding: 0.25rem 0;
  }
  .mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  }
  .pill {
    display: inline-block;
    border: 1px solid var(--theme-divider-color);
    border-radius: 999px;
    padding: 0.1rem 0.5rem;
    margin-right: 0.25rem;
  }
  .code-block {
    font-size: 12px;
    border: 1px solid var(--theme-divider-color);
    border-radius: 6px;
    padding: 0.5rem;
    overflow-x: auto;
    background: var(--theme-bg-color);
  }
  .muted {
    color: var(--theme-content-trans-color);
  }
  .row {
    margin-bottom: 0.25rem;
  }
</style>

<div class="details-container">
  <Section label={intlDetails}>
    <svelte:fragment slot="content">
      <div class="kv-row"><div class="muted">ID</div><div class="mono">{alert.id || '—'}</div></div>
      <div class="kv-row"><div class="muted">Name</div><div>{alert.displayName || alert.name || '—'}</div></div>
      <div class="kv-row"><div class="muted">FQN</div><div class="mono">{alert.fullyQualifiedName || '—'}</div></div>
      <div class="kv-row"><div class="muted">Type</div><div>{alert.alertType || '—'}</div></div>
      <div class="kv-row"><div class="muted">Provider</div><div>{alert.provider || '—'}</div></div>
      <div class="kv-row"><div class="muted">Status</div><div>{alert.enabled ? 'Enabled' : 'Disabled'}</div></div>
      <div class="kv-row"><div class="muted">Updated</div><div>{updatedAtDisplay} by {alert.updatedBy ?? '—'}</div></div>
      <div class="kv-row"><div class="muted">Batch Size</div><div>{alert.batchSize ?? '—'}</div></div>
      <div class="kv-row"><div class="muted">Retries</div><div>{alert.retries ?? '—'}</div></div>
      <div class="kv-row"><div class="muted">Poll Interval</div><div>{alert.pollInterval ?? '—'}</div></div>
      {#if alert.description}
        <div class="row"><Label label={getEmbeddedLabel('Description')} /></div>
        <div class="row">{alert.description}</div>
      {/if}
    </svelte:fragment>
  </Section>

  <Section label={intlConfiguration}>
    <svelte:fragment slot="content">
      {#if alert.filteringRules}
        <div class="row"><strong>Filtering Rules</strong></div>
        <div class="code-block mono">{JSON.stringify(alert.filteringRules, null, 2)}</div>
      {/if}
      {#if alert.input}
        <div class="row"><strong>Input</strong></div>
        <div class="code-block mono">{JSON.stringify(alert.input, null, 2)}</div>
      {/if}
    </svelte:fragment>
  </Section>

  {#if (alert.destinations && alert.destinations.length > 0)}
    <Section label={intlDestinations}>
      <svelte:fragment slot="content">
        {#each alert.destinations as d}
          <div class="kv-row">
            <div class="muted">{d.category}</div>
            <div>
              <span class="pill">{d.type}</span>
              <span class="pill">{d.enabled ? 'Enabled' : 'Disabled'}</span>
            </div>
          </div>
        {/each}
      </svelte:fragment>
    </Section>
  {/if}
</div>


