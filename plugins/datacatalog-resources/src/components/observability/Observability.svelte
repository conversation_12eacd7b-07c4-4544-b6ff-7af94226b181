<script lang="ts">
  import { onMount } from 'svelte'
  import {
    <PERSON><PERSON><PERSON>,
    TabsControl,
    type TabBase,
    Label,
    Button,
    Spinner,
    EditBox,
    Section,
    IconBack,
    IconCalendar,
    ButtonIcon
  } from '@hcengineering/ui'
  import { getEmbeddedLabel } from '@hcengineering/platform'
  import { observability as omObs } from '../../openmetadata'
  import AlertDetails from './AlertDetails.svelte'
  import datacatalog from '../../plugin'
  import DatacatalogItemHeader from '../DatacatalogItemHeader.svelte'
  import { IconActivity } from '@hcengineering/ui'

  type Paginated<T> = omObs.Paginated<T>

  export let selectedCategory: string | undefined = undefined

  const tabDefs = [
    { key: 'quality' as const, label: getEmbeddedLabel('Data Quality') },
    { key: 'incidents' as const, label: getEmbeddedLabel('Incident Manager') },
    { key: 'alerts' as const, label: getEmbeddedLabel('Alerts') }
  ]
  const model: TabBase[] = tabDefs.map((t) => ({ label: t.label }))
  let selected = 0

  // Search and paging
  let searchTerm = ''
  let limit = 20
  let offset = 0
  let total = 0
  let loading = false

  // Data sets
  let testSuites: omObs.TestSuiteEntity[] = []
  let testCases: omObs.TestCaseEntity[] = []
  let testDefinitions: omObs.TestDefinitionEntity[] = []
  let incidents: omObs.IncidentEntity[] = []
  let alerts: omObs.AlertEntity[] = []

  // Data Quality summaries
  let dqEntitiesWithTests: number | null = null
  let dqEntitiesWithFailedOrAborted: number | null = null
  let dqTablesTotal: number | null = null
  let dqTablesCovered: number | null = null
  let dqTablesNotCovered: number | null = null
  let dqHealthyEntities: number | null = null
  let dqUnhealthyEntities: number | null = null
  let dqStatusSuccess: number | null = null
  let dqStatusFailed: number | null = null
  let dqStatusAborted: number | null = null
  let dqDimensions: Record<string, { success: number; failed: number; aborted: number; none: number }> = {}
  let dqOpenIncidents: number | null = null
  let dqResolvedIncidents: number | null = null
  let dqAvgResponseTimeMs: number | null = null
  let dqAvgResolutionTimeMs: number | null = null
  let dqTestSuitesTotal: number | null = null
  let dqTestCasesTotal: number | null = null

  // Date range (last 31 days)
  const ONE_DAY = 24 * 60 * 60 * 1000
  let rangeEnd = Date.now()
  let rangeStart = rangeEnd - 30 * ONE_DAY
  function formatRange(a: number, b: number): string {
    const fmt = new Intl.DateTimeFormat(undefined, { month: 'short', day: '2-digit', year: 'numeric' })
    return `${fmt.format(a)} - ${fmt.format(b)}`
  }
  function formatDuration(ms: number | null): string {
    if (ms == null || !Number.isFinite(ms)) return '—'
    const totalSeconds = Math.max(0, Math.round(ms / 1000))
    const days = Math.floor(totalSeconds / 86400)
    const hours = Math.floor((totalSeconds % 86400) / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    if (days > 0) return `${days}d ${hours}h`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  // Selection state (details view)
  let selectedAlert: omObs.AlertEntity | null = null

  const intlObservability: any = getEmbeddedLabel('Observability')
  const intlSearch: any = getEmbeddedLabel('Search…')
  const intlNoData: any = getEmbeddedLabel('No data')
  const intlNextPage: any = getEmbeddedLabel('Next Page')
  const intlPrevPage: any = getEmbeddedLabel('Previous Page')
  const intlBack: any = getEmbeddedLabel('Back')
  const intlName: any = getEmbeddedLabel('Name')
  const intlFQN: any = getEmbeddedLabel('FQN')
  const intlDescription: any = getEmbeddedLabel('Description')
  const intlStatus: any = getEmbeddedLabel('Status')
  const intlSeverity: any = getEmbeddedLabel('Severity')
  const intlAssignees: any = getEmbeddedLabel('Assignees')
  const intlCreatedAt: any = getEmbeddedLabel('Created At')
  const intlType: any = getEmbeddedLabel('Type')
  const intlProvider: any = getEmbeddedLabel('Provider')
  const intlEnabled: any = getEmbeddedLabel('Enabled')
  const intlEntitiesWithTests: any = getEmbeddedLabel('Entities with Tests')
  const intlEntitiesFailedAborted: any = getEmbeddedLabel('Entities with Failed/Aborted')
  const intlSummary: any = getEmbeddedLabel('Summary')
  const intlTestCases: any = getEmbeddedLabel('Test Cases')
  const intlTestSuitesLbl: any = getEmbeddedLabel('Test Suites')
  const intlDataHealth: any = getEmbeddedLabel('Data Health')
  const intlDataAssetsCoverage: any = getEmbeddedLabel('Data Assets Coverage')
  const intlTables: any = getEmbeddedLabel('Tables')
  const intlCovered: any = getEmbeddedLabel('Covered')
  const intlNotCovered: any = getEmbeddedLabel('Not Covered')
  const intlHealthyDataAssets: any = getEmbeddedLabel('Healthy Data Assets')
  const intlEntities: any = getEmbeddedLabel('Entities')
  const intlHealthy: any = getEmbeddedLabel('Healthy')
  const intlUnhealthy: any = getEmbeddedLabel('Unhealthy')
  const intlTestCaseResults: any = getEmbeddedLabel('Test Case Results')
  const intlTests: any = getEmbeddedLabel('Tests')
  const intlSuccess: any = getEmbeddedLabel('Success')
  const intlFailed: any = getEmbeddedLabel('Failed')
  const intlAborted: any = getEmbeddedLabel('Aborted')
  const intlDataDimensions: any = getEmbeddedLabel('Data Dimensions')
  const intlNoDimension: any = getEmbeddedLabel('No Dimension')
  const intlTestCaseStatus: any = getEmbeddedLabel('Test Case Status')
  const intlIncidentMetrics: any = getEmbeddedLabel('Incident Metrics')
  const intlOpenIncidents: any = getEmbeddedLabel('Open Incidents')
  const intlResolvedIncidents: any = getEmbeddedLabel('Resolved Incidents')
  const intlResponseTime: any = getEmbeddedLabel('Response Time')
  const intlResolutionTime: any = getEmbeddedLabel('Resolution Time')

  $: if (selected >= 0) {
    offset = 0
    loadData()
  }

  // Allow external control of selected tab via prop, e.g. 'alerts'
  $: if (selectedCategory) {
    const idx = tabDefs.findIndex((t) => t.key === selectedCategory)
    if (idx >= 0 && idx !== selected) {
      selected = idx
    }
  }

  async function loadData(): Promise<void> {
    loading = true
    try {
      total = 0
      let params: omObs.ListParams = { limit, offset }

      const activeKey = tabDefs[selected]?.key
      if (activeKey === 'quality') {
        const [suites, cases, defs] = await Promise.all([
          omObs.listTestSuites(params),
          omObs.listTestCases(params),
          omObs.listTestDefinitions(params)
        ])
        testSuites = suites.data ?? []
        testCases = cases.data ?? []
        testDefinitions = defs.data ?? []
        total = Math.max(
          suites.paging?.total ?? 0,
          cases.paging?.total ?? 0,
          defs.paging?.total ?? 0
        )
        dqTestSuitesTotal = suites.paging?.total ?? null
        dqTestCasesTotal = cases.paging?.total ?? null
        await loadDataQualitySummaries()
      } else if (activeKey === 'incidents') {
        const res = await omObs.listIncidents(params)
        incidents = res.data ?? []
        total = res.paging?.total ?? 0
      } else if (activeKey === 'alerts') {
        // Fetch full objects to avoid server-side field filtering that may hide top-level fields
        const res = await omObs.listAlerts(params)
        alerts = res.data ?? []
        total = res.paging?.total ?? 0
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Observability load error', err)
    } finally {
      loading = false
    }
  }

  // Data Quality report aggregations for dashboard metrics
  async function loadDataQualitySummaries(): Promise<void> {
    dqEntitiesWithTests = dqEntitiesWithFailedOrAborted = null
    dqTablesTotal = dqTablesCovered = dqTablesNotCovered = null
    dqHealthyEntities = dqUnhealthyEntities = null
    dqStatusSuccess = dqStatusFailed = dqStatusAborted = null
    dqDimensions = {}
    dqOpenIncidents = dqResolvedIncidents = null
    dqAvgResponseTimeMs = dqAvgResolutionTimeMs = null

    const baseMust = [{ term: { deleted: false } }]
    const withinRange = (extraMust: any[] = []) => ({
      query: { bool: { must: [ ...extraMust, { range: { timestamp: { gte: rangeStart, lte: rangeEnd } } } ] } }
    })

    const sumNumericRows = (r: omObs.DataQualityReportResponse | null | undefined, preferredKeys: string[] = []): number => {
      if (!r || !Array.isArray(r.data)) return 0
      let sum = 0
      for (const row of r.data as Array<Record<string, any>>) {
        let v: any = undefined
        for (const k of preferredKeys) { if ((row as any)[k] != null) { v = (row as any)[k]; break } }
        if (v == null) {
          for (const val of Object.values(row)) {
            const n = typeof val === 'string' ? parseFloat(val) : Number(val)
            if (Number.isFinite(n)) { v = n; break }
          }
        }
        const n = typeof v === 'string' ? parseFloat(v) : Number(v)
        if (Number.isFinite(n)) sum += n
      }
      return sum
    }

    const parseStatusCounts = (r: omObs.DataQualityReportResponse | null | undefined) => {
      const acc = { Success: 0, Failed: 0, Aborted: 0 }
      if (!r || !Array.isArray(r.data)) return acc
      for (const row of r.data as Array<Record<string, any>>) {
        const statusVal = Object.values(row).find((v) => typeof v === 'string' && (v === 'Success' || v === 'Failed' || v === 'Aborted')) as string | undefined
        let num = 0
        for (const v of Object.values(row)) {
          const n = typeof v === 'string' ? parseFloat(v) : Number(v)
          if (Number.isFinite(n)) { num = n; break }
        }
        if (statusVal) { acc[statusVal as 'Success' | 'Failed' | 'Aborted'] += num }
      }
      return acc
    }

    const parseDimensionStatus = (r: omObs.DataQualityReportResponse | null | undefined, fallbackDim: string | null = null) => {
      if (!r || !Array.isArray(r.data)) return
      for (const row of r.data as Array<Record<string, any>>) {
        const dims = ['Accuracy','Completeness','Consistency','Integrity','SQL','Uniqueness','Validity']
        let dimension: string | null = null
        for (const v of Object.values(row)) {
          if (typeof v === 'string' && dims.includes(v)) { dimension = v; break }
        }
        if (!dimension) dimension = fallbackDim
        const statusVal = Object.values(row).find((v) => typeof v === 'string' && (v === 'Success' || v === 'Failed' || v === 'Aborted')) as string | undefined
        let num = 0
        for (const v of Object.values(row)) {
          const n = typeof v === 'string' ? parseFloat(v) : Number(v)
          if (Number.isFinite(n)) { num = n; break }
        }
        const key = dimension ?? intlNoDimension
        if (!dqDimensions[key]) dqDimensions[key] = { success: 0, failed: 0, aborted: 0, none: 0 }
        if (!statusVal) dqDimensions[key].none += num
        else if (statusVal === 'Success') dqDimensions[key].success += num
        else if (statusVal === 'Failed') dqDimensions[key].failed += num
        else if (statusVal === 'Aborted') dqDimensions[key].aborted += num
      }
    }

    try {
      const [
        tablesTotalRes,
        allEntitiesRes,
        failedOrAbortedRes,
        statusTermsRes,
        dimensionRes,
        noDimensionRes,
        openIncidentsRes,
        resolvedIncidentsRes,
        respTimeRes,
        resolTimeRes
      ] = await Promise.all([
        omObs.dataQualityReport({ index: 'table', q: { query: { bool: { must: baseMust } } }, aggregationQuery: 'bucketName=count:aggType=cardinality:field=fullyQualifiedName' }),
        omObs.dataQualityReport({ index: 'testCase', q: { query: { bool: { must: baseMust } } }, aggregationQuery: 'bucketName=entityWithTests:aggType=cardinality:field=originEntityFQN' }),
        omObs.dataQualityReport({ index: 'testCase', q: { query: { bool: { must: [ { terms: { 'testCaseStatus.keyword': ['Failed','Aborted'] } }, ...baseMust ] } } }, aggregationQuery: 'bucketName=entityWithTests:aggType=cardinality:field=originEntityFQN' }),
        omObs.dataQualityReport({ index: 'testCase', q: { query: { bool: { must: baseMust } } }, aggregationQuery: 'bucketName=status:aggType=terms:field=testCaseResult.testCaseStatus' }),
        omObs.dataQualityReport({ index: 'testCase', q: { query: { bool: { must: baseMust } } }, aggregationQuery: 'bucketName=dimension:aggType=terms:field=dataQualityDimension,bucketName=status:aggType=terms:field=testCaseResult.testCaseStatus' }),
        omObs.dataQualityReport({ index: 'testCase', q: { query: { bool: { must: [], must_not: [ { exists: { field: 'dataQualityDimension' } } ] } } }, aggregationQuery: 'bucketName=status:aggType=terms:field=testCaseResult.testCaseStatus' }),
        omObs.dataQualityReport({ index: 'testCaseResolutionStatus', q: withinRange([{ term: { testCaseResolutionStatusType: 'New' } }]), aggregationQuery: 'bucketName=byDay:aggType=date_histogram:field=timestamp&calendar_interval=day,bucketName=newIncidents:aggType=cardinality:field=stateId' }),
        omObs.dataQualityReport({ index: 'testCaseResolutionStatus', q: withinRange([{ term: { testCaseResolutionStatusType: 'Resolved' } }]), aggregationQuery: 'bucketName=byDay:aggType=date_histogram:field=timestamp&calendar_interval=day,bucketName=newIncidents:aggType=cardinality:field=stateId' }),
        omObs.dataQualityReport({ index: 'testCaseResolutionStatus', q: withinRange([{ nested: { path: 'metrics', query: { bool: { must: [ { match: { 'metrics.name.keyword': 'timeToResponse' } } ] } } } }]), aggregationQuery: 'bucketName=byDay:aggType=date_histogram:field=timestamp&calendar_interval=day,bucketName=metrics:aggType=nested:path=metrics,bucketName=byName:aggType=terms:field=metrics.name.keyword,bucketName=avgValue:aggType=avg:field=metrics.value' }),
        omObs.dataQualityReport({ index: 'testCaseResolutionStatus', q: withinRange([{ nested: { path: 'metrics', query: { bool: { must: [ { match: { 'metrics.name.keyword': 'timeToResolution' } } ] } } } }]), aggregationQuery: 'bucketName=byDay:aggType=date_histogram:field=timestamp&calendar_interval=day,bucketName=metrics:aggType=nested:path=metrics,bucketName=byName:aggType=terms:field=metrics.name.keyword,bucketName=avgValue:aggType=avg:field=metrics.value' })
      ])

      dqTablesTotal = sumNumericRows(tablesTotalRes, ['count','fullyQualifiedName'])
      dqEntitiesWithTests = sumNumericRows(allEntitiesRes, ['originEntityFQN'])
      dqTablesCovered = dqEntitiesWithTests
      dqTablesNotCovered = Math.max(0, (dqTablesTotal ?? 0) - (dqTablesCovered ?? 0))

      dqEntitiesWithFailedOrAborted = sumNumericRows(failedOrAbortedRes, ['originEntityFQN'])
      dqHealthyEntities = Math.max(0, (dqEntitiesWithTests ?? 0) - (dqEntitiesWithFailedOrAborted ?? 0))
      dqUnhealthyEntities = dqEntitiesWithFailedOrAborted

      const sc = parseStatusCounts(statusTermsRes)
      dqStatusSuccess = sc.Success
      dqStatusFailed = sc.Failed
      dqStatusAborted = sc.Aborted
      dqTestCasesTotal = (dqStatusSuccess ?? 0) + (dqStatusFailed ?? 0) + (dqStatusAborted ?? 0)

      parseDimensionStatus(dimensionRes)
      parseDimensionStatus(noDimensionRes, intlNoDimension)

      dqOpenIncidents = sumNumericRows(openIncidentsRes, ['newIncidents'])
      dqResolvedIncidents = sumNumericRows(resolvedIncidentsRes, ['newIncidents'])

      const avgFromNested = (r: omObs.DataQualityReportResponse | null | undefined): number | null => {
        if (!r || !Array.isArray(r.data) || r.data.length === 0) return null
        let sum = 0
        let count = 0
        for (const row of r.data as Array<Record<string, any>>) {
          const v = (row as any)['avgValue'] ?? (row as any)['metrics.value'] ?? (row as any)['value']
          const n = typeof v === 'string' ? parseFloat(v) : Number(v)
          if (Number.isFinite(n)) { sum += n; count++ }
        }
        return count > 0 ? sum / count : null
      }
      dqAvgResponseTimeMs = avgFromNested(respTimeRes)
      dqAvgResolutionTimeMs = avgFromNested(resolTimeRes)
    } catch (e) {
      dqTablesTotal = dqTablesCovered = dqTablesNotCovered = 0
      dqEntitiesWithTests = dqEntitiesWithFailedOrAborted = 0
      dqHealthyEntities = dqUnhealthyEntities = 0
      dqStatusSuccess = dqStatusFailed = dqStatusAborted = 0
      dqDimensions = {}
      dqOpenIncidents = dqResolvedIncidents = 0
      dqAvgResponseTimeMs = dqAvgResolutionTimeMs = null
      dqTestCasesTotal = 0
    }
  }

  function nextPage(): void {
    if (offset + limit < total) {
      offset += limit
      loadData()
    }
  }
  function prevPage(): void {
    if (offset > 0) {
      offset = Math.max(0, offset - limit)
      loadData()
    }
  }

  // Debounced search (placeholder for when search endpoints are wired)
  let searchHandle: number | null = null
  function onSearchInput(): void {
    if (searchHandle) window.clearTimeout(searchHandle)
    searchHandle = window.setTimeout(() => {
      offset = 0
      loadData()
    }, 400)
  }

  onMount(loadData)

  async function openAlertDetails(item: omObs.AlertEntity): Promise<void> {
    try {
      // Try to refetch details for freshest data
      const id = item.id
      if (id) {
        const detailed = await omObs.getAlertById(id)
        selectedAlert = { ...item, ...detailed }
      } else {
        selectedAlert = item
      }
    } catch (e) {
      selectedAlert = item
    }
  }

  function closeDetails(): void {
    selectedAlert = null
  }
</script>

<div class="observability-container">
    <DatacatalogItemHeader 
      icon={IconActivity} 
      label={datacatalog.string.Observability} 
      bind:value={searchTerm} 
      placeholder={intlSearch} 
      {onSearchInput} 
    />

  <div class="nav-container">
    <TabsControl {model} bind:selected size="small" />
  </div>

  <div class="list-container">
    {#if loading}
      <div class="loading-wrapper">
        <Spinner size="large" />
      </div>
    {:else if selectedAlert}
      <div class="controls" style="padding: 0.5rem 0.75rem; display: flex; gap: 0.5rem; align-items: center;">
        <Button kind="ghost" icon={IconBack} on:click={closeDetails}>{intlBack}</Button>
      </div>
      <Scroller>
        <AlertDetails alert={selectedAlert} />
      </Scroller>
    {:else}
      {#if tabDefs[selected]?.key === 'quality'}
        <Scroller>
          <div class="summary-chips">
            <div class="chip"><IconCalendar size="small" /> <span>{formatRange(rangeStart, rangeEnd)}</span></div>
          </div>
          <div class="summary-chips">
            <div class="chip"><Label label={intlSummary} /></div>
            <div class="chip"><Label label={intlTestCases} />: <span class="value">{dqTestCasesTotal ?? '—'}</span></div>
            <div class="chip"><Label label={intlTestSuitesLbl} />: <span class="value">{dqTestSuitesTotal ?? '—'}</span></div>
          </div>
          <div class="summary-chips">
            <div class="chip"><Label label={intlDataAssetsCoverage} />: <span class="value">{dqTablesCovered ?? '—'}</span> / {dqTablesTotal ?? '—'} <Label label={intlTables} /></div>
            <div class="chip"><Label label={intlHealthyDataAssets} />: <span class="value">{dqHealthyEntities ?? '—'}</span> / {dqEntitiesWithTests ?? '—'} <Label label={intlEntities} /></div>
          </div>
          <div class="summary-chips">
            <div class="chip"><Label label={intlTestCaseResults} />: <Label label={intlSuccess} /> <span class="value">{dqStatusSuccess ?? '0'}</span> • <Label label={intlFailed} /> <span class="value">{dqStatusFailed ?? '0'}</span> • <Label label={intlAborted} /> <span class="value">{dqStatusAborted ?? '0'}</span></div>
          </div>
          <div class="summary-chips">
            <div class="chip"><Label label={intlIncidentMetrics} />: <Label label={intlOpenIncidents} /> <span class="value">{dqOpenIncidents ?? '0'}</span> • <Label label={intlResolvedIncidents} /> <span class="value">{dqResolvedIncidents ?? '0'}</span> • <Label label={intlResponseTime} /> <span class="value">{formatDuration(dqAvgResponseTimeMs)}</span> • <Label label={intlResolutionTime} /> <span class="value">{formatDuration(dqAvgResolutionTimeMs)}</span></div>
          </div>

          <div class="content-container">
          <Section label={intlTestSuitesLbl}>
            <svelte:fragment slot="content">
              {#if testSuites.length === 0}
                <Label label={intlNoData} />
              {:else}
                <table class="antiTable highlightRows">
                  <thead class="scroller-thead">
                    <tr class="scroller-thead__tr">
                      <th><Label label={intlName} /></th>
                      <th><Label label={intlFQN} /></th>
                      <th><Label label={intlDescription} /></th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each testSuites as ts}
                      <tr>
                        <td>{ts.displayName ?? ts.name}</td>
                        <td class="caption">{ts.fullyQualifiedName}</td>
                        <td class="definition">{ts.description || '—'}</td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              {/if}
            </svelte:fragment>
          </Section>

          <Section label={intlTestCases}>
            <svelte:fragment slot="content">
              {#if testCases.length === 0}
                <Label label={intlNoData} />
              {:else}
                <table class="antiTable highlightRows">
                  <thead class="scroller-thead">
                    <tr class="scroller-thead__tr">
                      <th><Label label={intlName} /></th>
                      <th><Label label={intlFQN} /></th>
                      <th><Label label={intlTestSuitesLbl} /></th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each testCases as tc}
                      <tr>
                        <td>{tc.displayName ?? tc.name}</td>
                        <td class="caption">{tc.fullyQualifiedName}</td>
                        <td class="caption">{tc.testSuite?.name || '—'}</td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              {/if}
            </svelte:fragment>
          </Section>

          <Section label={getEmbeddedLabel('Test Definitions')}>
            <svelte:fragment slot="content">
              {#if testDefinitions.length === 0}
                <Label label={intlNoData} />
              {:else}
                <table class="antiTable highlightRows">
                  <thead class="scroller-thead">
                    <tr class="scroller-thead__tr">
                      <th><Label label={intlName} /></th>
                      <th><Label label={intlFQN} /></th>
                      <th><Label label={intlDescription} /></th>
                    </tr>
                  </thead>
                  <tbody>
                    {#each testDefinitions as td}
                      <tr>
                        <td>{td.displayName ?? td.name}</td>
                        <td class="caption">{td.fullyQualifiedName}</td>
                        <td class="definition">{td.description || '—'}</td>
                      </tr>
                    {/each}
                  </tbody>
                </table>
              {/if}
            </svelte:fragment>
          </Section>
          </div>
        </Scroller>
      {:else if tabDefs[selected]?.key === 'incidents'}
        <Scroller>
          {#if incidents.length === 0}
            <Label label={intlNoData} />
          {:else}
            <table class="antiTable highlightRows">
              <thead class="scroller-thead">
                <tr class="scroller-thead__tr">
                  <th><Label label={intlName} /></th>
                  <th><Label label={intlStatus} /></th>
                  <th><Label label={intlSeverity} /></th>
                  <th><Label label={intlAssignees} /></th>
                  <th><Label label={intlCreatedAt} /></th>
                </tr>
              </thead>
              <tbody>
                {#each incidents as inc}
                  <tr>
                    <td>{inc.displayName ?? inc.name ?? inc.fullyQualifiedName ?? inc.id}</td>
                    <td class="caption">{inc.status || '—'}</td>
                    <td class="caption">{inc.severity || '—'}</td>
                    <td class="caption">{(inc.assignees || []).map(a => a?.displayName || a?.name).filter(Boolean).join(', ') || '—'}</td>
                    <td class="caption">{inc.createdAt || '—'}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          {/if}
        </Scroller>
      {:else if tabDefs[selected]?.key === 'alerts'}
        <Scroller>
          {#if alerts.length === 0}
            <Label label={intlNoData} />
          {:else}
            <table class="antiTable highlightRows">
              <thead class="scroller-thead">
                <tr class="scroller-thead__tr">
                  <th><Label label={intlName} /></th>
                  <th><Label label={intlType} /></th>
                  <th><Label label={intlProvider} /></th>
                  <th><Label label={intlEnabled} /></th>
                </tr>
              </thead>
              <tbody>
                {#each alerts as alert}
                  <tr>
                    <td>
                      <button type="button" class="row-button" on:click={() => openAlertDetails(alert)}>
                        {alert.displayName || alert.name || alert.fullyQualifiedName || alert.id || '—'}
                      </button>
                    </td>
                    <td class="caption">{alert.alertType || '—'}</td>
                    <td class="caption">{alert.provider || '—'}</td>
                    <td class="caption">{alert.enabled ? 'Enabled' : 'Disabled'}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          {/if}
        </Scroller>
      
      {/if}

      {#if total > limit}
        <div class="pagination">
          <ButtonIcon icon={datacatalog.icon.ChevronLeft} size="small" disabled={offset === 0} on:click={prevPage}>{intlPrevPage}</ButtonIcon>
          <Label label={datacatalog.string.ObservabilityPagination} params={{ page: Math.floor(offset / limit) + 1, totalPages: Math.max(1, Math.ceil(total / limit)) }} />
          <ButtonIcon icon={datacatalog.icon.ChevronRight} size="small" disabled={offset + limit >= total} on:click={nextPage}>{intlNextPage}</ButtonIcon>
        </div>
      {/if}
    {/if}
  </div>
</div>

<style lang="scss">
  .observability-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  .list-container {
    flex: 1 1 auto;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .nav-container {
    padding: 0 0.75rem;
  }
  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-top: 1px solid var(--theme-popup-divider);
  }
  .summary-chips {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    flex-wrap: wrap;
  }
  .content-container {
    padding: 0 0.75rem;
  }
  .chip {
    border: 1px solid var(--theme-divider-color, #e0e4e7);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    background: var(--theme-bg-color, #fff);
    display: inline-flex;
    gap: 0.5rem;
    align-items: baseline;
  }
  .chip .value {
    font-weight: 700;
  }
  table.antiTable {
    width: 100%;
    border-collapse: collapse;
  }
  thead.scroller-thead th {
    text-align: left;
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid var(--theme-divider-color, #e0e4e7);
  }
  tbody td {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid var(--theme-divider-color, #e0e4e7);
  }
  .definition {
    max-width: 40ch;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .caption {
    color: var(--theme-secondary-color);
  }

  .loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    width: 100%;
    height: 100%;
  }
</style>