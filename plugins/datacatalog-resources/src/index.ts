import { type Resources } from '@hcengineering/platform'
import DatacatalogComponent from './components/DatacatalogComponent.svelte'
import DataAssets from './components/data-assets/DataAssets.svelte'
import AssetsView from './components/data-assets/AssetsView.svelte'
import AssetsControlPanel from './components/data-assets/AssetsControlPanel.svelte'
import AssetsActivity from './components/data-assets/AssetsActivity.svelte'


export default async (): Promise<Resources> => ({
    component: {
        DatacatalogComponent,
        DataAssets,
        AssetsView,
        AssetsControlPanel,
        AssetsActivity
    }
})