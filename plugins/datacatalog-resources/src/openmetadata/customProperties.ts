import { authRequest } from '../api'

export interface TypeDefinitionEntity {
  id: string
  name: string
  displayName?: string
  description?: string
  href?: string
  fullyQualifiedName?: string
  category: 'field' | 'entity'
  schema?: any
  customProperties?: CustomPropertyDefinition[]
  version?: number
  updatedAt?: number
  updatedBy?: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

export interface CustomPropertyDefinition {
  name: string
  description?: string
  propertyType: {
    id: string
    type: string
    name?: string
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

export interface PaginatedResponse<T> {
  data: T[]
  paging?: {
    after?: string
    before?: string
    total?: number
  }
}

export interface ListTypeDefinitionsParams {
  limit?: number
  after?: string
  before?: string
  fields?: string[]
  include?: 'all' | 'non-deleted' | 'deleted'
  category?: 'field' | 'entity'
}

/**
 * Fetch a paginated list of type definitions from OpenMetadata.
 * Type definitions contain custom property schemas.
 *
 * Docs: https://docs.open-metadata.org/latest/main-concepts/metadata-standard/schemas/type/customProperty
 */
export const listTypeDefinitions = async (
  params: ListTypeDefinitionsParams = {}
): Promise<PaginatedResponse<TypeDefinitionEntity>> => {
  const searchParams = new URLSearchParams()
  if (params.limit != null) searchParams.append('limit', String(params.limit))
  if (params.after != null) searchParams.append('after', params.after)
  if (params.before != null) searchParams.append('before', params.before)
  if (params.fields?.length) searchParams.append('fields', params.fields.join(','))
  if (params.include != null) searchParams.append('include', params.include)
  if (params.category != null) searchParams.append('category', params.category)

  const query = searchParams.toString()
  const path = `/metadata/types${query ? `?${query}` : ''}`

  console.log('Type Definitions API call:', path)
  return await authRequest<PaginatedResponse<TypeDefinitionEntity>>(path)
}

/**
 * Fetch a specific type definition by its ID.
 */
export const getTypeDefinitionById = async (
  id: string,
  fields?: string[]
): Promise<TypeDefinitionEntity> => {
  const searchParams = new URLSearchParams()
  if (fields?.length) searchParams.append('fields', fields.join(','))

  const query = searchParams.toString()
  const path = `/metadata/types/${encodeURIComponent(id)}${query ? `?${query}` : ''}`

  return await authRequest<TypeDefinitionEntity>(path)
}

/**
 * Fetch a specific type definition by its name.
 */
export const getTypeDefinitionByName = async (
  name: string,
  fields?: string[]
): Promise<TypeDefinitionEntity> => {
  const searchParams = new URLSearchParams()
  if (fields?.length) searchParams.append('fields', fields.join(','))

  const query = searchParams.toString()
  const path = `/metadata/types/name/${encodeURIComponent(name)}${query ? `?${query}` : ''}`

  return await authRequest<TypeDefinitionEntity>(path)
}

/**
 * Fetch custom properties for a specific entity type (e.g., 'domain', 'table', 'database').
 * This returns the type definition that contains custom property schemas for that entity type.
 */
export const getCustomPropertiesForEntityType = async (
  entityType: string
): Promise<TypeDefinitionEntity | null> => {
  try {
    // Try to get type definition for the specific entity type
    return await getTypeDefinitionByName(entityType, ['customProperties', 'schema'])
  } catch (error) {
    console.warn(`No custom properties found for entity type: ${entityType}`, error)
    return null
  }
}

/**
 * Fetch all custom properties defined in the system.
 * This returns all type definitions that have custom properties.
 */
export const getAllCustomProperties = async (): Promise<TypeDefinitionEntity[]> => {
  try {
    const response = await listTypeDefinitions({
      fields: ['customProperties', 'schema', 'category'],
      limit: 1000 // Get all type definitions
    })
    
    // Filter to only type definitions that have custom properties
    return response.data.filter(typeDef => 
      typeDef.customProperties && typeDef.customProperties.length > 0
    )
  } catch (error) {
    console.error('Failed to fetch custom properties:', error)
    return []
  }
}
