import { authRequest } from '../api'

// === OpenMetadata – Glossary Helpers ======================================

// Basic shape of a Glossary entity as returned by OpenMetadata. We only
// include the fields we care about for now and fall back to `any` for the
// rest so callers can still access additional properties if needed.
export interface GlossaryEntity {
  id: string
  name: string
  displayName?: string
  description?: string
  href?: string
  owners?: any[]
  reviewers?: any[]
  tags?: any[]
  relatedTerms?: any[]
  linkedAssets?: any[]
  dataProducts?: any[]
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

// Shape of a Glossary Term entity
export interface GlossaryTermEntity {
  id: string
  name: string
  displayName?: string
  description?: string
  href?: string
  glossary?: any
  parent?: any
  children?: any[]
  relatedTerms?: any[]
  reviewers?: any[]
  owners?: any[]
  tags?: any[]
  synonyms?: string[]
  mutuallyExclusive?: boolean
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

export interface PaginatedResponse<T> {
  data: T[]
  paging: {
    total?: number
    after?: string
    before?: string
  }
}

export interface ListGlossariesParams {
  limit?: number
  after?: string
  before?: string
  fields?: string[]
  include?: 'all' | 'non-deleted' | 'deleted'
}

export interface ListGlossaryTermsParams {
  glossary?: string
  parent?: string
  fields?: string[]
  limit?: number
  before?: string
  after?: string
  include?: 'all' | 'non-deleted' | 'deleted'
  directChildrenOf?: string
}

/**
 * Fetch a paginated list of glossaries from OpenMetadata.
 *
 * Docs: https://github.com/open-metadata/OpenMetadata/blob/main/docs/api/resources/glossary.md
 */
export const listGlossaries = async (
  params: ListGlossariesParams = {}
): Promise<PaginatedResponse<GlossaryEntity>> => {
  const searchParams = new URLSearchParams()
  if (params.limit != null) searchParams.append('limit', String(params.limit))
  if (params.after != null) searchParams.append('after', params.after)
  if (params.before != null) searchParams.append('before', params.before)

  // Allowlist only fields that are broadly supported to avoid backend 400s
  const allowedFields = new Set(['owners', 'tags', 'reviewers'])
  const filteredFields = (params.fields ?? []).filter((f) => allowedFields.has(f))
  if (filteredFields.length) searchParams.append('fields', filteredFields.join(','))

  if (params.include != null) searchParams.append('include', params.include)

  const query = searchParams.toString()
  const path = `/glossaries${query ? `?${query}` : ''}`

  return await authRequest<PaginatedResponse<GlossaryEntity>>(path)
}

/**
 * Fetch a paginated list of glossary terms from OpenMetadata.
 *
 * Docs: https://github.com/open-metadata/OpenMetadata/blob/main/docs/api/resources/glossaryTerms.md
 */
export const listGlossaryTerms = async (
  params: ListGlossaryTermsParams = {}
): Promise<PaginatedResponse<GlossaryTermEntity>> => {
  const searchParams = new URLSearchParams()
  if (params.glossary != null) searchParams.append('glossary', params.glossary)
  if (params.parent != null) searchParams.append('parent', params.parent)
  if (params.limit != null) searchParams.append('limit', String(params.limit))
  if (params.after != null) searchParams.append('after', params.after)
  if (params.before != null) searchParams.append('before', params.before)
  if (params.fields?.length) searchParams.append('fields', params.fields.join(','))
  if (params.include != null) searchParams.append('include', params.include)
  if (params.directChildrenOf != null) searchParams.append('directChildrenOf', params.directChildrenOf)

  const query = searchParams.toString()
  const path = `/glossaryTerms${query ? `?${query}` : ''}`

  return await authRequest<PaginatedResponse<GlossaryTermEntity>>(path)
} 