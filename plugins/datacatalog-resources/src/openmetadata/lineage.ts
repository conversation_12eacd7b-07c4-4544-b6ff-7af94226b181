import { authRequest } from '../api'

// === OpenMetadata – Lineage Helpers =========================================

export interface PlatformLineageParams {
  view?: string // e.g. 'service' | 'entity'
  upstreamDepth?: number
  downstreamDepth?: number
  includeDeleted?: boolean
  size?: number
}

export interface PlatformLineageResponse {
  // Map of node key -> node wrapper with `entity`
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  nodes: Record<string, { entity: any }>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  upstreamEdges?: Record<string, any>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  downstreamEdges?: Record<string, any>
}

/**
 * Fetch platform lineage graph from OpenMetadata (Collate-managed).
 * Docs observed in Collate UIs: GET /lineage/getPlatformLineage?view=service&...
 */
export async function getPlatformLineage(
  params: PlatformLineageParams = {}
): Promise<PlatformLineageResponse> {
  const sp = new URLSearchParams()
  if (params.view != null) sp.append('view', params.view)
  if (params.upstreamDepth != null) sp.append('upstreamDepth', String(params.upstreamDepth))
  if (params.downstreamDepth != null) sp.append('downstreamDepth', String(params.downstreamDepth))
  if (params.includeDeleted != null) sp.append('includeDeleted', String(params.includeDeleted))
  if (params.size != null) sp.append('size', String(params.size))

  const qs = sp.toString()
  const path = `/lineage/getPlatformLineage${qs ? `?${qs}` : ''}`
  return await authRequest<PlatformLineageResponse>(path)
}


