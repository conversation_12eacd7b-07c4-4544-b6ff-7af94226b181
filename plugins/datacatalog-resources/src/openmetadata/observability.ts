// === OpenMetadata – Observability Helpers ==================================
// Thin wrappers around common Observability endpoints exposed by OpenMetadata
// (and Collate-managed OpenMetadata).
//
// These functions intentionally accept generic shapes and return minimal typed
// responses to remain resilient across OpenMetadata versions.

import { authRequest } from '../api'

export interface ListParams {
  limit?: number
  offset?: number
  fields?: string | string[]
  include?: 'all' | 'non-deleted' | 'deleted'
}

export interface Paginated<T> {
  data: T[]
  paging?: {
    total?: number
    after?: string
    before?: string
    limit?: number
    offset?: number
  }
}

function buildQuery(params: ListParams = {}): string {
  const sp = new URLSearchParams()
  if (params.limit != null) sp.append('limit', String(params.limit))
  if (params.offset != null) sp.append('offset', String(params.offset))
  if (params.include != null) sp.append('include', params.include)
  if (params.fields) {
    sp.append('fields', Array.isArray(params.fields) ? params.fields.join(',') : params.fields)
  }
  const q = sp.toString()
  return q.length ? `?${q}` : ''
}

async function tryList<T>(endpoints: string[], params?: ListParams): Promise<Paginated<T>> {
  let lastError: any
  for (const ep of endpoints) {
    try {
      const res = await authRequest<Paginated<T>>(`${ep}${buildQuery(params)}`)
      return res
    } catch (e: any) {
      lastError = e
      // Try next candidate endpoint
      continue
    }
  }
  throw lastError
}

// Fetch single entity by canonical path with fallbacks
async function tryGet<T>(endpoints: string[]): Promise<T> {
  let lastError: any
  for (const ep of endpoints) {
    try {
      const res = await authRequest<T>(ep)
      return res
    } catch (e: any) {
      lastError = e
      // Try next candidate endpoint
      continue
    }
  }
  throw lastError
}

// ---------------------------------------------------------------------------
// Data Quality
// ---------------------------------------------------------------------------

export interface TestSuiteEntity { id?: string; name?: string; displayName?: string; fullyQualifiedName?: string; description?: string }
export interface TestCaseEntity { id?: string; name?: string; displayName?: string; fullyQualifiedName?: string; description?: string; testSuite?: { name?: string } }
export interface TestDefinitionEntity { id?: string; name?: string; displayName?: string; fullyQualifiedName?: string; description?: string }

export async function listTestSuites(params?: ListParams): Promise<Paginated<TestSuiteEntity>> {
  // Primary OpenMetadata path for data quality
  return await tryList<TestSuiteEntity>([
    '/dataQuality/testSuites',
    '/quality/testSuites',
    '/testSuites'
  ], params)
}

export async function listTestCases(params?: ListParams): Promise<Paginated<TestCaseEntity>> {
  return await tryList<TestCaseEntity>([
    '/dataQuality/testCases',
    '/quality/testCases',
    '/testCases'
  ], params)
}

export async function listTestDefinitions(params?: ListParams): Promise<Paginated<TestDefinitionEntity>> {
  return await tryList<TestDefinitionEntity>([
    '/dataQuality/testDefinitions',
    '/quality/testDefinitions',
    '/testDefinitions'
  ], params)
}

// Generic Data Quality report (aggregations over ES indices exposed by OM)
// Docs: observed in Collate-managed OM UIs as
//   GET /dataQuality/testSuites/dataQualityReport?q=...&index=...&aggregationQuery=...
export interface DataQualityReportResponse {
  metadata: {
    keys: string[]
    dimensions: Array<string | null>
    metrics: string[]
  }
  // Each row is a flattened map of metric/dimension name -> value
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: Array<Record<string, any>>
}

export interface DataQualityReportParams {
  // OM search index to operate on, e.g. 'testCase', 'testCaseResult', 'table'
  index: string
  // Either a pre-encoded string or a JSON object; both are supported by the OM API
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  q: string | Record<string, any>
  // Example: 'bucketName=entityWithTests:aggType=cardinality:field=originEntityFQN'
  aggregationQuery: string
}

export async function dataQualityReport(params: DataQualityReportParams): Promise<DataQualityReportResponse> {
  const sp = new URLSearchParams()
  if (typeof params.q === 'string') {
    sp.append('q', params.q)
  } else {
    sp.append('q', JSON.stringify(params.q))
  }
  sp.append('index', params.index)
  sp.append('aggregationQuery', params.aggregationQuery)

  const qs = sp.toString()
  return await authRequest<DataQualityReportResponse>(`/dataQuality/testSuites/dataQualityReport?${qs}`)
}

// ---------------------------------------------------------------------------
// Incidents
// ---------------------------------------------------------------------------

export interface IncidentEntity { id?: string; name?: string; displayName?: string; fullyQualifiedName?: string; status?: string; assignees?: any[]; severity?: string; createdAt?: string }

export async function listIncidents(params?: ListParams): Promise<Paginated<IncidentEntity>> {
  return await tryList<IncidentEntity>([
    // OpenMetadata Incident Manager (collection is plural)
    // Ref: Incident Manager docs (Context7)
    '/incidents',
    // Some deployments may expose a singular alias
    '/incident'
  ], params)
}

// ---------------------------------------------------------------------------
// Alerts (Event Subscriptions / Alerts)
// ---------------------------------------------------------------------------

export interface AlertEntity {
  id?: string
  name?: string
  fullyQualifiedName?: string
  displayName?: string
  description?: string
  href?: string
  version?: number
  updatedAt?: string | number
  updatedBy?: string
  alertType?: string
  filteringRules?: {
    resources?: string[]
    rules?: Array<{
      name?: string
      effect?: string
      condition?: string
      arguments?: any[]
      prefixCondition?: string
    }>
    actions?: any[]
  }
  destinations?: Array<{
    id?: string
    category?: string
    type?: string
    timeout?: number
    readTimeout?: number
    enabled?: boolean
  }>
  enabled?: boolean
  batchSize?: number
  provider?: string
  retries?: number
  pollInterval?: number
  input?: {
    filters?: Array<{
      name?: string
      effect?: string
      prefixCondition?: string
      arguments?: Array<{ name?: string; input?: string[] }>
    }>
    actions?: any[]
  }
}

export async function listAlerts(params?: ListParams): Promise<Paginated<AlertEntity>> {
  return await tryList<AlertEntity>([
    // Event Subscriptions are surfaced as Alerts in UI
    // Primary OM path
    '/events/subscriptions',
    // Legacy/singular alias
    '/eventSubscriptions',
    // Non-standard alias observed in some Collate setups
    '/alerts'
  ], params)
}

// ---------------------------------------------------------------------------
// KPIs
// ---------------------------------------------------------------------------

export interface KpiEntity { id?: string; name?: string; displayName?: string; metricType?: string; owner?: any; updatedAt?: string; enabled?: boolean }

export async function listKpis(params?: ListParams): Promise<Paginated<KpiEntity>> {
  return await tryList<KpiEntity>([
    // OM collections are plural; prefer /kpis
    '/kpis',
    // Some docs reference Data Insights namespace
    '/datainsights/kpis',
    // Fallback singular
    '/kpi'
  ], params)
}

// ---------------------------------------------------------------------------
// Ingestion Pipelines (health/status)
// ---------------------------------------------------------------------------

export interface IngestionPipelineEntity { id?: string; name?: string; displayName?: string; pipelineType?: string; enabled?: boolean; status?: any; schedule?: any; updatedAt?: string }

export async function listIngestionPipelines(params?: ListParams): Promise<Paginated<IngestionPipelineEntity>> {
  return await tryList<IngestionPipelineEntity>([
    // Primary OM path for listing ingestion pipelines
    '/services/ingestionPipelines',
    // Fallback observed in older/alt deployments
    '/ingestionPipelines'
  ], params)
}

// ---------------------------------------------------------------------------
// Details helpers
// ---------------------------------------------------------------------------

export async function getAlertById(id: string): Promise<AlertEntity> {
  return await tryGet<AlertEntity>([
    `/events/subscriptions/${id}`,
    `/eventSubscriptions/${id}`,
    `/alerts/${id}`
  ])
}



