import { authRequest } from '../api'


export interface DataProductEntity {
  id: string
  name: string
  displayName?: string
  description?: string
  href?: string
  fullyQualifiedName?: string
  domain?: any
  owners?: any[]
  experts?: any[]
  assets?: any[]
  tags?: any[]
  version?: number
  updatedAt?: number
  updatedBy?: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

export interface ListDataProductsParams {
  limit?: number
  after?: string
  before?: string
  fields?: string[]
  include?: 'all' | 'non-deleted' | 'deleted'
  domain?: string // Filter by domain name (not ID)
}

export interface PaginatedResponse<T> {
  data: T[]
  paging: {
    total?: number
    after?: string
    before?: string
    limit?: number
    offset?: number
  }
}

/**
 * Fetch a paginated list of data products from OpenMetadata.
 */
export const listDataProducts = async (
  params: ListDataProductsParams = {}
): Promise<PaginatedResponse<DataProductEntity>> => {
  const searchParams = new URLSearchParams()
  if (params.limit != null) searchParams.append('limit', String(params.limit))
  if (params.after != null) searchParams.append('after', params.after)
  if (params.before != null) searchParams.append('before', params.before)
  if (params.fields?.length) searchParams.append('fields', params.fields.join(','))
  if (params.include != null) searchParams.append('include', params.include)
  if (params.domain != null) searchParams.append('domain', params.domain) // Don't encode here

  const query = searchParams.toString()
  const path = `/dataProducts${query ? `?${query}` : ''}`

  console.log('Data Products API call:', path)
  console.log('Full URL would be: /api/v1' + path)
  return await authRequest<PaginatedResponse<DataProductEntity>>(path)
}

/**
 * Fetch data products for a specific domain using domain name.
 */
export const getDataProductsByDomain = async (
  domainName: string,
  params: Omit<ListDataProductsParams, 'domain'> = {}
): Promise<PaginatedResponse<DataProductEntity>> => {
  // Use domain name directly without encoding (URLSearchParams will handle it)
  return listDataProducts({
    ...params,
    domain: domainName // Pass the domain name as-is
  })
}

/**
 * Fetch a specific data product by its ID.
 */
export const getDataProductById = async (
  id: string,
  fields?: string[]
): Promise<DataProductEntity> => {
  const searchParams = new URLSearchParams()
  if (fields?.length) searchParams.append('fields', fields.join(','))

  const query = searchParams.toString()
  const path = `/dataProducts/${id}${query ? `?${query}` : ''}`

  return await authRequest<DataProductEntity>(path)
}
