import { authRequest } from '../api'


export interface DomainEntity {
  id: string
  name: string
  displayName?: string
  description?: string
  href?: string
  fullyQualifiedName?: string
  domainType?: 'Source-aligned' | 'Consumer-aligned' | 'Aggregate'
  owners?: any[]
  experts?: any[]
  parent?: any
  children?: any[]
  assets?: any[]
  tags?: any[]
  extension?: any
  style?: any
  version?: number
  updatedAt?: number
  updatedBy?: string
  changeDescription?: any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

export interface PaginatedResponse<T> {
  data: T[]
  paging: {
    total?: number
    after?: string
    before?: string
  }
}

export interface ListDomainsParams {
  limit?: number
  after?: string
  before?: string
  fields?: string[]
  include?: 'all' | 'non-deleted' | 'deleted'
}

/**
 * Fetch a paginated list of domains from OpenMetadata.
 *
 * Docs: https://docs.open-metadata.org/latest/main-concepts/metadata-standard/schemas/entity/domains/domain
 */
export const listDomains = async (
  params: ListDomainsParams = {}
): Promise<PaginatedResponse<DomainEntity>> => {
  const searchParams = new URLSearchParams()
  if (params.limit != null) searchParams.append('limit', String(params.limit))
  if (params.after != null) searchParams.append('after', params.after)
  if (params.before != null) searchParams.append('before', params.before)
  if (params.fields?.length) searchParams.append('fields', params.fields.join(','))
  if (params.include != null) searchParams.append('include', params.include)

  const query = searchParams.toString()
  const path = `/domains${query ? `?${query}` : ''}`

  return await authRequest<PaginatedResponse<DomainEntity>>(path)
}

/**
 * Fetch a specific domain by its fully qualified name.
 */
export const getDomainByFqn = async (
  fqn: string,
  fields?: string[]
): Promise<DomainEntity> => {
  const searchParams = new URLSearchParams()
  if (fields?.length) searchParams.append('fields', fields.join(','))

  const query = searchParams.toString()
  const path = `/domains/name/${encodeURIComponent(fqn)}${query ? `?${query}` : ''}`

  return await authRequest<DomainEntity>(path)
}

/**
 * Fetch a specific domain by its ID.
 */
export const getDomainById = async (
  id: string,
  fields?: string[]
): Promise<DomainEntity> => {
  const searchParams = new URLSearchParams()
  if (fields?.length) searchParams.append('fields', fields.join(','))

  const query = searchParams.toString()
  const path = `/domains/${encodeURIComponent(id)}${query ? `?${query}` : ''}`

  return await authRequest<DomainEntity>(path)
}
