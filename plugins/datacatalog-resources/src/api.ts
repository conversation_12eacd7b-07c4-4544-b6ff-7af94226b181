import { getMetadata } from '@hcengineering/platform'
import presentation from '@hcengineering/presentation'

// ---------------------------------------------------------------------------
// Configuration
// ---------------------------------------------------------------------------
// Resolve the base URL for the OpenMetadata backend. In order of precedence we
// check:
//   1. An explicit environment variable – process.env.OPENMETADATA_API_URL
//   2. A global injected into the browser – window.OPENMETADATA_API_URL
//   3. Fallback to the standard local‐dev address for OpenMetadata
//
// NOTE: The OpenMetadata REST API is namespaced under `/api/v1`, therefore the
// resolved value **must** already include that prefix (see the fallback).

declare const process: { env: Record<string, string | undefined> }

// Helper to safely access the browser `window` object when running in a
// non-browser (e.g. SSR / tests) context
const getWindowGlobal = <T = unknown>(key: string): T | undefined => {
  return typeof window !== 'undefined' && (window as any)[key]
}

// The OpenMetadata API is served at /api/v1
const API_BASE_URL: string =
  process.env.OPENMETADATA_API_URL ??
  (getWindowGlobal<string>('OPENMETADATA_API_URL')) ??
  'https://cors.erzen.tk/https://91-life.free-2.getcollate.cloud/api/v1'

// Utility to concatenate the base URL with the requested path while making
// sure we do not end up with a double slash or, inversely, miss a slash.
const buildUrl = (path: string): string => {
  if (!path.startsWith('/')) {
    path = `/${path}`
  }
  return `${API_BASE_URL}${path}`
}

// Default headers for unauthenticated requests
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

// ---------------------------------------------------------------------------
// Authentication helpers
// ---------------------------------------------------------------------------
// Resolve the token used to access the OpenMetadata API. In order of precedence
// we check:
//   1. Explicit env variable – process.env.OPENMETADATA_API_TOKEN
//   2. Browser global – window.OPENMETADATA_API_TOKEN (useful for injecting via
//      a <script> tag or SystemJS import map).
//   3. Platform presentation metadata (falls back to whatever the host app
//      already uses).

const resolveToken = (): string | undefined => {
  const tokenFromEnv = process.env.OPENMETADATA_API_TOKEN
  if (tokenFromEnv && tokenFromEnv.length > 0) return tokenFromEnv

  //const tokenFromWindow = getWindowGlobal<string>('OPEN_METADATA_TOKEN') ?? 'eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hLxYVRA0G8X6IgRJO9lE4LL2RHkJ2ek6Jwz-x7m5s4xl92gzTqczEZEkKBtGWNz_ldZ0jYi6SsLi24Vkgthj6KQiYyx_GhCwM1foZ7kaC6499_D81Hz0z4JF-6940ft_UsfHgHCWaUQUvOX8Cn65iwo7nnXRPMjeLb4egkmWAX0-K-JBQ8mN1FEAt2lky1V30um3H4mhYgf7_9tvct6rp9TXv5VyMlOWFH_aRJxocj89npwQsfbWcfxHIjwbOFwPfrUUNURCl4MC2cKNiWUgAeumsmfGReytjEVuLVLdL6n0brDyy9nB2Hco9jp0RtjH6p6BzRHJcP5221O7d27gtA'
  const tokenFromWindow = getWindowGlobal<string>('OPEN_METADATA_TOKEN') ?? 'eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Bm1WDxLU8vHw9VvnX50_Q65z1iH-L8nHzb9tMuIystkRolL6OWADjvXR1K1Tdvcmy27BXovnCcqlg6mNV4Nsmuip7L_vbdSNVzalZ2CAFQCCTtpBKWT_9S1J6OdP7bdxbuo0cSQZY50x65g-OJNu9KxRwOLxcyif8fBXQVUe_gTTWcGoSg9GzAfJ6iK4C3bzMRheAP86RFDQcKKcmtid9K-4WcUvHpSzGlRfdW4Hi4bT7MYGVuYWuEfpxkFb6koS7KUddvJoowYVlGukM_O2Ko0DEf47acQPWH-JGJZaO1gd9lckPwHqMhA1g94dZEZcXwzsj-2pmcTCJtdZEKKDtg'
  if (tokenFromWindow && tokenFromWindow.length > 0) return tokenFromWindow

  return getMetadata(presentation.metadata.Token) ?? undefined
}

// Function to get authenticated headers
const getAuthHeaders = (): Record<string, string> => {
  const token = resolveToken()

  if (!token) {
    throw new Error('No authentication token found. Provide it via the "OPENMETADATA_API_TOKEN" env or global, or login first.')
  }

  return {
    ...defaultHeaders,
    Authorization: `Bearer ${token}`
  }
}

// HTTP methods that don't accept request body
const NO_BODY_METHODS = ['GET', 'HEAD', 'DELETE', 'OPTIONS'] as const
type NoBodyMethod = typeof NO_BODY_METHODS[number]

// HTTP methods that accept request body
const BODY_METHODS = ['POST', 'PUT', 'PATCH'] as const
type BodyMethod = typeof BODY_METHODS[number]

// Function to make unauthenticated API calls (no body methods only)
export const unauthRequest = async <T>(
  path: string,
  method: NoBodyMethod = 'GET',
  options: Omit<RequestInit, 'method'> = {}
): Promise<T> => {
  const url = buildUrl(path)
  
  const response = await fetch(url, {
    method,
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}

// Function to make authenticated API calls (no body methods only)
export const authRequest = async <T>(
  path: string,
  method: NoBodyMethod = 'GET',
  options: Omit<RequestInit, 'method'> = {}
): Promise<T> => {
  const url = buildUrl(path)
  
  const response = await fetch(url, {
    method,
    ...options,
    headers: {
      ...getAuthHeaders(),
      ...options.headers
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}

// Function to make unauthenticated API calls with request body
export const unauthRequestWithBody = async <T>(
  path: string,
  method: BodyMethod = 'POST',
  body: any,
  options: Omit<RequestInit, 'method' | 'body'> = {}
): Promise<T> => {
  const url = buildUrl(path)
  
  const response = await fetch(url, {
    method,
    body: JSON.stringify(body ?? null),
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}

// Function to make authenticated API calls with request body
export const authRequestWithBody = async <T>(
  path: string,
  method: BodyMethod,
  body: any,
  options: Omit<RequestInit, 'method' | 'body'> = {}
): Promise<T> => {
  const url = buildUrl(path)
  
  const response = await fetch(url, {
    method,
    body: JSON.stringify(body),
    ...options,
    headers: {
      ...getAuthHeaders(),
      ...options.headers
    }
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}
