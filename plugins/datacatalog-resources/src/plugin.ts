import { mergeIds } from '@hcengineering/platform'
import datacatalog, { datacatalogId } from '@hcengineering/datacatalog'
import type { IntlString } from '@hcengineering/platform'

export default mergeIds(datacatalogId, datacatalog, {
    string: {
        ApplicationTitle: '' as IntlString,
        DataAssets: '' as IntlString,
        DataLineage: '' as IntlString,
        Observability: '' as IntlString,
        Domains: '' as IntlString,
        DataGovernance: '' as IntlString,
        LoadingDomains: '' as IntlString,
        NoDomains: '' as IntlString,
        Documentation: '' as IntlString,
        SubDomains: '' as IntlString,
        DataProducts: '' as IntlString,
        Assets: '' as IntlString,
        CustomProperties: '' as IntlString,
        Glossary: '' as IntlString,
        Classification: '' as IntlString,
        SearchGlossary: '' as IntlString,
        SearchClassification: '' as IntlString,
        Filter: '' as IntlString,
        Tag: '' as IntlString,
        TermForTable: '' as IntlString,
        DescriptionForTable: '' as IntlString,
        OwnersForTable: '' as IntlString,
        TagsForTable: '' as IntlString,
        DomainsForTable: '' as IntlString,
        Back: '' as IntlString,
        AllTags: '' as IntlString,
        Status: '' as IntlString,
        Active: '' as IntlString,
        Disabled: '' as IntlString,
        Approved: '' as IntlString,
        FullyQualifiedName: '' as IntlString,
        UsageCount: '' as IntlString,
        ChildrenCount: '' as IntlString,
        Owners: '' as IntlString,
        Reviewers: '' as IntlString,
        Tags: '' as IntlString,
        TotalTags: '' as IntlString,
        Terms: '' as IntlString,
        All: '' as IntlString,
        NoDescriptionAvailable: '' as IntlString,
        Databases: '' as IntlString,
        Dashboards: '' as IntlString,
        Pipelines: '' as IntlString,
        Topics: '' as IntlString,
        Charts: '' as IntlString,
        Metrics: '' as IntlString,
        Reports: '' as IntlString,
        MLModels: '' as IntlString,
        Containers: '' as IntlString,
        ObservabilityPagination: '' as IntlString,
    }
})