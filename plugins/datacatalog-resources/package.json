{"name": "@hcengineering/datacatalog-resources", "version": "0.1.0", "main": "src/index.ts", "author": "Anticrm Platform Contributors", "license": "EPL-2.0", "scripts": {"build": "compile ui", "build:docs": "api-extractor run --local", "format": "format src", "build:watch": "compile ui", "_phase:build": "compile ui", "_phase:format": "format src", "_phase:validate": "compile validate"}, "devDependencies": {"@hcengineering/platform-rig": "^0.6.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.54.0", "eslint-config-standard-with-typescript": "^40.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.4.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-svelte": "^2.35.1", "prettier": "^3.1.0", "prettier-plugin-svelte": "^3.2.2", "sass": "^1.53.0", "svelte-check": "^3.6.9", "svelte-loader": "^3.2.0", "svelte-preprocess": "^5.1.3", "typescript": "^5.3.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@types/jest": "^29.5.5", "svelte-eslint-parser": "^0.33.1"}, "dependencies": {"@hcengineering/core": "^0.6.32", "@hcengineering/platform": "^0.6.11", "@hcengineering/presentation": "^0.6.3", "@hcengineering/ui": "^0.6.15", "@hcengineering/view": "^0.6.13", "@hcengineering/view-resources": "^0.6.0", "@hcengineering/workbench": "^0.6.16", "@hcengineering/workbench-resources": "^0.6.1", "@hcengineering/datacatalog": "^0.1.0", "@hcengineering/panel": "^0.6.23", "@hcengineering/model-datacatalog": "^0.1.0", "@hcengineering/contact": "^0.6.24", "@hcengineering/contact-resources": "^0.6.0", "@hcengineering/tags": "^0.6.16", "@hcengineering/activity": "^0.6.32", "svelte": "^4.2.19"}}