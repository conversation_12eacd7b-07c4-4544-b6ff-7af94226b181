<!--
// Copyright © 2023 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
    import { Avatar, personByIdStore, personAccountByIdStore, employeeByIdStore } from '@hcengineering/contact-resources'
    import hr, { Department } from '@hcengineering/hr'
    import { Ref, getDisplayTime, getCurrentAccount } from '@hcengineering/core'
    import { getEmbeddedLabel } from '@hcengineering/platform'
    import {
      tooltip,
      getPlatformColorDef,
      themeStore,
      showPopup,
      location,
      Location,
      EmojiPopup,
      getEventPositionElement,
      closePopup,
      ButtonIcon
    } from '@hcengineering/ui'
    import DueDatePresenter from '../dates/CustomDueDatePresenter.svelte'
    import activity, { ActivityMessage, DailyPriorityCreatorGroup, Reaction } from '@hcengineering/activity'
    import { ReactionsPresenter, ReplyMessagePresenter } from '@hcengineering/activity-resources'
    import { PersonAccount, Employee } from '@hcengineering/contact'
    import { MessageViewer, getClient } from '@hcengineering/presentation'
    import { replyToThread } from '../../navigation'
    import { onDestroy } from 'svelte'
    import NewDailyPrioritiesModal from '../modal/NewDailyPrioritiesModal.svelte'
    import { Project } from '@hcengineering/tracker'
    import StatusEditor from './StatusEditor.svelte'
    import MessageActions from './MessageActions.svelte'
    import ExtraMessageActions from './ExtraMessageActions.svelte'
    import dailyPriorities from '../../plugin'

    export let creatorGroup: DailyPriorityCreatorGroup
    export let allProjects: Project[] = []
    export let readonly: boolean

    const client = getClient()

    let selectedMessageId: Ref<ActivityMessage> | undefined

    const unsubcribe = location.subscribe((loc) => {
      syncLocation(loc)
    })
    onDestroy(() => {
      unsubcribe()
    })

    function syncLocation (loc: Location): void {
      if (loc.path[4] != null) {
        selectedMessageId = loc.path[4] as Ref<ActivityMessage> | undefined
      } else {
        selectedMessageId = undefined
      }
    }

    function getProject (projectId: Ref<Project>): Project | undefined {
      if (projectId == null) {
        return undefined
      }
      return allProjects.find(({ _id }) => _id === projectId)
    }

    async function getDepartment (departmentId: Ref<Department>): Promise<Department | undefined> {
      return await client.findOne(hr.class.Department, {
        _id: departmentId
      })
    }

    function handleEditMessage (): void {
      showPopup(NewDailyPrioritiesModal, {
        myProjects: allProjects.filter(project => project.members.includes(getCurrentAccount()._id)),
        existingMessages: creatorGroup.messages
      }, 'top')
    }

    function handleDeleteMessage (): void {
      for (const message of creatorGroup.messages) {
        void client.remove(message)
      }
    }

    async function handleAddReaction (message: ActivityMessage, ev?: Event): Promise<void> {
      if (message === undefined || ev === undefined) return

      const reactions: Reaction[] =
          (message.reactions ?? 0) > 0
            ? await client.findAll<Reaction>(activity.class.Reaction, { attachedTo: message._id, space: message.space })
            : []

      const element = getEventPositionElement(ev as MouseEvent)

      closePopup()

      showPopup(EmojiPopup, {}, element, async (emoji: string) => {
        if (emoji === undefined || message === undefined) {
          return
        }

        const currentAccount = getCurrentAccount()
        const reaction = reactions.find((r) => r.emoji === emoji && r.createBy === currentAccount._id)

        if (reaction == null) {
          await client.addCollection(activity.class.Reaction, message.space, message._id, message._class, 'reactions', {
            emoji,
            createBy: currentAccount._id
          })
        } else {
          await client.remove(reaction)
        }
      })
    }

    $: showEditIcon = creatorGroup.createdBy === getCurrentAccount()._id

    $: fullDate = new Date(creatorGroup.earliestMessageTime).toLocaleString('default', {
      minute: '2-digit',
      hour: 'numeric',
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })

    $: personAccount = $personAccountByIdStore.get(creatorGroup.createdBy as Ref<PersonAccount>)
    $: person =
        personAccount?.person !== undefined
          ? ($employeeByIdStore.get(personAccount.person as Ref<Employee>) ?? $personByIdStore.get(personAccount.person))
          : undefined

    $: personStaff = (person as any)['hr:mixin:Staff']
</script>

<div class="message-group-card">
    {#if person != null}
        <div class="creator-header">
            <Avatar {person} size="small" name={person.name} />
            <div class="creator-meta">
                <div class="creator-row">
                    <span class="creator-name">{person.name.split(',').reverse().join(' ')}</span>
                    <span class="creator-time" use:tooltip={{ label: getEmbeddedLabel(fullDate) }}>
                        {getDisplayTime(creatorGroup.earliestMessageTime)}
                    </span>
                </div>
                <div class="creator-role">
                    {#await getDepartment(personStaff.department) then department}
                        {department?.name ?? 'Unknown position'}
                    {/await}
                </div>
            </div>
            {#if showEditIcon && !readonly}
              <ExtraMessageActions
                  onDelete={handleDeleteMessage}
                  onEdit={handleEditMessage}
              />
            {/if}
        </div>
    {/if}
    <div class="messages-container">
      {#each creatorGroup.messages as message}
        {#if message.attachedToProject != null}
          {@const project = getProject(message.attachedToProject)}
          {@const isSelected = message._id === selectedMessageId}
          <div class="message-card {isSelected ? 'message-card-selected' : ''}">
                                <div class="message-card-content">
                      <div class="message-card-content-message">
                          <StatusEditor value={message.status ?? 'tracker:status:Todo'} message={message} />
                          <MessageViewer message={message.message} />
                      </div>
                      <div class="message-card-date-and-tag">
                          <div class="message-card-reactions">
                            <ReactionsPresenter object={message} {readonly} />
                          </div>
                          <ReplyMessagePresenter object={message} onReply={replyToThread} />
                          {#if (message.replies ?? 0) === 0}
                            <ButtonIcon
                                icon={dailyPriorities.icon.ThreadUpdate}
                                iconSize={'small'}
                                size="small"
                                kind="tertiary"
                                tooltip={{ label: dailyPriorities.string.Thread, direction: 'bottom' }}
                                iconProps={{ style: 'color: var(--theme-halfcontent-color)!important;' }}
                                on:click={(e) => { void replyToThread(message, e) }}
                            />
                          {/if}
                          {#if message.reactions == null || message.reactions === 0}
                            <ButtonIcon
                                icon={dailyPriorities.icon.Emoji}
                                iconSize={'small'}
                                size="small"
                                kind="tertiary"
                                tooltip={{ label: dailyPriorities.string.EmojiText, direction: 'bottom' }}
                                iconProps={{ style: 'color: var(--theme-halfcontent-color);' }}
                                on:click={(e) => { void handleAddReaction(message, e) }}
                            />
                          {/if}
                          {#if showEditIcon}
                            <DueDatePresenter
                                value={message.dueDate}
                                shouldRender={true}
                                onChange={(newDate) => {
                                  if (newDate !== message.dueDate) {
                                    void client.update(message, { dueDate: newDate ?? undefined })
                                  }
                                }}
                                editable={!readonly && showEditIcon}
                                size="small"
                                width="auto"
                                shouldShowLabel={message.dueDate !== null && message.dueDate !== undefined}
                                showSimpleDatePicker={true}
                            />
                          {/if}
                          {#if project != null}
                              {@const color = getPlatformColorDef(project.color ?? 0, $themeStore.dark).icon}
                              <span
                                  class="tag"
                                  style="background-color: {color}1A; color: {color}; border-color: {color};"
                              >
                                  {project.name}
                              </span>
                          {:else}
                              <span
                                  class="tag"
                              >
                                  Removed Project
                              </span>
                          {/if}
                          {#if showEditIcon && !readonly}
                            <ExtraMessageActions
                                {message}
                                {showEditIcon}
                                onDelete={() => { void client.remove(message) }}
                                onEdit={() => {
                                  showPopup(NewDailyPrioritiesModal, {
                                    myProjects: allProjects.filter(project => project.members.includes(getCurrentAccount()._id)),
                                    existingMessages: creatorGroup.messages,
                                    selectedMessageToEdit: message._id
                                  }, 'top')
                                }}
                            />
                          {/if}
                      </div>
                  </div>
              <MessageActions
                message={message}
                cardMessages={creatorGroup.messages}
                allProjects={allProjects}
                showEditIcon={false}
                onDelete={() => { void client.remove(message) }}
                onEdit={() => {}}
              />
          </div>
        {/if}
      {/each}
    </div>
  </div>

<style lang="scss">
    .message-group-card {
        border: 1px solid var(--theme-divider-color);
        border-radius: 0.75rem;
        padding: 0.5rem;
        margin: 0.5rem 0.5rem;
    }
    .creator-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
        min-height: 48px;
        background-color: var(--theme-mention-bg-color);
        padding: 0.5rem;
        margin: -0.5rem -0.5rem 0.5rem -0.5rem;
        border-bottom: 1px solid var(--theme-divider-color);
        border-top-left-radius: 0.75rem;
        border-top-right-radius: 0.75rem;
    }
    .creator-meta {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    .creator-row {
        display: flex;
        align-items: baseline;
        gap: 0.5rem;
    }
    .creator-name {
        font-weight: 600;
        font-size: 0.9rem;
        margin-right: 0.25rem;
    }
    .creator-time {
        color: var(--theme-text-editor-palette-text-gray);
        font-size: 0.8em;
    }
    .creator-role {
        font-size: 0.95em;
        color: var(--theme-trans-color);
        margin-top: 0.1rem;
    }
    .creator-edit-icons {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
    }

  .message-card {
    position: relative;
    border: 1px solid var(--theme-bg-color);
    border-radius: 0.5rem;
    padding: 0.25rem 0.5rem 0.25rem 0.25rem;
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .message-card-selected {
    border: 1px solid var(--theme-link-color);
  }
  .message-card:hover {
    background-color: var(--global-ui-BackgroundColor);
  }
  .message-card-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
  }
  .message-card-content-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
  .message-card-date-and-tag {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
  }
  .tag {
    background-color: var(--theme-toggle-bg-color);
    color: var(--theme-calendar-event-caption-color);
    border: 1px solid var(--theme-calendar-event-caption-color);
    border-radius: 0.25rem;
    padding: 0.1rem 0.5rem;
    font-size: 0.85em;
    font-weight: 600;
    white-space: nowrap;
  }
  .done-tag {
    background-color: var(--positive-button-hovered);
    color: var(--white-color);
    border: 1px solid var(--positive-button-pressed);
    border-radius: 0.25rem;
    padding: 0.1rem 0.5rem;
    font-size: 0.85em;
    font-weight: 600;
  }

  .message-card-events {
    margin-left: 0.5rem;
  }

  :global(.message-card-reactions .footer) {
    margin-top: 0;
  }

  :global(.message-card-reactions .container) {
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: nowrap;
  }

  :global(.message-card:hover > .message-card-actions) {
    visibility: visible;
  }
</style>
