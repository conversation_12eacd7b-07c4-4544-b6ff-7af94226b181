<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import activity, { ActivityMessage, type DailyPriorityGroupedMessages } from '@hcengineering/activity'
  import { messageInFocus, ActivityMessagePresenter } from '@hcengineering/activity-resources'
  import core, {
    Doc,
    generateId,
    getDay,
    Ref,
    Space,
    Timestamp,
    Tx,
    TxCUD,
    getCurrentAccount
  } from '@hcengineering/core'
  import { type PersonAccount } from '@hcengineering/contact'
  import { DocNotifyContext } from '@hcengineering/notification'
  import { InboxNotificationsClientImpl } from '@hcengineering/notification-resources'
  import { addTxListener, getClient, removeTxListener } from '@hcengineering/presentation'
  import { ModernButton, Scroller } from '@hcengineering/ui'
  import { afterUpdate, onDestroy, onMount, tick } from 'svelte'
  import MessageCard from './message-card/MessageCard.svelte'
  import OutToday from './OutToday.svelte'
  import tracker, { Project, type Issue, type IssueStatus } from '@hcengineering/tracker'

  import { ChannelDataProvider, MessageMetadata } from '../channelDataProvider'
  import dailyPriorities from '../plugin'
  import { getScrollToDateOffset, getSelectedDate, jumpToDate, readViewportMessages } from '../scroll'
  import { chatReadMessagesStore, recheckNotifications } from '../utils'
  import BaseChatScroller from './BaseChatScroller.svelte'
  import BlankView from './BlankView.svelte'
  import ChannelInput from './ChannelInput.svelte'
  import ActivityMessagesSeparator from './ChannelMessagesSeparator.svelte'
  import JumpToDateSelector from './JumpToDateSelector.svelte'
  import HistoryLoading from './LoadingHistory.svelte'
  import TaskSuggestion from './TaskSuggestion.svelte'
  import dailyPrioritiesPkg, { type ChatMessage } from '@hcengineering/dailypriorities'
  import { ALL_DAILY_UPDATES_PROJECT_ID, ALL_DAILY_UPDATES_PROJECT } from './chat/constants'

  export let provider: ChannelDataProvider
  export let channel: Doc
  export let allProjects: Project[] = []
  export let currentProjectId: Ref<Project> | undefined = undefined
  export let message: Doc | undefined = undefined
  export let viewType: '2-card' | '3-card' | '4-card' | 'list' = '2-card'
  export let selectedMessageId: Ref<ActivityMessage> | undefined = undefined
  export let fixedInput = true
  export let collection: string = 'messages'
  export let fullHeight = true
  export let freeze = false
  export let loadMoreAllowed = true
  export let autofocus = true
  export let withInput: boolean = true
  export let readonly: boolean = false

  // Task suggestion state
  let issues: Issue[] = []
  let suggestedIssues: Issue[] = []
  let showTaskSuggestionCard = true

  const minMsgHeightRem = 2
  const loadMoreThreshold = 200

  const client = getClient()
  const hierarchy = client.getHierarchy()
  const inboxClient = InboxNotificationsClientImpl.getClient()
  const contextByDocStore = inboxClient.contextByDoc
  const notificationsByContextStore = inboxClient.inboxNotificationsByContext

  // Stores
  const metadataStore = provider.metadataStore
  const messagesStore = provider.messagesStore
  const isLoadingStore = provider.isLoadingStore
  const isTailLoadedStore = provider.isTailLoaded
  const newTimestampStore = provider.newTimestampStore
  const canLoadNextForwardStore = provider.canLoadNextForwardStore
  const isLoadingMoreStore = provider.isLoadingMoreStore

  const doc = message ?? channel
  const uuid = generateId()

  let groupedMessages: DailyPriorityGroupedMessages[] = []
  let messagesCount = 0

  // Elements
  let scroller: Scroller | undefined | null = undefined
  let scrollDiv: HTMLDivElement | undefined | null = undefined
  let contentDiv: HTMLDivElement | undefined | null = undefined
  let separatorDiv: HTMLDivElement | undefined | null = undefined

  // Dates
  let selectedDate: Timestamp | undefined = undefined
  let dateToJump: Timestamp | undefined = undefined

  // Scrolling
  let isScrollInitialized = false
  let shouldScrollToNew = false
  let isScrollAtBottom = false

  let isLatestMessageButtonVisible = false

  // Pagination
  let backwardRequested = false
  let restoreScrollTop = 0
  let restoreScrollHeight = 0

  let isPageHidden = false
  let lastMsgBeforeFreeze: Ref<ActivityMessage> | undefined = undefined
  let needUpdateTimestamp = false

  // Track last loaded project id for suggestions reload
  let lastLoadedProjectId: Ref<Project> | undefined = undefined
  let activeProjectId: Ref<Project> | undefined = undefined

  $: groupedMessages = $messagesStore
  $: notifyContext = $contextByDocStore.get(doc._id)
  $: isThread = hierarchy.isDerived(doc._class, activity.class.ActivityMessage)
  $: isChunterSpace = hierarchy.isDerived(doc._class, dailyPriorities.class.ChunterSpace)
  $: readonly = hierarchy.isDerived(channel._class, core.class.Space)
    ? readonly || (channel as Space).archived
    : readonly

  $: separatorIndex =
    $newTimestampStore !== undefined
      ? groupedMessages.findIndex((message) => (message.startOfDay ?? 0) >= ($newTimestampStore ?? 0))
      : -1

  $: if (!freeze && !isPageHidden && isScrollInitialized) {
    read()
  }

  $: isCardView = (viewType === '2-card' || viewType === '3-card' || viewType === '4-card') && !isThread

  const unsubscribe = inboxClient.inboxNotificationsByContext.subscribe(() => {
    if (notifyContext !== undefined && !isFreeze()) {
      recheckNotifications(notifyContext)
      read()
    }
  })

  $: void initializeScroll($isLoadingStore, separatorDiv, separatorIndex)
  $: adjustScrollPosition(selectedMessageId)
  $: void handleMessagesUpdated(groupedMessages.reduce((acc, group) => acc + (group?.creatorGroups?.length ?? 0), 0))

  $: availableDates = groupedMessages.map((group) => new Date(group.startOfDay))

  // No need to pick a single best issue; we will show all In Progress issues

  function buildMessageMarkup (iss: Issue): string {
    const text = `Focus: ${iss.identifier} ${iss.title}`
    return JSON.stringify({ type: 'doc', content: [{ type: 'paragraph', content: [{ type: 'text', text }] }] })
  }

  async function acceptTaskSuggestion (issue: Issue): Promise<void> {
    if (issue == null) return
    const ops = client.apply(
      undefined,
      'dailyPriorities.create.dailyPriorities:class:ChatMessage dailyPriorities:class:Channel'
    )
    const msg = buildMessageMarkup(issue)
    await ops.addCollection<Doc, ChatMessage>(
      dailyPrioritiesPkg.class.ChatMessage,
      'daily-updates-channel' as Ref<Space>,
      'daily-updates-channel' as Ref<Doc>,
      dailyPrioritiesPkg.class.Channel,
      'messages',
      {
        message: msg,
        attachments: 0,
        attachedToProject: issue.space,
        status: 'tracker:status:InProgress',
        dueDate: issue.dueDate ?? undefined
      } as any
    )
    await ops.commit()
    // Persist suppression and remove from the suggestion list
    addDismissedSuggestion(issue._id as unknown as string)
    suggestedIssues = suggestedIssues.filter((i) => i._id !== issue._id)
    if (suggestedIssues.length === 0) {
      showTaskSuggestionCard = false
    }
  }

  function getDismissKey (): string | null {
    try {
      const account = getCurrentAccount() as PersonAccount
      const personId = account?.person as unknown as string | undefined
      return personId != null && personId !== '' ? `dailypriorities:dismissedSuggestions:${personId}` : null
    } catch {
      return null
    }
  }

  function readDismissedSuggestions (): Set<string> {
    try {
      const key = getDismissKey()
      if (key == null) return new Set()
      const raw = localStorage.getItem(key)
      if (raw == null || raw === '') return new Set()
      const arr = JSON.parse(raw)
      return Array.isArray(arr) ? new Set(arr.map((x: unknown) => String(x))) : new Set()
    } catch {
      return new Set()
    }
  }

  function addDismissedSuggestion (issueId: string): void {
    try {
      const key = getDismissKey()
      if (key == null) return
      const current = readDismissedSuggestions()
      current.add(String(issueId))
      localStorage.setItem(key, JSON.stringify(Array.from(current)))
    } catch {
      // ignore storage errors
    }
  }

  function dismissIssue (issue: Issue): void {
    addDismissedSuggestion(issue._id as unknown as string)
    suggestedIssues = suggestedIssues.filter((i) => i._id !== issue._id)
    if (suggestedIssues.length === 0) {
      showTaskSuggestionCard = false
    }
  }

  async function loadTaskSuggestion (): Promise<void> {
    try {
      const account = getCurrentAccount() as PersonAccount
      if (account?.person == null) {
        showTaskSuggestionCard = false
        return
      }
      // Reset current suggestions while loading new context
      issues = []
      suggestedIssues = []
      showTaskSuggestionCard = true
      // Load all issues assigned to current user which are In Progress
      const projectFilter =
        activeProjectId != null && activeProjectId !== ALL_DAILY_UPDATES_PROJECT_ID ? { space: activeProjectId } : {}
      issues = await client.findAll<Issue>(tracker.class.Issue, {
        assignee: account.person,
        status: 'tracker:status:InProgress' as Ref<IssueStatus>,
        ...projectFilter
      })
      // Filter out dismissed suggestions from local storage
      const dismissed = readDismissedSuggestions()
      suggestedIssues = issues.filter((iss) => !dismissed.has(iss._id as unknown as string))
      if (suggestedIssues.length === 0) showTaskSuggestionCard = false
    } catch (err) {
      console.error('Failed to load task suggestion', err)
      showTaskSuggestionCard = false
    }
  }

  function adjustScrollPosition (selectedMessageId?: Ref<ActivityMessage>): void {
    if ($isLoadingStore || !isScrollInitialized) {
      return
    }
    const msgData = $metadataStore.find(({ _id }) => _id === selectedMessageId)
    if (msgData !== undefined) {
      const isReload = provider.jumpToMessage(msgData)
      if (isReload) {
        reinitializeScroll()
      } else {
        void scrollToMessage()
      }
    } else if (selectedMessageId === undefined) {
      provider.jumpToEnd()
      reinitializeScroll()
    }
  }

  function handleWindowFocus (): void {
    checkWindowVisibility(false)
  }

  function handleWindowBlur (): void {
    checkWindowVisibility(true)
  }

  function handleVisibilityChange (): void {
    checkWindowVisibility(document.hidden)
  }

  function checkWindowVisibility (hidden: boolean): void {
    if (document.hidden || !document.hasFocus() || hidden) {
      if (isPageHidden) return
      isPageHidden = true
      needUpdateTimestamp = true
      lastMsgBeforeFreeze = shouldScrollToNew ? groupedMessages[0]?.creatorGroups[0]?.messages[0]?._id : undefined
    } else {
      if (isPageHidden) {
        isPageHidden = false
        needUpdateTimestamp = false
      }
    }
  }

  function isFreeze (): boolean {
    return freeze || isPageHidden
  }

  function scrollToBottom (): void {
    if (scroller != null && scrollDiv != null && !isFreeze()) {
      scrollDiv.scroll({ top: 0, behavior: 'instant' })
      updateSelectedDate()
    }
  }

  function scrollToSeparator (): void {
    if (separatorDiv == null || scrollDiv == null || contentDiv == null) {
      return
    }

    // TODO: fix this
    // const messagesElements = contentDiv?.getElementsByClassName('activityMessage')
    // const messagesHeight = groupedMessages
    //   .slice(separatorIndex)
    //   .reduce((res, msg) => res + (messagesElements?.[msg._id as any]?.clientHeight ?? 0), 0)

    separatorDiv.scrollIntoView()

    // if (messagesHeight >= scrollDiv.clientHeight) {
    //   scroller?.scrollBy(-newSeparatorOffset)
    // }

    updateShouldScrollToNew()
    read()
  }

  async function scrollToMessage (): Promise<void> {
    if (selectedMessageId === undefined) return
    if (scrollDiv == null || contentDiv == null) {
      // Retry after a short delay if DOM elements aren't ready
      setTimeout(() => {
        void scrollToMessage()
      }, 50)
      return
    }

    // Check if the message exists in the current data
    const messageExists = groupedMessages?.some(({ creatorGroups }) =>
      creatorGroups?.some(({ messages }) => messages?.some(({ _id }) => _id === selectedMessageId))
    )

    if (!messageExists) {
      return
    }

    // Wait for the message element to be rendered in the DOM
    const messagesElements = contentDiv?.getElementsByClassName('activityMessage')
    const msgElement = messagesElements?.[selectedMessageId as any]

    if (msgElement == null) {
      // Retry with a longer delay if the element isn't rendered yet
      await new Promise((resolve) => setTimeout(resolve, 50))
      await tick()

      // Try one more time
      const retryElements = contentDiv?.getElementsByClassName('activityMessage')
      const retryElement = retryElements?.[selectedMessageId as any]

      if (retryElement != null) {
        retryElement.scrollIntoView({ block: 'start' })
      }
    } else {
      msgElement.scrollIntoView({ block: 'start' })
    }
    read()
  }

  function scrollToStartOfNew (): void {
    scrollToBottom()

    // TODO: fix this
    // if (scrollDiv == null || lastMsgBeforeFreeze === undefined) return
    // if (needUpdateTimestamp || $newTimestampStore === undefined) {
    //   void provider.updateNewTimestamp(notifyContext)
    //   needUpdateTimestamp = false
    // }
    // const lastIndex = groupedMessages.findIndex(({ creatorGroups }) => creatorGroups?.some(({ messages }) => messages?.some(({ _id }) => _id === lastMsgBeforeFreeze)))
    // if (lastIndex === -1) return
    // const firstNewMessage = groupedMessages.find(({ creatorGroups }, index) => index > lastIndex && creatorGroups?.some(({ createdBy }) => createdBy !== me._id))

    // if (firstNewMessage === undefined) {
    //   scrollToBottom()
    //   return
    // }

    // const messagesElements = contentDiv?.getElementsByClassName('activityMessage')
    // const msgElement = messagesElements?.[messagesElements?.length - 1]

    // if (msgElement == null) return

    // const messageRect = msgElement.getBoundingClientRect()
    // const topOffset = messageRect.top - newSeparatorOffset

    // if (topOffset < 0) {
    //   scroller?.scrollBy(topOffset)
    // } else if (topOffset > 0) {
    //   scroller?.scrollBy(topOffset)
    // }
  }

  function updateShouldScrollToNew (): void {
    if (scrollDiv != null && contentDiv != null) {
      const { scrollTop } = scrollDiv
      const offset = 100

      shouldScrollToNew = Math.abs(scrollTop) < offset
    }
  }

  async function wait (): Promise<void> {
    // One tick is not enough for messages to be rendered,
    // I think this is due to the fact that we are using a Component, which takes some time to load,
    // because after one tick I see spinners from Component
    await tick() // wait until the DOM is updated
    await tick() // wait until the DOM is updated
  }

  async function waitForDOMElement (elementId: string, maxAttempts = 10, delayMs = 50): Promise<boolean> {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const element = document.getElementById(elementId)
      if (element !== null) {
        return true
      }
      await new Promise((resolve) => setTimeout(resolve, delayMs))
      await tick()
    }
    return false
  }

  async function initializeScroll (
    isLoading: boolean,
    separatorElement?: HTMLDivElement | null,
    separatorIndex?: number
  ): Promise<void> {
    if (isLoading || isScrollInitialized) {
      return
    }

    const selectedMessageExists =
      selectedMessageId !== undefined &&
      groupedMessages.some(({ creatorGroups }) =>
        creatorGroups?.some(({ messages }) => messages?.some(({ _id }) => _id === selectedMessageId))
      )
    if (selectedMessageExists) {
      await wait()
      void scrollToMessage()
      isScrollInitialized = true
    } else if (separatorIndex === -1) {
      isScrollInitialized = true
      shouldScrollToNew = true
      isScrollAtBottom = true
    } else if (separatorElement != null) {
      await wait()
      scrollToSeparator()
      isScrollInitialized = true
    }

    if (isScrollInitialized) {
      await wait()
      updateSelectedDate()
      updateScrollData()
      updateDownButtonVisibility($metadataStore, groupedMessages, scrollDiv)
      loadMore()
    }
  }

  function reinitializeScroll (): void {
    isScrollInitialized = false
    void initializeScroll($isLoadingStore, separatorDiv, separatorIndex)
  }

  function handleJumpToDate (e: CustomEvent<{ date?: Timestamp }>): void {
    const result = jumpToDate(e, provider, uuid, scrollDiv)

    dateToJump = result.dateToJump

    if (result.scrollOffset !== undefined && result.scrollOffset !== 0 && scroller != null) {
      scroller?.scroll(result.scrollOffset)
    }
  }

  async function scrollToDate (date: Timestamp): Promise<void> {
    const day = getDay(date)
    const elementId = `${uuid}-${day.toString()}`

    // Wait for the DOM element to be rendered
    const elementFound = await waitForDOMElement(elementId)

    if (!elementFound) {
      // Element not found after waiting, clear dateToJump to prevent infinite retry
      dateToJump = undefined
      return
    }

    const offset = getScrollToDateOffset(date, uuid)

    if (offset !== undefined && offset !== 0 && scroller != null) {
      scroller?.scroll(offset)
      dateToJump = undefined
    } else {
      // Clear dateToJump even if scroll fails to prevent infinite retry
      // This could happen if offset calculation fails for other reasons
      dateToJump = undefined
    }
  }

  function updateSelectedDate (): void {
    if (isThread) return
    const newSelectedDate = getSelectedDate(provider, uuid, scrollDiv, contentDiv)
    selectedDate = newSelectedDate
  }

  function read (): void {
    if (isFreeze() || notifyContext === undefined || !isScrollInitialized) return
    readViewportMessages(groupedMessages, notifyContext._id, scrollDiv, contentDiv)
  }

  function updateScrollData (): void {
    if (scrollDiv == null) return
    const { scrollTop } = scrollDiv

    isScrollAtBottom = Math.abs(scrollTop) < 50
  }

  // function canGroupChatMessages (message: ActivityMessage, prevMessage?: ActivityMessage): boolean {
  //   let prevMetadata: MessageMetadata | undefined = undefined

  //   if (prevMessage === undefined) {
  //     const metadata = $metadataStore
  //     prevMetadata = metadata.find((_, index) => metadata[index + 1]?._id === message._id)
  //   }

  //   return canGroupMessages(message, prevMessage ?? prevMetadata)
  // }

  $: updateDownButtonVisibility($metadataStore, groupedMessages, scrollDiv)

  function updateDownButtonVisibility (
    metadata: MessageMetadata[],
    messages: DailyPriorityGroupedMessages[],
    scrollDiv?: HTMLDivElement | null
  ): void {
    if (metadata.length === 0 || messages.length === 0 || messages[0]?.creatorGroups?.length === 0) {
      isLatestMessageButtonVisible = false
      return
    }

    if (!$isTailLoadedStore) {
      isLatestMessageButtonVisible = true
    } else if (scrollDiv != null) {
      const { scrollTop } = scrollDiv

      isLatestMessageButtonVisible = Math.abs(scrollTop) > 200
    } else {
      isLatestMessageButtonVisible = false
    }
  }

  async function handleScrollToLatestMessage (): Promise<void> {
    selectedMessageId = undefined
    messageInFocus.set(undefined)

    const metadata = $metadataStore
    const lastMetadata = metadata[metadata.length - 1]
    const lastMessages =
      groupedMessages[groupedMessages.length - 1].creatorGroups?.find(
        (creatorGroup) => creatorGroup.createdBy === lastMetadata.createdBy
      )?.messages ?? []

    if (lastMessages?.find(({ _id }) => _id === lastMetadata._id) == null) {
      separatorIndex = -1
      provider.jumpToEnd(true)
      reinitializeScroll()
    } else {
      scrollToBottom()
    }

    await inboxClient.readDoc(doc._id)
  }

  let forceRead = false
  $: void forceReadContext(isScrollAtBottom, notifyContext)

  async function forceReadContext (isScrollAtBottom: boolean, context?: DocNotifyContext): Promise<void> {
    if (context === undefined || !isScrollAtBottom || forceRead || isFreeze()) return
    const { lastUpdateTimestamp = 0, lastViewedTimestamp = 0 } = context

    if (lastViewedTimestamp >= lastUpdateTimestamp) return

    const notifications = $notificationsByContextStore.get(context._id) ?? []
    const unViewed = notifications.filter(({ isViewed }) => !isViewed)

    if (unViewed.length === 0) {
      forceRead = true
      await inboxClient.readDoc(channel._id)
    }
  }

  function shouldLoadMoreUp (): boolean {
    if (scrollDiv == null) return false
    const { scrollHeight, scrollTop, clientHeight } = scrollDiv

    return scrollHeight + Math.ceil(scrollTop - clientHeight) <= loadMoreThreshold
  }

  function shouldLoadMoreDown (): boolean {
    if (scrollDiv == null) return false

    return Math.abs(scrollDiv.scrollTop) <= loadMoreThreshold
  }

  function loadMore (): void {
    if (!loadMoreAllowed || $isLoadingMoreStore || scrollDiv == null || !isScrollInitialized) {
      return
    }

    const minMsgHeightPx = minMsgHeightRem * parseFloat(getComputedStyle(document.documentElement).fontSize)
    const maxMsgPerScreen = Math.ceil(scrollDiv.clientHeight / minMsgHeightPx)
    const limit = Math.max(maxMsgPerScreen, provider.limit)
    const isLoadMoreUp = shouldLoadMoreUp()
    const isLoadMoreDown = shouldLoadMoreDown()

    if (!isLoadMoreUp && backwardRequested) {
      backwardRequested = false
    }

    const olderMessages =
      groupedMessages?.[0]?.creatorGroups?.[0]?.messages?.map(({ createdOn }) => createdOn ?? 0) ?? []
    const newerMessages =
      groupedMessages?.[groupedMessages.length - 1]?.creatorGroups?.[
        groupedMessages?.[groupedMessages?.length - 1]?.creatorGroups?.length - 1
      ]?.messages?.map(({ createdOn }) => createdOn ?? 0) ?? []

    if (isLoadMoreUp && !backwardRequested) {
      shouldScrollToNew = false
      restoreScrollTop = scrollDiv?.scrollTop ?? 0
      restoreScrollHeight = 0
      void provider.addNextChunk('backward', Math.min(...olderMessages), limit)
      backwardRequested = true
    } else if (isLoadMoreUp && backwardRequested) {
      restoreScrollTop = scrollDiv?.scrollTop ?? 0
    } else if (isLoadMoreDown && !$isTailLoadedStore) {
      restoreScrollTop = 0
      restoreScrollHeight = scrollDiv?.scrollHeight ?? 0
      shouldScrollToNew = false
      isScrollAtBottom = false
      void provider.addNextChunk('forward', Math.max(...newerMessages), limit)
    }
  }

  async function restoreScroll (): Promise<void> {
    await wait()

    if (scrollDiv == null || scroller == null) return

    if (restoreScrollTop !== 0) {
      scroller.scroll(restoreScrollTop)
    } else if (restoreScrollHeight !== 0) {
      const delta = restoreScrollHeight - scrollDiv.scrollHeight
      scroller.scroll(delta)
    }
    backwardRequested = false
    restoreScrollHeight = 0
    restoreScrollTop = 0
    // Don't clear dateToJump here - let it be handled in the main flow
  }

  function scrollToNewMessages (): void {
    if (scrollDiv == null || !shouldScrollToNew) {
      read()
      return
    }

    scrollToBottom()
    read()
  }

  async function handleMessagesUpdated (newCount: number): Promise<void> {
    if (newCount === messagesCount) {
      return
    }

    const prevCount = messagesCount
    messagesCount = newCount

    if (isFreeze()) {
      await wait()
      scrollToStartOfNew()
      return
    }

    if (restoreScrollTop !== 0 || restoreScrollHeight !== 0) {
      await restoreScroll()
      // After restoration, check if we still need to handle date jumping
      if (dateToJump !== undefined) {
        await wait()
        await scrollToDate(dateToJump)
      }
    } else if (dateToJump !== undefined) {
      await wait()
      await scrollToDate(dateToJump)
    } else if (shouldScrollToNew && prevCount > 0 && newCount > prevCount) {
      await wait()
      scrollToNewMessages()
    } else {
      await wait()
      read()
    }
  }

  async function handleScroll (): Promise<void> {
    updateScrollData()
    updateDownButtonVisibility($metadataStore, groupedMessages, scrollDiv)
    updateShouldScrollToNew()
    loadMore()
    updateSelectedDate()
    read()
  }

  function handleResize (): void {
    if (!isScrollInitialized) return
    if (shouldScrollToNew) {
      scrollToBottom()
    }

    loadMore()
  }

  const newMessageTxListener = (txes: Tx[]): void => {
    const ctx = txes
      .map((it) => it as TxCUD<ActivityMessage>)
      .filter((it) => it.attachedTo === doc._id && it._class === core.class.TxCreateDoc)

    if (ctx.length > 0 && shouldScrollToNew) {
      void wait().then(scrollToNewMessages)
    }
  }

  afterUpdate(() => {
    if (isFreeze()) {
      updateScrollData()
    }
  })

  onMount(() => {
    chatReadMessagesStore.update(() => new Set())
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleWindowFocus)
    window.addEventListener('blur', handleWindowBlur)
    addTxListener(newMessageTxListener)

    // Load task suggestion
    void loadTaskSuggestion()
  })

  // Determine active project from prop or provider
  $: activeProjectId = currentProjectId ?? provider?.projectId

  // Reload suggestions when project changes (either prop or provider)
  $: if (activeProjectId !== lastLoadedProjectId) {
    lastLoadedProjectId = activeProjectId
    void loadTaskSuggestion()
  }

  onDestroy(() => {
    unsubscribe()
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('focus', handleWindowFocus)
    window.removeEventListener('blur', handleWindowBlur)
    removeTxListener(newMessageTxListener)
  })

  $: showBlankView = !$isLoadingStore && groupedMessages.length === 0 && !isThread

  // Thread messages are not grouped, so we need to cast them to ActivityMessage[] to not break TS validation
  // Thread messages follow the same structure as regular chunter messages
  $: threadMessages = groupedMessages as unknown as ActivityMessage[]
</script>

<div
  class={`flex-col relative ${isCardView && !showBlankView ? `reverse-channel-scroll-view-${viewType}` : ''}`}
  class:h-full={fullHeight}
>
  {#if !isThread && groupedMessages.length > 0 && selectedDate}
    <div class="selectedDate">
      <JumpToDateSelector
        {selectedDate}
        {availableDates}
        fixed
        on:jumpToDate={handleJumpToDate}
        idPrefix={`${uuid}-`}
      />
    </div>
  {/if}

  {#if !showBlankView && !isThread}
    <OutToday project={allProjects.find((project) => project._id === currentProjectId) ?? ALL_DAILY_UPDATES_PROJECT} />
  {/if}

  {#if !isThread && showTaskSuggestionCard && suggestedIssues.length > 0}
    <TaskSuggestion
      title="Daily Priority Suggestion"
      issues={suggestedIssues}
      onAccept={(issue) => {
        if (issue) void acceptTaskSuggestion(issue)
      }}
      onDismiss={(issue) => {
        if (issue) dismissIssue(issue)
      }}
    />
  {/if}

  <BaseChatScroller
    bind:scroller
    bind:scrollDiv
    bind:contentDiv
    bottomStart={!showBlankView}
    loadingOverlay={$isLoadingStore || !isScrollInitialized}
    onScroll={handleScroll}
    onResize={handleResize}
  >
    {#if showBlankView}
      <BlankView
        icon={dailyPriorities.icon.Thread}
        header={dailyPriorities.string.NoMessagesInChannel}
        label={readonly ? undefined : dailyPriorities.string.SendMessagesInChannel}
        project={allProjects.find((project) => project._id === currentProjectId) ?? ALL_DAILY_UPDATES_PROJECT}
        {isThread}
      />
    {/if}

    {#if loadMoreAllowed && !isThread && $isLoadingMoreStore}
      <HistoryLoading isLoading={$isLoadingMoreStore} />
    {/if}

    <slot name="header" />

    {#if !isThread}
      {#each groupedMessages as dailyPriorityGroupedMessages, index (dailyPriorityGroupedMessages.startOfDay)}
        {#if separatorIndex === index}
          <ActivityMessagesSeparator bind:element={separatorDiv} label={activity.string.New} />
        {/if}

        {#if !isThread}
          <JumpToDateSelector
            idPrefix={`${uuid}-`}
            visible={selectedDate !== dailyPriorityGroupedMessages.startOfDay}
            selectedDate={dailyPriorityGroupedMessages.startOfDay}
            {availableDates}
            on:jumpToDate={handleJumpToDate}
          />
        {/if}

        {#each dailyPriorityGroupedMessages.creatorGroups as creatorGroup (creatorGroup.createdBy)}
          <MessageCard {creatorGroup} {allProjects} {readonly} />
        {/each}
      {/each}
    {:else}
      {#each threadMessages as message (message._id)}
        {@const isSelected = message._id === selectedMessageId}
        <ActivityMessagePresenter
          {doc}
          value={message}
          skipLabel={isThread || isChunterSpace}
          hoverStyles="filledHover"
          attachmentImageSize="x-large"
          type={'default'}
          isHighlighted={isSelected}
          shouldScroll={false}
          {readonly}
        />
      {/each}
    {/if}

    {#if groupedMessages.length > 0}
      <div class="h-4" />
    {/if}

    {#if loadMoreAllowed && $canLoadNextForwardStore}
      <HistoryLoading isLoading={$isLoadingMoreStore} />
    {/if}
    {#if !fixedInput && withInput && isThread}
      <ChannelInput
        channelOrMessage={message ?? channel}
        {readonly}
        boundary={scrollDiv}
        {collection}
        {isThread}
        {autofocus}
      />
    {/if}
  </BaseChatScroller>
  {#if !isThread && isLatestMessageButtonVisible}
    <div class="down-button absolute">
      <ModernButton
        label={dailyPriorities.string.LatestMessages}
        shape="round"
        size="small"
        kind="primary"
        on:click={handleScrollToLatestMessage}
      />
    </div>
  {/if}
</div>

{#if fixedInput && withInput && isThread}
  <ChannelInput
    channelOrMessage={message ?? channel}
    {readonly}
    boundary={scrollDiv}
    {collection}
    {isThread}
    {autofocus}
  />
{/if}

{#if readonly}
  <div class="h-6" />
{/if}

<style lang="scss">
  .selectedDate {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: transparent;
  }

  .down-button {
    width: 100%;
    display: flex;
    justify-content: center;
    bottom: 0.5rem;
    animation: 0.5s fadeIn;
    animation-fill-mode: forwards;
    visibility: hidden;
  }

  @keyframes fadeIn {
    99% {
      visibility: hidden;
    }
    100% {
      visibility: visible;
    }
  }

  // Base styles for all card views
  :global(
      .reverse-channel-scroll-view-2-card .box,
      .reverse-channel-scroll-view-3-card .box,
      .reverse-channel-scroll-view-4-card .box
    ) {
    flex-direction: row !important;
    flex-wrap: wrap !important;
    gap: 0.5rem;
  }

  // 2-card layout (2 columns)
  :global(.reverse-channel-scroll-view-2-card .message-group-card) {
    flex: 0 1 calc(50% - 1rem) !important;
    margin: 0.25rem;
    min-width: calc(50% - 1rem);
  }

  // 3-card layout (3 columns)
  :global(.reverse-channel-scroll-view-3-card .message-group-card) {
    flex: 0 1 calc(33.3% - 1rem) !important;
    margin: 0.25rem;
    min-width: calc(33.3% - 1rem);
  }

  // 4-card layout (4 columns)
  :global(.reverse-channel-scroll-view-4-card .message-group-card) {
    flex: 0 1 calc(25% - 1rem) !important;
    margin: 0.25rem;
    min-width: calc(25% - 1rem);
  }

  // Date selectors and Who's Out section should always span full width
  :global(
      .reverse-channel-scroll-view-2-card .dateSelector,
      .reverse-channel-scroll-view-3-card .dateSelector,
      .reverse-channel-scroll-view-4-card .dateSelector
    ) {
    flex: 1 1 100% !important;
  }

  .message-group-card {
    background-color: var(--theme-navpanel-color);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.75rem;
    padding: 0.5rem;
    margin: 0.5rem 0.5rem;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
  }

  .ts-meta-inline {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.35rem;
    flex-wrap: wrap;
  }

  /* moved to TaskSuggestion */
  .ts-chip {
    display: contents;
  }
  .ts-chip-text {
    display: contents;
  }
  .due-inline {
    display: contents;
  }

  .priority-inline {
    margin-top: 0.25rem;
  }

  .meta-inline {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
  }

  .task-suggestion-card {
    margin-bottom: 0.5rem;
  }

  /* moved to TaskSuggestion */
  .ts-header-inline {
    display: contents;
  }

  .ts-id {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 600;
    font-size: 0.85rem;
    background: var(--theme-toggle-bg-color);
    border: 1px solid var(--theme-popup-divider);
    border-radius: 0.375rem;
    padding: 0.1rem 0.4rem;
    line-height: 1;
  }

  /* moved to TaskSuggestion */
  .ts-id-text {
    display: contents;
  }

  /* moved to TaskSuggestion */
  .ts-title {
    display: contents;
  }
</style>
