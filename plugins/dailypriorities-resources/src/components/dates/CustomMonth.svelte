<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { afterUpdate, createEventDispatcher } from 'svelte'
  import { capitalizeFirstLetter } from '@hcengineering/ui/src/utils'
  import ButtonIcon from '@hcengineering/ui/src/components/ButtonIcon.svelte'
  import IconChevronLeft from '@hcengineering/ui/src/components/icons/ChevronLeft.svelte'
  import IconChevronRight from '@hcengineering/ui/src/components/icons/ChevronRight.svelte'
  import {
    ICell,
    TCellStyle,
    areDatesEqual,
    day,
    daysInMonth,
    firstDay,
    fromCurrentToTz,
    getMonthName,
    getUserTimezone,
    getWeekDayName
  } from '@hcengineering/ui/src/components/calendar/internal/DateUtils'

  export let currentDate: Date | null
  export let noDateSelected: boolean = false
  export let mondayStart: boolean = true
  export let timeZone: string = getUserTimezone()
  export let hideNavigator: boolean = false
  export let replacementDay: boolean = false
  export let availableDates: Date[] = []

  const dispatch = createEventDispatcher()

  let monthYear: string
  const today: Date = new Date(Date.now())
  const viewDate: Date = new Date(currentDate ?? today)
  let selectedDate: Date | null = noDateSelected ? null : new Date(currentDate ?? today)
  $: firstDayOfCurrentMonth = firstDay(viewDate, mondayStart)
  const isToday = (n: number): boolean => {
    if (areDatesEqual(today, new Date(viewDate.getFullYear(), viewDate.getMonth(), n))) return true
    return false
  }

  // Function to check if a date is available
  function isDateAvailable (date: Date): boolean {
    const isAvailable = availableDates.some(availableDate => areDatesEqual(availableDate, date))
    return isAvailable
  }

  // Function to check if a month has any available dates
  function hasAvailableDatesInMonth (year: number, month: number): boolean {
    const daysInMonth = new Date(year, month + 1, 0).getDate()
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day)
      if (isDateAvailable(date)) {
        return true
      }
    }
    return false
  }

  // Function to find the next month with available dates
  function findNextMonthWithDates (currentYear: number, currentMonth: number): { year: number, month: number } {
    let year = currentYear
    let month = currentMonth + 1

    // Check up to 12 months ahead
    for (let i = 0; i < 12; i++) {
      if (month > 11) {
        month = 0
        year++
      }

      if (hasAvailableDatesInMonth(year, month)) {
        return { year, month }
      }

      month++
    }

    // If no future months found, return current month
    return { year: currentYear, month: currentMonth }
  }

  // Function to find the previous month with available dates
  function findPreviousMonthWithDates (currentYear: number, currentMonth: number): { year: number, month: number } {
    let year = currentYear
    let month = currentMonth - 1

    // Check up to 12 months back
    for (let i = 0; i < 12; i++) {
      if (month < 0) {
        month = 11
        year--
      }

      if (hasAvailableDatesInMonth(year, month)) {
        return { year, month }
      }

      month--
    }

    // If no past months found, return current month
    return { year: currentYear, month: currentMonth }
  }

  let days: ICell[] = []
  const getDateStyle = (date: Date): TCellStyle => {
    if (selectedDate != null) {
      const zonedTime = fromCurrentToTz(selectedDate, timeZone)
      if (areDatesEqual(zonedTime, date)) {
        return 'selected'
      }
    }
    return 'not-selected'
  }
  const renderCellStyles = (): void => {
    days = []
    for (let i = 1; i <= daysInMonth(viewDate); i++) {
      const tempDate = new Date(viewDate.getFullYear(), viewDate.getMonth(), i)
      days.push({
        dayOfWeek: tempDate.getDay() === 0 ? 7 : tempDate.getDay(),
        style: getDateStyle(tempDate)
      })
    }
    days = days
    monthYear = capitalizeFirstLetter(getMonthName(viewDate)) + ' ' + viewDate.getFullYear()
  }

  afterUpdate(() => {
    if (viewDate) renderCellStyles()
  })
</script>

<div class="month-container">
  <div class="header">
    {#if viewDate}
      <div class="monthYear font-medium-14">{monthYear}</div>
      <slot name="header" />
      {#if !hideNavigator}
        <div class="flex-row-center flex-no-shrink gap-2 tertiary-textColor">
          <ButtonIcon
            icon={IconChevronLeft}
            kind={'tertiary'}
            size={'extra-small'}
            inheritColor
            on:click={() => {
              const { year, month } = findPreviousMonthWithDates(viewDate.getFullYear(), viewDate.getMonth())
              viewDate.setFullYear(year)
              viewDate.setMonth(month)
              viewDate.setDate(1)
              renderCellStyles()
            }}
          />
          <ButtonIcon
            icon={IconChevronRight}
            kind={'tertiary'}
            size={'extra-small'}
            inheritColor
            on:click={() => {
              const { year, month } = findNextMonthWithDates(viewDate.getFullYear(), viewDate.getMonth())
              viewDate.setFullYear(year)
              viewDate.setMonth(month)
              viewDate.setDate(1)
              renderCellStyles()
            }}
          />
        </div>
      {/if}
    {/if}
  </div>

  {#if viewDate}
    <div class="caption">
      {#each [...Array(7).keys()] as dayOfWeek}
        <span class="weekdays ui-regular-12">
          {capitalizeFirstLetter(getWeekDayName(day(firstDayOfCurrentMonth, dayOfWeek), 'short'))}
        </span>
      {/each}
    </div>
    <div class="calendar">
      {#each days as day, i}
        {@const currentDate = new Date(viewDate.getFullYear(), viewDate.getMonth(), i + 1)}
        {@const isDisabled = !isDateAvailable(currentDate)}
        <button
          class="day ui-regular-14 {day.style}"
          class:today={isToday(i + 1)}
          class:day-off={day.dayOfWeek > 5}
          class:disabled={isDisabled}
          style="grid-column: {mondayStart ? day.dayOfWeek : day.dayOfWeek === 7 ? 1 : day.dayOfWeek + 1}/{mondayStart
            ? day.dayOfWeek + 1
            : day.dayOfWeek === 7
              ? 2
              : day.dayOfWeek + 2};"
          on:click|stopPropagation={() => {
            if (isDisabled) return
            viewDate.setDate(i + 1)
            selectedDate = new Date(viewDate)
            dispatch('update', viewDate)
          }}
        >
          <slot day={{ display: i + 1, date: currentDate }}>
            {i + 1}
          </slot>
          {#if $$slots.default && !replacementDay}{i + 1}{/if}
        </button>
      {/each}
    </div>
  {/if}
</div>

<style lang="scss">
  .month-container {
    display: flex;
    flex-direction: column;
    min-width: 0;
    min-height: 0;
    width: 100%;
    height: 100%;
    border-radius: var(--medium-BorderRadius);

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-2) var(--spacing-2) var(--spacing-2) var(--spacing-2_75);

      .monthYear {
        white-space: nowrap;
        word-break: break-all;
        text-overflow: ellipsis;
        overflow: hidden;
        min-width: 0;
        color: var(--global-primary-TextColor);

        &::first-letter {
          text-transform: capitalize;
        }
      }
    }
  }

  .caption,
  .calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);

    .weekdays,
    .day {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 2rem;
      height: 2rem;
      margin: 0.125rem;
      color: var(--content-color);
    }
  }
  .caption {
    padding: 0 var(--spacing-2);

    .weekdays {
      height: 2rem;
      color: var(--dark-color);
      &::first-letter {
        text-transform: capitalize;
      }
    }
  }
  .calendar {
    padding: var(--spacing-1) var(--spacing-2) var(--spacing-2);

    .day {
      position: relative;
      margin: 0;
      padding: 0;
      color: var(--accent-color);
      background-color: rgba(var(--accent-color), 0.05);
      border: 1px solid transparent;
      border-radius: 0.25rem;
      outline: none;

      &.day-off {
        color: var(--content-color);
      }
      &.disabled {
        cursor: default;
        color: #8a8f98;
      }
      &.disabled:hover {
        color: var(--content-color);
        background-color: transparent;
      }
      &.focused {
        box-shadow: 0 0 0 3px var(--primary-button-outline);
      }
      &:hover {
        color: var(--caption-color);
        background-color: var(--primary-button-transparent);
      }
      &.today:not(.selected) {
        font-weight: 700;
        color: var(--global-primary-LinkColor);
      }
      &.selected {
        font-weight: 700;
        color: var(--primary-button-color);
        background-color: var(--primary-button-default);
        cursor: default;
      }

      &:before {
        content: '';
        position: absolute;
        inset: -0.625rem;
      }
    }
  }
</style>
