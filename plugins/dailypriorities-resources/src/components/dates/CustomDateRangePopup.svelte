<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { DateRangeMode, Timestamp } from '@hcengineering/core'
  import { createEventDispatcher } from 'svelte'
  import CustomMonth from './CustomMonth.svelte'
  import Shifts from '@hcengineering/ui/src/components/calendar/Shifts.svelte'
  import Button from '@hcengineering/ui/src/components/Button.svelte'
  import ui from '@hcengineering/ui'

  export let selectedDate: Timestamp | undefined
  export let noDateSelected: boolean = false
  export let direction: 'before' | 'after' = 'after'
  export let minutes: number[] = [5, 15, 30]
  export let hours: number[] = [1, 2, 4, 8, 12]
  export let days: number[] = [1, 3, 7, 30]
  export let shift: boolean = false
  export let mode: DateRangeMode = DateRangeMode.DATE
  export let availableDates: Date[] = []

  const dispatch = createEventDispatcher()

  const today = selectedDate != null ? new Date(selectedDate) : new Date(Date.now())
  const startDate = new Date(0)

  $: defaultDate =
    mode === DateRangeMode.TIME
      ? new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate(),
        today.getHours(),
        today.getMinutes()
      )
      : today
  $: currentDate = noDateSelected ? null : defaultDate
  const mondayStart: boolean = true

</script>

<div class="month-popup-container">
  {#if mode !== DateRangeMode.TIME}
    <CustomMonth
      bind:currentDate
      {mondayStart}
      {noDateSelected}
      {availableDates}
      on:update={(result) => {
        if (result.detail !== undefined) {
          const selectedDate = result.detail
          dispatch('close', selectedDate)
        }
      }}
    />
  {/if}
  <Shifts
    {currentDate}
    on:change={(evt) => (currentDate = evt.detail)}
    {direction}
    {days}
    {minutes}
    {hours}
    {shift}
    {mode}
  />
  {#if selectedDate != null}
    <div class="clear-button-container">
      <Button label={ui.string.ClearDate} on:click={() => dispatch('close', undefined)} />
    </div>
  {/if}
</div>

<style lang="scss">
  .month-popup-container {
    position: relative;
    background: var(--popup-bg-color);
    border-radius: 0.5rem;
    box-shadow: var(--popup-shadow);
  }

  .clear-button-container {
    display: flex;
    justify-content: flex-end;
    margin: 0 0.5rem 0.5rem 0;
  }
</style>
