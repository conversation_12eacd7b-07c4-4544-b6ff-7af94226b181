<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Icon, Label } from '@hcengineering/ui'
  import { Asset, IntlString } from '@hcengineering/platform'
  import { Project } from '@hcengineering/tracker'
  import OutToday from './OutToday.svelte'

  export let icon: Asset
  export let header: IntlString
  export let label: IntlString | undefined = undefined
  export let project: Project | null = null
  export let isThread: boolean = false

</script>

<div class="root">
  <!-- {#if !isThread} -->
  <OutToday {project} />
  <!-- {/if} -->
  <div class="text-container">
    <Icon {icon} size="large" />
    <div class="an-element__label header">
      <Label label={header} />
    </div>
    {#if label}
      <span class="an-element__label">
        <Label {label} />
      </span>
    {/if}
  </div>
</div>

<style lang="scss">
  .root {
    display: flex;
    flex-direction: column;
    align-items: space-between;
    justify-content: center;
    height: 70vh;
    width: 100%;
  }

  .text-container {
    display: flex;
    align-self: center;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 100%;
    width: 30rem;
  }

  .header {
    font-weight: 600;
    margin: 1rem;
  }
</style>
