<!--
// Copyright © 2024 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import type { Widget } from '@hcengineering/workbench'
  import dailyPriorities from '../plugin'
  import { onMount, createEventDispatcher } from 'svelte'
  import { getBambooHRData } from '../api'
  import { Label, SearchInput, Loading, SectionEmpty, IconCalendar, IconSearch, ListView, Panel, Button, IconClose, navigate } from '@hcengineering/ui'
  import { Avatar, getContactLink } from '@hcengineering/contact-resources'
  import contact from '@hcengineering/contact'
  import { getClient } from '@hcengineering/presentation'

  export let widget: Widget | undefined
  export let height: string
  export let width: string
  // Mark as used to satisfy linter while still exposing contract for parent
  $: void widget

  type EnrichedTimeOff = {
    id: number
    type: string
    employeeId: number
    name: string
    email?: string
    start: string
    end: string
  }

  let loading = true
  let error: string | null = null
  let items: EnrichedTimeOff[] = []
  let searchValue = ''
  const dispatch = createEventDispatcher()
  $: filtered = items
    .filter((it) => {
      const q = searchValue.trim().toLowerCase()
      if (q.length === 0) return true
      const key = canonicalType(it.type)
      const label = typeLabel(key).toLowerCase()
      return (
        it.name.toLowerCase().includes(q) ||
        (it.email?.toLowerCase().includes(q) ?? false) ||
        key.includes(q) ||
        label.includes(q)
      )
    })
    .sort((a, b) => a.start.localeCompare(b.start))

  const toStartOfDay = (d: Date): Date => new Date(d.getFullYear(), d.getMonth(), d.getDate())
  const addDays = (d: Date, days: number): Date => {
    const r = new Date(d)
    r.setDate(r.getDate() + days)
    return r
  }
  const getMonday = (d: Date): Date => {
    const r = toStartOfDay(d)
    const day = r.getDay() // 0=Sun,1=Mon,...
    const diff = (day + 6) % 7 // days since Monday
    r.setDate(r.getDate() - diff)
    return r
  }
  const isBetween = (d: Date, start: Date, end: Date): boolean => d >= start && d <= end
  const canonicalType = (t?: string): string => {
    const s = (t ?? '').toLowerCase().trim()
    if (s.includes('vacation')) return 'vacation'
    if (s.includes('work from home') || s === 'wfh' || s.includes('wfh')) return 'wfh'
    if (s.includes('sick')) return 'sick'
    if (s.includes('holiday')) return 'holiday'
    if (s.includes('personal')) return 'personal'
    if (s.includes('unpaid')) return 'unpaid'
    if (s.includes('parental') || s.includes('maternity') || s.includes('paternity')) return 'parental'
    if (s.includes('bereav')) return 'bereavement'
    if (s.includes('train')) return 'training'
    return 'other'
  }
  const typeLabel = (key: string): string => {
    switch (key) {
      case 'vacation':
        return 'Vacation'
      case 'wfh':
        return 'Work From Home'
      case 'sick':
        return 'Sick'
      case 'holiday':
        return 'Holiday'
      case 'personal':
        return 'Personal'
      case 'unpaid':
        return 'Unpaid'
      case 'parental':
        return 'Parental Leave'
      case 'bereavement':
        return 'Bereavement'
      case 'training':
        return 'Training'
      default:
        return 'Out'
    }
  }
  const typeEmoji = (t?: string): string => {
    const k = (t ?? '').toLowerCase()
    switch (k) {
      case 'vacation':
      case 'pto':
      case 'annual leave':
        return '🌴'
      case 'holiday':
        return '🎉'
      case 'sick':
      case 'sick leave':
        return '🤒'
      case 'wfh':
        return '🏠'
      case 'personal':
        return '🧘'
      case 'unpaid':
      case 'unpaid leave':
        return '💤'
      case 'parental':
      case 'maternity':
      case 'paternity':
        return '👶'
      case 'bereavement':
        return '🕊️'
      case 'training':
        return '📚'
      default:
        return '🗓️'
    }
  }
  const getReturnText = (end: string): string => {
    try {
      const endDate = toStartOfDay(new Date(end))
      const returnDate = addDays(endDate, 1)
      const today = toStartOfDay(new Date())
      const diffDays = Math.round((returnDate.getTime() - today.getTime()) / 86400000)

      if (diffDays === 0) return 'Returns Today'
      if (diffDays === 1) return 'Returns Tomorrow'

      const monday = getMonday(today)
      const nextWeekStart = addDays(monday, 7)
      const nextWeekEnd = addDays(nextWeekStart, 6)
      if (isBetween(returnDate, nextWeekStart, nextWeekEnd)) return 'Returns Next week'

      return `Returns ${returnDate.toLocaleDateString(undefined, { month: 'long', day: 'numeric' })}`
    } catch {
      return `Returns ${end}`
    }
  }

  onMount(async () => {
    try {
      loading = true
      error = null
      items = await getBambooHRData()
    } catch (e: any) {
      error = e?.message ?? 'Failed to load data'
    } finally {
      loading = false
    }
  })

  // Cache resolved persons to avoid repeated queries per row
  const personPromiseCache = new Map<string, Promise<any | undefined>>()
  const personKey = (it: EnrichedTimeOff): string =>
    it.email && it.email.includes('@') ? it.email.toLowerCase() : `name:${it.name.trim().toLowerCase()}`

  async function getPerson (it: EnrichedTimeOff): Promise<any | undefined> {
    const key = personKey(it)
    const cached = personPromiseCache.get(key)
    if (cached !== undefined) return cached

    const p = (async () => {
      try {
        const client = getClient()
        let person: any | undefined = undefined

        // Try resolve by email via PersonAccount
        if (it.email && it.email.includes('@')) {
          const account = await client.findOne(contact.class.PersonAccount, { email: it.email })
          if (account !== undefined) {
            person = await client.findOne(contact.class.Person, { _id: account.person })
          }
        }

        // Fallback: try match active employees by name
        if (person === undefined) {
          const matches = await client.findAll(
            contact.mixin.Employee,
            { name: { $like: `%${it.name}%` }, active: true },
            { limit: 10 }
          )
          const lower = it.name.trim().toLowerCase()
          person = matches.find((e: any) => (e.name ?? '').trim().toLowerCase() === lower) ?? matches[0]
        }

        return person
      } catch {
        return undefined
      }
    })()

    personPromiseCache.set(key, p)
    return p
  }

  async function openContact (it: EnrichedTimeOff): Promise<void> {
    try {
      const person = await getPerson(it)

      if (person !== undefined) {
        const loc = await getContactLink(person)
        navigate(loc)
      } else {
        // Silent fail to avoid noisy UI; useful during data sync gaps
        // console.warn('WhoIsOut: contact not found for', it)
      }
    } catch (err) {
      // console.error('WhoIsOut: failed to open contact', err)
    }
  }
</script>

<div class="whoisout" style={`height:${height}; width:${width}`}>
<Panel embedded hideSearch={false} allowClose={false} adaptive="disabled" on:close>
  <svelte:fragment slot="title">
    <Label label={dailyPriorities.string.WhosOut} />
  </svelte:fragment>
  <svelte:fragment slot="search">
    <SearchInput bind:value={searchValue} collapsed />
  </svelte:fragment>
  <svelte:fragment slot="actions">
    <Button kind={'icon'} icon={IconClose} on:click={() => dispatch('close')} />
  </svelte:fragment>

  {#if loading}
    <div class="content">
      <Loading shrink>
        <Label label={dailyPriorities.string.Loading} />
      </Loading>
    </div>
  {:else if error}
    <div class="content error">{error}</div>
  {:else}
    {#if items.length === 0}
      <SectionEmpty icon={IconCalendar} label={dailyPriorities.string.NoEmployeesOut} />
    {:else}
      {#if filtered.length === 0 && searchValue.trim().length > 0}
        <SectionEmpty icon={IconSearch} label={dailyPriorities.string.NoSearchResults} labelParams={{ searchValue }} />
      {/if}
      <ListView count={filtered.length} addClass={'card'}>
        <svelte:fragment slot="item" let:item>
          {@const it = filtered[item]}
          <div
            class="content-row clickable"
            role="button"
            tabindex="0"
            on:click={() => openContact(it)}
            on:keydown={(e) => (e.key === 'Enter' || e.key === ' ') && openContact(it)}
          >
            <div class="left">
              {#await getPerson(it) then person}
                {#if person}
                  <Avatar person={person} name={it.name} size="small" />
                {:else}
                  <Avatar name={it.name} size="small" />
                {/if}
              {:catch}
                <Avatar name={it.name} size="small" />
              {/await}
            </div>
            <div class="right">
              <div class="name">{it.name}</div>
              <div class="sub">{typeEmoji(canonicalType(it.type))} {typeLabel(canonicalType(it.type))} · {getReturnText(it.end)}</div>
            </div>
          </div>
        </svelte:fragment>
      </ListView>
    {/if}
  {/if}
</Panel>
</div>

<style lang="scss">
  /* Ensure panel fills wrapper */
  .whoisout :global(.popupPanel.panel) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .content.error {
    color: var(--theme-danger-color);
    font-size: 0.9rem;
  }
  /* Card-styled list items via ListView */
  :global(.list-item.card) {
    padding: 0.75rem;
    border: 1px solid var(--theme-button-border);
    border-radius: 0.5rem;
    background-color: var(--theme-comp-BackgroundColor);
  }
  .content-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  .clickable { cursor: pointer; }
  .clickable:hover .name { text-decoration: underline; }
  .left {
    width: 1.5rem;
    text-align: center;
  }
  .name {
    font-weight: 500;
  }
  .sub {
    font-size: 0.85rem;
    color: var(--theme-disabled-text-color);
  }
</style>
