<script lang="ts">
  import { onMount } from 'svelte'
  import dailyPriorities from '../plugin'
  import { Label, tooltip as tp } from '@hcengineering/ui'
  import { Avatar, personByIdStore, personAccountByPersonId, personAccountByIdStore } from '@hcengineering/contact-resources'
  import { type Ref } from '@hcengineering/core'
  import { getBambooHRData } from '../api'
  import { Project } from '@hcengineering/tracker'
  import { ALL_DAILY_UPDATES_PROJECT_ID } from './chat/constants'
  export let project: Project | null = null

  // BambooHR data state
  let whosOutData: any[] = []
  let isLoadingWhosOut = false
  let whosOutError: string | null = null

  $: persons = Array.from($personByIdStore.values())
  $: personAccountsByPersonId = $personAccountByPersonId
  $: personAccountsById = $personAccountByIdStore
  $: employees = whosOutData.map((employee) => {
    let person: any = persons.find(p => getPersonEmail(p._id) === employee.email)

    if (project != null && project._id !== ALL_DAILY_UPDATES_PROJECT_ID) {
      const projectMemberEmails = project.members.map(member => {
        const memberAccount = personAccountsById.get(member as any)
        const memberPerson = persons.find(p => p._id === memberAccount?.person)

        if (memberPerson == null) {
          return null
        }

        return getPersonEmail(memberPerson._id)
      })

      if (!projectMemberEmails.includes(employee.email)) {
        return null
      }
    }

    if (person == null) {
      person = {
        name: employee.name.replaceAll(' ', ','),
        email: employee.email
      }
    }

    return {
      ...employee,
      person
    }
  }).filter(employee => employee != null).sort((a, b) => a.person.name.localeCompare(b.person.name))

  // Helper function to get email for a person
  function getPersonEmail (personId: Ref<any>): string | undefined {
    const personAccounts = personAccountsByPersonId.get(personId)
    return personAccounts?.[0]?.email
  }

  // Helper function to convert icon names to emojis
  function getIconEmoji (iconName: string): string {
    const iconMap: Record<string, string> = {
      'palm-trees': '🏖️',
      'time-off-luggage': '⏰',
      'first-aid-kit': '🤒',
      home: '🏠'
    }
    return iconMap[iconName] ?? '📅'
  }

    // BambooHR data fetching functions
    async function fetchWhosOutData (): Promise<void> {
      if (isLoadingWhosOut) return

      isLoadingWhosOut = true
      whosOutError = null

      try {
        const allOut = await getBambooHRData()
        whosOutData = allOut ?? []
      } catch (error) {
        console.error('Failed to fetch who\'s out data:', error)
        whosOutError = error instanceof Error ? error.message : 'Failed to fetch data'
        whosOutData = []
      } finally {
        isLoadingWhosOut = false
      }
    }

  const getTooltipLabel = (employee: any) => {
    if (employee.type === 'Work From Home') {
      return dailyPriorities.string.WorkFromHome
    } else if (employee.type.startsWith('Vacation')) {
      return dailyPriorities.string.Vacation
    } else if (employee.type === 'Sick Leave') {
      return dailyPriorities.string.SickLeave
    } else if (employee.type === 'Absence in hours') {
      return dailyPriorities.string.AbsenceInHours
    } else if (employee.type === 'Marriage') {
      return dailyPriorities.string.Marriage
    } else if (employee.type === 'Parental') {
      return dailyPriorities.string.Parental
    } else if (employee.type === 'Passing away relatives') {
      return dailyPriorities.string.PassingAwayRelatives
    } else if (employee.type === 'Blood Donation') {
      return dailyPriorities.string.BloodDonation
    } else {
      return dailyPriorities.string.Other
    }
  }

  onMount(() => {
    void fetchWhosOutData()
  })
</script>

<div class="out-today">
    <div class="out-today-title-container" style={!isLoadingWhosOut && employees.length === 0 ? 'justify-content: start; margin-bottom: 0' : ''}>
        <h4>
            <Label label={dailyPriorities.string.WhosOut} />
        </h4>
        {#if !isLoadingWhosOut && employees.length === 0}
            <div class="out-today-subtitle">
                <Label label={project?._id === ALL_DAILY_UPDATES_PROJECT_ID ? dailyPriorities.string.NoEmployeesOutSubtitle : dailyPriorities.string.NoEmployeesOutSubtitle2} />
            </div>
        {/if}
    </div>

    {#if isLoadingWhosOut}
    <div class="loading-indicator">
        <Label label={dailyPriorities.string.Loading} />
    </div>
    {:else if whosOutError}
    <div class="error-message">
        <div class="error-text">{whosOutError}</div>
        <button class="retry-button" on:click={fetchWhosOutData}>
        Retry
        </button>
    </div>
    {:else if employees.length > 0}
    <div class="out-today-employees-container">
        {#each employees as employee}
        {@const person = persons.find(p => getPersonEmail(p._id) === employee.email) ?? employee.person}
        {#if person}
            {@const nameParts = person?.name.split(',').reverse()}
            <div class="out-today-employee" use:tp={{ label: getTooltipLabel(employee) }}>
            <div class="avatar-container">
                <Avatar {person} size="medium" name={person?.name} />
                {#if employee.icon}
                <div class="avatar-icon"/>
                {/if}
            </div>
            {#if employee.icon}
                <div class="emoji-container">
                    {getIconEmoji(employee.icon)}
                </div>
            {/if}
            <div class="out-today-employee-name">
                {#if nameParts != null && nameParts.length >= 2}
                <div class="name-first">{nameParts[0].trim()}</div>
                <div class="name-last">{nameParts[1].trim()}</div>
                {:else}
                <div class="name-single">{person?.name}</div>
                {/if}
            </div>
            </div>
        {/if}
        {/each}
    </div>
    {/if}
</div>

<style lang="scss">
  // Who's Out section should always span full width in card views
  :global(.reverse-channel-scroll-view-2-card .out-today, .reverse-channel-scroll-view-3-card .out-today, .reverse-channel-scroll-view-4-card .out-today) {
    flex: 1 1 100%!important;
    width: 100%!important;
    max-width: 100%!important;
  }

  .out-today {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: var(--theme-navpanel-color);
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.75rem;
    padding: 0.5rem;
    margin: 0.5rem 0.5rem;
    gap: 1rem;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
  }

  .out-today-title-container {
    display: flex;
    justify-content: center;
    align-items: baseline;
    margin-bottom: 0.5rem;
    gap: 2rem;
  }

  .out-today-title-container h4 {
    font-size: 1.25rem;
    font-weight: 800;
    color: var(--color-text-primary);
    margin: 0;
    text-align: center;
    grid-column: 2;
  }

  .out-today-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--theme-text-placeholder-color);
    margin: 0;
    text-align: center;
  }

  .out-today-search {
    grid-column: 3;
    min-width: 200px;
  }

  .out-today-employees-container {
    display: flex;
    flex-direction: row;
    gap: 1.5rem;
    overflow-x: auto;
    padding: 0 0.5rem 0.5rem 0.5rem;
    align-items: flex-start;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .out-today-content-area {
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .out-today-employee {
    position: relative;
    min-width: 65px;
    max-width: 65px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    text-align: center;
    overflow: hidden;
  }

  .out-today-employee-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
    line-height: 1.2;
    text-overflow: ellipsis;
  }

  .name-first {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .name-last {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .name-single {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text-primary);
  }

  .loading-indicator {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
  }

  .error-message {
    color: #d32f2f;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }

  .error-text {
    margin-bottom: 0.25rem;
  }

  .retry-button {
    background-color: var(--theme-primary-color);
    color: var(--theme-caption-color);
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .retry-button:hover {
    background-color: var(--theme-primary-hover-color);
  }

  .retry-button:active {
    background-color: var(--theme-primary-active-color);
  }

  .no-search-results,
  .no-employees-out {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
    text-align: center;
    font-style: italic;
  }

  .no-search-results {
    color: var(--color-text-primary);
  }

  .avatar-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: clip;
  }

  .avatar-icon {
    position: absolute;
    bottom: -16px;
    right: -12px;
    background-color: var(--theme-navpanel-color);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px solid var(--theme-bg-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .emoji-container {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    line-height: 1;
    top: 34px;
    right: 2px;
  }
</style>
