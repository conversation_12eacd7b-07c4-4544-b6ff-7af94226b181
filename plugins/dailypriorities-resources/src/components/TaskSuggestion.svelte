<!--
// Copyright © 2022 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
-->
<script lang="ts">
  import { Icon, Button, Component, DueDatePresenter } from '@hcengineering/ui'
  import { Avatar } from '@hcengineering/contact-resources'
  import tracker, { type Issue, IssuePriority } from '@hcengineering/tracker'
  import dailyPriorities from '../plugin'

  export let title: string
  export let issue: Issue | undefined = undefined
  export let issues: Issue[] | undefined = undefined

  // Handlers accept optional issue to support multi or single modes
  export let onAccept: (issue?: Issue) => void = () => {}
  export let onDismiss: (issue?: Issue) => void = () => {}

  function handleAccept (iss?: Issue): void {
    onAccept(iss)
  }

  function handleDismiss (iss?: Issue): void {
    onDismiss(iss)
  }

  // Sort issues by priority (Urgent → High → Medium → Low → NoPriority) and then by due date (earlier first)
  let sortedIssues: Issue[] = []
  const priorityWeight: Record<IssuePriority, number> = {
    [IssuePriority.Urgent]: 5,
    [IssuePriority.High]: 4,
    [IssuePriority.Medium]: 3,
    [IssuePriority.Low]: 2,
    [IssuePriority.NoPriority]: 1
  }
  $: sortedIssues = (issues ?? []).slice().sort((a, b) => {
    const aw = priorityWeight[a.priority] ?? 0
    const bw = priorityWeight[b.priority] ?? 0
    if (bw !== aw) return bw - aw
    const aDue = a.dueDate ?? Number.MAX_SAFE_INTEGER
    const bDue = b.dueDate ?? Number.MAX_SAFE_INTEGER
    return aDue - bDue
  })
</script>

<div class="message-group-card">
  <div class="header">
    <Avatar size="small" name={null} icon={dailyPriorities.icon.Lightbulb} />
    <div class="meta">
      <div class="row">
        <span class="name">{title}</span>
      </div>
    </div>
  </div>

  <div class="messages-container">
    {#if sortedIssues.length > 0}
      {#each sortedIssues as iss (iss._id)}
        <div class="message-card">
          <div class="message-card-content">
            <div class="message-card-content-message">
              <div class="ts-header-inline">
                <span class="ts-id">
                  <Component is={tracker.component.IssueStatusPresenter} props={{ value: iss, size: 'small' }} />
                  <span class="ts-id-text">{iss.identifier}</span>
                  <span class="divider"></span>
                  <span class="ts-title">{iss.title}</span>
                </span>
                <span class="ts-chip">
                  <Icon
                    icon={iss.priority === IssuePriority.Urgent
                      ? tracker.icon.PriorityUrgent
                      : iss.priority === IssuePriority.High
                        ? tracker.icon.PriorityHigh
                        : iss.priority === IssuePriority.Medium
                          ? tracker.icon.PriorityMedium
                          : iss.priority === IssuePriority.Low
                            ? tracker.icon.PriorityLow
                            : tracker.icon.PriorityNoPriority}
                    size="small"
                  />
                  <span class="ts-chip-text">{IssuePriority[iss.priority]}</span>
                </span>
                <div class="due-inline">
                  <DueDatePresenter
                    value={iss.dueDate}
                    shouldRender={iss.dueDate != null}
                    size="small"
                    kind="link"
                    onChange={() => {}}
                    editable={false}
                  />
                </div>
              </div>
            </div>
            <div class="message-card-date-and-tag">
              <Button icon={dailyPriorities.icon.Checkmark} size="small" kind="primary" on:click={() => { handleAccept(iss) }} nonIntlLabel="Approve" />
              <Button icon={dailyPriorities.icon.Failed} size="small" kind="regular" on:click={() => { handleDismiss(iss) }} nonIntlLabel="Decline" />
            </div>
          </div>
        </div>
      {/each}
    {:else}
      <div class="message-card">
        <div class="message-card-content">
          <div class="message-card-content-message">
            {#if issue}
              <div class="ts-header-inline">
                <span class="ts-id">
                  <Component is={tracker.component.IssueStatusPresenter} props={{ value: issue, size: 'small' }} />
                  <span class="ts-id-text">{issue.identifier}</span>
                  <span class="divider"></span>
                  <span class="ts-title">{issue.title}</span>
                </span>
                <span class="ts-chip">
                  <Icon
                    icon={issue.priority === IssuePriority.Urgent
                      ? tracker.icon.PriorityUrgent
                      : issue.priority === IssuePriority.High
                        ? tracker.icon.PriorityHigh
                        : issue.priority === IssuePriority.Medium
                          ? tracker.icon.PriorityMedium
                          : issue.priority === IssuePriority.Low
                            ? tracker.icon.PriorityLow
                            : tracker.icon.PriorityNoPriority}
                    size="small"
                  />
                  <span class="ts-chip-text">{IssuePriority[issue.priority]}</span>
                </span>
                <div class="due-inline">
                  <DueDatePresenter
                    value={issue.dueDate}
                    shouldRender={issue.dueDate != null}
                    size="small"
                    kind="link"
                    onChange={() => {}}
                    editable={false}
                  />
                </div>
              </div>
            {:else}
              <!-- No issue provided -->
            {/if}
          </div>

          <div class="message-card-date-and-tag">
            <Button icon={dailyPriorities.icon.Checkmark} size="small" kind="primary" on:click={() => { handleAccept(issue) }} nonIntlLabel="Approve" />
            <Button icon={dailyPriorities.icon.Failed} size="small" kind="regular" on:click={() => { handleDismiss(issue) }} nonIntlLabel="Decline" />
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>

<style lang="scss">
  .message-group-card {
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.75rem;
    padding: 0.5rem;
    margin: 0.5rem 0.5rem;
  }

  .header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    min-height: 48px;
    background-color: var(--theme-mention-bg-color);
    padding: 0.5rem;
    margin: -0.5rem -0.5rem 0.5rem -0.5rem;
    border-bottom: 1px solid var(--theme-divider-color);
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
  }

  .meta {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .row {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
  }

  .name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-right: 0.25rem;
  }

  .messages-container {
    width: 100%;
  }

  .message-card {
    position: relative;
    border: 1px solid var(--theme-bg-color);
    border-radius: 0.5rem;
    padding: 0.25rem 0.5rem 0.25rem 0.25rem;
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  .message-card-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
  }

  .message-card-content-message {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }

  .message-card-date-and-tag {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
  }

  /* description removed: was unused */

  /* Existing styles retained for issue content presentation */
  .ts-header-inline {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .ts-id {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.2rem 0.4rem;
    box-shadow: inset 0 0 0 1px var(--global-subtle-ui-BorderColor);
    border-radius: var(--extra-small-BorderRadius);
    background: var(--tag-nuance-SkyBackground);
  }

  .ts-id-text {
    color: var(--theme-caption-color);
  }

  .ts-title {
    font-weight: 500;
    color: var(--theme-content-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .ts-chip {
    display: inline-flex;
    align-items: center;
    gap: 0.35rem;
    padding: 0.15rem 0.5rem;
    border-radius: 9999px;
  }

  .ts-chip-text {
    font-size: 0.8rem;
  }

  .due-inline {
    margin-top: 0;
  }

  .divider {
    width: 1px;
    height: 1rem;
    background-color: var(--theme-popup-divider);
  }
</style>
