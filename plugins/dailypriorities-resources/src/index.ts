//
// Copyright © 2020 Anticrm Platform Contributors.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import { type ActivityMessage } from '@hcengineering/activity'
import dailyPriorities, { type ChatMessage } from '@hcengineering/dailypriorities'
import { type Resources } from '@hcengineering/platform'
import { getClient } from '@hcengineering/presentation'
import { writable } from 'svelte/store'

import ChannelHeader from './components/ChannelHeader.svelte'
import ChannelIcon from './components/ChannelIcon.svelte'
import ChannelPanel from './components/ChannelPanel.svelte'
import ChannelPresenter from './components/ChannelPresenter.svelte'
import ChannelPreview from './components/ChannelPreview.svelte'
import ChatMessageInput from './components/chat-message/ChatMessageInput.svelte'
import ChatMessagePresenter from './components/chat-message/ChatMessagePresenter.svelte'
import ChatMessagePreview from './components/chat-message/ChatMessagePreview.svelte'
import ChatMessagesPresenter from './components/chat-message/ChatMessagesPresenter.svelte'
import Chat from './components/chat/Chat.svelte'
import DailyPrioritiesBrowser from './components/chat/specials/DailyPrioritiesBrowser.svelte'
import ChannelMessageNotificationLabel from './components/notification/ChannelMessageNotificationLabel.svelte'
import ThreadNotificationPresenter from './components/notification/ThreadNotificationPresenter.svelte'
import ThreadMessagePresenter from './components/threads/ThreadMessagePresenter.svelte'
import ThreadMessagePreview from './components/threads/ThreadMessagePreview.svelte'
import ThreadParentPresenter from './components/threads/ThreadParentPresenter.svelte'
import Threads from './components/threads/Threads.svelte'
import ThreadView from './components/threads/ThreadView.svelte'
import ThreadViewPanel from './components/threads/ThreadViewPanel.svelte'
import ChatWidget from './components/ChatWidget.svelte'
import ChatWidgetTab from './components/ChatWidgetTab.svelte'
import WorkbenchTabExtension from './components/WorkbenchTabExtension.svelte'
import EmployeePresenter from './components/DailyPrioritiesEmployeePresenter.svelte'
import WhoIsOutWidget from './components/WhoIsOutWidget.svelte'

import {
  chunterSpaceLinkFragmentProvider,
  closeChatWidgetTab,
  getMessageLink,
  getMessageLocation,
  getThreadLink,
  locationDataResolver,
  openChannelInSidebar,
  openChannelInSidebarAction,
  openThreadInSidebar,
  replyToThread
} from './navigation'
import {
  ChannelTitleProvider,
  canCopyMessageLink,
  canDeleteMessage,
  canReplyToThread,
  dmIdentifierProvider,
  getDmName,
  getTitle,
  getUnreadThreadsCount,
  translateMessage,
  showOriginalMessage,
  canTranslateMessage
} from './utils'

export { default as ChatMessageInput } from './components/chat-message/ChatMessageInput.svelte'
export { default as ChatMessagePopup } from './components/chat-message/ChatMessagePopup.svelte'
export { default as ChatMessagesPresenter } from './components/chat-message/ChatMessagesPresenter.svelte'
export { default as Header } from './components/Header.svelte'
export { default as ThreadView } from './components/threads/ThreadView.svelte'

export const userSearch = writable('')

export async function dailyPrioritiesBrowserVisible (): Promise<boolean> {
  return false
}

export function chatMessagesFilter (message: ActivityMessage): boolean {
  return message._class === dailyPriorities.class.ChatMessage
}

export async function deleteChatMessage (message: ChatMessage): Promise<void> {
  const client = getClient()

  await client.remove(message)
}

export { replyToThread } from './navigation'

export default async (): Promise<Resources> => ({
  filter: {
    ChatMessagesFilter: chatMessagesFilter
  },
  component: {
    ThreadParentPresenter,
    ThreadViewPanel,
    ChannelHeader,
    ChannelPanel,
    ChannelPresenter,
    ChannelPreview,
    DailyPrioritiesBrowser,
    ThreadView,
    ChatMessagePresenter,
    ChatMessageInput,
    ChatMessagesPresenter,
    Chat,
    ThreadMessagePresenter,
    Threads,
    ChannelIcon,
    ChannelMessageNotificationLabel,
    ThreadNotificationPresenter,
    ThreadMessagePreview,
    ChatMessagePreview,
    ChatWidget,
    ChatWidgetTab,
    WorkbenchTabExtension,
    EmployeePresenter,
    WhoIsOutWidget
  },
  activity: {},
  function: {
    GetDmName: getDmName,
    DailyPrioritiesBrowserVisible: dailyPrioritiesBrowserVisible,
    GetFragment: getTitle,
    GetLink: getMessageLink,
    ChannelTitleProvider,
    DmIdentifierProvider: dmIdentifierProvider,
    CanDeleteMessage: canDeleteMessage,
    CanCopyMessageLink: canCopyMessageLink,
    GetChunterSpaceLinkFragment: chunterSpaceLinkFragmentProvider,
    GetUnreadThreadsCount: getUnreadThreadsCount,
    GetThreadLink: getThreadLink,
    ReplyToThread: replyToThread,
    CanReplyToThread: canReplyToThread,
    GetMessageLink: getMessageLocation,
    CloseChatWidgetTab: closeChatWidgetTab,
    OpenChannelInSidebar: openChannelInSidebar,
    CanTranslateMessage: canTranslateMessage,
    OpenThreadInSidebar: openThreadInSidebar,
    LocationDataResolver: locationDataResolver
  },
  actionImpl: {
    DeleteChatMessage: deleteChatMessage,
    ReplyToThread: replyToThread,
    OpenInSidebar: openChannelInSidebarAction,
    TranslateMessage: translateMessage,
    ShowOriginalMessage: showOriginalMessage
  }
})
