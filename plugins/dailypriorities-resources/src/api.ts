import { getMetadata } from '@hcengineering/platform'
import dailyPriorities from './plugin'
import presentation from '@hcengineering/presentation'

const defaultHeaders = {
  'Content-Type': 'application/json',
  Accept: 'application/json'
}

const getAuthHeaders = (): Record<string, string> => {
  const token = getMetadata(presentation.metadata.Token)

  if (token === undefined || token === null || token === '') {
    throw new Error('No authentication token found. Please login first.')
  }

  return {
    ...defaultHeaders,
    Authorization: `Bearer ${token}`
  }
}

const getWebhookServiceURL = (): string => {
  const url = getMetadata(dailyPriorities.metadata.WebhookServiceURL) as string

  if (url === undefined || url === null || url === '') {
    throw new Error('Webhook service URL not configured')
  }

  return url
}

export const getBambooHRData = async (): Promise<any> => {
  const webhookServiceURL = getWebhookServiceURL()

  const response = await fetch(`${webhookServiceURL}/api/bamboo-hr/whos-out/current`, {
    method: 'GET',
    headers: getAuthHeaders()
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  const data = await response.json()
  return data.data
}
