<script lang="ts">
  import { Icon, IconCalendar, Label } from '@hcengineering/ui'
  import tracker from '../../plugin'

  export let type: string
  export let startDate: string
  export let endDate: string
  export let description: string | undefined = undefined
</script>

<div class="tooltip-container">
  <div class="tooltip-row">
    <div class="cell header"><Label label={tracker.string.Type} /></div>
    <div class="cell header"><Label label={tracker.string.DateRangeTooltipStart} /></div>
    <div class="cell header"><Label label={tracker.string.DateRangeTooltipEnd} /></div>
  </div>
  <div class="tooltip-row">
    <div class="cell value">{type}</div>
    <div class="cell value">
      <Icon icon={IconCalendar} size={'small'} />
      {startDate}
    </div>
    <div class="cell value">
      <Icon icon={IconCalendar} size={'small'} />
      {endDate}
    </div>
  </div>
  {#if description}
    <div class="divider" />
    <div class="description">
      {description}
    </div>
  {/if}
</div>

<style lang="scss">
  .tooltip-container {
    background: var(--theme-dialog-background-color);
    border-radius: 6px;
    padding: 8px 12px;
    font-family: var(--theme-font-family);
    min-width: 260px;
    border: 1px solid var(--theme-dialog-border-color);
  }

  .tooltip-row {
    display: flex;
    align-items: center;

    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }

  .cell {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 4px;

    &:first-child {
      flex: 1.2;
    }
  }

  .header {
    color: var(--theme-dark-color);
    font-size: 0.75rem;
    font-weight: 500;
  }

  .value {
    color: var(--theme-content-color);
    font-size: 0.875rem;

    :global(svg) {
      color: var(--theme-dark-color);
      width: 12px;
      height: 12px;
    }
  }

  .divider {
    height: 1px;
    background-color: var(--theme-divider-color);
    margin: 8px 0;
  }

  .description {
    color: var(--theme-state-negative-color);
    font-size: 0.875rem;
    line-height: 1.4;
    max-width: 300px;
  }
</style>
