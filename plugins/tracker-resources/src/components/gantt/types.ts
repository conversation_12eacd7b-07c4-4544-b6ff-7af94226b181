export interface ProjectMenuItem {
  id: string
  label: string
  selected: boolean
}

export interface EpicRow {
  label: string
  project: string
  id: string
  businessDescription?: string
  startDate: Date
  greenDate: Date
  blueDate: Date
  redDate: Date
  endDate: Date
  assignee?: string
}

export type ProjectGroups = Record<string, EpicRow[]>

export interface HeaderRow {
  type: 'header'
  label: string
}
