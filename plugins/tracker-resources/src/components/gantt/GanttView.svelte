<script lang="ts">
  import { Ref, Space } from '@hcengineering/core'
  import { ButtonIcon, IconBack, IconForward, ModernButton, Switcher, SearchInput, Button, IconFilter, Icon, Label, showPopup, Menu, type Action, IconCheck } from '@hcengineering/ui'
  import type { IntlString } from '@hcengineering/platform'
  import tracker from '../../plugin'
  import EpicsTable from './EpicsTable.svelte'
  import { FilterButton } from '@hcengineering/view-resources'
  import { getKpisData } from './EpicsData'
  import type { EpicRow, ProjectGroups, HeaderRow } from './types'
  import { mockRows, projectCodes } from './constants'

  export let space: Ref<Space> | undefined = undefined
  export let label: IntlString = tracker.string.Roadmaps
  let search: string = ''

  let currentDate = new Date(2025, 7, 1) // Start view at August 1, 2025
  let timeRange: '3Months' | '6Months' | '12Months' = '3Months'

  $: startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
  $: endDate = getEndDateForRange(currentDate, timeRange)
  $: dateRangeDisplay = startDate && endDate ? getDateRangeDisplay() : ''

  // Rows fetched and mapped to the expected shape
  let fetchedRows: EpicRow[] = []
  let useMock: boolean = false
  let baseRows: EpicRow[] = []
  const mockDataLabel = 'Mock data' as unknown as IntlString

  // Reactively fetch data when date range changes
  $: if (startDate && endDate) {
    void loadKpis()
  }

  async function loadKpis (): Promise<void> {
    const formatYMD = (d: Date): string => {
      const y = d.getFullYear()
      const m = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${y}-${m}-${day}`
    }

    const startIso = formatYMD(startDate)
    const endIso = formatYMD(endDate)

    const parseMs = (s?: string): number | null => {
      if (s == null || s === '') return null
      const d = new Date(s)
      const t = d.getTime()
      return Number.isNaN(t) ? null : t
    }

    console.log('Loading KPI epics', { startIso, endIso, projects: projectCodes })

    const allProjectRows = await Promise.all(
      projectCodes.map(async (project) => {
        try {
          const raw: any = await getKpisData(project)
          const items: any[] = Array.isArray(raw) ? raw : (Array.isArray(raw?.data) ? raw.data : [])
          console.log('KPI raw items', { project, count: items.length, items })
          const isSentinel = (s?: string) => typeof s === 'string' && s.startsWith('1970-01-01')
          const filtered = items.filter((item: any) => !(
            isSentinel(item.dueDate) ||
            isSentinel(item.greenDate) ||
            isSentinel(item.blueDate) ||
            isSentinel(item.redDate)
          ))
          console.log('Filtered KPI items', { project, before: items.length, after: filtered.length })
          const mapped = filtered.map((item: any): EpicRow => {
            const greenMs = parseMs(item.greenDate) ?? parseMs(item.dueDate) ?? parseMs(item.redDate) ?? parseMs(item.blueDate) ?? Date.now()
            const blueMs = parseMs(item.blueDate) ?? greenMs
            const redMs = parseMs(item.redDate) ?? blueMs
            const dueMs = parseMs(item.dueDate) ?? redMs

            // Ensure non-decreasing order to avoid negative widths
            const greenC = greenMs
            const blueC = Math.max(blueMs, greenC)
            const redC = Math.max(redMs, blueC)
            const dueC = Math.max(dueMs, redC)

            // Start date logic: 2 weeks before green, or today if green is in the future
            const nowMs = Date.now()
            const twoWeeksMs = 14 * 24 * 60 * 60 * 1000
            const startC = greenC > nowMs ? nowMs : (greenC - twoWeeksMs)

            const start = new Date(startC)
            const end = new Date(Math.max(redC, dueC))

            return {
              label: item.summary ?? item.epicName ?? item.key ?? '',
              project,
              id: item.key ?? item.epicName ?? '',
              startDate: start,
              greenDate: new Date(greenC),
              blueDate: new Date(blueC),
              redDate: new Date(redC),
              endDate: end,
              assignee: item.assignee
            }
          })
          console.log('Mapped KPI rows', { project, count: mapped.length, mapped })
          return mapped
        } catch (err) {
          console.warn('Failed to load KPIs for project', project, err)
          return []
        }
      })
    )

    const flattened = allProjectRows.flat()
    console.log('All projects mapped rows total', flattened.length, flattened)
    fetchedRows = flattened
  }

  $: baseRows = useMock ? mockRows : fetchedRows

  // Get unique projects from fetched data
  let projects: string[] = []
  $: projects = [...new Set(baseRows.map(row => row.project))]
  let selectedProjects: string[] = []

  // Filter rows based on search and selected projects
  $: filteredAndSortedRows = baseRows
    .filter(row => {
      // If no projects selected, show all
      if (selectedProjects.length === 0) return true
      return selectedProjects.includes(row.project)
    })
    .filter(row => {
      if (!search) return true
      return calculateMatchScore(row.label, search) > 0
    })
    .sort((a, b) => {
      if (search) {
        return calculateMatchScore(b.label, search) - calculateMatchScore(a.label, search)
      }
      return 0
    })

  // Group rows by project
  $: groupedRows = filteredAndSortedRows.reduce<ProjectGroups>((acc, row) => {
    if (!acc[row.project]) {
      acc[row.project] = []
    }
    acc[row.project].push(row)
    return acc
  }, {})

  // Convert grouped rows to array format with headers
  $: processedRows = Object.entries(groupedRows).flatMap<EpicRow | HeaderRow>(([project, rows]) => [
    { type: 'header', label: project },
    ...rows
  ])

  function calculateMatchScore (label: string, searchTerm: string): number {
    const normalizedLabel = label.toLowerCase()
    const normalizedSearch = searchTerm.toLowerCase()
    if (normalizedLabel === normalizedSearch) return 100
    if (normalizedLabel.includes(normalizedSearch)) return 75
    const searchWords = normalizedSearch.split(' ')
    const matchedWords = searchWords.filter(word => normalizedLabel.includes(word))
    return (matchedWords.length / searchWords.length) * 50
  }

  function getEndDateForRange (date: Date, range: '3Months' | '6Months' | '12Months'): Date {
    const start = new Date(date.getFullYear(), date.getMonth(), 1)
    switch (range) {
      case '3Months':
        return new Date(start.getFullYear(), start.getMonth() + 3, 0)
      case '6Months':
        return new Date(start.getFullYear(), start.getMonth() + 6, 0)
      case '12Months':
        return new Date(start.getFullYear(), start.getMonth() + 12, 0)
    }
  }

  function getMonthName (date: Date): string {
    return new Intl.DateTimeFormat('default', { month: 'long' }).format(date)
  }

  function inc (val: number): void {
    const monthsToMove = timeRange === '3Months' ? 3 : timeRange === '6Months' ? 6 : 12
    currentDate.setDate(1)
    currentDate.setMonth(currentDate.getMonth() + (val * monthsToMove))
    currentDate = currentDate
  }

  function handleModeSelect (event: any): void {
    const newRange = event.detail.id
    if (newRange === '3Months' || newRange === '6Months' || newRange === '12Months') {
      timeRange = newRange
    }
  }

  function handleProjectFilter (event: MouseEvent): void {
    const target = event.target as HTMLElement
    const items: Action[] = projects.map(project => ({
      label: project as unknown as IntlString,
      action: async () => {
        if (selectedProjects.includes(project)) {
          selectedProjects = selectedProjects.filter(p => p !== project)
        } else {
          selectedProjects = [...selectedProjects, project]
        }
      },
      icon: selectedProjects.includes(project) ? IconCheck : undefined
    }))

    // Add "Show all" option at the top
    items.unshift({
      label: tracker.string.All,
      action: async () => {
        selectedProjects = []
      },
      icon: selectedProjects.length === 0 ? IconCheck : undefined
    })

    showPopup(
      Menu,
      {
        actions: items,
        title: tracker.string.Project,
        addClass: 'menu-with-checks'
      },
      target
    )
  }

  // Update the button label to show selected projects
  $: filterButtonLabel = selectedProjects.length > 0
    ? `${selectedProjects.join(', ')}` as unknown as IntlString
    : tracker.string.Project

  function getDateRangeDisplay (): string {
    if (!startDate || !endDate) return ''

    const start = getMonthName(startDate)
    const end = getMonthName(endDate)
    const startYear = startDate.getFullYear()
    const endYear = endDate.getFullYear()

    if (startYear === endYear) {
      return `${start} - ${end} ${startYear}`
    } else {
      return `${start} ${startYear} - ${end} ${endYear}`
    }
  }
</script>

<div class="gantt-container">
  <div class="header-container">
    <div class="left-controls">
      <div class="title">
        <span class="icon">
          <Icon icon={tracker.icon.Gantt} size={'large'} />
        </span>
        <span class="label"><Label label={label} /></span>
      </div>
    </div>
    <div class="right-controls">
      <SearchInput
        bind:value={search}
        placeholder={tracker.string.Epics}
        collapsed
      />
      <Button
        icon={IconFilter}
        label={tracker.string.Filter}
        kind={'regular'}
        size={'medium'}
        pressed={selectedProjects.length > 0}
        on:click={handleProjectFilter}
      />
      <Button
        label={mockDataLabel}
        kind={'regular'}
        size={'medium'}
        pressed={useMock}
        on:click={() => { useMock = !useMock }}
      />
    </div>
  </div>

  <div class="hulyHeader-container clearPadding justify-between flex-gap-4">
    <div class="flex-row-center flex-gap-2">
      <ButtonIcon
        icon={IconBack}
        kind={'tertiary'}
        size={'small'}
        on:click={() => {
          inc(-1)
        }}
      />
      <ModernButton
        kind={'tertiary'}
        size={'small'}
        on:click={() => (currentDate = new Date())}
      >
        <Label label={tracker.string.Today} />
      </ModernButton>
      <ButtonIcon
        icon={IconForward}
        kind={'tertiary'}
        size={'small'}
        on:click={() => {
          inc(1)
        }}
      />
      <div class="hulyHeader-divider short" />
      <div class="fs-title flex-row-center flex-grow firstLetter">
        {dateRangeDisplay}
      </div>
    </div>
    <Switcher
      name={'gantt-mode-view'}
      selected={timeRange}
      kind={'subtle'}
      items={[
        { id: '3Months', labelIntl: tracker.string.ThreeMonths },
        { id: '6Months', labelIntl: tracker.string.SixMonths },
        { id: '12Months', labelIntl: tracker.string.TwelveMonths }
      ]}
      on:select={handleModeSelect}
    />
  </div>

  <EpicsTable
    {currentDate}
    {startDate}
    {endDate}
    {timeRange}
    rows={processedRows}
    {search}
  />
</div>

<style lang="scss">
  .gantt-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--theme-divider-color);
    background-color: var(--theme-bg-color);
    flex-shrink: 0;
  }

  .left-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 700;
  }

  .right-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  :global(.icon) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :global(.menu-with-checks) {
    :global(.icon) {
      order: 1;
      margin-left: 0.5rem;
      margin-right: 0;
    }
  }

  .hulyHeader-container {
    flex-shrink: 0;
  }
</style>
