import type { EpicRow } from './types'

// Projects to fetch from backend
export const projectCodes = ['DO', 'MP', 'HP', 'HAR', 'PF', 'AAM', 'SHT']

// Mock milestone data
export const milestone = {
  date: new Date(2025, 8, 13), // September 13, 2025
  label: 'HRX on 13 sep'
}

export const mockRows: EpicRow[] = [
  {
    label: 'R&D | Annotation Tool',
    project: 'TMP',
    id: 'epic-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 1),
    greenDate: new Date(2025, 7, 15),
    blueDate: new Date(2025, 7, 30),
    redDate: new Date(2025, 8, 15),
    endDate: new Date(2025, 8, 15)
  },
  {
    label: 'Yale Unsolicited Workflow',
    project: 'Heart+',
    id: 'epic-2',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 15),
    greenDate: new Date(2025, 8, 1),
    blueDate: new Date(2025, 8, 15),
    redDate: new Date(2025, 9, 1),
    endDate: new Date(2025, 9, 1),
    assignee: '<PERSON> Doe'
  },
  {
    label: 'Improved Transmissions Syncing',
    project: 'Heart+',
    id: 'epic-3',
    startDate: new Date(2025, 8, 1),
    greenDate: new Date(2025, 8, 15),
    blueDate: new Date(2025, 9, 1),
    redDate: new Date(2025, 9, 15),
    endDate: new Date(2025, 9, 15),
    assignee: 'Mario Rossi'
  },
  {
    label: 'Engage+ Improvements',
    project: 'Heart+',
    id: 'epic-31',
    startDate: new Date(2025, 5, 3),
    greenDate: new Date(2025, 5, 18),
    blueDate: new Date(2025, 6, 4),
    redDate: new Date(2025, 7, 4),
    endDate: new Date(2025, 7, 4),
    assignee: 'Lukas Kaczmarek'
  },
  {
    label: 'DocOps | Publishing Pipeline',
    project: 'DO',
    id: 'epic-DO-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 5),
    greenDate: new Date(2025, 7, 20),
    blueDate: new Date(2025, 8, 5),
    redDate: new Date(2025, 8, 20),
    endDate: new Date(2025, 8, 20),
    assignee: 'Mario Rossi'
  },
  {
    label: 'Docs | MDX Revamp',
    project: 'DO',
    id: 'epic-DO-2',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 8, 10),
    greenDate: new Date(2025, 8, 25),
    blueDate: new Date(2025, 9, 10),
    redDate: new Date(2025, 9, 24),
    endDate: new Date(2025, 9, 24),
    assignee: 'Mario Rossi'
  },
  {
    label: 'Platform | Manhattan Sync',
    project: 'MP',
    id: 'epic-MP-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 3),
    greenDate: new Date(2025, 7, 18),
    blueDate: new Date(2025, 8, 2),
    redDate: new Date(2025, 8, 18),
    endDate: new Date(2025, 8, 18),
    assignee: 'John Doe'
  },
  {
    label: 'Manhattan | Storage Layer',
    project: 'MP',
    id: 'epic-MP-2',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 8, 12),
    greenDate: new Date(2025, 8, 26),
    blueDate: new Date(2025, 9, 9),
    redDate: new Date(2025, 9, 23),
    endDate: new Date(2025, 9, 23),
    assignee: 'Mario Rossi'
  },
  {
    label: 'Heart Platform | Notifications',
    project: 'HP',
    id: 'epic-HP-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 22),
    greenDate: new Date(2025, 8, 5),
    blueDate: new Date(2025, 8, 19),
    redDate: new Date(2025, 9, 2),
    endDate: new Date(2025, 9, 2)
  },
  {
    label: 'Heart Platform | Patient Timeline',
    project: 'HP',
    id: 'epic-HP-2',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 8, 8),
    greenDate: new Date(2025, 8, 22),
    blueDate: new Date(2025, 9, 6),
    redDate: new Date(2025, 9, 20),
    endDate: new Date(2025, 9, 20),
    assignee: 'Mario Rossi'
  },
  {
    label: 'Hardware | Device SDK v2',
    project: 'HAR',
    id: 'epic-HAR-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 9),
    greenDate: new Date(2025, 7, 23),
    blueDate: new Date(2025, 8, 7),
    redDate: new Date(2025, 8, 21),
    endDate: new Date(2025, 8, 21),
    assignee: 'John Doe'
  },
  {
    label: 'Hardware | BLE Improvements',
    project: 'HAR',
    id: 'epic-HAR-2',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 8, 14),
    greenDate: new Date(2025, 8, 28),
    blueDate: new Date(2025, 9, 12),
    redDate: new Date(2025, 9, 26),
    endDate: new Date(2025, 9, 26)
  },
  {
    label: 'Platform Framework | Theming',
    project: 'PF',
    id: 'epic-PF-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 1),
    greenDate: new Date(2025, 7, 16),
    blueDate: new Date(2025, 7, 31),
    redDate: new Date(2025, 8, 14),
    endDate: new Date(2025, 8, 14),
    assignee: 'Lukas Kaczmarek'
  },
  {
    label: 'Platform Framework | Widgets',
    project: 'PF',
    id: 'epic-PF-2',
    startDate: new Date(2025, 8, 3),
    greenDate: new Date(2025, 8, 17),
    blueDate: new Date(2025, 9, 1),
    redDate: new Date(2025, 9, 15),
    endDate: new Date(2025, 9, 15),
    assignee: 'Mario Rossi'
  },
  {
    label: 'Auto Assessment | Risk Model',
    project: 'AAM',
    id: 'epic-AAM-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 6),
    greenDate: new Date(2025, 7, 21),
    blueDate: new Date(2025, 8, 4),
    redDate: new Date(2025, 8, 18),
    endDate: new Date(2025, 8, 18)
  },
  {
    label: 'Auto Assessment | Explainability',
    project: 'AAM',
    id: 'epic-AAM-2',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 5, 7),
    greenDate: new Date(2025, 5, 20),
    blueDate: new Date(2025, 6, 8),
    redDate: new Date(2025, 7, 10),
    endDate: new Date(2025, 7, 10),
    assignee: 'John Doe'
  },
  {
    label: 'Shared Tools | CI optimization',
    project: 'SHT',
    id: 'epic-SHT-1',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 7, 4),
    greenDate: new Date(2025, 7, 19),
    blueDate: new Date(2025, 8, 2),
    redDate: new Date(2025, 8, 16),
    endDate: new Date(2025, 8, 16),
    assignee: 'Lukas Kaczmarek'
  },
  {
    label: 'Shared Tools | Release tooling',
    project: 'SHT',
    id: 'epic-SHT-2',
    businessDescription: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
    startDate: new Date(2025, 8, 7),
    greenDate: new Date(2025, 8, 21),
    blueDate: new Date(2025, 9, 4),
    redDate: new Date(2025, 9, 18),
    endDate: new Date(2025, 9, 18),
    assignee: 'Bob Smith'
  }
]
