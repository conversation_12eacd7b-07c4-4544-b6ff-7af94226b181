import { getMetadata } from '@hcengineering/platform'
import type { EpicData } from '../../types'
import kpis from '@hcengineering/kpis'

export async function getKpisData (projectId: string, startDate?: string, endDate?: string): Promise<EpicData[]> {
  const kpiUrl = getMetadata(kpis.metadata.KpiUrl)

  if (typeof kpiUrl !== 'string' || kpiUrl === '') {
    throw new Error('KPI service URL is not configured')
  }

  if (projectId === '') {
    throw new Error('ProjectId missing!')
  }

  const params = new URLSearchParams({ projectIds: projectId.toUpperCase(), issueType: 'Epic' })

  if (startDate != null && startDate !== '') {
    params.append('startDate', startDate)
  }
  if (endDate != null && endDate !== '') {
    params.append('endDate', endDate)
  }

  const url = `${kpiUrl}/api/v1/jira/issues?${params.toString()}`
  const res = await fetch(url)
  const content = await res.text()
  if (!res.ok) {
    throw new Error(`Failed to load KPIs: ${res.status} ${res.statusText} - ${content}`)
  }

  try {
    const json: unknown = JSON.parse(content)
    if (typeof json === 'object' && json !== null && Array.isArray((json as { data?: unknown }).data)) {
      const data = (json as { data: EpicData[] }).data
      return data
    }
    if (Array.isArray(json)) {
      return json as EpicData[]
    }
    return []
  } catch (e) {
    throw new Error('Invalid JSON response')
  }
}
