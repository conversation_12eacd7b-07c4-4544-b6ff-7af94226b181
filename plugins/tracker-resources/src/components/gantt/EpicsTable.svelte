<script lang="ts">
    import { Scroller, resizeObserver, deviceOptionsStore as deviceInfo, tooltip, Label, Icon } from '@hcengineering/ui'
    import DateRangeTooltip from './DateRangeTooltip.svelte'
    import tracker from '../../plugin'
    import { milestone } from './constants'
    import contact from '@hcengineering/contact'

    const headerHeightRem = 4.375
    const minColWidthRem = 2.5
    const epicRowHeightRem = 3.0
    const projectHeaderHeightRem = 2.5

    export let currentDate: Date = new Date()
    export let startDate: Date = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
    export let endDate: Date = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
    export let timeRange: '3Months' | '6Months' | '12Months' = '3Months'
    export let search: string = ''

    export let rows: any[] = []

    // Collapsible projects state
    let collapsed: Set<string> = new Set()
    function toggleProject (project: string): void {
      if (collapsed.has(project)) collapsed.delete(project)
      else collapsed.add(project)
      // force reactivity by reassigning
      collapsed = new Set(collapsed)
    }

    $: visibleRows = (() => {
      const result: any[] = []
      let currentProject: string | null = null
      let hide = false
      for (const r of rows) {
        if (r?.type === 'header') {
          currentProject = r.label
          hide = collapsed.has(r.label)
          result.push(r)
        } else {
          if (!hide) result.push(r)
        }
      }
      return result
    })()

    const todayDate = new Date()

    let headerWidth: number = 0
    let containerWidth: number = 0
    let scrollContainer: HTMLDivElement | undefined
    $: headerWidthRem = headerWidth / $deviceInfo.fontSize
    $: containerWidthRem = containerWidth / $deviceInfo.fontSize
    $: values = getTimeValues(startDate, endDate)
    $: visibleColumnsCount = getVisibleColumnsCount(startDate, endDate)
    $: columnWidthRem = getColumnWidth(containerWidthRem - headerWidthRem, visibleColumnsCount)

    function isMilestoneInColumn (value: any): boolean {
      return milestone.date >= value.date && milestone.date <= value.endDate
    }

    function getMilestonePosition (values: any[]): number {
      const columnIndex = values.findIndex(isMilestoneInColumn)
      if (columnIndex === -1) return -1

      const column = values[columnIndex]
      const offset = (milestone.date.getTime() - column.date.getTime()) /
        (column.endDate.getTime() - column.date.getTime())

      return (columnIndex + offset) * columnWidthRem
    }

    function getTimeValues (start: Date, end: Date): any[] {
      const extraMonths = 12
      if (timeRange === '12Months') {
        // For 12 months view, show months instead of weeks
        const months = []
        const extendedStartMonth = new Date(start.getFullYear(), start.getMonth() - extraMonths, 1)
        const originalTotalMonths = (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth() + 1
        const totalMonths = originalTotalMonths + 2 * extraMonths

        for (let i = 0; i < totalMonths; i++) {
          const monthStart = new Date(extendedStartMonth.getFullYear(), extendedStartMonth.getMonth() + i, 1)
          const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0)

          months.push({
            type: 'month',
            date: monthStart,
            endDate: monthEnd
          })
        }
        return months
      } else {
        // Existing weekly view logic
        const weeks = []
        const viewStart = new Date(start.getFullYear(), start.getMonth() - extraMonths, 1)
        const viewEnd = new Date(end.getFullYear(), end.getMonth() + extraMonths, 0)
        const viewStartWeek = new Date(viewStart)
        viewStartWeek.setDate(viewStartWeek.getDate() - (viewStartWeek.getDay() + 6) % 7)

        const totalDays = Math.ceil((viewEnd.getTime() - viewStartWeek.getTime()) / (1000 * 60 * 60 * 24))
        const totalWeeks = Math.ceil(totalDays / 7)

        for (let i = 0; i < totalWeeks; i++) {
          const weekStart = new Date(viewStartWeek.getTime() + (i * 7 * 24 * 60 * 60 * 1000))
          const weekEnd = new Date(weekStart.getTime() + (6 * 24 * 60 * 60 * 1000))

          weeks.push({
            type: 'week',
            date: weekStart,
            endDate: weekEnd
          })
        }
        return weeks
      }
    }

    function getVisibleColumnsCount (start: Date, end: Date): number {
      if (timeRange === '12Months') {
        return (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth() + 1
      } else {
        const startWeek = new Date(start)
        startWeek.setDate(startWeek.getDate() - (startWeek.getDay() + 6) % 7)
        const totalDays = Math.ceil((end.getTime() - startWeek.getTime()) / (1000 * 60 * 60 * 24))
        return Math.ceil(totalDays / 7)
      }
    }

    function getColumnWidth (gridWidth: number, numColumns: number): number {
      const width = gridWidth / numColumns
      return Math.max(width, minColWidthRem)
    }

    export function getCellStyle (): string {
      return `width: ${columnWidthRem}rem;`
    }

    export function getRowStyle (isProjectHeader: boolean = false): string {
      return `height: ${isProjectHeader ? projectHeaderHeightRem : epicRowHeightRem}rem;`
    }

    function isToday (value: any): boolean {
      return todayDate >= value.date && todayDate <= value.endDate
    }

    function getDisplayText (value: any): string {
      if (timeRange === '12Months') {
        return value.date.toLocaleDateString('default', { month: 'short' })
      }
      return value.date.getDate().toString()
    }

    function getSubText (value: any): string {
      if (timeRange === '12Months') {
        return value.date.getFullYear().toString()
      }
      return value.date.toLocaleDateString('default', { month: 'short' })
    }

    function getEpicPosition (epic: any, values: any[], startDate: Date, endDate: Date): { left: number, width: number } {
      const epicStart = new Date(startDate)
      const epicEnd = new Date(endDate)

      // Find the week that contains the epic start date
      let startWeekIndex = values.findIndex(week =>
        epicStart >= week.date && epicStart <= week.endDate
      )
      if (startWeekIndex === -1) {
        startWeekIndex = 0
      }

      // Find the week that contains the epic end date
      let endWeekIndex = values.findIndex(week =>
        epicEnd >= week.date && epicEnd <= week.endDate
      )
      if (endWeekIndex === -1) {
        endWeekIndex = values.length - 1
      }

      // Calculate the offset within the start week (0 to 1)
      const startOffset = startWeekIndex >= 0
        ? (epicStart.getTime() - values[startWeekIndex].date.getTime()) /
          (values[startWeekIndex].endDate.getTime() - values[startWeekIndex].date.getTime())
        : 0

      // Calculate the offset within the end week (0 to 1)
      const endOffset = endWeekIndex >= 0
        ? (epicEnd.getTime() - values[endWeekIndex].date.getTime()) /
          (values[endWeekIndex].endDate.getTime() - values[endWeekIndex].date.getTime())
        : 1

      // Calculate final position and width
      const left = (startWeekIndex + startOffset) * columnWidthRem
      const width = ((endWeekIndex - startWeekIndex) + (endOffset - startOffset)) * columnWidthRem

      return { left, width }
    }

    function formatDate (date: Date): string {
      return date.toLocaleDateString('default', { day: 'numeric', month: 'short' })
    }

    function getTooltipContent (type: string, startDate: Date, endDate: Date, isOverdue: boolean = false): { component: any, props: any } {
      const description = isOverdue
        ? 'Deadline was missed due to unexpected technical challenges and resource constraints. The team is working on mitigation strategies to get back on track.'
        : undefined

      return {
        component: DateRangeTooltip,
        props: {
          type,
          startDate: formatDate(startDate),
          endDate: formatDate(endDate),
          description
        }
      }
    }

    function highlightSearchText (text: string, searchTerm: string): string {
      if (!searchTerm) return text
      const normalizedText = text
      const normalizedSearch = searchTerm.toLowerCase()
      const index = normalizedText.toLowerCase().indexOf(normalizedSearch)
      if (index >= 0) {
        const before = text.slice(0, index)
        const match = text.slice(index, index + searchTerm.length)
        const after = text.slice(index + searchTerm.length)
        return before + '<mark class="highlight">' + match + '</mark>' + after
      }
      return text
    }

    // Function to safely encode HTML entities
    function encodeHTML (text: string): string {
      return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;')
    }

    function getHighlightParts (text: string, searchTerm: string): { before: string, match: string, after: string } | null {
      if (!searchTerm) return null
      const index = text.toLowerCase().indexOf(searchTerm.toLowerCase())
      if (index === -1) return null
      return {
        before: text.slice(0, index),
        match: text.slice(index, index + searchTerm.length),
        after: text.slice(index + searchTerm.length)
      }
    }

    function isOverdue (epic: any): boolean {
      const now = new Date()
      return now > epic.redDate && now > epic.endDate
    }

    $: if (scrollContainer && values.length > 0 && columnWidthRem) {
      // Find the center of the current visible period
      const centerDate = new Date(startDate.getTime() + (endDate.getTime() - startDate.getTime()) / 2)
      const centerIndex = values.findIndex(v => 
        centerDate >= v.date && centerDate <= v.endDate
      )
      if (centerIndex !== -1) {
        const pxPerRem = $deviceInfo.fontSize
        const targetX = centerIndex * columnWidthRem * pxPerRem
        const desired = Math.max(0, headerWidth + targetX - (scrollContainer.clientWidth / 2))
        scrollContainer.scrollTo({ left: desired, behavior: 'auto' })
      }
    }
</script>

<div class="timeline-container">
  {#if rows.length}
    <Scroller fade={{ multipler: { top: headerHeightRem, left: headerWidthRem } }} noFade>
      <div bind:this={scrollContainer} class="vertical-scroll" use:resizeObserver={(evt) => { containerWidth = evt.clientWidth }}>
        <div class="timeline">
          {#key [containerWidthRem, columnWidthRem, headerWidthRem, timeRange]}
            <div
              use:resizeObserver={(evt) => { headerWidth = evt.clientWidth }}
              class="timeline-header timeline-resource-header"
            >
              <div class="timeline-row">
                <div class="timeline-resource-cell">
                  <div class="timeline-resource-header__title">
                    <Label label={tracker.string.Timeline} />
                  </div>
                </div>
              </div>
            </div>

            <div class="timeline-resource-content">
              {#each visibleRows as row}
                <div
                  class="timeline-row"
                  class:project-header-row={row.type === 'header'}
                  style={getRowStyle(row.type === 'header')}
                >
                  <div class="timeline-resource-cell" class:project-header={row.type === 'header'} class:project-item={!row.type}>
                    {#if row.type === 'header'}
                      <button class="collapse-btn" on:click={() => toggleProject(row.label)} aria-label="Toggle project">
                        <svg class="chevron" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class:expanded={!collapsed.has(row.label)}>
                          <polyline points="8 5 16 12 8 19" />
                        </svg>
                      </button>
                    {/if}
                    {#if !row.type && search}
                      {@const parts = getHighlightParts(row.label, search)}
                      {#if parts}
                        <div class="epic-row">
                          <div class="epic-title" use:tooltip={{ nonIntlLabel: row.businessDescription }}>
                            {parts.before}<span class="highlight">{parts.match}</span>{parts.after}
                          </div>
                          {#if row.assignee}
                            <div class="assignee-avatar" use:tooltip={{ nonIntlLabel: row.assignee }}>
                              <Icon icon={contact.icon.Person} size="small" />
                            </div>
                          {/if}
                        </div>
                      {:else}
                        <div class="epic-row">
                          <div class="epic-title" use:tooltip={{ nonIntlLabel: row.businessDescription }}>
                            {row.label}
                          </div>
                          {#if row.assignee}
                            <div class="assignee-avatar" use:tooltip={{ nonIntlLabel: row.assignee }}>
                              <Icon icon={contact.icon.Person} size="small" />
                            </div>
                          {/if}
                        </div>
                      {/if}
                    {:else}
                      <div class="epic-row">
                        <div class="epic-title" use:tooltip={{ nonIntlLabel: row.businessDescription }}>
                          {row.label}
                        </div>
                        {#if !row.type && row.assignee}
                          <div class="assignee-avatar" use:tooltip={{ nonIntlLabel: row.assignee }}>
                            <Icon icon={contact.icon.Person} size="small" />
                          </div>
                        {/if}
                      </div>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>

            <div class="timeline-header timeline-grid-header">
              <div class="timeline-row flex">
                {#each values as value}
                  {@const today = isToday(value)}
                  <div
                    class="timeline-cell timeline-day-header flex-col-center justify-center"
                    style={getCellStyle()}
                  >
                    <div class:timeline-day-header__day--today={today} class="timeline-day-header__day">
                      {getDisplayText(value)}
                    </div>
                    <div class="timeline-day-header__weekday">{getSubText(value)}</div>
                  </div>
                {/each}
              </div>
            </div>

            <div class="timeline-grid-content">
              {#each visibleRows as row}
                <div
                  class="timeline-row flex"
                  class:project-header-row={row.type === 'header'}
                  style={getRowStyle(row.type === 'header')}
                >
                  {#each values as value}
                    {@const today = isToday(value)}
                    <div
                      class="timeline-cell"
                      class:today-column={today}
                      class:project-header-cell={row.type === 'header'}
                      style={getCellStyle()}
                    >
                    </div>
                  {/each}
                  {#if !row.type && row.startDate && row.endDate}
                    {@const greenPos = getEpicPosition(row, values, row.startDate, row.greenDate)}
                    {@const bluePos = getEpicPosition(row, values, row.greenDate, row.blueDate)}
                    {@const redPos = getEpicPosition(row, values, row.blueDate, row.redDate)}
                    <div class="epic-container">
                      <div
                        class="epic-section epic-green"
                        style="left: {greenPos.left}rem; width: {greenPos.width}rem;"
                        use:tooltip={getTooltipContent('Green', row.startDate, row.greenDate)}
                      >
                        <div class="epic-content"></div>
                      </div>
                      <div
                        class="epic-section epic-blue"
                        style="left: {bluePos.left}rem; width: {bluePos.width}rem;"
                        use:tooltip={getTooltipContent('Blue', row.greenDate, row.blueDate)}
                      >
                        <div class="epic-content"></div>
                      </div>
                      <div
                        class="epic-section epic-red"
                        style="left: {redPos.left}rem; width: {redPos.width}rem;"
                        use:tooltip={getTooltipContent('Red', row.blueDate, row.redDate, isOverdue(row))}
                      >
                        <div class="epic-content">
                          {#if isOverdue(row)}
                            <div class="overdue-icon">
                              <span class="overdue-mark">!</span>
                            </div>
                          {/if}
                        </div>
                      </div>
                    </div>
                  {/if}
                </div>
              {/each}

              {#if true}
                {@const milestoneLeft = getMilestonePosition(values)}
                {#if milestoneLeft >= 0}
                  <div
                    class="milestone-line"
                    style="left: {milestoneLeft}rem;"
                    use:tooltip={{
                      component: DateRangeTooltip,
                      props: {
                        type: 'Milestone',
                        startDate: formatDate(milestone.date),
                        endDate: formatDate(milestone.date),
                        description: milestone.label
                      }
                    }}
                  >
                    <div class="milestone-label">{milestone.label}</div>
                  </div>
                {/if}
              {/if}
            </div>
          {/key}
        </div>
      </div>
    </Scroller>
  {:else}
    <div class="flex-center h-full w-full flex-grow fs-title">
      <Label label={tracker.string.NoItemsAvailable} />
    </div>
  {/if}
</div>

<style lang="scss">
    $timeline-row-height: 3.0rem;
    $timeline-header-height: 4.5rem;
    $timeline-column-width: 2rem;
    $timeline-bg-color: var(--theme-comp-header-color);
    $timeline-border-color: var(--theme-bg-divider-color);
    $timeline-border: 1px solid $timeline-border-color;
    $timeline-weekend-stroke-color: var(--theme-calendar-weekend-stroke-color);

    .timeline-container {
      flex: 1;
      min-height: 0;
      position: relative;
      overflow: hidden;
    }

    .vertical-scroll {
      height: 100%;
      overflow-y: auto;
      overflow-x: auto;
    }

    .timeline {
      min-width: 100%;
      width: max-content;
      min-height: 100%;
      display: grid;
      grid-auto-flow: column;
      grid-template-columns: auto 1fr;
      grid-template-rows: auto 1fr;
      position: relative;
    }

    .timeline-header {
      height: $timeline-header-height;
      background-color: $timeline-bg-color;
      position: sticky;
      top: 0;
      z-index: 10;

      &.timeline-resource-header {
        left: 0;
        z-index: 11;
      }

      .timeline-row {
        height: $timeline-header-height !important;
      }

      .timeline-resource-cell {
        height: $timeline-header-height;
        display: flex;
        align-items: center;
        border-bottom: $timeline-border;
      }

      .timeline-day-header {
        height: $timeline-header-height;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
    }

    .timeline-resource-header__title {
      font-size: 1rem;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }

    .timeline-resource-header__subtitle {
      font-size: 0.75rem;
      font-weight: 400;
      line-height: 1.25rem;
      opacity: 0.4;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }

    .timeline-resource-content {
      background-color: $timeline-bg-color;
      position: sticky;
      left: 0;
      z-index: 9;
    }

    .timeline-grid-content {
      position: relative;
      z-index: 1;
      margin: 0;
      padding: 0;
    }

    .timeline-day-header {
      cursor: pointer;

      .timeline-day-header__day {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1.3125rem;
        height: 1.3125rem;
        font-size: 0.8125rem;
        font-weight: 500;
        margin: 0 auto;
        color: var(--theme-content-color);

        &.timeline-day-header__day--today {
          color: var(--theme-calendar-today-color);
          background-color: var(--theme-calendar-today-bgcolor);
          border-radius: 0.375rem;
        }
      }

      .timeline-day-header__weekday {
        font-size: 0.6875rem;
        font-weight: 400;
        line-height: 1.25rem;
        opacity: 0.4;
        text-align: center;
        color: var(--theme-content-color);
      }
    }

    .timeline-grid-bg {
      background-image: linear-gradient(
        135deg,
        $timeline-weekend-stroke-color 10%,
        $timeline-bg-color 10%,
        $timeline-bg-color 50%,
        $timeline-weekend-stroke-color 50%,
        $timeline-weekend-stroke-color 60%,
        $timeline-bg-color 60%,
        $timeline-bg-color 100%
      );
      background-size: 7px 7px;
    }

    .timeline-row {
      position: relative;
      height: $timeline-row-height;
      border-bottom: $timeline-border;

      &.project-header-row {
        background-color: $timeline-bg-color;
      }
    }
    .timeline-cell {
      border-right: $timeline-border;
      width: $timeline-column-width;
      height: 100%;
      background-color: $timeline-bg-color;
      position: relative;
      z-index: 1;
      padding: 0;
      margin: 0;

      &.today-column {
        background-image: linear-gradient(
          135deg,
          var(--theme-calendar-weekend-color) 10%,
          $timeline-bg-color 10%,
          $timeline-bg-color 50%,
          var(--theme-calendar-weekend-color) 50%,
          var(--theme-calendar-weekend-color) 60%,
          $timeline-bg-color 60%,
          $timeline-bg-color 100%
          );
          background-size: 7px 7px;
          opacity: 0.3;
        }

        &.project-header-cell {
          background-color: $timeline-bg-color !important;
          border-right: none;
          background-image: none !important;
        }
    }

    .timeline-resource-cell {
      border-right: $timeline-border;
      width: 300px;
      min-width: 300px;
      height: 100%;
      padding: 0.25rem 1.5rem;
      background-color: $timeline-bg-color;
      position: relative;
      z-index: 9;
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-size: 0.95rem;
      font-weight: 500;
      color: var(--theme-content-color);
      white-space: nowrap;
      overflow: hidden;

      &.project-header {
        font-size: 1rem;
        font-weight: 600;
        color: var(--theme-primary-color);
        padding-left: 0.5rem;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        text-align: left;
        gap: 0.5rem;
      }

      .collapse-btn {
        border: none;
        background: transparent;
        color: var(--theme-primary-color);
        cursor: pointer;
        padding: 0.125rem;
        margin-right: 0.25rem;
        line-height: 1;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .chevron {
        width: 16px;
        height: 16px;
        transform: rotate(0deg);
        transition: transform 0.15s ease-in-out;
      }

      .chevron.expanded {
        transform: rotate(90deg);
      }

      &.project-item {
        padding-left: 2rem;

        .epic-row {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .epic-title {
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 0.875rem;
          font-weight: 450;
          color: var(--theme-primary-color);
          flex: 1;
          min-width: 0;
        }

        .assignee-avatar {
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--theme-state-primary-color);
          flex-shrink: 0;
        }
      }
    }

    .epic-container {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      height: 1.8rem;
      width: 100%;
      pointer-events: all;
      cursor: pointer;
      z-index: 2;
    }

    .epic-section {
      position: absolute;
      height: 100%;

      .epic-content {
        height: 100%;
        padding: 0.25rem 0.5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.75rem;
        font-weight: 500;
        color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px var(--accent-shadow);
        margin: 0 -1px; // Negative margin to overlap borders
        position: relative;

        .overdue-mark {
          color: var(--white-color);
          font-weight: 800;
          font-size: 1rem;
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25),
                       0 0 15px rgba(255, 255, 255, 0.2);
          z-index: 3; // Ensure hovered item is above others
        }
      }

      &.epic-green .epic-content {
        background: var(--theme-state-positive-color);
        border-radius: 4px 0 0 4px;
        margin-right: 0; // No right margin to connect with blue
      }

      &.epic-blue .epic-content {
        background: var(--theme-state-primary-color);
        border-radius: 0;
        margin: 0 -1px; // Negative margin on both sides
      }

      &.epic-red .epic-content {
        background: var(--theme-state-negative-color);
        border-radius: 0 4px 4px 0;
        margin-left: 0; // No left margin to connect with blue
      }
    }

    :global(.tooltip-container) {
      background: var(--theme-popup-background);
      border-radius: 4px;
      padding: 8px 12px;
      min-width: 150px;
    }

    :global(.tooltip-header) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    :global(.tooltip-type) {
      font-weight: 500;
    }

    :global(.tooltip-dates) {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .highlight {
      background-color: var(--theme-state-primary-background-color);
      color: var(--theme-state-primary-color);
      border-radius: 2px;
      padding: 0.1rem 0.2rem;
      margin: 0 -0.2rem;
      font-weight: 600;
    }

    :global(mark.highlight) {
      background-color: var(--theme-state-primary-background-color);
      color: var(--theme-state-primary-color);
      border-radius: 2px;
      padding: 0.1rem 0.2rem;
      margin: 0 -0.2rem;
      font-weight: 600;
    }

    .milestone-line {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background-color: var(--theme-state-primary-color);
      z-index: 8;
      pointer-events: all;
      cursor: pointer;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -4px;
        width: 10px;
        height: 10px;
        background-color: var(--theme-state-primary-color);
        border-radius: 50%;
      }

      .milestone-label {
        position: absolute;
        top: 16px;
        left: 8px;
        background-color: var(--theme-state-primary-color);
        color: var(--white-color);
        padding: 4px 10px;
        border-radius: 4px;
        font-size: 0.75rem;
        white-space: nowrap;
        transform: translateY(-50%);
        transition: all 0.2s ease;
        cursor: pointer;
      }
    }
  .overdue-icon {
    padding: 0 7px;
    border: 1px solid #fff;
    border-radius: 50%;
  }
</style>
