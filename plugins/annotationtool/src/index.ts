//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import { Class, Doc, Ref } from '@hcengineering/core'
import { Asset, IntlString, Metadata, Plugin } from '@hcengineering/platform'
import { plugin } from '@hcengineering/platform'
import { AnnotationToolDoc, Label, TriageLabel, Annotator, ConflictResolvingAnnotator, AnnotationActivity } from './types'
import { AnyComponent } from '@hcengineering/ui/src/types'


/**
 * @public
*/
export const annotationToolId = 'annotationtool' as Plugin

export * from './types'

export default plugin(annotationToolId, {
  icon: {
    AnnotationTool: '' as Asset,
    Lock: '' as Asset,
    Copy: '' as Asset,
    Bookmarks: '' as Asset,
    Delete: '' as Asset,
    Failed: '' as Asset,
    Edit: '' as Asset,
    Dashboard: '' as Asset,
    CheckmarkCircle: '' as Asset,
    X: '' as Asset,
    XCircle: '' as Asset,
    Calendar: '' as Asset,
    CardView: '' as Asset,
    ListView: '' as Asset,
    Hospital: '' as Asset,
    Patient: '' as Asset,
    Manufacturer: '' as Asset,
    Label: '' as Asset,
    Doctor: '' as Asset,
    DateFrom: '' as Asset,
    DateTo: '' as Asset,
    Sorting: '' as Asset,
    Timer: '' as Asset,
    Tag: '' as Asset,
    TagAdd: '' as Asset,
    Key: '' as Asset,
    Ecg: '' as Asset,
    ChevronDown: '' as Asset,
    ChevronUp: '' as Asset,
    ChevronLeft: '' as Asset,
    ChevronRight: '' as Asset,
    ChevronDoubleLeft: '' as Asset,
    ChevronDoubleRight: '' as Asset,
    ZoomOut: '' as Asset,
    Reply: '' as Asset,
    ZoomIn: '' as Asset,
    Expand: '' as Asset,
    History: '' as Asset,
    EpisodeCollapse: '' as Asset,
    EpisodeExpand: '' as Asset,
    AnnotatorPermissions: '' as Asset,
    Warning: '' as Asset,
    Conflict: '' as Asset,
    Activity: '' as Asset,
    LoadMoreSeconds: '' as Asset,
    UnloadMoreSeconds: '' as Asset,
    ApplyToWholeEpisode: '' as Asset,
    MyAnnotations: '' as Asset,
    AllAnnotations: '' as Asset
  },
  class: {
    AnnotationToolDoc: '' as Ref<Class<AnnotationToolDoc>>,
    Label: '' as Ref<Class<Label>>,
    TriageLabel: '' as Ref<Class<TriageLabel>>,
    Annotator: '' as Ref<Class<Annotator>>,
    ConflictResolvingAnnotator: '' as Ref<Class<ConflictResolvingAnnotator>>,
    AnnotationActivity: '' as Ref<Class<AnnotationActivity>>
  },
  string: {
    AnnotationActivityTitle: '' as IntlString,
    AnnotationActivityNotificationBody: '' as IntlString,
    ShowingEpisodes: '' as IntlString,
    LoadingEpisodes: '' as IntlString
  },
  component: {
    AnnotationTool: '' as AnyComponent,
    Settings: '' as AnyComponent
  },
  app: {
    AnnotationToolApp: '' as Ref<Doc>
  },
  metadata: {
    RdUrl: '' as Metadata<string>
  } 
})