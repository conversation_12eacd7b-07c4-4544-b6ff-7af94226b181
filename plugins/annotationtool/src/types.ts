//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import type { Doc, Domain, Ref, Space, Account } from '@hcengineering/core'
import type { Project } from '@hcengineering/tracker'


/**
 * Domain for annotationtool-related documents
 * @public
 */
export const DOMAIN_ANNOTATIONTOOL = 'annotationtool' as Domain
export const DOMAIN_ANNOTATIONTOOL_ACTIVITIES = 'annotationtool-activities' as Domain

/**
 * Example interface for annotationtool
 * @public
 */
export interface AnnotationToolDoc extends Doc {
  name: string
  description?: string
  space: Ref<Space>
}

export interface Label extends Doc {
  value: string
  label: string
  color: number
  attachedToProject: Ref<Project>
}

export interface LabelWithColors extends Label {
  borderColor: string
  backgroundColor: string
  textColor: string
}

export interface TriageLabel extends Label {}

export interface Annotator extends Doc {
  accountId: Ref<Account>
  accessLevel: string
  attachedToProject: Ref<Project>
}

export interface ConflictResolvingAnnotator extends Doc {
  accountId: Ref<Account>
}

export interface AnnotationActivity extends Doc {
  episodeId?: string
  stripId?: string
  projectId: Ref<Project>
  datasetId: string
  space: Ref<Project>
}