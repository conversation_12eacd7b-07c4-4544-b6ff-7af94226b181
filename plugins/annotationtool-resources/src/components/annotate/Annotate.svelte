<script lang="ts">
    import { selectedFiltersStore } from '../../filtersStore'
    import AnnotateHeader from './components/AnnotateHeader.svelte'
    import EpisodeCard from './components/EpisodeCard.svelte'
    import { fetchEpisodes } from '../../api'
    import { <PERSON><PERSON><PERSON>, Spinner, Label } from '@hcengineering/ui'
    import { Annotator, Label as LabelType, TriageLabel as TriageLabelType } from '@hcengineering/annotationtool'
    import { Ref, Class, WithLookup, SortingOrder, getCurrentAccount, Account } from '@hcengineering/core'
    import { createQuery } from '@hcengineering/presentation'
    import { personAccountByIdStore } from '@hcengineering/contact-resources'
    import type { Episode, SelectedFiltersConfig, EpisodeLabellingStoreConfig } from '../../types'
    import { createEpisodeLabellingStore } from '../../episodeLabellingStore'
    import annotationTool from '../../plugin'
    import type { Project } from '@hcengineering/tracker'
    import { createSubmitLabelsStore } from '../../submitLabelsStore'
    import { annotatorAccessLevelItems } from '../../constants'

    export let project: Project

    let viewMode: 'card' | 'list' = (localStorage.getItem('annotation-tool-viewMode') as 'card' | 'list') ?? 'list'

    const labelsQuery = createQuery()
    const triageLabelsQuery = createQuery()
    const annotatorAccessLevelsQuery = createQuery()

    let labels: WithLookup<LabelType>[] = []
    let triageLabels: WithLookup<TriageLabelType>[] = []
    let annotatorAccessLevels: Map<string, string> = new Map()
    let numDoneEpisodes = 0

    const { addToSubmitQueue } = createSubmitLabelsStore()

    $: selectedFilters = $selectedFiltersStore

    // Reset the number of done episodes when the filters change
    $: if (selectedFilters) {
        numDoneEpisodes = 0
    }

    let episodesLoading = true
    let episodesError = false
    let episodes: Episode[] = []
    let episodeLabellingStores: EpisodeLabellingStoreConfig[] = []
    let totalEpisodes = 0
    let currentPage = 1
    let hasMore = true
    let loadingMore = false

    const getEpisodes = async (selectedFilters: SelectedFiltersConfig, project: Project, currentUserId: string) => {
        try {
            episodesLoading = true
            episodesError = false
            const datasetID = project.datasetID || `project-${project._id}` || 'default-dataset'
            const response = await fetchEpisodes(selectedFilters, datasetID)
            episodes = response.episodes
            totalEpisodes = response.total_count
            episodeLabellingStores = episodes.map(episode => createEpisodeLabellingStore(episode, currentUserId, datasetID, addToSubmitQueue))
            currentPage = parseInt(selectedFilters.page?.value ?? '1') || 1
            const itemsPerPage = parseInt(selectedFilters.limit?.value ?? '10') || 10
            hasMore = episodes.length < totalEpisodes && episodes.length >= itemsPerPage
        } catch (error) {
            console.error(error)
            episodesError = true
        } finally {
            episodesLoading = false
        }
    }

    const loadMoreEpisodes = async () => {
        if (loadingMore || episodesLoading || episodesError || !hasMore) return

        try {
            loadingMore = true
            const datasetID = project.datasetID || `project-${project._id}` || 'default-dataset'
            const nextPage = currentPage + 1
            const moreFilters: SelectedFiltersConfig = {
                ...selectedFilters,
                page: { value: String(nextPage), label: '' }
            }
            const response = await fetchEpisodes(moreFilters, datasetID)
            const newEpisodes = response.episodes || []
            episodes = [...episodes, ...newEpisodes]
            episodeLabellingStores = [
                ...episodeLabellingStores,
                ...newEpisodes.map(episode => createEpisodeLabellingStore(episode, getCurrentAccount()._id, datasetID, addToSubmitQueue))
            ]
            currentPage = nextPage
            totalEpisodes = response.total_count
            hasMore = episodes.length < totalEpisodes && newEpisodes.length > 0
        } catch (error) {
            console.error(error)
        } finally {
            loadingMore = false
        }
    }

    let scrollDiv: HTMLDivElement | undefined | null = undefined
    function maybeLoadMore (): void {
        if (loadingMore || episodesLoading || episodesError || !hasMore || !scrollDiv) return
        const scrollTop = scrollDiv.scrollTop
        const clientHeight = scrollDiv.clientHeight
        const scrollHeight = scrollDiv.scrollHeight
        if (scrollTop + clientHeight >= scrollHeight - 200) {
            loadMoreEpisodes()
        }
    }

    function onScrollerScroll (): void {
        maybeLoadMore()
    }

    const getAnnotatorAccessLevels = async (currentUserId: string) => {
        const currentPersonId = $personAccountByIdStore.get(currentUserId as any)?.person as any

        annotatorAccessLevelsQuery.query(
            annotationTool.class.Annotator as Ref<Class<Annotator>>,
            {
                accountId: currentPersonId as Ref<Account>
            },
            (res) => {
                annotatorAccessLevels = res.reduce((acc, item) => {
                    acc.set(item.attachedToProject, item.accessLevel)
                    return acc
                }, new Map<string, string>())
            }
        )
    }
    
    const getLabels = async (projectId: string) => {
        if(!projectId) return
        
        labelsQuery.query(
            annotationTool.class.Label as Ref<Class<LabelType>>,
            {
                attachedToProject: projectId as Ref<Project>
            },
            (res) => {
                labels = res
            },
            { sort: { createdOn: SortingOrder.Ascending } }
        )
    }

    const getTriageLabels = async (projectId: string) => {
        if(!projectId) return

        triageLabelsQuery.query(
            annotationTool.class.TriageLabel as Ref<Class<TriageLabelType>>,
            {
                attachedToProject: projectId as Ref<Project>
            },
            (res) => {
                triageLabels = res
            },
            { sort: { createdOn: SortingOrder.Ascending } }
        )
    }

    const handleEpisodeMarkedAsDone = (ev: CustomEvent<{ isDone: boolean }>) => {
        if (ev.detail.isDone) {
            numDoneEpisodes++
        } else if (numDoneEpisodes > 0) {
            numDoneEpisodes--
        }
    }
    
    $: getLabels(project._id)
    $: getTriageLabels(project._id)

    $: userId = getCurrentAccount()._id
    $: getEpisodes(selectedFilters, project, userId)
    $: getAnnotatorAccessLevels(userId)
    $: localStorage.setItem('annotation-tool-viewMode', viewMode)
</script>



<AnnotateHeader bind:viewMode {project} annotatorAccessLevel={annotatorAccessLevels.get(project._id) ?? annotatorAccessLevelItems[0].id} episodesLoaded={episodes.length} {totalEpisodes} {episodesLoading} {numDoneEpisodes} />

{#if episodesLoading}
    <div class="flex justify-center items-center p-8 episodes-loading">
        <Spinner size="x-large" />
    </div>
{:else if !episodesError}
    <Scroller disablePointerEventsOnScroll bind:divScroll={scrollDiv} onScroll={onScrollerScroll}>
        {#each episodes as episode, index (episode.episode_id)}
                <EpisodeCard 
                    episode={episode} 
                    store={episodeLabellingStores[index]} 
                    labels={labels} 
                    triageLabels={triageLabels}
                    topMargin={index !== 0}
                    {viewMode}  
                    catalog={project.datasetID ?? ''}
                    {project}
                    annotatorAccessLevel={annotatorAccessLevels.get(project._id) ?? ''}
                    on:episode-marked-as-done={handleEpisodeMarkedAsDone}
                />
                {#if index !== episodes.length - 1}
                    <div class="horizontal-divider" />
                {/if}
        {/each}
        {#if !episodes.length}
            <div class="flex items-center justify-center text-base text-center" style="height: 500px">
                <Label label={annotationTool.string.NoEpisodesFound} />
            </div>
        {/if}
        {#if hasMore && loadingMore}
            <div class="flex items-center justify-center p-6">
                <Spinner size="large" />
            </div>
        {/if}
    </Scroller>
{:else}
    <div class="flex items-center justify-center text-red-500 text-base text-center" style="height: 500px">
        <Label label={annotationTool.string.ErrorLoadingEpisodes} />
    </div>
{/if}

<style lang="scss">
    .episodes-loading {
        height: 80vh;
    }

    .not-labelled-episodes-warning {
        position: fixed;
        width: -webkit-fill-available;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        bottom: 2rem;
        margin: 0 auto;
        text-align: center;
    }

    .horizontal-divider {
        position: relative;
        padding: 1.5rem 1rem 1rem;
        width: 100%;
        border-top: 1px solid var(--theme-divider-color);
        margin-top: 1rem;
    }

    .horizontal-divider::before {
        content: "Start of next episode";
        position: absolute;
        top: -0.6em;
        left: calc(50% - 80px);
        background: var(--theme-bg-color, white);
        padding: 0.25em 0.75em;
        font-weight: bold;
        font-size: 0.875rem;
        border-radius: 0.25rem;
        color: var(--theme-content-color);
        line-height: 1;
    }
</style>
