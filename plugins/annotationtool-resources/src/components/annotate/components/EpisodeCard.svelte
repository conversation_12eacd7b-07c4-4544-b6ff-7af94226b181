<script lang="ts">
  import { Label as LabelType, TriageLabel as TriageLabelType } from '@hcengineering/annotationtool'
  import chunter from '@hcengineering/chunter'
  import { getClient } from '@hcengineering/presentation'
  import { Project } from '@hcengineering/tracker'
  import { Button, ButtonIcon, CheckBox, closePopup, Icon, Label, showPopup, tooltip as tp } from '@hcengineering/ui'
  import { createEventDispatcher } from 'svelte'
  import annotationTool from '../../../plugin'
  import { Episode, EpisodeLabellingStoreConfig } from '../../../types'
  import { getEpisodeMetaItems } from '../../../utils'
  import { generateShuffledArray1to21 } from '../../../utils/labelLayoutUtils'
  import AnnotationLabel from '../../settings/components/AnnotationLabel.svelte'
  import UserActivityModal from '../../user-activity/UserActivityModal.svelte'
  import EpisodeStrip from './EpisodeStrip.svelte'

  export let episode: Episode
  export let store: EpisodeLabellingStoreConfig
  export let labels: LabelType[]
  export let triageLabels: TriageLabelType[]
  export let topMargin: boolean = false
  export let viewMode: 'list' | 'card' = 'card'
  export let isConflictResolution: boolean = false
  export let catalog: string
  export let project: Project
  export let annotatorAccessLevel: string

  const client = getClient()
  const dispatch = createEventDispatcher()

  const shuffledColors = generateShuffledArray1to21()

  let isExpanded: boolean = true
  let hasEpisodeActivity: boolean = false
  let stripActivity: string[] = []
  let zoomLevel: number = viewMode === 'card' ? 0 : 0.5
  let annotationsView: 'my' | 'all' = 'my'

  const { episodeLabellingStore, episodeLabellingStoreActions } = store;

  const getEpisodeActivity = async (episodeId: string, projectId: string, catalog: string) => {
    if(!episodeId || !projectId || !catalog) return
    
    try {
      hasEpisodeActivity = false
      stripActivity = []
      
      const activities = await client.findAll(annotationTool.class.AnnotationActivity, {
        episodeId,
        projectId: projectId as any,
        datasetId: catalog
      })

      if(!activities || activities.length === 0) {
        hasEpisodeActivity = false
        stripActivity = []

        return
      }

      const episodeActivityId = activities.find(activity => !activity.stripId)?._id
      const activityIds = activities.map(activity => activity._id)

      const activityMessages = await client.findAll(chunter.class.ChatMessage, {
        attachedTo: {$in: activityIds},
      }) as any

      activityMessages.forEach((message: any) => {
        if(message.attachedTo === episodeActivityId) {
          hasEpisodeActivity = true
        } else {
          const stripActivityID = activityIds.find(id => id === message.attachedTo)
          const stripID = activities.find(activity => activity._id === stripActivityID)?.stripId
          stripActivity.push(stripID ?? '')
        }
      })

      stripActivity = [...stripActivity]
    } catch (error) {
      console.error(error)
    }
  }

  const handleOnKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      isExpanded = true
    }
  }

  const handleOpenEpisodeActivity = () => {
    showPopup(UserActivityModal, {
      episodeId: episode.episode_id,
      stripId: undefined,
      catalog,
      labels: labels,
      hidden: false,
      project,
      annotatorAccessLevel,
      onCancel: closePopup
    })
  }

  const handleEpisodeActivityKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleOpenEpisodeActivity()
    }
  }

  const handleEpisodeMarkedAsDone = (isDone: boolean) => {
    episodeLabellingStoreActions.addRemoveEpisodeDone(isDone)
    
    dispatch('episode-marked-as-done', { isDone })
  }

  function handleStripZoomOut() {
        if (zoomLevel >= 1) return

        zoomLevel += 0.25
    }

    function handleStripZoomIn() {
        if (zoomLevel <= 0) return

        zoomLevel -= 0.25
    }

  $: getEpisodeActivity(episode.episode_id, project._id, catalog)
</script>

<div id={episode.episode_id} class="episode-card shadow-md" class:top-margin={topMargin} data-episode-id={episode.episode_id}>
  <div class="episode-header">
    <div class="flex w-full justify-between items-stretch h-full" class:episode-header-divider={isExpanded}>
      <div class="episode-meta">
        {#each getEpisodeMetaItems(episode) as item, index (index)}
          <!-- svelte-ignore a11y-no-noninteractive-tabindex -->
          <div 
            class="episode-meta-item" 
            class:clickable={index === 0}
            class:has-activity={index === 0 && hasEpisodeActivity}
            use:tp={item.tooltip} 
            on:click={index === 0 ? handleOpenEpisodeActivity : undefined}
            on:keydown={index === 0 ? handleEpisodeActivityKeyDown : undefined}
            tabindex={index === 0 ? 0 : undefined}
            role={index === 0 ? "button" : undefined}
          >
            <Icon icon={item.icon} size="medium" />
            <span>{item.label}</span>
          </div>
        {/each}
      </div>
      <div class="episode-meta-controls">
        {#if isExpanded}
          <ButtonIcon icon={annotationTool.icon.ZoomOut} size="small" kind="tertiary" disabled={zoomLevel >= 1} on:click={handleStripZoomOut} />
          <ButtonIcon icon={annotationTool.icon.ZoomIn} size="small" kind="tertiary" disabled={zoomLevel <= 0} on:click={handleStripZoomIn} />
          <div class="vertical-divider" />
            <ButtonIcon
                icon={annotationTool.icon.EpisodeCollapse}
                iconSize="medium"
                size="small"
                kind="tertiary"
                tooltip={{label: annotationTool.string.CollapseEpisode}}
                on:click={() => isExpanded = false}
            />
        {:else}
            <ButtonIcon
                icon={annotationTool.icon.EpisodeExpand}
                iconSize="medium"
                size="small"
                kind="tertiary"
                tooltip={{label: annotationTool.string.ExpandEpisode}}
                on:click={() => isExpanded = true}
            />
        {/if}
      </div>
    </div>
    {#if isExpanded}
      <div class="episode-annotation-container">
          <div class="episode-labels-container">
              <div class="episode-labels">
                  {#each labels as label (label.value)}
                      <AnnotationLabel 
                          label={label} 
                          selected={$episodeLabellingStore.episodeLabels.some(l => l.label === label.value)}
                          approved={$episodeLabellingStore.episodeApprovedLabels.some(l => l.label === label.value)}
                          disapproved={$episodeLabellingStore.episodeDisapprovedLabels.some(l => l.label === label.value)}
                          numSelected={$episodeLabellingStore.episodeLabels.filter(l => l.label === label.value).length}
                          selectable
                          width="125px"
                          onSelect={() => episodeLabellingStoreActions.addRemoveEpisodeLabel({label: label.value, timestamp: new Date().getTime()})} 
                          allowDelete={false}
                          {isConflictResolution}
                          onApprove={() => episodeLabellingStoreActions.approveEpisodeLabel({label: label.value, timestamp: new Date().getTime()})}
                          onDisapprove={() => episodeLabellingStoreActions.disapproveEpisodeLabel({label: label.value, timestamp: new Date().getTime()})}
                      />
                  {/each}
              </div>
              <div class="flex items-center flex-gap-2">
                {#if !isConflictResolution}
                  <CheckBox 
                    size="large"
                    checked={$episodeLabellingStore.isInterestingEpisode} 
                    on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeInteresting(ev.detail)} 
                  />
                  <Label label={annotationTool.string.IsInterestingEpisode} />
                  <div class="vertical-divider" />
                  <CheckBox 
                    size="large"
                    checked={$episodeLabellingStore.isDoneEpisode} 
                    on:value={(ev) => handleEpisodeMarkedAsDone(ev.detail)} 
                  />
                  <Label label={annotationTool.string.IsDoneEpisode} />
                {:else}
                  <CheckBox 
                    size="large"
                    checked={$episodeLabellingStore.isConflictResolvedEpisode} 
                    on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeConflictResolved(ev.detail)} 
                  />
                  <Label label={annotationTool.string.IsConflictResolvedEpisode} />
                {/if}
                <div class="vertical-divider" />
                <Button
                  icon={annotationTool.icon.Delete}
                  label={annotationTool.string.ClearAll}
                  size="medium"
                  kind="negative"
                  on:click={() => episodeLabellingStoreActions.resetEpisodeLabelling()}
                />
              </div>
          </div>
      </div>
    {/if}
  </div>
  {#if isExpanded}
    <div class:episode-list-view={viewMode === 'list'} class:episode-card-view={viewMode === 'card'}>
        {#each episode.strips as strip, index (index)}
          {@const hasActivity = stripActivity.includes(strip.id)}
          <EpisodeStrip
              stripId={strip.id}
              nextStripId={episode.strips[index + 1]?.id}
              episodeId={episode.episode_id}
              episodeStore={episodeLabellingStore}
              episodeStoreActions={episodeLabellingStoreActions}
              stripValues={strip.voltages}
              stripTimestamps={strip.timestamps}
              nextStripValues={episode.strips[index + 1]?.voltages}
              nextStripTimestamps={episode.strips[index + 1]?.timestamps}
              episodeLevelZoom={zoomLevel}
              {annotatorAccessLevel}
              {hasActivity}
              {labels}
              {triageLabels}
              {viewMode}
              {isConflictResolution}
              {catalog}
              {project}
              {shuffledColors}
              bind:annotationsView
          />
        {/each}
    </div>
    <div class="horizontal-divider" style="margin-top: 0.5rem;" />
    <div class="episode-annotation-container">
        <div class="episode-labels-container">
            <div class="episode-meta-item" style="font-weight: 500;">
              <Icon icon={annotationTool.icon.Key} size="medium" />
              <span>{episode.episode_id}</span>
            </div>
            <div class="flex items-center flex-gap-2">
              {#if !isConflictResolution}
                <CheckBox 
                  size="large"
                  checked={$episodeLabellingStore.isInterestingEpisode} 
                  on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeInteresting(ev.detail)} 
                />
                <Label label={annotationTool.string.IsInterestingEpisode} />
                <div class="vertical-divider" />
                <CheckBox 
                  size="large"
                  checked={$episodeLabellingStore.isDoneEpisode} 
                  on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeDone(ev.detail)} 
                />
                <Label label={annotationTool.string.IsDoneEpisode} />
              {:else}
                <CheckBox 
                  size="large"
                  checked={$episodeLabellingStore.isConflictResolvedEpisode} 
                  on:value={(ev) => episodeLabellingStoreActions.addRemoveEpisodeConflictResolved(ev.detail)} 
                />
                <Label label={annotationTool.string.IsConflictResolvedEpisode} />
              {/if}
              <div class="vertical-divider" />
              <Button
                icon={annotationTool.icon.Delete}
                label={annotationTool.string.ClearAll}
                size="medium"
                kind="negative"
                on:click={() => episodeLabellingStoreActions.resetEpisodeLabelling()}
              />
            </div>
        </div>
    </div>
  {:else}
    <div 
        class="episode-collapsed cursor-pointer" 
        role="button"
        tabindex="0"
        aria-label={annotationTool.string.ExpandEpisode}
        on:click={() => isExpanded = true}
        on:keydown={handleOnKeyDown}    
        use:tp={{label: annotationTool.string.ExpandEpisode}}
    >
        <Icon icon={annotationTool.icon.EpisodeCollapse} size="medium" />
        <Label label={annotationTool.string.EpisodeIsCollapsed} />
    </div>
{/if}
</div>

<style lang="scss">
  .episode-card {
    border: 1px solid var(--theme-divider-color);
    border-radius: 0.75rem;
    padding: 0.5rem;
    margin: 0.5rem 0.5rem 3rem 0.5rem;
  }

  .top-margin {
    margin-top: 3rem;
  }

  .episode-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
    min-height: 48px;
    background-color: var(--theme-mention-bg-color-notransparent);
    padding: 0.5rem;
    margin: -0.5rem -0.5rem 0.5rem -0.5rem;
    border-bottom: 1px solid var(--theme-divider-color);
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;

    // Sticky header when scrolling
    position: sticky;
    top: 0;
    z-index: 1001;
  }

  .episode-id {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 600;
    font-size: 16px;
  }

  .horizontal-divider {
    width: 100%;
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .vertical-divider {
    height: 30px;
    border-right: 1px solid var(--theme-divider-color);
  }

  .episode-header-divider {
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--theme-divider-color);
  }

  .episode-meta {
    display: grid;
    grid-template-columns: auto max-content auto auto auto auto auto;
    width: 100%;
    gap: 1rem;
    width: 100%;
    font-weight: 500;
  }

  .episode-meta-controls {
    display: flex;
    flex-direction: row;
    gap: 0.25rem;
  }

  .episode-buttons {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    justify-self: stretch;
    align-items: flex-end;
  }

  .episode-meta-item {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-wrap: nowrap;
  }

  .has-activity span {
    position: relative; 
  }

  .has-activity span::after {
    content: '';
    position: absolute;
    top: 0;
    right: -10px;
    width: 8px;
    height: 8px;
    background-color: var(--highlight-red);;
    border-radius: 100%;
  }

  .episode-list-view {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .episode-card-view {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .episode-collapsed {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    font-weight: 600;
  }

  .episode-annotation-container {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      gap: 8px;
      margin-top: 0.5rem;

      :global(button) {
        margin: auto 0;
      }
  }

  .vertical-divider {
    border-right: 1px solid var(--theme-divider-color);
  }

  .episode-labels-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 8px;
  }

  .episode-labels-text {
      display: flex;
      justify-content: space-between;
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 600;
      color: var(--color-text-primary);
  }

  .episode-labels {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
  }

  .clickable:hover {
    cursor: pointer;
  }
</style>
