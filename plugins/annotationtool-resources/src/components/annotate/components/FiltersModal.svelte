<script lang="ts">
    import annotationTool from '../../../plugin'
    import { Modal, Button } from '@hcengineering/ui'
    import { filtersStoreActions } from '../../../filtersStore'
    import { getFilterItemsByAccessLevel, annotatorAccessLevelItems } from '../../../constants'
    import SelectWithSearch from './SelectWithSearch.svelte'
    import type { Project } from '@hcengineering/tracker'
    
    export let hidden: boolean = true
    export let onCancel: () => void = () => {}
    export let project: Project
    export let selectedApplication: 'annotate' | 'dashboard' | 'conflict'
    export let hiddenFilters: string[] = []
    export let annotatorAccessLevel: string = annotatorAccessLevelItems[0].id
    
    // Get filtered items based on access level
    $: filteredItems = getFilterItemsByAccessLevel(annotatorAccessLevel)
    
    function handleClose() {
        hidden = true
        onCancel()
    }

    function handleClearFilters() {
        filtersStoreActions.clearAllFilters(selectedApplication, project._id)
    }
</script>

<Modal
  type="type-popup"
  label={annotationTool.string.AnnotateFilters}
  {hidden}
  onCancel={handleClose}
  showOkButton={false}
  cancelLabel={annotationTool.string.CloseFilters}
  canSave={false}
>
    <div class="wrapper">
        <div class="filters-grid">
            {#each filteredItems as item (item.name)}
                {#if !hiddenFilters.includes(item.name)}
                    <SelectWithSearch
                        name={item.name}
                        icon={item.icon}
                        label={item.label}
                        placeholder={item.placeholder}
                        options={item.options}
                        fetchOptions={item.fetchOptions}
                        dependencies={item.dependencies}
                        type={item.type}
                        onChange={(name, newValue, newLabel) => filtersStoreActions.setFilter(name, newValue, newLabel, selectedApplication, project._id)}
                        onClear={(name) => filtersStoreActions.removeFilter(name, selectedApplication, project._id)}
                        parent={selectedApplication}
                        {project}
                    />
                {/if}
          {/each}
        </div>
        <div class="actions-container">
            <Button
                label={annotationTool.string.ClearFilters}
                icon={annotationTool.icon.Delete}
                kind={'negative'}
                size={'medium'}
                pressed={false}
                on:click={handleClearFilters}
            />
        </div>
    </div>
</Modal>

<style lang="scss">
    .wrapper {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 20px;
        row-gap: 30px;
    }
    .actions-container {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
</style>