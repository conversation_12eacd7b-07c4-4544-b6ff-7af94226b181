<script lang="ts">
    import { ButtonIcon, IconFilter, showPopup, closePopup, Label, Icon, But<PERSON>, Spinner } from '@hcengineering/ui'
    import annotationTool from '../../../plugin'
    import view from '@hcengineering/view'
    import { selectedFiltersStore, filtersStoreActions } from '../../../filtersStore'
    import { filterItems, annotatorAccessLevelItems } from '../../../constants'
    import FiltersModal from './FiltersModal.svelte'
    import AnnotationSettingsPopup from './AnnotationSettingsPopup.svelte'
    import type { Project } from '@hcengineering/tracker'

    export let viewMode: 'card' | 'list' = 'card'
    export let project: Project
    export let annotatorAccessLevel: string = annotatorAccessLevelItems[0].id
    export let episodesLoaded: number = 0
    export let totalEpisodes: number = 0
    export let episodesLoading: boolean = false
    export let numDoneEpisodes: number = 0

    const handleOpenFiltersModal = (event: MouseEvent) => {
        showPopup(
            FiltersModal, 
            { 
                hidden: false, 
                onCancel: closePopup, 
                project, 
                selectedApplication: 'annotate',
                hiddenFilters: ['conflict_status'],
                annotatorAccessLevel
            }, 
            event.currentTarget as HTMLElement
        )
    }

    const handleOpenSettingsModal = (event: MouseEvent) => {
        showPopup(AnnotationSettingsPopup, { hidden: false, onCancel: closePopup, viewMode, handleViewModeChange }, event.currentTarget as HTMLElement)
    }

    const handleViewModeChange = (mode: 'card' | 'list') => {
        viewMode = mode ?? 'card'
    }

    $: selectedFilters = $selectedFiltersStore
</script>

<div class="hulyHeader-container">
    <div class="hulyHeader-buttonsGroup before mr-2 no-print">
        <ButtonIcon
            icon={view.icon.Configure}
            kind="secondary"
            size={'medium'}
            pressed={false}
            on:click={handleOpenSettingsModal}
        />
    </div>
    <div class="hulyHeader-titleGroup">
      <button class="hulyBreadcrumb-container large current">
        <ButtonIcon
            icon={annotationTool.icon.Edit}
            kind="tertiary"
            size={'medium'}
            pressed={false}
            disabled={true}
        />
        <span class="heading-medium-16 line-height-auto hulyBreadcrumb-label overflow-label">
          <Label label={annotationTool.string.Title} /> - <Label label={annotationTool.string.Annotate} />
        </span>
      </button>
    </div>
    <div class="hulyHeader-buttonsGroup search no-print">
        <!-- Search input -->
        <Button
            icon={IconFilter}
            label={annotationTool.string.Filter}
            kind={'regular'}
            size={'medium'}
            pressed={false}
            showTooltip={{label: annotationTool.string.FilterTooltip}}
            on:click={handleOpenFiltersModal}
        />
    </div>
</div>
{#if Object.keys(selectedFilters).length > 0 || episodesLoaded > 0 || totalEpisodes > 0}
    <div class="filterbar-container">
        {#each Object.entries(selectedFilters) as [key, {value, label}], index}
            {#if label}
                {@const icon = filterItems.find(item => item.name === key)?.icon}
                <div class="filterbar-item">
                    {#if icon}
                        <Icon icon={icon} size={'small'} />
                    {/if}
                    {label}
                    <ButtonIcon
                        icon={annotationTool.icon.X}
                        kind="tertiary"
                        size={'small'}
                        on:click={() => filtersStoreActions.removeFilter(key, 'annotate', project._id)}
                    />
                </div>
            {/if}
        {/each}
        <div class="episode-counter-wrapper">
            <div class="episode-counter">
                {#if episodesLoading}
                    <Spinner size={'small'} />
                    <Label label={annotationTool.string.LoadingEpisodes} />
                {:else}
                    <span><Label label={annotationTool.string.ShowingEpisodes} params={{currentEpisodes: episodesLoaded, totalEpisodes: totalEpisodes}} /></span>
                {/if}
            </div>
            {#if numDoneEpisodes > 0}
                <div class="episode-counter">
                    <Label label={annotationTool.string.NumDoneEpisodes} params={{numDoneEpisodes}} />
                </div>
            {/if}
        </div>
    </div>
{/if}

<style lang="scss">
    .filterbar-container {
        display: flex;
        flex-direction: row;
        gap: 4px;
        padding: var(--spacing-0_5) var(--spacing-2);
        width: 100%;
        min-width: 0;
        background-color: var(--theme-comp-header-color);
        border-bottom: 1px solid var(--theme-divider-color);
    }

    .filterbar-item {
        display: flex;
        flex-direction: row;    
        align-items: center;
        gap: 6px;
        padding: 0 12px;
        border-radius: 12px;
        background-color: var(--theme-bg-divider-color);
        border: 1px solid var(--theme-divider-color);
        color: var(--theme-caption-color);
        font-size: 12px;
        font-weight: 500;
    }

    .episode-counter-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 4px;
        margin-left: auto;
    }

    .episode-counter {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 6px;
        padding: 0 12px;
        border-radius: 12px;
        background-color: var(--theme-bg-divider-color);
        border: 1px solid var(--theme-divider-color);
        color: var(--theme-caption-color);
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
        min-height: 34px;
    }
</style>