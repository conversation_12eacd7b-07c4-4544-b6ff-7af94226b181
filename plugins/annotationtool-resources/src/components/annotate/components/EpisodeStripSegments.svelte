<script lang="ts">
    import type {Label as LabelType } from '@hcengineering/annotationtool'
    import { tooltip, getPlatformColorDef } from '@hcengineering/ui'
    import { getUserSectionYOffset, getLabelYPosition, getContrastingTextColor } from '../../../utils/labelLayoutUtils'
    import { Line, Rect, Text } from 'layerchart'
    import { personAccountByIdStore, personByIdStore } from '@hcengineering/contact-resources'
    import type { LabelWithTrack } from '../../../utils/labelLayoutUtils'

    export let labelsWithTracks: Record<string, LabelWithTrack[]>
    export let isMultiUserMode: boolean
    export let shuffledColors: number[]
    export let height: number
    export let width: number
    export let xScale: any
    export let stripId: string
    export let episodeStoreActions: any
    export let labels: LabelType[]
    export let currentAccount: string
</script>

{#each Object.entries(labelsWithTracks) as [userId, userLabels], userIndex}
    {@const userSectionOffset = isMultiUserMode ? getUserSectionYOffset(userId, labelsWithTracks, 24, 4, 2, 6) : 0}
    {@const multiUserColorIndex = shuffledColors[userIndex]}
    
    <!-- Add separator line before each user section (except the first) -->
    {#if isMultiUserMode && userIndex > 0}
        {@const separatorY = height + 28 + userSectionOffset - (2 + 6)} <!-- Center line in separator area -->
        <Line
            x1={0}
            x2={width}
            y1={separatorY}
            y2={separatorY}
            stroke="var(--theme-divider-color)"
            stroke-width="1"
        />
    {/if}
    
    {#each userLabels as labelWithTrack}
        {@const userColor = isMultiUserMode ? multiUserColorIndex : (labelWithTrack.color ?? 0)}
        {@const platformColor = getPlatformColorDef(userColor, false)}
        {@const backgroundColor = platformColor.background}
        {@const textColor = platformColor.title}
        {@const yPosition = getLabelYPosition(labelWithTrack.track, height + 28, 24, 4, userSectionOffset)}
        
        <Rect
            x={xScale(labelWithTrack.start)}
            y={yPosition}
            width={xScale(labelWithTrack.end) - xScale(labelWithTrack.start)}
            height={24}
            style={`fill: ${backgroundColor}; rx: 4px; ry: 4px;`}
        />
        
        <!-- Avatar inside the label rectangle in multi-user mode -->
        {#if isMultiUserMode}
            <!-- SVG-native avatar inside the label -->
            {@const personAccount = Array.from($personAccountByIdStore.values()).find(pa => pa._id === userId)}
            {@const person = personAccount ? $personByIdStore.get(personAccount.person) : undefined}
            {@const userName = person?.name ?? 'Unknown User'}
            {@const nameparts = userName.includes(',') 
                ? userName.trim().split(',').map(part => part.trim())
                : userName.trim().split(/\s+/)}
            {@const userInitials = nameparts.length === 1 
                ? nameparts[0].charAt(0)?.toUpperCase() ?? '?' 
                : nameparts.length === 2 && userName.includes(',')
                    ? (nameparts[1].charAt(0) + nameparts[0].charAt(0)).toUpperCase() // firstname + surname for comma format
                    : (nameparts[0].charAt(0) + nameparts[nameparts.length - 1].charAt(0)).toUpperCase()}
            {@const formattedUserName = userName.split(',').reverse().join(' ')}
            
            {@const avatarBackgroundColor = person?.avatarProps?.color}
            {@const avatarTextColor = getContrastingTextColor(avatarBackgroundColor ?? '')}
            <rect
                x={xScale(labelWithTrack.start) + 4} 
                y={yPosition + 4} 
                width={16}
                height={16}
                rx="4"
                ry="4"
                fill={avatarBackgroundColor} 
                use:tooltip={{nonIntlLabel: formattedUserName}}
            />
            <text 
                x={xScale(labelWithTrack.start) + 12} 
                y={yPosition + 15} 
                text-anchor="middle" 
                fill={avatarTextColor} 
                font-size="9"
                font-weight="bold"
                font-family="system-ui, -apple-system, sans-serif"
                use:tooltip={{nonIntlLabel: formattedUserName}} 
            >
                {userInitials}
            </text>
        {/if}
        
        <Text
            value={labelWithTrack.label}
            x={xScale(labelWithTrack.start) + (isMultiUserMode ? 28 : 8)}
            y={yPosition + 16}
            class="strip-label-text"
            style={`fill: ${textColor};`}
        />
        <!-- Invisible clickable area for delete button -->
        <rect
            x={xScale(labelWithTrack.end) - 25}
            y={yPosition + 2}
            width={24}
            height={20}
            fill="transparent"
            style="pointer-events: all; cursor: pointer;"
            on:click={() => {episodeStoreActions.addRemoveLabelStrip(stripId, {...labelWithTrack, label: labels.find(l => l.label === labelWithTrack.label)?.value ?? ''})}}
            on:keydown={() => void 0}
            role="button"
            tabindex="0"
            aria-label="Delete label {labelWithTrack.label}"
        />
        {#if userId === currentAccount}
            <Text
                value="ⓧ"
                x={xScale(labelWithTrack.end) - 20}
                y={yPosition + 18}
                class="strip-label-button"
                style={`fill: ${textColor}; pointer-events: none;`}
            />
        {/if}
    {/each}
{/each}