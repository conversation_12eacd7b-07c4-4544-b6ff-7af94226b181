<script lang="ts">
  import { themeStore } from '@hcengineering/ui'

  export let width: number
  export let height: number
  export let padding: { left?: number; right?: number; top?: number; bottom?: number }
  export let xScale: any
  export let yScale: any
  export let viewMode: 'card' | 'list'

  $: majorGridColor = $themeStore.dark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'
  $: minorGridColor = $themeStore.dark ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.03)'

  function computeGridStyle(
    width: number,
    height: number,
    padding: { left?: number; right?: number; top?: number; bottom?: number },
    xScale: any,
    yScale: any,
    viewMode: 'card' | 'list',
    majorGridColor: string,
    minorGridColor: string
  ): string {
    const pl = padding?.left || 0
    const pr = padding?.right || 0
    const pt = padding?.top || 0
    const pb = padding?.bottom || 0
    const innerW = Math.max(0, width - pl - pr)
    const innerH = Math.max(0, height - pt - pb)

    const xTickCount = 10
    const xTicks = typeof xScale?.ticks === 'function' ? xScale.ticks(xTickCount) : []
    const xStep = xTicks.length >= 2 ? xScale(xTicks[1]) - xScale(xTicks[0]) : innerW / xTickCount
    const xRangeStart = typeof xScale?.range === 'function' ? (xScale.range()[0] as number) : 0
    let xOffset = xTicks.length ? xScale(xTicks[0]) - xRangeStart : 0

    const yTickCount = Math.max(4, Math.round(height / (width / 50) / (viewMode === 'card' ? 2 : 1)))
    const yTicks = typeof yScale?.ticks === 'function' ? yScale.ticks(yTickCount) : []
    const yStep = yTicks.length >= 2 ? Math.abs(yScale(yTicks[1]) - yScale(yTicks[0])) : innerH / 10
    let yPositions: number[] = []
    for (const tick of yTicks as any[]) {
      yPositions.push(yScale(tick))
    }
    let yOffset = yPositions.length ? Math.min(...yPositions) - pt : 0

    const gx = Number.isFinite(xStep) && xStep > 0 ? xStep : 0
    const gy = Number.isFinite(yStep) && yStep > 0 ? yStep : 0
    const gxMinor = gx / 5
    const gyMinor = gy / 5

    function mod(n: number, m: number): number {
      return m === 0 ? 0 : ((n % m) + m) % m
    }
    xOffset = mod(xOffset, gx || 1)
    yOffset = mod(yOffset, gy || 1)
    // Shift major grid lines 1px before the axis ticks
    const xOffsetMajor = mod(xOffset - 1, gx || 1) + 0.5
    const yOffsetMajor = mod(yOffset - 1, gy || 1) + 0.5

    return `
      left:${pl}px; top:${pt}px; right:${pr}px; bottom:${pb}px;
      --gx:${gx}px; --gy:${gy}px; --gx-minor:${gxMinor}px; --gy-minor:${gyMinor}px;
      --grid-major-color:${majorGridColor}; --grid-minor-color:${minorGridColor};
      background-position:${xOffset}px 0px, 0px ${yOffset}px, ${xOffsetMajor}px 0px, 0px ${yOffsetMajor}px;
    `
  }
</script>

<div
  class="css-grid-overlay"
  style={computeGridStyle(width, height, padding, xScale, yScale, viewMode, majorGridColor, minorGridColor)}
></div>

<style>
    .css-grid-overlay {
        position: absolute;
        pointer-events: none;
        /* Four edges are set via inline style to match plot padding */
        background:
            /* minor vertical lines */
            repeating-linear-gradient(
                to right,
                var(--grid-minor-color, var(--theme-overlay-color)) 0 1px,
                transparent 1px var(--gx-minor)
            ),
            /* minor horizontal lines */
            repeating-linear-gradient(
                to bottom,
                var(--grid-minor-color, var(--theme-overlay-color)) 0 1px,
                transparent 1px var(--gy-minor)
            ),
            /* major vertical lines */
            repeating-linear-gradient(
                to right,
                var(--grid-major-color, var(--theme-divider-color)) 0 1px,
                transparent 1px var(--gx)
            ),
            /* major horizontal lines */
            repeating-linear-gradient(
                to bottom,
                var(--grid-major-color, var(--theme-divider-color)) 0 1px,
                transparent 1px var(--gy)
            );
    }
</style>