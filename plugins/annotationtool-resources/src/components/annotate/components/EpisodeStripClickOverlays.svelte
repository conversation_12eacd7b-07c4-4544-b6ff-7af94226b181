<script lang="ts">
    export let width: number
    export let height: number
    export let padding: any
    export let xScale: any
    export let yScale: any
    export let onStripClick: (e: MouseEvent, params: { xScale: any, yScale: any, padding: { left?: number; right?: number; top?: number; bottom?: number }, width: number, height: number }) => void
    export let onLeftOrRightStripClick: (e: MouseEvent, params: { xScale: any, yScale: any, padding: { left?: number; right?: number; top?: number; bottom?: number }, width: number, height: number }, direction: 'left' | 'right') => void
</script>

<!-- HTML click overlay for annotations - excludes label area -->
<div
    class="absolute z-10"
    style="
        left: {padding.left}px; 
        top: {padding.top}px; 
        width: {width}px;
        height: {height}px;
        pointer-events: all;
    "
    role="button"
    tabindex="0"
    aria-label="Click to add annotation at time point"
    on:click={(e) => onStripClick(e, { xScale, yScale, padding, width, height })}
    on:keydown={() => void 0}
>
    <span class="sr-only">Click to add annotation</span>
</div>
<!-- HTML left zone click overlay -->
<div
class="absolute z-10"
style="
    left: {padding.left - 20}px; 
    top: {padding.top}px; 
    width: 20px;
    height: {height}px;
    pointer-events: all;
"
role="button"
tabindex="0"
aria-label="Click to add annotation at time point"
on:click={(e) => onLeftOrRightStripClick(e, { xScale, yScale, padding, width, height }, 'left')}
on:keydown={() => void 0}
>
<span class="sr-only">Click to add annotation</span>
</div>
<!-- HTML right zone click overlay -->
<div
    class="absolute z-10"
    style="
        right: -20px; 
        top: {padding.top}px; 
        width: 20px;
        height: {height}px;
        pointer-events: all;
    "
    role="button"
    tabindex="0"
    aria-label="Click to add annotation at time point"
    on:click={(e) => onLeftOrRightStripClick(e, { xScale, yScale, padding, width, height }, 'right')}
    on:keydown={() => void 0}
>
    <span class="sr-only">Click to add annotation</span>
</div>