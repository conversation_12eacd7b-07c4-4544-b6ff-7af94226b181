<script lang="ts">
    import { Label as LabelType, LabelWithColors, TriageLabel as TriageLabelType } from '@hcengineering/annotationtool'
    import { getCurrentAccount } from '@hcengineering/core'
    import { Project } from '@hcengineering/tracker'
    import { ButtonIcon, CheckBox, closePopup, getPlatformColorDef, Icon, Label, showPopup, themeStore } from '@hcengineering/ui'
    import { scaleTime } from 'd3-scale'
    import {
      Axis,
      Canvas,
      Chart,
      Highlight,
      Rect,
      Rule,
      Spline,
      Svg,
      Tooltip
    } from 'layerchart'
    import { State } from 'svelte-ux'
    import annotationTool from '../../../plugin'
    import { EpisodeLabellingStoreConfig } from '../../../types'
    import { assignLabelsToTracks, calculateLabelAreaHeight, getLabelsWithPositions } from '../../../utils/labelLayoutUtils'
    import AnnotationLabel from '../../settings/components/AnnotationLabel.svelte'
    import UserActivityModal from '../../user-activity/UserActivityModal.svelte'
    import EpisodeStripSegments from './EpisodeStripSegments.svelte'
    import EpisodeStripGrid from './EpisodeStripGrid.svelte'
    import EpisodeStripClickOverlays from './EpisodeStripClickOverlays.svelte'

    export let stripId: string;
    export let nextStripId: string;
    export let episodeId: string;
    export let stripValues: string[] = []
    export let stripTimestamps: number[] = []
    export let nextStripValues: string[] = []
    export let nextStripTimestamps: number[] = []
    export let nextStripAllLabels: Record<string, {label: string, timestamp: number, start?: number, end?: number}[]> = {}
    export let labels: LabelType[]
    export let triageLabels: TriageLabelType[]
    export let viewMode: 'card' | 'list' = 'card'
    export let isConflictResolution: boolean = false
    export let catalog: string
    export let project: Project
    export let hasActivity: boolean = false
    export let episodeStore: EpisodeLabellingStoreConfig['episodeLabellingStore']
    export let episodeStoreActions: EpisodeLabellingStoreConfig['episodeLabellingStoreActions'];
    export let annotatorAccessLevel: string
    export let episodeLevelZoom: number = 0
    export let annotationsView: 'my' | 'all' = 'my'
    export let shuffledColors: number[] = []

    const currentAccount = getCurrentAccount()?._id;

    $: stripStore = $episodeStore.strips[stripId]

    $: zoomLevel = episodeLevelZoom
    
    let resetZoomFn: any = undefined
    let lastStripTimestampIndex = stripTimestamps.length
    let moreSecondsLoaded: boolean = false

    let selectedLabel: LabelWithColors | null = null
    let firstClickTimestamp: number | null = null
    let secondClickTimestamp: number | null = null

    $: stripLength = stripTimestamps?.[stripTimestamps.length - 1] - stripTimestamps?.[0]
    $: stripSecs = moreSecondsLoaded ? 15000 : 10000

    // Value domain used to derive the Y domain (vertical zoom)
    $: numericValues = stripValues.map((v) => Number(v)).filter((v) => Number.isFinite(v))
    $: baseYMin = numericValues.length ? Math.min(...numericValues) : -1
    $: baseYMax = numericValues.length ? Math.max(...numericValues) : 1
    // Ensure non-zero range when all values are equal
    $: [yMin, yMax] = baseYMin === baseYMax ? [baseYMin - 1, baseYMax + 1] : [baseYMin, baseYMax]
    $: amplitude = Math.max(Math.abs(yMin), Math.abs(yMax)) || 1
    $: zoomFactor = amplitude * zoomLevel
    $: yDomain = [yMin - zoomFactor, yMax + zoomFactor]

    $: dateSeriesData = stripValues.map((value, index) => ({
        date: stripTimestamps[index],
        value: value == null ? null : Number(value)
    }))

    // Filter labels based on user level and view mode
    $: filteredAllLabels = (() => {
        const allLabels = stripStore?.allLabels ?? {};
        
        // For basic users (non-level2) or level2 users in 'my' mode: only show current user's labels
        if (annotatorAccessLevel !== 'level2' || annotationsView === 'my') {
            return currentAccount ? { [currentAccount]: allLabels[currentAccount] || [] } : {};
        }
        
        // For level2 users in 'all' mode: show all labels, but prioritize current user's labels
        if (annotationsView === 'all') {
            const result: Record<string, any[]> = {};
            
            // Add current user's labels first
            if (currentAccount && allLabels[currentAccount]) {
                result[currentAccount] = allLabels[currentAccount];
            }
            
            // Add other users' labels
            Object.entries(allLabels).forEach(([userId, userLabels]) => {
                if (userId !== currentAccount) {
                    result[userId] = userLabels;
                }
            });
            
            return result;
        }
        
        return allLabels;
    })()

    $: labelsWithPositions = getLabelsWithPositions(
        filteredAllLabels, 
        nextStripAllLabels, 
        originalStripEndTime, 
        moreSecondsLoaded, 
        nextStripId, 
        labels
    )

    // Assign labels to tracks to avoid visual overlaps
    $: labelsWithTracks = assignLabelsToTracks(labelsWithPositions)
    
    // Determine if we're in multi-user mode
    $: isMultiUserMode = annotatorAccessLevel === 'level2' && annotationsView === 'all'
    
    // Calculate the total height needed for the label area
    $: labelAreaHeight = calculateLabelAreaHeight(labelsWithTracks, 24, 4, isMultiUserMode, 2, 6)

    // Calculate the strip boundary time (original end time without more seconds)
    $: originalStripEndTime = stripTimestamps[lastStripTimestampIndex - 1]
    
    // Calculate the next strip start time
    $: nextStripStartTime = nextStripTimestamps?.[0]

    $: if (stripLength < (moreSecondsLoaded ? 14500 : 9500) && dateSeriesData.length > 1) {
        // Fill dateSeriesData until stripTimestamps[0] + 10000 or 15000, following the same frequency
        const freq = dateSeriesData[1].date - dateSeriesData[0].date;
        let lastDate = dateSeriesData[dateSeriesData.length - 1].date;
        let nextIndex = dateSeriesData.length;
        const endDate = stripTimestamps[0] + stripSecs;
        while (lastDate + freq <= endDate) {
            lastDate += freq;
            dateSeriesData.push({
                date: lastDate,
                value: null
            });
            nextIndex++;
        }
    }

    function handleStripZoomOut() {
        if (zoomLevel >= 1) return

        zoomLevel += 0.25
    }

    function handleStripZoomIn() {
        if (zoomLevel <= 0) return

        zoomLevel -= 0.25
    }

    function handleStripExpand() {
        resetZoomFn(null)
        resetZoomFn = undefined   
    }

    function handleLoadMoreSeconds() {
        const next5Seconds = stripTimestamps[stripTimestamps.length - 1] + 5000

        // Find the index of the value in nextStripTimestamps closest to 5 seconds
        let closest5SecondIndex = -1;
        if (Array.isArray(nextStripTimestamps) && nextStripTimestamps.length > 0) {
            let minDiff = Infinity;
            for (let i = 0; i < nextStripTimestamps.length; i++) {
                const diff = Math.abs(nextStripTimestamps[i] - next5Seconds);
                if (diff < minDiff) {
                    minDiff = diff;
                    closest5SecondIndex = i;
                }
            }
        }

        stripTimestamps = [...stripTimestamps, ...nextStripTimestamps.slice(1, closest5SecondIndex + 1)]
        stripValues = [...stripValues, ...nextStripValues.slice(1, closest5SecondIndex + 1)]
        moreSecondsLoaded = true
    }
    
    function handleUnloadMoreSeconds() {
        stripTimestamps = stripTimestamps.slice(0, lastStripTimestampIndex)
        stripValues = stripValues.slice(0, lastStripTimestampIndex)
        moreSecondsLoaded = false
    }

    const handleOpenStripActivity = () => {
    showPopup(UserActivityModal, {
      episodeId,
      stripId,
      catalog,
      labels: labels,
      hidden: false,
      project,
      annotatorAccessLevel,
      onCancel: closePopup
    })
  }

  const handleStripKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'e') {
        handleApplyToEntireStrip()
    } else if (e.key === 'd') {
        handleApproveSegmentation()
    } else if (e.key === 'x') {
        handleDisapproveSegmentation()
    }
  }

  const handleStripActivityKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleOpenStripActivity()
    }
  }

  const removeLabelByLabel = (label: LabelType) => {
    if (stripStore?.labels) {
      const labelsToRemove = stripStore.labels.filter((existingLabel: any) => existingLabel.label === label.value)
      labelsToRemove.forEach((labelToRemove: any) => {
        episodeStoreActions.addRemoveLabelStrip(stripId, {
            label: labelToRemove.label,
            timestamp: labelToRemove.timestamp,
            start: labelToRemove.start ?? 0,
            end: labelToRemove.end ?? 0
        })
      })
    }
  }

  const onStripLabelSelect = (label: LabelType) => {
    if(selectedLabel?.label !== label.label) {
        removeLabelByLabel(label)
        const dynamicPlatformColor = getPlatformColorDef(label?.color, $themeStore.dark)
        const whiteThemePlatformColor = getPlatformColorDef(label?.color, false)
        const borderColor = dynamicPlatformColor.color
        const backgroundColor = whiteThemePlatformColor.background || 'white'
        const textColor = whiteThemePlatformColor.title || 'black'
        
        selectedLabel = {
            ...label,
            borderColor,
            backgroundColor,
            textColor
        } as any
    } else {
        selectedLabel = null
        firstClickTimestamp = null
        secondClickTimestamp = null
    }
  }

  const onStripClick = (event: MouseEvent, ctx: any) => {
    if(selectedLabel === null) return
    
    // Get the click position relative to the HTML div
    const target = event.currentTarget as HTMLElement;
    if (!target) return;
    
    const rect = target.getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    
    const xVal = ctx.xScale.invert(offsetX);
    const timestamp = xVal instanceof Date ? xVal.getTime() : xVal;
    
    if (firstClickTimestamp === null) {
        firstClickTimestamp = timestamp
    } else {
        secondClickTimestamp = timestamp
    }
  }

  const onLeftOrRightStripClick = (event: MouseEvent, ctx: any, side: 'left' | 'right') => {
    if(selectedLabel === null) return
    
    if ((side === 'left' && firstClickTimestamp === stripTimestamps[0]) || 
        (side === 'right' && firstClickTimestamp === stripTimestamps[stripTimestamps.length - 1])) 
    {
        return
    }

    if (firstClickTimestamp === null) {
        firstClickTimestamp = side === 'left' ? stripTimestamps[0] : stripTimestamps[stripTimestamps.length - 1]
    } else {
        secondClickTimestamp = side === 'left' ? stripTimestamps[0] : stripTimestamps[stripTimestamps.length - 1]
    }
  }

  const handleApplyToEntireStrip = () => {
    if(!selectedLabel) {
        firstClickTimestamp = null
        secondClickTimestamp = null
        return
    }

    firstClickTimestamp = stripTimestamps[0]
    secondClickTimestamp = stripTimestamps[stripTimestamps.length - 1]

    handleApproveSegmentation()
  }

  const handleApproveSegmentation = () => {
    if(!selectedLabel || !firstClickTimestamp || !secondClickTimestamp) {
        selectedLabel = null
        firstClickTimestamp = null
        secondClickTimestamp = null
        return
    }

    const labelStart = Math.min(firstClickTimestamp, secondClickTimestamp)
    const labelEnd = Math.max(firstClickTimestamp, secondClickTimestamp)
    const currentTime = new Date().getTime()

    // Check if we're in "more seconds loaded" mode and if the label crosses the original strip boundary
    if (moreSecondsLoaded && originalStripEndTime && labelEnd > originalStripEndTime && nextStripId && nextStripStartTime) {
        // Split the label: current strip gets truncated version, next strip gets continuation
        
        // Add truncated label to current strip (from labelStart to originalStripEndTime)
        episodeStoreActions.addRemoveLabelStrip(stripId, {
            label: selectedLabel.value, 
            timestamp: currentTime,
            start: labelStart,
            end: originalStripEndTime
        })

        // Calculate the continuation for next strip
        // The remaining time that extends beyond current strip boundary
        const remainingTime = labelEnd - originalStripEndTime
        
        // Ensure the next strip exists in the store before adding the label
        if (nextStripId) {
            // Add continuation label to next strip
            // Start: end of current strip (originalStripEndTime)
            // End: start of next strip + remaining time (nextStripStartTime + remainingTime)
            episodeStoreActions.addRemoveLabelStrip(nextStripId, {
                label: selectedLabel.value, 
                timestamp: currentTime + 1, // Slightly later timestamp to maintain order
                start: originalStripEndTime, // Start where current strip ended
                end: nextStripStartTime + remainingTime // Next strip start + remaining time
            })
        }
    } else {
        // Normal case: add label to current strip only
        episodeStoreActions.addRemoveLabelStrip(stripId, {
            label: selectedLabel.value, 
            timestamp: currentTime,
            start: labelStart,
            end: labelEnd
        })
    }

    selectedLabel = null
    firstClickTimestamp = null
    secondClickTimestamp = null
  }

  const handleDisapproveSegmentation = () => {
    firstClickTimestamp = null
    secondClickTimestamp = null
  }

</script>


{#if stripStore}
    <div class="strip-card shadow-md"
        on:keydown={handleStripKeyDown}
        tabindex="0"
        role="button"
    >
        <div class="strip-header">
            <div 
                class="strip-meta text-lg font-semi-bold cursor-pointer" 
                on:click={handleOpenStripActivity}
                on:keydown={handleStripActivityKeyDown}
                tabindex="0"
                role="button"
            >
                <div class="strip-number">
                    <Icon icon={annotationTool.icon.Ecg} size="large" />
                </div>
                <span class="strip-id" class:has-activity={hasActivity}>
                    {stripId}
                </span>
            </div>
            {#if !isConflictResolution}
                <div class="strip-labels">
                    {#if annotatorAccessLevel === 'level2'}
                        <ButtonIcon icon={annotationTool.icon.MyAnnotations} pressed={annotationsView === 'my'} size="small" kind="tertiary" on:click={() => annotationsView = 'my'} tooltip={{label: annotationTool.string.MyAnnotations}} />
                        <ButtonIcon icon={annotationTool.icon.AllAnnotations} pressed={annotationsView === 'all'} size="small" kind="tertiary" on:click={() => annotationsView = 'all'} tooltip={{label: annotationTool.string.AllAnnotations}} />
                        <div class="vertical-divider" />
                    {/if}
                    <CheckBox 
                        size="large"
                        checked={stripStore.isInterestingStrip} 
                        on:value={(ev) => episodeStoreActions.addRemoveInterestingStrip(stripId, ev.detail)} 
                    />
                    <Label label={annotationTool.string.IsInterestingStrip} />
                </div>
            {/if}
        </div>
        <div class="horizontal-divider" />
        <div class="strip-graph strip-graph-card">
            <div class="relative w-full">
                <State initial={[null, null]} let:value={xDomain} let:set>
                    <div class="h-[300px] p-4 border rounded" style={`margin-bottom: ${Math.max(32, 28 + labelAreaHeight)}px;`}>
                        <Chart
                            data={dateSeriesData}
                            x="date"
                            xScale={scaleTime()}
                            {xDomain}
                            y="value"
                            {yDomain}
                            yNice
                            padding={{ left: 4, bottom: 8 }}
                            tooltip={{ mode: "bisect-x" }}
                            brush={{
                                resetOnEnd: true,
                                onbrushend: (e) => {
                                    // @ts-expect-error
                                    set(e.xDomain);
                                    resetZoomFn = set
                                },
                                classes: { range: "ecg-brush-range", handle: "ecg-brush-handles" } 
                            }}

                            let:width
                            let:height
                            let:padding
                            let:xScale
                            let:yScale
                        >
                            <Svg>
                                <Axis 
                                    placement="left" 
                                    format={(d) => (`${d} mV`)}
                                    rule
                                    ticks={Math.max(4, Math.round(height / (width / 50) / (viewMode === 'card' ? 2 : 1)))}
                                    tickLength={8}
                                    classes={{
                                        rule: 'ecg-graph-axis-color',
                                        tick: 'ecg-graph-axis-color',
                                        tickLabel: 'ecg-graph-axis-label-hidden'
                                    }}
                                />
                                <Axis
                                    placement="bottom"
                                    format={(d) => (`${resetZoomFn ? (d / 1000).toFixed(1) : ~~(d / 1000)}s`)}
                                    rule
                                    ticks={10}
                                    tickLabelProps={{ textAnchor: "start", dx: 8, dy: 4 }}
                                    tickLength={18}
                                    classes={{
                                        rule: 'ecg-graph-axis-color',
                                        tick: 'ecg-graph-axis-color',
                                        tickLabel: $themeStore.dark ? 'ecg-graph-axis-label-white' : 'ecg-graph-axis-label-black'
                                    }}
                                />

                                {#if firstClickTimestamp && selectedLabel}
                                    <Rule 
                                        x={firstClickTimestamp}
                                        class="segmentation-rule"
                                        style={`stroke: ${selectedLabel.textColor}`}
                                    />
                                {/if}
                                {#if secondClickTimestamp && selectedLabel}
                                    <Rule 
                                        x={secondClickTimestamp}
                                        class="segmentation-rule"
                                        style={`stroke: ${selectedLabel.textColor}`}
                                    />
                                {/if}
                                {#if firstClickTimestamp && secondClickTimestamp && selectedLabel}
                                    <Rect
                                        x={xScale(Math.min(firstClickTimestamp, secondClickTimestamp))}
                                        y={0}
                                        width={Math.abs(xScale(secondClickTimestamp) - xScale(firstClickTimestamp))}
                                        height={height}
                                        style={`fill: ${selectedLabel.backgroundColor}; fill-opacity: 0.5;`}
                                    />
                                {/if}

                                {#if moreSecondsLoaded}
                                    <Rule 
                                        x={stripTimestamps[lastStripTimestampIndex]}
                                        class="more-seconds-rule"
                                    />
                                {/if}

                                <EpisodeStripSegments 
                                    labelsWithTracks={labelsWithTracks}
                                    isMultiUserMode={isMultiUserMode}
                                    shuffledColors={shuffledColors}
                                    height={height}
                                    width={width}
                                    xScale={xScale}
                                    stripId={stripId}
                                    episodeStoreActions={episodeStoreActions}
                                    labels={labels}
                                    currentAccount={currentAccount}
                                /> 

                                <Highlight
                                    lines={{
                                        class: "ecg-graph-highlight-line-color"
                                    }}
                                />
                            </Svg>

                            <EpisodeStripGrid
                                width={width}
                                height={height}
                                padding={padding}
                                xScale={xScale}
                                yScale={yScale}
                                viewMode={viewMode}
                            />

                            <Canvas>
                                <Spline strokeWidth={1} class="ecg-graph-stroke-color" />
                            </Canvas>
                            
                            <EpisodeStripClickOverlays 
                                width={width}
                                height={height}
                                padding={padding}
                                xScale={xScale} 
                                yScale={yScale}
                                onStripClick={onStripClick}
                                onLeftOrRightStripClick={onLeftOrRightStripClick}
                            />
                    
                            <!-- Selected segment action buttons -->
                            {#if firstClickTimestamp && secondClickTimestamp && selectedLabel}
                                {@const top = 10}
                                {@const left = xScale(firstClickTimestamp) + 10}
                                <div
                                    class="absolute z-10"
                                    style="top: {top}px; left: {left}px;"
                                    on:mousemove|stopPropagation|preventDefault
                                    on:mousedown|stopPropagation|preventDefault
                                    on:mouseup|stopPropagation|preventDefault
                                    on:mouseenter|stopPropagation|preventDefault
                                    on:mouseleave|stopPropagation|preventDefault
                                    on:mouseover|stopPropagation|preventDefault
                                    on:mouseout|stopPropagation|preventDefault
                                    on:focus|stopPropagation|preventDefault
                                    on:blur|stopPropagation|preventDefault
                                    role="button"
                                    tabindex="0"
                                    aria-label="Accept or reject segmentation"
                                >
                                    <ButtonIcon icon={annotationTool.icon.CheckmarkCircle} size="small" on:click={handleApproveSegmentation} tooltip={{label: annotationTool.string.DoneD}} />
                                    <ButtonIcon icon={annotationTool.icon.XCircle} size="small" on:click={handleDisapproveSegmentation} tooltip={{label: annotationTool.string.ClearX}} />
                                    <ButtonIcon icon={annotationTool.icon.ApplyToWholeEpisode} size="small" on:click={handleApplyToEntireStrip} tooltip={{label: annotationTool.string.ApplyToEntireStripE}} />
                                </div>
                            {/if}

                            <Tooltip.Root let:data class="ecg-graph-tooltip-background">
                                <Tooltip.Header class="ecg-graph-tooltip-text">
                                    {`${(data.date / 1000).toFixed(2)}s`}
                                </Tooltip.Header>
                                <Tooltip.List>
                                    {#if data.value != null}
                                        <Tooltip.Item 
                                            label="" 
                                            value={`${data.value} mV`} 
                                            class="ecg-graph-tooltip-text" 
                                        />
                                    {/if}
                                </Tooltip.List>
                            </Tooltip.Root>
                        </Chart>
                    </div>
                </State>
                {#if selectedLabel}
                    <div class="strip-apply-to-whole-strip">
                        <ButtonIcon icon={annotationTool.icon.ApplyToWholeEpisode} size="small" on:click={handleApplyToEntireStrip} tooltip={{label: annotationTool.string.ApplyToEntireStripE}}  />
                    </div>
                {/if}
                <div class="strip-zoomout">
                    <ButtonIcon icon={annotationTool.icon.ZoomOut} size="small" disabled={zoomLevel >= 1} on:click={handleStripZoomOut} />
                    <ButtonIcon icon={annotationTool.icon.ZoomIn} size="small" disabled={zoomLevel <= 0} on:click={handleStripZoomIn} />
                    {#if resetZoomFn}
                        <ButtonIcon icon={annotationTool.icon.Expand} size="small" on:click={handleStripExpand} />
                    {/if}
                    {#if !moreSecondsLoaded && nextStripValues?.length > 0 && nextStripTimestamps?.length > 0}
                        <ButtonIcon icon={annotationTool.icon.LoadMoreSeconds} size="small" on:click={handleLoadMoreSeconds} />
                    {/if}
                    {#if moreSecondsLoaded}
                        <ButtonIcon icon={annotationTool.icon.UnloadMoreSeconds} size="small" on:click={handleUnloadMoreSeconds} />
                    {/if}
                </div>
            </div>
            <div class="horizontal-divider" />
            <div class="strip-labels-container">
                <div class="strip-labels justify-between flex-nowrap overflow-x-auto">
                    {#each labels as label (label.value)}
                        <AnnotationLabel 
                            label={label} 
                            selected={stripStore.labels.some(l => l.label === label.value) || selectedLabel?.value === label.value}
                            segmentationSelected={selectedLabel?.value === label.value}
                            approved={stripStore.approvedLabels.some(l => l.label === label.value)}
                            disapproved={stripStore.disapprovedLabels.some(l => l.label === label.value)}
                            numSelected={stripStore.labels.filter(l => l.label === label.value).length}
                            selectable
                            width="100%"
                            onSelect={() => onStripLabelSelect(label)} 
                            allowDelete={false}
                            {isConflictResolution}
                            onApprove={() => episodeStoreActions.approveStripLabel(stripId, {label: label.value, timestamp: new Date().getTime()})}
                            onDisapprove={() => episodeStoreActions.disapproveStripLabel(stripId, {label: label.value, timestamp: new Date().getTime()})}
                        />
                    {/each}
                </div>
            </div>
        </div>
    </div>
{/if}

<style lang="scss">
    .strip-card {
        border: 1px solid var(--theme-divider-color);
        border-radius: 0.75rem;
        margin: 0.5rem 0.5rem;
    }
    .strip-header {
        width: 100%;
        display: flex;
        gap: 16px;
        padding: 0.5rem;
        border-top-left-radius: 0.75rem;
        border-top-right-radius: 0.75rem;
        padding: 0.5rem;
        border-top-left-radius: 0.75rem;
        border-top-right-radius: 0.75rem;
        justify-content: space-between;
        align-items: center;
        background-color: var(--theme-comp-header-color);
        background-color: var(--theme-comp-header-color);
    }
    .horizontal-divider {
        width: 100%;
        border-bottom: 1px solid var(--theme-divider-color);
    }
    .vertical-divider {
        display: flex;
        align-self: stretch;
        width: 1px;
        border-left: 1px solid var(--theme-divider-color);
    }
    .strip-number {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
    }
    .strip-number-list {
        flex-direction: row;
        align-items: center;
    }
    .strip-id {
        position: relative;
        font-size: 14px;
        font-weight: 600;
        color: var(--theme-text-editor-palette-text-gray);
    }
    .has-activity::after {
        content: '';
        position: absolute;
        top: 4px;
        right: -10px;
        width: 8px;
        height: 8px;
        background-color: var(--highlight-red);;
        border-radius: 100%;
    }
    .strip-meta {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        gap: 8px;
    }
    .strip-annotation-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    .strip-labels-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 0 0.25rem 0.25rem 0.25rem;
        padding: 0 0.25rem 0.25rem 0.25rem;
    }
    .strip-labels-text {
        display: flex;
        justify-content: space-between;
        text-transform: uppercase;
        font-size: 12px;
        font-weight: 600;
        color: var(--color-text-primary);
    }
    .strip-labels {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 8px;
    }
    .strip-labels-list {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
        gap: 8px;
    }
    .strip-checkbox {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }
    .strip-graph {
        width: 100%;
        display: flex;
        gap: 0.5rem;
        padding: 0 0.25rem 0.5rem 0.25rem;
        gap: 0.5rem;
        padding: 0 0.25rem 0.5rem 0.25rem;
    }
    .strip-graph-card {
        flex-direction: column;
    }
    .strip-side-labels-list {
        width: 180px;
        width: 180px;
        display: flex;
        flex-direction: column;
        justify-self: stretch;
        padding-top: 8px;
        padding-left: 0.25rem;
        padding-top: 8px;
        padding-left: 0.25rem;
    }
    .strip-zoomout {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 1000;
    }
    .strip-apply-to-whole-strip {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        position: absolute;
        top: 8px;
        left: 24px;
        z-index: 1000;
    }
    .segmentation-decision {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        z-index: 1000;
    }
    
    .user-avatar {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        pointer-events: all;
        border-radius: 50%;
        background: var(--theme-bg-color);
    }

    :global(.more-seconds-rule) {
        box-sizing: border-box!important;
        stroke-linecap: round!important;
        stroke-dasharray: 4!important;
        stroke-width: 2!important;
        stroke: var(--negative-button-disabled-color)!important;
    }
    :global(.segmentation-rule) {
        box-sizing: border-box!important;
        stroke-linecap: round!important;
        stroke-width: 2!important;
    }
    :global(.strip-label-text tspan) {
        font-size: 0.875rem;
        font-weight: 600;
    }
    :global(.strip-label-button tspan) {
        cursor: pointer;
        font-size: 1rem;
        font-weight: 800;
        transform: scaleX(1.25);
        transition: all 0.2s ease;
    }
    :global(.strip-label-button:hover tspan) {
        transform: scaleX(1.25) scale(1.2);
        opacity: 0.8;
    }
    :global(.ecg-graph-stroke-color) {
        stroke: var(--primary-button-default)!important;
    }
    :global(.ecg-brush-handles) {
        background-color: var(--secondary-button-pressed)!important;
    }
    :global(.ecg-brush-range) {
        background-color: var(--primary-button-transparent)!important;
    }
    :global(.ecg-graph-tooltip-background) {
        background-color: var(--theme-tooltip-color)!important;
    }
    :global(.ecg-graph-tooltip-text) {
        color: var(--theme-tooltip-bg)!important;
    }
    :global(.ecg-graph-highlight-line-color) {
        stroke: var(--primary-button-default)!important;
        stroke-width: 1px!important;
    }
    :global(.ecg-graph-axis-color) {
        stroke: var(--theme-navpanel-icons-color)!important;
    }
    :global(.ecg-graph-grid-color) {
        stroke: var(--theme-overlay-color)!important;
    }
    :global(.ecg-graph-grid-color-secondary) {
        stroke: var(--theme-divider-color)!important;
    }
    :global(.ecg-graph-axis-label-black) {
        stroke: unset!important;
        fill: black!important;
    }
    :global(.ecg-graph-axis-label-white) {
        stroke: unset!important;
        fill: white!important;
    }
    :global(.ecg-graph-axis-label-hidden) {
        display: none !important;
    }
</style>