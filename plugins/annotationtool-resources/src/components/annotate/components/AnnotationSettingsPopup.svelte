<script lang="ts">
  import { Label, ToggleWithLabel, Icon, Button } from '@hcengineering/ui'
  import annotationTool from '../../../plugin'
  import { selectedFiltersStore, filtersStoreActions } from '../../../filtersStore'

  export let viewMode = 'list'
  export let handleViewModeChange: any
  export let selectedApplication: 'annotate' | 'dashboard' | 'conflict'
  export let projectId: string

  $: handleViewModeChange(viewMode)

  $: filters = $selectedFiltersStore
</script>

<div class="selectPopup">
  <div class="menu-space" />
  <div class="scroll">
    <div class="box">
      <div class="title">
        <Label label={annotationTool.string.EpisodeViewMode} />
      </div>
      <div class="flex gap-1">
        <Icon icon={annotationTool.icon.ListView} size="medium" />
        <ToggleWithLabel
          on={viewMode === 'list'}
          label={annotationTool.string.EpisodeListView}
          on:change={(e) => {
            viewMode = viewMode === 'list' ? 'card' : 'list'
          }}
        />
      </div>
      <div class="flex gap-1">
        <Icon icon={annotationTool.icon.CardView} size="medium" />
        <ToggleWithLabel
          on={viewMode === 'card'}
          label={annotationTool.string.EpisodeCardView}
          on:change={(e) => {
            viewMode = viewMode === 'card' ? 'list' : 'card'
          }}
        />
      </div>
    </div>
  </div>
</div>

<style>
  .box {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 10px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: var(--color-text-primary);
  }
</style>
