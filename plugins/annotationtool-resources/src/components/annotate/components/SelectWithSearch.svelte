<script lang="ts">
  import { Button, Label, SelectPopup, eventToHTMLElement, showPopup, DateRangePopup } from '@hcengineering/ui'
  import type { IntlString, Asset } from '@hcengineering/platform'
  import annotationTool from '../../../plugin'
  import { selectedFiltersStore } from '../../../filtersStore'
  import { selectedDashboardFiltersStore } from '../../../dashboardFiltersStore'
  import type { Project } from '@hcengineering/tracker'
  import SearchInput from '@hcengineering/ui/src/components/SearchInput.svelte'

  export let name: string
  export let label: IntlString
  export let icon: Asset
  export let width: string = '100%'
  export let placeholder: IntlString
  export let type: 'dropdown' | 'date' | 'input' = 'dropdown'
  export let options: Array<{ id: string, label: string }> = []
  export let fetchOptions: any = undefined
  export let dependencies: string[] = []
  export let onChange: (name: string, newValue: string, newLabel: string) => void = () => {}
  export let onClear: (name: string) => void = () => {}
  export let parent: 'annotate' | 'dashboard' | 'conflict'
  export let project: Project

  
  $: selectedFilters = ['annotate', 'conflict'].includes(parent) ? $selectedFiltersStore : $selectedDashboardFiltersStore
  
  $: selectedValue = selectedFilters[name]?.value
  
  $: episodeIdSearchValue = selectedValue ?? ''

  let optionsToShow: any[] = []
  let optionsLoading = false
  let lastFetchValues: string[] | undefined = undefined

  const runFetchOptions = async (dependencies: string[], selectedFilters: Record<string, { value: string, label: string }>) => {    
    if(optionsToShow?.length > 0 && dependencies?.length === 0) {
      return
    }

    if (lastFetchValues && dependencies.map(dep => selectedFilters[dep]?.value).join(',') === lastFetchValues.join(',')) {
      return
    }

    if(dependencies?.length > 0 && dependencies.some(dep => !selectedFilters?.[dep]?.value)) {
      optionsToShow = [{id: 'empty', label: annotationTool.string.SelectClinicFirst, icon: annotationTool.icon.Failed}]

      return
    }

    lastFetchValues = dependencies.map(dep => selectedFilters[dep].value)

    try { 
      optionsLoading = true
      const options = await fetchOptions(...(dependencies.map(dep => selectedFilters[dep].value) ?? [project.datasetID, parent, project._id]), project.datasetID, parent, project._id)

      optionsToShow = options?.length ? [
        {id: 'none', value: 'none', label: annotationTool.string.None },
        ...options
      ] : [
        {id: 'empty', label: annotationTool.string.NoOptionsAvailable, icon: annotationTool.icon.Failed}
      ]
    } catch (error) {
      console.error(error)
      optionsToShow = []
    } finally {
      optionsLoading = false
    }
  }

  $: if(fetchOptions) {
    runFetchOptions(dependencies, selectedFilters)
  } else {
    optionsToShow = options
  }

  const handleSelectOpened = (event: MouseEvent) => {
    event.stopPropagation()

    if (type === 'input') {
      return
    }

    if(type === 'date') {
      showPopup(DateRangePopup, { direction: 'before', selectedDate: selectedValue, noDateSelected: selectedValue == null }, eventToHTMLElement(event), (v) => {
        if (v != null) {
          v.setHours(0, 0, 0, 0)
          const date = new Date(v.getTime())
          const isoDate = date.toISOString()
          onChange(name, isoDate, isoDate)
        } else {
          onClear(name)
        }
      })
    } else {
      showPopup(
        SelectPopup,
        { 
          value: optionsToShow?.length > 0 ? 
            optionsToShow : 
            [{ id: 'empty', label: annotationTool.string.NoOptionsAvailable, icon: annotationTool.icon.Failed }], 
          placeholder, 
          searchable: optionsToShow?.length > 0,
          emptyValueMessage: annotationTool.string.SelectClinicFirst
        },
        eventToHTMLElement(event),
        changeValue
      )
    }
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const changeValue = async (newValue: string | undefined) => {
    if (optionsToShow?.length === 0 || newValue == null || selectedValue === newValue) {
      return
    }

    onChange(name, newValue, optionsToShow?.find(opt => opt.id === newValue)?.label)
  }

  let selectedOption: { label: string, id: string } | undefined

  $: if (type === 'date') {
    if(selectedValue) {
      selectedOption = {label: formatDate(new Date(selectedValue)), id: selectedValue}
    } else {
      selectedOption = undefined
    }
  } else if (type === 'dropdown') {
    selectedOption = optionsToShow.find(opt => opt.id === selectedValue)
  } else {
    selectedOption = selectedFilters[name]?.value ? {label: selectedFilters[name]?.value ?? undefined, id: selectedFilters[name]?.value ?? undefined} : undefined
  } 

  const handleInputChange = () => {
    if(episodeIdSearchValue === selectedOption?.label) {
      return
    }

    onChange(name, episodeIdSearchValue, episodeIdSearchValue)
  }
</script>

<div class="select-container">
  {#if type === 'input'}
    <SearchInput
      bind:value={episodeIdSearchValue}
      placeholder={placeholder}
      on:change={handleInputChange}
    />
  {:else}
    <Button
        icon={icon}
        iconRight={annotationTool.icon.ChevronDown}
        showTooltip={placeholder ? { label: placeholder } : undefined}
        label={selectedOption ? undefined : label}
        nonIntlLabel={selectedOption ? selectedOption?.label : undefined}
        size="large"
        width={width}
        justifyLabel="space-between"
        loading={optionsLoading}
        on:click={handleSelectOpened}
    />
    {/if}
    {#if selectedOption}
        <div class="floating-label">
            <Label label={label} />
        </div>
    {/if}
</div>

<style lang="scss">
  .select-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex-shrink: 0;
    min-width: 0;
    gap: 0.5rem;

    .icon {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      width: 1rem;
      height: 1rem;
      color: var(--theme-caption-color);
    }
    &:hover {
      .icon {
        color: var(--theme-caption-color) !important;
      }
    }
  }
  .floating-label {
    position: absolute;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    top: -1.1rem;
    left: 0;
    font-size: 0.75rem;
    color: var(--theme-caption-color);
    white-space: nowrap;
  }

  .select-container {
    :global(.searchInput-wrapper) {
      min-height: var(--status-bar-height);

      &:focus-within {
        max-width: none !important;
      }
    }
  }
  
</style>
