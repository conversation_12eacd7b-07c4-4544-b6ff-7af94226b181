<script lang="ts">
  import { <PERSON><PERSON>er, NavItem, NavGroup } from '@hcengineering/ui'
  import { Label, getPlatformColorDef, themeStore } from '@hcengineering/ui'
  import { translateCB } from '@hcengineering/platform'
  import { NavFooter } from '@hcengineering/workbench-resources'
  import annotationTool from '../../plugin'
  import { Project } from '@hcengineering/tracker'
  import { getCurrentAccount, Ref, Account } from '@hcengineering/core'
  import { createQuery } from '@hcengineering/presentation'
  import { personAccountByIdStore } from '@hcengineering/contact-resources'
  import { Annotator } from "@hcengineering/annotationtool"
  import { fetchNumConflictingEpisodes } from '../../api'

  export let selectedProject: Project | undefined = undefined
  export let selectedApplication: 'annotate' | 'dashboard' | 'activity' | 'conflict' = 'annotate'
  export let projects: Project[] = []

  const userPermissionsQuery = createQuery()
  let annotatorPermissions: Annotator[] = []

  const getUserPermissions = async () => {
    const currentPersonId = $personAccountByIdStore.get(getCurrentAccount()._id as any)?.person as any

    if(!currentPersonId) {
      return
    }
    
    await userPermissionsQuery.query(
      annotationTool.class.Annotator, {
        accountId: currentPersonId as Ref<Account>
      }, (res) => {
        if(res?.length > 0) {
          annotatorPermissions = res
        }
    })
  }

  const getNumConflictingEpisodes = async (project: Project, annotatorPermissions: Annotator[]) => {
    if(!annotatorPermissions?.find(annotator => annotator.attachedToProject === project._id && annotator.accessLevel === 'level2')) {
      return 0
    }

    return await fetchNumConflictingEpisodes(project.datasetID ?? '')
  }

  let annotateLabel: string, dashboardLabel: string, conflictLabel: string, activityLabel: string
  translateCB(annotationTool.string.Annotate, {}, $themeStore.language, (r) => {annotateLabel = r})
  translateCB(annotationTool.string.Dashboard, {}, $themeStore.language, (r) => {dashboardLabel = r})
  translateCB(annotationTool.string.Conflict, {}, $themeStore.language, (r) => {conflictLabel = r})
  translateCB(annotationTool.string.Activity, {}, $themeStore.language, (r) => {activityLabel = r})

  function handleNavItemClick(project: Project, application: 'annotate' | 'dashboard' | 'conflict' | 'activity'): void {
    selectedApplication = application
    selectedProject = project
  }

  getUserPermissions()
</script>

<div class="hulyNavPanel-header">
    <span class="overflow-label">
        <Label label={annotationTool.string.Title} />
    </span>
</div>

<Scroller shrink>
  {#each projects as project}
    <NavGroup
      categoryName={project.name}
      title={project.name}
      icon={project.icon}
      iconProps={{color: getPlatformColorDef(project.color ?? 0, $themeStore.dark).icon}}
      highlighted
      isFold
      visible={project.members.includes(getCurrentAccount()._id)}
      noDivider
    >
      <NavItem
        _id={'annotate'}
        icon={annotationTool.icon.Edit}
        selected={selectedProject && selectedProject._id === project._id && selectedApplication === 'annotate'}
        title={annotateLabel}
        on:click={() => handleNavItemClick(project, 'annotate')}
      />
      <NavItem
        _id={'dashboard'}
        icon={annotationTool.icon.Dashboard}
        selected={selectedProject && selectedProject._id === project._id && selectedApplication === 'dashboard'}
        title={dashboardLabel}
        on:click={() => handleNavItemClick(project, 'dashboard')}
      />
      <NavItem
          _id={'activity'}
          icon={annotationTool.icon.Activity}
          selected={selectedProject && selectedProject._id === project._id && selectedApplication === 'activity'}
          title={activityLabel}
          on:click={() => handleNavItemClick(project, 'activity')}
      />
      {#if annotatorPermissions?.find(annotator => annotator.attachedToProject === project._id && annotator.accessLevel === 'level2')}
        {#await getNumConflictingEpisodes(project, annotatorPermissions)}
          <div />
          {:then numConflictingEpisodes}
            <NavItem
              _id={'conflict'}
              icon={annotationTool.icon.Conflict}
              selected={selectedProject && selectedProject._id === project._id && selectedApplication === 'conflict'}
              title={conflictLabel}
              count={numConflictingEpisodes}
              on:click={() => handleNavItemClick(project, 'conflict')}
            />
          {:catch error}
            <NavItem
              _id={'conflict'}
              icon={annotationTool.icon.Conflict}
              selected={selectedProject && selectedProject._id === project._id && selectedApplication === 'conflict'}
              title={conflictLabel}
              on:click={() => handleNavItemClick(project, 'conflict')}
            />
        {/await}
      {/if}
    </NavGroup>
  {/each}
</Scroller>
<NavFooter />

<style lang="scss">

  .filter-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 16px 24px 8px 24px;
  }
</style>
