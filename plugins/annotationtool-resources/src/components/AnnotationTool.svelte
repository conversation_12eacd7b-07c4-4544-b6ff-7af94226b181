<script lang="ts">
  import { onDestroy } from 'svelte'
  import {
    deviceOptionsStore as deviceInfo,
    Location,
    location,
    restoreLocation,
    getCurrentLocation,
    navigate,
    Separator,
  } from '@hcengineering/ui'
  import { annotationToolId } from '@hcengineering/annotationtool'
  import Sidebar from './sidebar/Sidebar.svelte'
  import Annotate from './annotate/Annotate.svelte'
  import Dashboard from './dashboard/Dashboard.svelte'
  import Disagreement from './disagreement/Disagreement.svelte'
  import Stats from './stats/Stats.svelte'
  import tracker, { Project } from '@hcengineering/tracker'
  import { createQuery } from '@hcengineering/presentation'
  import { filtersStoreActions, selectedFiltersStore} from '../filtersStore'
  import { getCurrentAccount } from '@hcengineering/core'

  let selectedProject: Project | undefined = undefined
  let selectedApplication: 'annotate' | 'dashboard' | 'conflict' | 'activity' | undefined = undefined
  let replacedPanel: HTMLElement

  const projectsQuery = createQuery()

  let projects: Project[] = []
  $: projectsQuery.query(
    tracker.class.Project, 
    { 
      type: tracker.ids.RnDProjectType,
      members: getCurrentAccount()._id
    }, 
    (res) => {
      projects = res ?? []
    }
  )

  let loc: Location | undefined = undefined

  const unsubcribe = location.subscribe((currLocation) => {
    loc = currLocation
  })
  onDestroy(() => {
    unsubcribe()
  })

  $: syncLocation(loc, projects)

  function syncLocation (loc: Location | undefined, projects: Project[]): void {
    if(!loc) {
      return
    }

    if (loc.path[2] !== annotationToolId) {
      return
    }
    if (loc.path[3]) {
      const selectedProjectId = decodeURIComponent(loc.path[3])
      if(selectedProject?._id !== selectedProjectId && projects.find((p) => p._id === selectedProjectId)) {
        selectedProject = projects.find((p) => p._id === selectedProjectId)
      } else {
        restoreLocation(loc, annotationToolId)
        return 
      }
    } else {
      restoreLocation(loc, annotationToolId)
      return 
    }

    if(loc.path[4]) {
      selectedApplication = decodeURIComponent(loc.path[4]) as 'annotate' | 'dashboard'
    } else {
      selectedApplication = 'annotate'
    }
  }

  function updateLocation(selectedApplication?: 'annotate' | 'dashboard' | 'conflict' | 'activity', selectedProject?: Project | undefined): void {
    if(!selectedProject) {
      return
    }

    const loc = getCurrentLocation()

    loc.path[3] = encodeURIComponent(selectedProject._id)
    if(selectedApplication) {
      loc.path[4] = encodeURIComponent(selectedApplication)
      filtersStoreActions.setFiltersToDefault(selectedApplication, selectedProject._id, $selectedFiltersStore)
    }

    navigate(loc)
  }

  $: updateLocation(selectedApplication, selectedProject)

  $: $deviceInfo.replacedPanel = replacedPanel
  onDestroy(() => ($deviceInfo.replacedPanel = undefined))
</script>

<div class="hulyPanels-container">
  {#if $deviceInfo.navigator.visible}
    <div
      class="antiPanel-navigator {$deviceInfo.navigator.direction === 'horizontal'
        ? 'portrait'
        : 'landscape'} border-left"
      class:fly={$deviceInfo.navigator.float}
    >
      <div class="antiPanel-wrap__content hulyNavPanel-container">
        <Sidebar {projects} bind:selectedApplication bind:selectedProject />
      </div>
      {#if !($deviceInfo.isMobile && $deviceInfo.isPortrait && $deviceInfo.minWidth)}
        <Separator name="chat" float={$deviceInfo.navigator.float ? 'navigator' : true} index={0} />
      {/if}
    </div>
    <Separator
      name="chat"
      float={$deviceInfo.navigator.float}
      index={0}
      color={'transparent'}
      separatorSize={0}
      short
    />
  {/if}
  <div bind:this={replacedPanel} class="hulyComponent">
    {#if selectedProject}
      {#if selectedApplication === 'annotate'}
        <Annotate project={selectedProject} />
      {:else if selectedApplication === 'dashboard'}
        <Dashboard project={selectedProject} />
      {:else if selectedApplication === 'activity'}
        <Stats project={selectedProject} />
      {:else if selectedApplication === 'conflict'}
        <Disagreement project={selectedProject} />
      {/if}
    {/if}
  </div>
</div>

<style lang="scss">
  .annotationtool-component {
    padding: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    
    h2 {
      margin-top: 0;
    }
  }

  .no-project-selected {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
</style>