<script lang="ts">
    import { Label } from '@hcengineering/ui'
    import { getCurrentAccount } from '@hcengineering/core'
    import { Label as LabelType } from '@hcengineering/annotationtool'
    import { LabellingHistory } from '../../../types'
    import { Avatar, personByIdStore, personAccountByIdStore } from '@hcengineering/contact-resources'
    import { getDisplayTime } from '@hcengineering/core'
    import AnnotationLabel from '../../settings/components/AnnotationLabel.svelte'
    import annotationTool from '../../../plugin'
  import { IntlString } from '@hcengineering/platform'

    export let entry: LabellingHistory
    export let labels: LabelType[]
    export let isEpisodeActivity: boolean
    export let blurExternalActivity: boolean


    const blurLabel = blurExternalActivity && entry.labeller_id !== getCurrentAccount()._id

    const labellingPerson = $personByIdStore.get($personAccountByIdStore.get(entry.labeller_id as any)?.person as any) as any

    interface GroupedByStrip {
        strip_id: string;
        entries: {title: IntlString, labels: string[]}[];
    }

    let entryGroupedByStrip: GroupedByStrip[] = []

    function groupByStripId(entry: LabellingHistory, isEpisodeActivity: boolean) {
        entryGroupedByStrip = []
        
        let groupedEntries: any[] = []
        if(isEpisodeActivity) {
            const groups: Record<string, any[]> = {};
            for (const e of entry.entries) {
                const key = e.strip_id ?? "0";
                if (!groups[key]) groups[key] = [];
                groups[key].push(e);
            }
            // Always ensure there is an entry for "null" if any entries have no strip_id
            groupedEntries = Object.entries(groups).map(([strip_id, entries]) => ({
                strip_id,
                entries
            }));
        } else {
            groupedEntries = [{
                strip_id: entry.entries[0].strip_id,
                entries: entry.entries
            }]
        }

        entryGroupedByStrip = groupedEntries.map((e: any) => {
            const additions = e.entries.filter((e: any) => e.operation === 'addition').map((e: any) => e.label)
            const removals = e.entries.filter((e: any) => e.operation === 'removal').map((e: any) => e.label)
            const approvals = e.entries.filter((e: any) => e.operation === 'approval').map((e: any) => e.label)
            const disapprovals = e.entries.filter((e: any) => e.operation === 'disapproval').map((e: any) => e.label)

            return {
                strip_id: e.strip_id,
                entries: [
                    ...(additions.length > 0 ? [{
                        title: annotationTool.string.Added,
                        labels: additions
                    }] : []),
                    ...(removals.length > 0 ? [{
                        title: annotationTool.string.Removed,
                        labels: removals
                    }] : []),
                    ...(approvals.length > 0 ? [{
                        title: annotationTool.string.Approved,
                        labels: approvals
                    }] : []),
                    ...(disapprovals.length > 0 ? [{
                        title: annotationTool.string.Disapproved,
                        labels: disapprovals
                    }] : [])
                ]
            }
        }).sort((a: any, b: any) => a.strip_id.localeCompare(b.strip_id))
    }

    $: groupByStripId(entry, isEpisodeActivity);
</script>

<div class="labelling-history-entry">
    <div class="labelling-history-entry-header">
        <Avatar person={labellingPerson} size="x-small" name={labellingPerson.name} />
        <div class="labelling-history-entry-header-content">
            <div class="labelling-history-entry-header-content-name">
                {labellingPerson.name.split(',').reverse().join(' ')}
            </div>
            <div class="labelling-history-entry-header-content-timestamp">
                {getDisplayTime(entry.start_at)}
            </div>
        </div>
    </div>
    <div class="labelling-history-entry-body">
        <div class="vertical-divider"/>
        <div class="labelling-history-entry-body-content">
            {#if entryGroupedByStrip.length > 0}
                {#each entryGroupedByStrip[0].entries as entry, index (index)}
                    <div class="labelling-history-entry-body-labels">
                        <div class="labelling-history-entry-body-title">
                            <Label label={entry.title} /> 
                        </div>
                        {#each entry.labels as labelEntry, index (index)}
                            {@const label = labels.find(l => l.value === labelEntry)}
                            {#if label}
                                <div class="labelling-history-entry-body-label">
                                    <AnnotationLabel {label} blur={blurLabel} selected padding="0 0.2rem" />
                                </div>
                            {/if}
                            {#if labelEntry === 'isInterestingFlag'}
                                <div class="labelling-history-entry-body-custom-label">
                                    MARKED AS INTERESTING
                                </div>
                            {/if}
                            {#if labelEntry === 'isDoneFlag'}
                                <div class="labelling-history-entry-body-custom-label">
                                    MARKED AS DONE
                                </div>
                            {/if}
                            {#if labelEntry === 'isConflictResolvedFlag'}
                                <div class="labelling-history-entry-body-custom-label">
                                    CONFLICT RESOLVED
                                </div>
                            {/if}
                        {/each}
                    </div>  
                {/each}
                <div class="labelling-history-entry-body-subcontent">
                    <div class="vertical-divider"/>
                    <div class="labelling-history-entry-body-content">
                        {#each entryGroupedByStrip.slice(1) as entry, index (index)}
                            <div class="labelling-history-entry-body-content">
                                <div class="labelling-history-entry-body-title-bold">
                                    <Label label={annotationTool.string.StripTitle} params={{ stripId: entry.strip_id }} /> 
                                </div>
                                {#each entry.entries as entryWithTitle, index (index)}
                                    <div class="labelling-history-entry-body-labels">
                                        <div class="labelling-history-entry-body-title">
                                            <Label label={entryWithTitle.title} /> 
                                        </div>
                                        {#each entryWithTitle.labels as labelEntry, index (index)}
                                            {@const label = labels.find(l => l.value === labelEntry)}
                                            {#if label}
                                                <div class="labelling-history-entry-body-label">
                                                    <AnnotationLabel {label} blur={blurLabel} selected padding="0 0.2rem" />
                                                </div>
                                            {/if}
                                            {#if labelEntry === 'isInterestingFlag'}
                                                <div class="labelling-history-entry-body-custom-label">
                                                    MARKED AS INTERESTING
                                                </div>
                                            {/if}
                                            {#if labelEntry === 'isDoneFlag'}
                                                <div class="labelling-history-entry-body-custom-label">
                                                    MARKED AS DONE
                                                </div>
                                            {/if}
                                            {#if labelEntry === 'isConflictResolvedFlag'}
                                                <div class="labelling-history-entry-body-custom-label">
                                                    CONFLICT RESOLVED
                                                </div>
                                            {/if}
                                        {/each}
                                    </div>
                                {/each}
                            </div> 
                        {/each}
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>

<style lang="scss"> 
    .labelling-history-entry {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .labelling-history-entry-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 1.1rem;
        gap: 0.5rem;
    }

    .labelling-history-entry-header-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 0.25rem;
    }

    .labelling-history-entry-header-content-name {
        font-weight: 600;
        font-size: 1.1rem;
        margin-right: 0.25rem;
    }

    .labelling-history-entry-header-content-timestamp {
        font-size: 0.75rem;
        color: var(--color-text-secondary);
    }

    .labelling-history-entry-body {
        display: flex;
        flex-direction: row;
    }

    .labelling-history-entry-body-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .labelling-history-entry-body-subcontent {
        display: flex;
        flex-direction: row;
    }

    .labelling-history-entry-body-labels {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        gap: 0.5rem;
    }

    .labelling-history-entry-body-label {
        font-size: 0.875rem;
    }

    .labelling-history-entry-body-custom-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--content-color);
        border: 1px solid var(--theme-divider-color);
        border-radius: 0.25rem;
        padding: 0.1rem 0.2rem;
        box-shadow: 0 0 4px 2px var(--divider-trans-color);
        white-space: nowrap;
    }

    .vertical-divider {
        display: flex;
        align-self: stretch;
        width: 1px;
        background-color: var(--theme-divider-color);
        margin: 0 11px 0 11px;
    }

    :global(.labelling-history-entry-body-title) {
        font-size: 0.875rem;
        font-weight: 400!important;
        width: 80px;
    }

    :global(.labelling-history-entry-body-title-bold) {
        font-size: 0.875rem;
        font-weight: 500!important;
    }
</style>