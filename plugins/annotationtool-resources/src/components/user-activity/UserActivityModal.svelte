<script lang="ts">
    import annotationTool from '../../plugin'
    import { Modal } from '@hcengineering/ui'
    import { Project } from '@hcengineering/tracker'
    import { Label as LabelType} from '@hcengineering/annotationtool'
    import UserActivityContent from './UserActivityContent.svelte'

    export let episodeId: string | undefined = undefined
    export let stripId: string | undefined = undefined
    export let catalog: string
    export let hidden: boolean = true
    export let labels: LabelType[] = []
    export let project: Project
    export let onCancel: () => void = () => {}
    export let annotatorAccessLevel: string
  
    export let isEpisodeActivity = stripId === undefined

    const blurExternalActivity = !annotatorAccessLevel || annotatorAccessLevel === 'level1'

    function handleClose() {
      hidden = true
      onCancel()
    }
</script>
  
<Modal
    type="type-popup"
    label={isEpisodeActivity ? annotationTool.string.EpisodeActivity : annotationTool.string.StripActivity}
    labelProps={isEpisodeActivity ? { episodeId } : { stripId }}
    cancelLabel={annotationTool.string.Close}
    {hidden}
    onCancel={handleClose}
    showOkButton={false}
    allowFullsize
>
    <UserActivityContent {episodeId} {stripId} {catalog} {labels} {project} {isEpisodeActivity} {annotatorAccessLevel} />
</Modal>
