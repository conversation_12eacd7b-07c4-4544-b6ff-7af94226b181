<script lang="ts">
import activityPlugin from '@hcengineering/activity'
import { AnnotationActivity, Label as LabelType } from '@hcengineering/annotationtool'
import { getCurrentAccount } from '@hcengineering/core'
import { getClient } from '@hcengineering/presentation'
import { Project } from '@hcengineering/tracker'
import { Component, Label, Scroller, Spinner } from '@hcengineering/ui'
import { fetchEpisodeDetails, fetchEpisodeHistory, fetchStripHistory } from '../../api'
import { createEpisodeLabellingStore } from '../../episodeLabellingStore'
import annotationTool from '../../plugin'
import { createSubmitLabelsStore } from '../../submitLabelsStore'
import { Episode, LabellingHistory, Strip } from '../../types'
import EpisodeCard from '../annotate/components/EpisodeCard.svelte'
import EpisodeStrip from '../annotate/components/EpisodeStrip.svelte'
import LabellingHistoryEntry from './components/LabellingHistoryEntry.svelte'

    export let episodeId: string | undefined = undefined
    export let stripId: string | undefined = undefined
    export let catalog: string
    export let labels: LabelType[] = []
    export let project: Project
    export let isPreview: boolean = false
    export let annotatorAccessLevel: string
  
    export let isEpisodeActivity = stripId === undefined

    const blurExternalActivity = !annotatorAccessLevel || annotatorAccessLevel === 'level1'

    let history: LabellingHistory[] = []
    let activity: AnnotationActivity | undefined = undefined
    let activityBoundary: HTMLElement | undefined = undefined
    let loading: boolean = false
    let activityLoading: boolean = false
    let episode: Episode | undefined = undefined
    let strip: Strip | undefined = undefined
    let nextStrip: Strip | undefined = undefined
    let episodeStore: any | undefined = undefined
    let episodeLoading: boolean = false

    const client = getClient()
  
    async function createActivity(episodeId: string | undefined, stripId: string | undefined, project: Project) {
        activityLoading = true

        try {
            activity = await client.findOne(annotationTool.class.AnnotationActivity, {
                episodeId,
                stripId,
                projectId: project._id,
                datasetId: catalog
            })
            if(!activity) {
            
                const id = await client.createDoc(annotationTool.class.AnnotationActivity, project.space, {
                        episodeId,
                        stripId,
                        projectId: project._id,
                        datasetId: catalog,
                        // @ts-ignore
                        space: project._id
                    }
                )
                
                activity = await client.findOne(annotationTool.class.AnnotationActivity, {_id: id as any})
            }
        } catch (error) {
            console.error(error)
        }
        activityLoading = false
    }

    async function getEpisodeHistory(episodeId: string, catalog: string) {
        loading = true
        try {
            const response = await fetchEpisodeHistory(episodeId, catalog)
            history = response
        } catch (error) {
            console.error(error)
        }
        loading = false
    }

    async function getStripHistory(stripId: string, catalog: string) {
        loading = true
        try {
            const response = await fetchStripHistory(stripId, catalog)
            history = response
        } catch (error) {
            console.error(error)
        }
        loading = false
    }

    async function getEpisodeAndPrepareForPreview(episodeId: string, stripId: string, catalog: string) {
        episodeLoading = true
        
        try {
            episode = await fetchEpisodeDetails(catalog, episodeId)

            if(!episode) return

            const { addToSubmitQueue } = createSubmitLabelsStore()

            episodeStore = createEpisodeLabellingStore(episode, getCurrentAccount()._id, project.datasetID ?? '', addToSubmitQueue)

            if(stripId) {
                strip = episode?.strips.find((strip: Strip) => strip.id === stripId) ?? undefined
                nextStrip = episode?.strips.find((strip: Strip) => strip.id === stripId + 1) ?? undefined
            }
        } catch (error) {
            console.error(error)
        }

        episodeLoading = false
    }

    $: createActivity(episodeId, stripId, project)
    $: if(isEpisodeActivity) {
        getEpisodeHistory(episodeId ?? '', catalog)
    } else {
        getStripHistory(stripId ?? '', catalog)
    }
    $: isPreview && getEpisodeAndPrepareForPreview(episodeId ?? '', stripId ?? '', catalog)    
</script>

<div class="modal-content" class:preview={isPreview}>
    {#if loading || activityLoading || episodeLoading}
        <div class="flex justify-center items-center p-8 episodes-loading">
            <Spinner size="x-large" />
        </div>
    {:else}
        {#if isPreview && (episode != null || strip != null)}
            <div class="preview-container">
                {#if isEpisodeActivity && episode != null}
                    <EpisodeCard 
                        {episode} 
                        labels={labels} 
                        triageLabels={[]}
                        viewMode="card"  
                        catalog={project.datasetID ?? ''}
                        {project}
                        store={episodeStore}
                        {annotatorAccessLevel}
                    />
                {:else if strip != null && episode != null}
                    <EpisodeStrip 
                        stripId={strip.id}
                        nextStripId={nextStrip?.id ?? ''}
                        episodeId={episode.episode_id}
                        episodeStore={episodeStore.episodeLabellingStore}
                        episodeStoreActions={episodeStore.episodeLabellingStoreActions}
                        stripValues={strip.voltages}
                        stripTimestamps={strip.timestamps}
                        nextStripTimestamps={nextStrip?.timestamps ?? []}
                        nextStripValues={nextStrip?.voltages ?? []}
                        triageLabels={[]}
                        hasActivity
                        {labels}
                        {catalog}
                        {project}
                        {annotatorAccessLevel}
                    />
                {/if}
            </div>
        {/if}
        <div class="wrapper" class:wrapper-preview={isPreview} class:wrapper-preview-strip={isPreview && !isEpisodeActivity}>
            <div class="activity-container" bind:this={activityBoundary}>
                {#if activity != null}
                    <Component
                        is={activityPlugin.component.Activity}
                        props={{
                            object: activity,
                            showCommenInput: true,
                            shouldScroll: true,
                            focusIndex: 1000,
                            boundary: activityBoundary
                        }}
                    />
                {/if}
            </div>
            <div class="vertical-divider" />
            <div class="history-container">
                {#if history.length > 0}
                <Scroller>
                    {#each history as entry, index (index)}
                        <div class="history-entry">
                        <LabellingHistoryEntry {entry} {labels} {isEpisodeActivity} {blurExternalActivity} />
                        </div>
                        {#if index !== history.length - 1}
                        <div class="horizontal-divider"/>
                        {/if}
                    {/each}
                </Scroller>
                {:else}
                    <div class="flex justify-center items-center h-full">
                        <Label label={annotationTool.string.NoHistory} />
                    </div>
                {/if}
            </div>
        </div>    
    {/if}
</div>

<style lang="scss">
    .modal-content {
        width: 60vw;
        height: 60vh;
        max-height: 60vh;
    }

    .preview {
        padding: 20px;
        width: 100%!important;
        height: calc(100vh - 38px)!important;
        max-height: calc(100vh - 38px)!important;
    }

    .preview-container {
        overflow-y: auto;
        max-height: 50%;
    }

    .wrapper {
        display: flex;
        flex-direction: row;
        gap: 1rem;
        height: 100%;
    }

    .wrapper-preview {
        height: 50%;
    }

    .wrapper-preview-strip {
        height: calc(100% - 450px);
    }
    
    .activity-container {
        display: flex;
        flex: 5;
    }

    .history-container {
        display: flex;
        flex: 2;
        flex-direction: column;
        gap: 0.5rem;
        max-height: 60vh;
    }

    .horizontal-divider {
        width: 100%;
        height: 1px;
        background-color: var(--theme-divider-color);
    }

    .vertical-divider {
        width: 1px;
        height: 100%;
        background-color: var(--theme-divider-color);
    }

    :global(.activity-container .step-tb-6) {
        width: 100%;
    }

    :global(.activity-container .antiSection) {
        height: 100%;
    }

    :global(.activity-container .p-activity) {
        height: 100%;
    }

    :global(.activity-container .ref-input) {
        padding-bottom: 0!important;
    }

    :global(.activity-container .ref-input .root) {
        display: none!important;
    }

    :global(.activity-container .activityMessage-actionPopup > button:nth-child(4)) {
        display: none !important;
    }

    :global(.activity-container .antiSection-header__tag) {
        display: none!important;
    }

    :global(.activity-container .w-4) {
        display: none!important;
    }

    :global(.activity-container .buttons-group) {
        display: none!important;
    }

    :global(.activity-container .p-activity) {
        height: 100%;
    }
</style>