<script lang="ts">
    import StatsHeader from './components/StatsHeader.svelte'
    import StatCard from './components/StatCard.svelte'
    import { fetchLabellingStats } from '../../api'
    import type { Project } from '@hcengineering/tracker'
    import type { LabellingStats } from '../../types'
    import annotationTool from '../../plugin'
    import { createQuery } from '@hcengineering/presentation'
    import { Ref, Class, Account, getCurrentAccount } from '@hcengineering/core'
    import type { Annotator } from '@hcengineering/annotationtool'

    export let project: Project

    const annotatorAccessLevelsQuery = createQuery()

    let stats: LabellingStats[] = []
    let annotatorAccessLevel: string = 'level1'
    let loading = false

    const getStats = async (project: Project) => {
        loading = true
        stats = await fetchLabellingStats(project.datasetID ?? '')
        loading = false
    }

    const getAnnotatorAccessLevels = async (currentUserId: string) => {
        annotatorAccessLevelsQuery.query(
            annotationTool.class.Annotator as Ref<Class<Annotator>>,
            {
                accountId: currentUserId as Ref<Account>,
                attachedToProject: project._id as Ref<Project>
            },
            (res) => {
                annotatorAccessLevel = res[0]?.accessLevel ?? 'level1'
            }
        )
    }

    $: getStats(project)
    $: getAnnotatorAccessLevels(getCurrentAccount()._id)

</script>

<StatsHeader />

<div class="stats-container">
    {#if loading}
        <div class="loading-container">
            <div class="loading-spinner"></div>
        </div>
    {:else}
        {#each stats.filter(stat => annotatorAccessLevel === 'level2' || stat.labeller_id === getCurrentAccount()._id) as stat}
            <StatCard {stat} />
        {/each}
    {/if}
</div>

<style>
    .stats-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
</style>