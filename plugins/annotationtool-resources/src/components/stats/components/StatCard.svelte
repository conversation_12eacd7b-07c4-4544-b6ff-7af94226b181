<script lang="ts">
    import type { LabellingStats } from '../../../types'
    import { Avatar, personByIdStore, personAccountByIdStore } from '@hcengineering/contact-resources'
    import { Label } from '@hcengineering/ui'
    import annotationTool from '../../../plugin'

    export let stat: LabellingStats

    $: daysArray = Object.keys(stat)
        .filter(key => !['person', 'labeller_id'].includes(key))
        .map(day => ({day, count: stat[day] as number}))
        .sort((a, b) => new Date(b.day).getTime() - new Date(a.day).getTime())

    const labellingPerson = $personByIdStore.get($personAccountByIdStore.get(stat.labeller_id as any)?.person as any) as any
</script>

<div class="stat-card">
    <div class="stat-card-person">
        <Avatar person={labellingPerson} size="medium" name={labellingPerson.name} />
        <p>{labellingPerson.name.split(',').reverse().join(' ')}</p>
    </div>
    <div class="vertical-divider" />
    <div class="stat-card-content">
        <div class="stat-card-entry">
            <div class="stat-card-entry-title">
                <Label label={annotationTool.string.ByDay} />
            </div>
            <div class="stat-card-entry-values">
                {#each daysArray.slice(0, 10) as day, index}
                    <div class="stat-card-entry-value">
                        <span class="stat-card-entry-value-date">
                            {new Date(day.day).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                        </span>
                        <br />
                        {day.count} <Label label={annotationTool.string.Strips} />
                    </div>
                    {#if index !== daysArray.length - 1}
                        <div class="vertical-divider" />
                    {/if}
                {/each}
            </div>
        </div>
        <div class="horizontal-divider" />
        <div class="stat-card-entry">
            <div class="stat-card-entry-title">
                <Label label={annotationTool.string.Week} />
            </div>
            <div class="stat-card-entry-values">
                {daysArray.slice(0, 10).reduce((acc, day) => acc + day.count, 0)} <Label label={annotationTool.string.Strips} />
            </div>
        </div>
        <div class="horizontal-divider" />
        <div class="stat-card-entry">
            <div class="stat-card-entry-title">
                <Label label={annotationTool.string.Total} />
            </div>
            <div class="stat-card-entry-values">
                {daysArray.reduce((acc, day) => acc + day.count, 0)} <Label label={annotationTool.string.Strips} />
            </div>
        </div>
    </div>
</div>

<style>
    .stat-card {
        display: flex;
        flex-direction: row;
        gap: 20px;
        border: 1px solid var(--theme-navpanel-divider);
        border-radius: 10px;
        padding: 10px;
        background-color: var(--theme-navpanel-color);
        align-items: stretch;
    }

    .vertical-divider {
        width: 1px;
        height: 100%;
        background-color: var(--theme-navpanel-divider);
    }

    .horizontal-divider {
        width: 100%;
        height: 1px;
        background-color: var(--theme-navpanel-divider);
    }

    .stat-card-person {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100px;
        font-size: 15px;
        font-weight: 600;
        text-align: center;
    }

    .stat-card-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 8px;
        width: 100%;
        height: 100%;
    }

    .stat-card-entry-title {
        font-size: 14px;
        font-weight: 500;
        width: 90px;
    }

    .stat-card-entry {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 5px;
    }

    .stat-card-entry-values {
        display: flex;
        flex-direction: row;
        align-items: center;
        align-self: stretch;
        gap: 8px;
    }

    .stat-card-entry-value {
        text-align: center;
    }

    .stat-card-entry-value-date {
        font-weight: 450;
    }
</style>