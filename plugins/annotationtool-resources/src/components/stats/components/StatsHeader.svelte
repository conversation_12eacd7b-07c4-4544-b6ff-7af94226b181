<script lang="ts">
    import { ButtonIcon, Label } from '@hcengineering/ui'
    import annotationTool from '../../../plugin'
</script>

<div class="hulyHeader-container">
      <button class="hulyBreadcrumb-container large current">
        <ButtonIcon
            icon={annotationTool.icon.Activity}
            kind="tertiary"
            size={'medium'}
            pressed={false}
            disabled={true}
        />
        <span class="heading-medium-16 line-height-auto hulyBreadcrumb-label overflow-label">
          <Label label={annotationTool.string.Title} /> - <Label label={annotationTool.string.Activity} />
        </span>
      </button>
</div>