<script lang="ts">
    import UserActivityContent from '../user-activity/UserActivityContent.svelte'
    import { Ref, Class, getCurrentAccount } from '@hcengineering/core'
    import { getClient } from '@hcengineering/presentation'
    import { AnnotationActivity, Label as LabelType } from '@hcengineering/annotationtool'
    import annotationTool from '@hcengineering/annotationtool'
    import tracker, { Project } from '@hcengineering/tracker'
    
    export let _id: Ref<AnnotationActivity>
    export let _class: Ref<Class<AnnotationActivity>>

    const client = getClient()

    let activity: AnnotationActivity | undefined = undefined
    let episodeId: string = ''
    let stripId: string = ''
    let catalog: string = ''
    let labels: LabelType[] = []
    let project: Project | undefined = undefined
    let isEpisodeActivity: boolean = false
    let annotatorAccessLevel: string = ''

    const fetchData = async (id: Ref<AnnotationActivity>, _class: Ref<Class<AnnotationActivity>>) => {
        activity = await client.findOne(_class, {_id: id})

        if(!activity) return

        episodeId = activity.episodeId ?? ''
        stripId = activity.stripId ?? ''
        catalog = activity.datasetId ?? '' // Use datasetId instead of catalog, with fallback
        
        isEpisodeActivity = activity.stripId == null

        project = (await client.findOne(tracker.class.Project, {_id: activity.projectId}))

        if(!project) return

        annotatorAccessLevel = (await client.findOne(annotationTool.class.Annotator, {attachedToProject: activity?.projectId, accountId: getCurrentAccount()._id}))?.accessLevel ?? ''
        labels = (await client.findAll(annotationTool.class.Label, {attachedToProject: project._id})) ?? []
    }

    $: fetchData(_id, _class)
</script>

{#if activity != null && project != null && labels != null}
    <div class="activity-panel">
        <UserActivityContent {episodeId} {stripId} {catalog} {labels} {project} {isEpisodeActivity} {annotatorAccessLevel} isPreview={true} />
    </div>
{/if}