<script lang="ts">
    import { Label as LabelType } from '@hcengineering/annotationtool'
    import annotationTool from '../../../plugin'
    import { getPlatformColorDef, themeStore, ButtonIcon } from '@hcengineering/ui'

    export let label: LabelType
    export let allowDelete: boolean = false
    export let selectable: boolean = true
    export let selected: boolean = false
    export let segmentationSelected: boolean = false
    export let approved: boolean = false
    export let numSelected: number = 0
    export let disapproved: boolean = false
    export let width: string | null = null
    export let padding: string = '0.5rem'
    export let isConflictResolution: boolean = false
    export let blur: boolean = false
    export let onSelect: () => void = () => {}
    export let onDelete: (label: LabelType) => void = () => {}
    export let onApprove: () => void = () => {}
    export let onDisapprove: () => void = () => {}

    $: dynamicPlatformColor = getPlatformColorDef(blur ? 21 : label?.color, $themeStore.dark)
    const whiteThemePlatformColor = getPlatformColorDef(blur ? 21 : label?.color, false)
    $: borderColor = dynamicPlatformColor.color
    $: backgroundColor = selected ? whiteThemePlatformColor.background : dynamicPlatformColor.background
    $: textColor = selected ? whiteThemePlatformColor.title : dynamicPlatformColor.title


</script>

{#if dynamicPlatformColor && whiteThemePlatformColor}
    <div 
        class="label-wrapper" 
        role="button"
        tabindex="0"
        style={`
            padding: ${padding};
            border: ${segmentationSelected ? '2px' : '1px'} solid ${segmentationSelected ? 'var(--primary-edit-border-color)' : borderColor}; 
            background-color: ${selected ? `${backgroundColor}` : 'var(--color-white)'};
            box-shadow: 0 0 4px 2px ${borderColor}33;
            ${width ? `width: ${width};` : ''}
        `}
        on:click={() => {
            if (selectable) {
                onSelect()
            }
        }}
        on:keydown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                if (selectable) {
                    onSelect()
                }
            }
        }}
    >
        <div class="label-text" class:blur={blur} style={`color: ${textColor}; font-weight: 600;`}>{label.label}</div>
        {#if allowDelete}
            <div class="divider" style={`background-color: ${textColor};`}></div>
            <ButtonIcon 
                icon={annotationTool.icon.Delete} 
                size="extra-small" 
                iconProps={{ color: textColor }} 
                kind="tertiary"
                on:click={() => onDelete(label)}
            />
        {/if}
        {#if isConflictResolution}
            <div class="divider" style={`background-color: ${textColor};`} />
            {#if !approved}
                <ButtonIcon 
                    icon={annotationTool.icon.CheckmarkCircle} 
                    size="extra-small" 
                    tooltip={{label: annotationTool.string.Approve}}
                    iconProps={{ color: textColor }} 
                    kind="tertiary"
                    on:click={onApprove}
                />
            {/if}
            {#if !disapproved}
                <ButtonIcon 
                    icon={annotationTool.icon.XCircle} 
                    size="extra-small" 
                    tooltip={{label: annotationTool.string.Reject}}
                    iconProps={{ color: textColor }} 
                    kind="tertiary"
                    on:click={onDisapprove}
                />
            {/if}
        {/if}
        {#if approved}
            <div class="approved-badge">Approved</div>
        {/if}
        {#if disapproved}
            <div class="rejected-badge">Rejected</div>
        {/if}  
        {#if isConflictResolution} 
            <div class="num-selections" style={`border-color: ${textColor}; background-color: ${backgroundColor}; color: ${textColor};`}>
                {numSelected ?? 0}
            </div>
        {/if}
    </div>
{/if}

<style lang="scss">
    .label-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        gap: 10px;
        cursor: pointer;
    }

    .divider {
        width: 1px;
        height: 16px;
    }

    .approved-badge, .rejected-badge {
        position: absolute;
        top: -10px;
        left: 4px;
        font-size: 12px;
        font-weight: 600;
        z-index: 2;
        white-space: nowrap;
        text-transform: uppercase;
    }

    .approved-badge {
        background-color: #e6f9ed;
        color: #1a7f37;
        border-radius: 5px;
        padding: 0 4px;
        border: 1px solid #1a7f37;
    }

    .rejected-badge {
        background-color: #fdeaea;
        color: #c72c2c;
        border-radius: 5px;
        padding: 0 4px;
        border: 1px solid #c72c2c;
    }

    .num-selections {
        position: absolute;
        top: -10px;
        right: -4px;
        font-size: 12px;
        font-weight: 600;
        z-index: 2;
        border-radius: 12px;
        padding: 0 4px;
        border: 1px solid;
    }

    .label-text.blur {
        filter: blur(1rem);
    }
</style>