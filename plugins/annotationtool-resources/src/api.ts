import { filtersStoreActions } from "./filtersStore"
import annotationTool from "./plugin"
import { getMetadata } from "@hcengineering/platform"
import presentation from '@hcengineering/presentation'
import type { ClinicApiResponse, Episode, EpisodeApiResponse, LabelToSubmit } from "./types"
import { getLabellerName } from "./utils"
import { getCurrentAccount } from "@hcengineering/core"
import type { LabellingStats } from "./types"

const rdUrl = getMetadata(annotationTool.metadata.RdUrl)

// Default headers for unauthenticated requests
const defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}

// Function to get authenticated headers
const getAuthHeaders = (): Record<string, string> => {
    const token = getMetadata(presentation.metadata.Token)
  
    if (!token) {
      throw new Error('No authentication token found. Please login first.')
    }
  
    return {
      ...defaultHeaders,
      Authorization: `Bearer ${token}`
    }
  }

export const fetchEpisodes = async (selectedFilters: Record<string, { value: string, label: string }>, catalog: string): Promise<EpisodeApiResponse> => {
    try {
        let filters: any = {
            catalog
        };

        Object.entries(selectedFilters).forEach(([key, value]) => {
            if(key === 'sorting' && value.value) {
                const [sort_by, sort_order] = value.value.split('-')
                filters.order_by = sort_by
                filters.order_dir = sort_order
            }

            if(key === 'date_from' || key === 'date_to') {
                filters.date_from = value.value === 'none' ? undefined : value.value
            } 
            else if(key === 'page') {
                filters.offset = (Number(value.value) - 1) * Number(filters.limit)
            }
            else if(key === 'limit') {
                filters.limit = Number(value.value)
            }
            else if(key === 'maxLabellingTime') {
                filters.max_labelling_time = !value.value || value.value === 'none' ? undefined : value.value
            }
            else {
                filters[`${key}${!key.includes('labell') ? 's' : ''}`] = value.value === 'none' || value.value === '' ? undefined : [value.value]
            }
        })

        delete filters.sorting;

        filters.current_user = getCurrentAccount()?._id;

        const response = await fetch(`${rdUrl}api/v1/ecg/query`, {
            headers: getAuthHeaders(),
            method: 'POST',
            body: JSON.stringify(filters)
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        try {
            if(data.data.episodes && !selectedFilters.episode_id?.value) {
                data.data.episodes = data.data.episodes.sort((a: Episode, b: Episode) => {
                    if(!a || !b) return 0;
                    
                    const timeA = new Date(a.report_time).getTime();
                    const timeB = new Date(b.report_time).getTime();
                    return timeA - timeB;
                });
            }
        } catch (error) {
            console.error('Error sorting episodes', error)
        }

        return data.data ?? {episodes: [], total_count: 0, page: 1, limit: 5, total_pages: 1}
    } catch (error) {
        console.error(error)

        return {episodes: [], total_count: 0, page: 1, limit: 5, total_pages: 1};
    }
}       

export const fetchEpisodeDetails = async (catalog: string, episodeId: string): Promise<Episode | undefined> => {
    try {
        const response = await fetch(`${rdUrl}api/v1/ecg/episodes/${catalog}/${episodeId}`, {
            headers: getAuthHeaders()
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        return data.data
    } catch (error) {
        console.error(error)

        return undefined;
    }
}

export const submitLabels = async (labelToSubmit: LabelToSubmit) => {
    try {
        const response = await fetch(`${rdUrl}api/v1/operation`, {
            headers: getAuthHeaders(),
            method: 'POST',
            body: JSON.stringify(labelToSubmit)
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        return true 
    } catch (error) {
        console.error(error)

        return false
    }
}

export const fetchClinics = async (catalog: string): Promise<ClinicApiResponse[]> => {
    try {
        const response = await fetch(`${rdUrl}api/v1/ecg/clinics?catalog=${encodeURIComponent(catalog)}`, {
            headers: getAuthHeaders()
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        return data.data?.map((data: any) => ({id: data.clinic_id, label: data.clinic_name})) ?? []
    } catch (error) {
        console.error(error)

        return [];
    }
}

export const fetchPatients = async (clinicId: string, catalog: string, selectedApplication: 'annotate' | 'dashboard' | 'conflict', projectId: string): Promise<{id: string, label: string}[]> => {
    if(!clinicId) {
        filtersStoreActions.removeFilter('patient', selectedApplication, projectId)
    }

    try {
        const response = await fetch(`${rdUrl}api/v1/ecg/patients?clinic_id=${encodeURIComponent(clinicId)}&catalog=${encodeURIComponent(catalog)}`, {
            headers: getAuthHeaders()
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        return data.data?.map((data: any) => ({id: data.patient_id, label: data.patient_name})) ?? []
    } catch (error) {
        console.error(error)

        return [];
    }
}

export const fetchLabelledBy = async (catalog: string): Promise<string[]> => {
    try {
        const response = await fetch(`${rdUrl}api/v1/labellers?catalog=${encodeURIComponent(catalog)}`, {
            headers: getAuthHeaders()
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        return data.data?.map((data: any) => ({id: data.labeller_id, label: getLabellerName(data.labeller_id)   })) ?? []
    } catch (error) {
        console.error(error)

        return [];
    }
}

export const fetchDashboard = async (tableFilters: Record<string, { value: string, label: string }>, tableSettings: Record<string, boolean>, catalog: string): Promise<any[][]> => {
    try {

        let filters: any = {
            catalog
        };

        Object.entries(tableSettings).filter(([key, value]) => key.startsWith('leftColumnDistributor-') && !!value).forEach(([key, value]) => {
            filters[key] = 1
        })

        Object.entries(tableSettings).filter(([key, value]) => key.startsWith('hierarichical-') && !!value)
            .sort(([aKey, aValue], [bKey, bValue]) => {
                if (aValue === bValue) return 0;
                return aValue < bValue ? -1 : 1;
            })
            .forEach(([key, value]) => {
                filters[key] = 1
            })

        Object.entries(tableFilters).filter(([key, value]) => !!value).forEach(([key, value]) => {
            filters[key] = value.value === 'none' ? undefined : [value.value]
        })

        // Build query string
        const queryString = Object.entries(filters)
            .filter(([_, value]) => value !== undefined)
            .map(([key, value]) => Array.isArray(value) 
                ? value.map(v => `${key}=${encodeURIComponent(v)}`).join('&')
                : `${key}=${encodeURIComponent(value as any)}`)
            .join('&');

        const url = `${rdUrl}api/v1/ecg/dashboard${queryString ? '?' + queryString : ''}`;

        const response = await fetch(url, {
            headers: getAuthHeaders(),
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        const userIdColumnIndexes = data.data.column_order.map((column: string, index: number) => column.toLowerCase().includes('doctorlabel') ? index : -1).filter((index: number) => index !== -1)

        const dashboardData = data.data.data?.map((row: any) => {
            return row.map((cell: any, index: number) => {
                if(userIdColumnIndexes.includes(index) && !!cell?.length) {
                    const labellers = cell.split(', ')
                    return labellers.map((labeller: string) => getLabellerName(labeller)).join(', ')
                }
                return cell
            })
        })

        return dashboardData ?? []
    } catch (error) {
        console.error(error)

        return []
    }
}

export const fetchConflictingEpisodes = async (selectedFilters: Record<string, { value: string, label: string }>, catalog: string): Promise<EpisodeApiResponse> => {
    try {
        let filters: any = {
            catalog
        };

        Object.entries(selectedFilters).forEach(([key, value]) => {
            if(key === 'sorting' && value.value) {
                const [sort_by, sort_order] = value.value.split('-')
                filters.order_by = sort_by
                filters.order_dir = sort_order
            }

            if(key === 'date_from' || key === 'date_to') {
                filters.date_from = value.value === 'none' ? undefined : value.value
            } 
            else if(key === 'page') {
                filters.offset = (Number(value.value) - 1) * Number(filters.limit)
            }
            else if(key === 'limit') {
                filters.limit = Number(value.value)
            }
            else {
                filters[`${key}${!key.includes('labell') && key !== 'conflict_status' ? 's' : ''}`] = value.value === 'none' || value.value === '' ? undefined : [value.value]
            }
        })

        delete filters.sorting;

        const response = await fetch(`${rdUrl}api/v1/ecg/conflicting-episodes`, {
            headers: getAuthHeaders(),
            method: 'POST',
            body: JSON.stringify(filters)
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        return data.data ?? {episodes: [], total_count: 0, page: 1, limit: 5, total_pages: 1}
    } catch (error) {
        console.error(error)

        return {episodes: [], total_count: 0, page: 1, limit: 5, total_pages: 1};
    }
}

export const fetchNumConflictingEpisodes = async (catalog: string): Promise<number> => {
    try {
        const response = await fetch(`${rdUrl}api/v1/ecg/num-conflicting-episodes?catalog=${encodeURIComponent(catalog)}`, {
            headers: getAuthHeaders(),
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        return data.data.num_conflicting_episodes ?? 0
    } catch (error) {
        console.error(error)

        return 0;
    }
}

export const fetchEpisodeHistory = async (episodeId: string, catalog: string): Promise<any[]> => {
    try {
        const response = await fetch(`${rdUrl}api/v1/labelling-history/episode/${catalog}/${episodeId}`, {
            headers: getAuthHeaders()
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        return data.data ?? []
    } catch (error) {
        console.error(error)

        return [];
    }
}

export const fetchStripHistory = async (stripId: string, catalog: string): Promise<any[]> => {
    try {
        const response = await fetch(`${rdUrl}api/v1/labelling-history/strip/${catalog}/${stripId}`, {
            headers: getAuthHeaders()
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        
        return data.data ?? []
    } catch (error) {
        console.error(error)

        return [];
    }
}

export const fetchLabellingStats = async (catalog: string): Promise<LabellingStats[]> => {
    try {
        const response = await fetch(`${rdUrl}api/v1/labelling-stats?catalog=${encodeURIComponent(catalog)}`, {
            headers: getAuthHeaders()
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        return data.data as LabellingStats[] ?? []
    } catch (error) {
        console.error(error)

        return [];
    }
}