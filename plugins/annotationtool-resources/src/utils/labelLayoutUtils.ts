/**
 * Utility functions for laying out overlapping labels on multiple tracks/lines
 */

import { Label as LabelType } from "@hcengineering/annotationtool";
import { getMetadata } from '@hcengineering/platform'
import contact, { getFirstName, getLastName } from "@hcengineering/contact";

export interface LabelWithPosition {
  label: string;
  start: number;
  end: number;
  timestamp: number;
  color?: number;
}

export interface LabelWithTrack extends LabelWithPosition {
  track: number; // Which line/track this label should be rendered on (0, 1, 2, etc.)
}

/**
 * Checks if two time intervals overlap
 */
function doIntervalsOverlap(start1: number, end1: number, start2: number, end2: number): boolean {
  return start1 < end2 && start2 < end1;
}

// Process labels with start/end times for overlap detection and track assignment
export const getLabelsWithPositions = (
  currentStripAllLabels: Record<string, {label: string, timestamp: number, start?: number, end?: number}[]> = {},
  nextStripAllLabels: Record<string, {label: string, timestamp: number, start?: number, end?: number}[]> = {},
  originalStripEndTime: number,
  moreSecondsLoaded: boolean,
  nextStripId: string,
  labels: LabelType[]
) => {
  const currentStripLabels: Record<string, LabelWithPosition[]> = {}

  Object.entries(currentStripAllLabels).forEach(([key, value]) => {
      currentStripLabels[key] = value.filter(label => label.start != null && label.end != null)
      ?.map(label => ({
          label: labels.find(l => l.value === label.label)?.label,
          start: label.start!,
          end: label.end!,
          timestamp: label.timestamp,
          color: labels.find(l => l.value === label.label)?.color
      } as LabelWithPosition)) || []
  })

  // If in "more seconds loaded" mode, also include continuation labels from next strip
  if (moreSecondsLoaded && nextStripId && Object.keys(nextStripAllLabels).length > 0) {
      
      const nextStripLabels: Record<string, LabelWithPosition[]> = {}

      Object.entries(nextStripAllLabels).forEach(([key, value]) => {
          nextStripLabels[key as string] = value?.filter(label => label.start != null && label.end != null && label.start === originalStripEndTime)
          ?.map(label => ({
              label: labels.find(l => l.value === label.label)?.label || label.label,
              start: label.start!,
              end: label.end!,
              timestamp: label.timestamp,
              color: labels.find(l => l.value === label.label)?.color
          } as LabelWithPosition)) || []
      })

      console.log('nextStripLabels', nextStripLabels)

      Object.entries(currentStripLabels).forEach(([key, value]) => {
          currentStripLabels[key as string] = [...value, ...nextStripLabels[key as string]]
      })
  }

  return currentStripLabels
}

/**
 * Assigns labels to tracks (lines) to avoid visual overlaps.
 * Labels are processed in chronological order (by timestamp), and each label
 * is assigned to the lowest available track where it doesn't overlap with existing labels.
 * 
 * Algorithm:
 * 1. Sort labels by timestamp (earlier labels get priority for lower tracks)
 * 2. For each label, find the lowest track number where it doesn't overlap
 * 3. Assign the label to that track
 * 
 * Example:
 * - Label A: start=1, end=3, timestamp=100 → track=0
 * - Label B: start=2, end=5, timestamp=200 → track=1 (overlaps with A)
 * - Label C: start=7, end=10, timestamp=300 → track=0 (doesn't overlap with A)
 */
export function assignLabelsToTracks(labels: Record<string, LabelWithPosition[]>): Record<string, LabelWithTrack[]> {
  if (Object.keys(labels).length === 0) return {};

  const labelsWithTracks: Record<string, LabelWithTrack[]> = {};

  Object.entries(labels).forEach(([key, value]) => {

    // Sort labels by timestamp (earlier labels get priority)
    const sortedLabels = value.sort((a, b) => a.timestamp - b.timestamp);
    
    // Track assignments: track[i] contains array of labels assigned to track i
    const tracks: LabelWithTrack[][] = [];

    for (const label of sortedLabels) {
      let assignedTrack = -1;
      
      // Find the lowest track where this label doesn't overlap with existing labels
      for (let trackIndex = 0; trackIndex < tracks.length; trackIndex++) {
        const labelsInTrack = tracks[trackIndex];
        
        // Check if this label overlaps with any label in this track
        const hasOverlap = labelsInTrack.some(existingLabel => 
          doIntervalsOverlap(label.start, label.end, existingLabel.start, existingLabel.end)
        );
        
        if (!hasOverlap) {
          assignedTrack = trackIndex;
          break;
        }
      }
      
      // If no existing track works, create a new track
      if (assignedTrack === -1) {
        assignedTrack = tracks.length;
        tracks.push([]);
      }
      
      // Assign the label to the track
      const labelWithTrack: LabelWithTrack = {
        ...label,
        track: assignedTrack
      };
      
      tracks[assignedTrack].push(labelWithTrack);
      
      if(!labelsWithTracks[key as string]) {
        labelsWithTracks[key as string] = [];
      }

      labelsWithTracks[key as string].push(labelWithTrack);
    }
  })

  return labelsWithTracks;
}

/**
 * Calculates the total height needed to display all label tracks
 */
export function calculateLabelAreaHeight(
  labels: Record<string, LabelWithTrack[]>, 
  labelHeight: number = 24, 
  trackSpacing: number = 4,
  isMultiUserMode: boolean = false,
  separatorHeight: number = 2,
  separatorPadding: number = 6
): number {
  if (Object.keys(labels).length === 0) return 0;
  
  if (!isMultiUserMode) {
    // Single user mode: find the maximum track number across all labels
    const maxTrackNumber = Object.values(labels).reduce((maxTrack, userLabels) => {
      const userMaxTrack = userLabels.reduce((max, label) => Math.max(max, label.track), -1);
      return Math.max(maxTrack, userMaxTrack) - 1;
    }, -1);
    
    if (maxTrackNumber === -1) return 0;
    
    const numTracks = maxTrackNumber + 1;
    return numTracks * labelHeight + (numTracks - 1) * trackSpacing + 8;
  } else {
    // Multi-user mode: calculate height for each user's section separately
    const userSections = Object.entries(labels).map(([_, userLabels], index) => {
      if (userLabels.length === 0) return 0;
      
      const userMaxTrack = userLabels.reduce((max, label) => Math.max(max, label.track), -1);
      if (userMaxTrack === -1) return 0;
      
      const numTracks = userMaxTrack + (index < Object.keys(labels).length - 1 ? 1 : 0);
      return numTracks * labelHeight + (numTracks - 1) * trackSpacing;
    });
    
    const totalUserHeight = userSections.reduce((sum, height) => sum + height, 0);
    const numSeparators = Math.max(0, userSections.filter(h => h > 0).length - 1);
    
    // Each separator includes: padding above + separator line + padding below
    const separatorTotalHeight = separatorHeight + (separatorPadding * 2);
    
    return totalUserHeight + (numSeparators * separatorTotalHeight) + 8; // +8 for minimal padding
  }
}

/**
 * Calculates the Y offset for a specific user's section in multi-user mode
 */
export function getUserSectionYOffset(
  userId: string,
  labels: Record<string, LabelWithTrack[]>,
  labelHeight: number = 24,
  trackSpacing: number = 4,
  separatorHeight: number = 2,
  separatorPadding: number = 6
): number {
  let offset = 0;
  
  for (const [currentUserId, userLabels] of Object.entries(labels)) {
    if (currentUserId === userId) break;
    
    if (userLabels.length > 0) {
      const userMaxTrack = userLabels.reduce((max, label) => Math.max(max, label.track), -1);
      if (userMaxTrack >= 0) {
        const numTracks = userMaxTrack + 1;
        const userSectionHeight = numTracks * labelHeight + (numTracks - 1) * trackSpacing;
        
        // Each separator includes: padding above + separator line + padding below
        const separatorTotalHeight = separatorHeight + (separatorPadding * 2);
        
        offset += userSectionHeight + separatorTotalHeight; // Add separator with padding after each user section
      }
    }
  }
  
  return offset;
}

/**
 * Calculates the Y position for a label based on its track and user section
 */
export function getLabelYPosition(
  track: number, 
  baseY: number, 
  labelHeight: number = 24, 
  trackSpacing: number = 4,
  userSectionOffset: number = 0
): number {
  return baseY + userSectionOffset + track * (labelHeight + trackSpacing);
}

/**
 * Given a hex color, returns either '#fff' (white) or '#000' (black) for optimal text contrast.
 * @param hexColor - The background color in hex format (e.g. '#aabbcc' or '#abc')
 * @returns '#fff' for dark backgrounds, '#000' for light backgrounds
 */
export function getContrastingTextColor(hexColor: string): '#fff' | '#000' {
  // Remove leading '#' if present
  let hex = hexColor.replace(/^#/, '');

  // Expand shorthand hex (e.g. 'abc' -> 'aabbcc')
  if (hex.length === 3) {
    hex = hex.split('').map((c) => c + c).join('');
  }

  // Parse r, g, b
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Calculate luminance (per ITU-R BT.709)
  const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;

  // Threshold: 128 is a common midpoint, but 150 is often more visually balanced
  return luminance < 150 ? '#fff' : '#000';
}

/**
 * Generates an array of numbers from 1 to 21 and shuffles it randomly.
 * @returns {number[]} The shuffled array.
 */
export function generateShuffledArray1to21(): number[] {
  const arr = Array.from({ length: 21 }, (_, i) => i + 1);
  // Fisher-Yates shuffle
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr;
}

