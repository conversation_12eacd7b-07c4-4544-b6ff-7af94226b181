import annotationTool from './plugin'
import { fetchClinics, fetchLabelledBy, fetchPatients } from './api'
import { Episode } from './types'
import { getIntlString } from './utils'

export const annotatorAccessLevelItems = [
    { id: 'level1', label: 'Basic annotator' },
    { id: 'level2', label: 'Advanced annotator' }
]

// Clear, readable access level constants
export const ACCESS_LEVELS = {
    BASIC: annotatorAccessLevelItems[0].id, // 'level1'
    ADVANCED: annotatorAccessLevelItems[1].id // 'level2'
}

// Type for access level values
export type AccessLevel = typeof ACCESS_LEVELS[keyof typeof ACCESS_LEVELS]

export const filterItems = [
    {
        name: 'clinic_id',
        label: annotationTool.string.ClinicFilter,
        icon: annotationTool.icon.Hospital,
        placeholder: annotationTool.string.SelectClinic,
        type: 'dropdown' as 'dropdown',
        fetchOptions: fetchClinics,
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access clinic filter
    },
    {
        name: 'patient_id',
        label: annotationTool.string.PatientFilter,
        icon: annotationTool.icon.Patient,
        placeholder: annotationTool.string.SelectPatient,
        type: 'dropdown' as 'dropdown',
        dependencies: ['clinic_id'],
        fetchOptions: fetchPatients,
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access patient filter
    },
    {
        name: 'manufacturer',
        label: annotationTool.string.Manufacturer,
        icon: annotationTool.icon.Manufacturer,
        placeholder: annotationTool.string.SelectManufacturer,
        type: 'dropdown' as 'dropdown',
        options: [
            { id: 'none', label: annotationTool.string.None },
            { id: 'STJ', label: 'STJ' },
            { id: 'BSX', label: 'BSX' },
            { id: 'MDT', label: 'MDT' },
            { id: 'BIO', label: 'BIO' }
        ],
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access manufacturer filter
    },
    {
        name: 'labelling_status',
        label: annotationTool.string.LabelFilter,
        icon: annotationTool.icon.Label,
        placeholder: annotationTool.string.SelectLabel,
        type: 'dropdown' as 'dropdown',
        options: [
            { id: 'none', label: annotationTool.string.None },
            { id: 'unlabeledEpisodes', label: 'Unlabeled Episodes' }, 
            { id: 'labeledEpisodes', label: 'Labeled Episodes' },
            { id: 'unsureEpisodes', label: 'Unsure Episodes' }
        ],
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access labelling status filter
    },
    {
        name: 'conflict_status',
        label: annotationTool.string.EpisodeFilter,
        icon: annotationTool.icon.Label,
        placeholder: annotationTool.string.SelectConflictStatus,
        type: 'dropdown' as 'dropdown',
        options: [
            { id: 'none', label: annotationTool.string.None },
            { id: 'conflicting', label: 'Episodes with conflicts' }, 
            { id: 'resolved', label: 'Episodes with resolved conflicts' }
        ],
        requiredAccessLevel: [ACCESS_LEVELS.ADVANCED] // Basic annotators can access conflict status filter
    },
    {
        name: 'labelled_by',
        label: annotationTool.string.LabelledBy,
        icon: annotationTool.icon.Doctor,
        placeholder: annotationTool.string.SelectLabelledBy,
        type: 'dropdown' as 'dropdown',
        fetchOptions: fetchLabelledBy,
        requiredAccessLevel: [ACCESS_LEVELS.ADVANCED] // Basic annotators can access who labelled filter
    },        
    {
        name: 'not_labelled_by',
        label: annotationTool.string.NotLabelledBy,
        icon: annotationTool.icon.Doctor,
        placeholder: annotationTool.string.SelectNotLabelledBy,
        type: 'dropdown' as 'dropdown',
        fetchOptions: fetchLabelledBy,
        requiredAccessLevel: [ACCESS_LEVELS.ADVANCED] // Basic annotators can access who labelled filter
    }, 
    {
        name: 'episode_id',
        label: annotationTool.string.EpisodeIDFilter,
        icon: annotationTool.icon.Key,
        placeholder: annotationTool.string.SelectEpisodeID,
        type: 'input' as 'input',
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access who labelled filter
    },  
    {
        name: 'date_from',
        label: annotationTool.string.FromDate,
        icon: annotationTool.icon.DateFrom,
        placeholder: annotationTool.string.SelectFromDate,
        type: 'date' as 'date',
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access who labelled filter
    },
    {
        name: 'date_to',
        label: annotationTool.string.ToDate,
        icon: annotationTool.icon.DateTo,
        placeholder: annotationTool.string.SelectToDate,
        type: 'date' as 'date',
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access who labelled filter
    },
    {
        name: 'sorting',
        label: annotationTool.string.Sorting,
        icon: annotationTool.icon.Sorting,
        placeholder: annotationTool.string.SelectSorting,
        type: 'dropdown' as 'dropdown',
        options: [
            { id: 'none', label: annotationTool.string.None },
            { id: 'reporttime-asc', label: 'Time - Earliest first' }, 
            { id: 'reporttime-desc', label: 'Time - Latest first' },
        ],
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access who labelled filter
    }
]

// Function to filter items based on annotator access level
export function getFilterItemsByAccessLevel(accessLevel: string = ACCESS_LEVELS.BASIC) {
    return filterItems.filter(item => {
        // If no requiredAccessLevel is specified, allow access
        if (!item.requiredAccessLevel) return true
        
        // Check if user has any of the required access levels
        return item.requiredAccessLevel.includes(accessLevel)
    })
}

export const dashboardFilterItems = [
    {
        name: 'clinic_id',
        label: annotationTool.string.ClinicFilter,
        icon: annotationTool.icon.Hospital,
        placeholder: annotationTool.string.SelectClinic,
        type: 'dropdown' as 'dropdown',
        fetchOptions: fetchClinics,
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access clinic filter
    },
    {
        name: 'manufacturer',
        label: annotationTool.string.Manufacturer,
        icon: annotationTool.icon.Manufacturer,
        placeholder: annotationTool.string.SelectManufacturer,
        type: 'dropdown' as 'dropdown',
        options: [
            { id: 'none', label: getIntlString(annotationTool.string.None) },
            { id: 'STJ', label: 'STJ' },
            { id: 'BSX', label: 'BSX' },
            { id: 'MDT', label: 'MDT' },
            { id: 'BIO', label: 'BIO' }
        ],
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access manufacturer filter
    },
    {
        name: 'episodeLengthFrom',
        label: annotationTool.string.EpisodeLengthFrom,
        icon: annotationTool.icon.DateFrom,
        placeholder: annotationTool.string.SelectEpisodeLengthFrom,
        type: 'dropdown' as 'dropdown',
        options: [{ id: 'none', label: getIntlString(annotationTool.string.None) }, { id: '0', label: getIntlString(annotationTool.string.LessThan1Minute) }, { id: '1', label: getIntlString(annotationTool.string["1Minute"]) }, { id: '3', label: getIntlString(annotationTool.string["3Minutes"]) }, { id: '5', label: getIntlString(annotationTool.string["5Minutes"]) }],
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access episode length filters
    },
    {
        name: 'episodeLengthTo',
        label: annotationTool.string.EpisodeLengthTo,
        icon: annotationTool.icon.DateTo,
        placeholder: annotationTool.string.SelectEpisodeLengthTo,
        type: 'dropdown' as 'dropdown',
        options: [{ id: 'none', label: getIntlString(annotationTool.string.None) }, { id: '0', label: getIntlString(annotationTool.string.LessThan1Minute) }, { id: '1', label: getIntlString(annotationTool.string["1Minute"]) }, { id: '3', label: getIntlString(annotationTool.string["3Minutes"]) }, { id: '5', label: getIntlString(annotationTool.string["5Minutes"]) }],
        requiredAccessLevel: [ACCESS_LEVELS.BASIC, ACCESS_LEVELS.ADVANCED] // Basic annotators can access episode length filters
    },
]

// Function to filter dashboard items based on annotator access level
export function getDashboardFilterItemsByAccessLevel(accessLevel: string = ACCESS_LEVELS.BASIC) {
    return dashboardFilterItems.filter(item => {
        // If no requiredAccessLevel is specified, allow access
        if (!item.requiredAccessLevel) return true
        
        // Check if user has any of the required access levels
        return item.requiredAccessLevel.includes(accessLevel)
    })
}

export const tableSettingsItems = [
    { id: 'totalPatients', label: annotationTool.string.TableSettingsTotalPatients },
    { id: 'totalEpisodes', label: annotationTool.string.TableSettingsTotalEpisodes },
    { id: 'totalEcgStrips', label: annotationTool.string.TableSettingsTotalEcgStrips },

]

export const tableSettingsHierarchicalItems = [
    { id: 'hierarichical-clinic', label: annotationTool.string.TableSettingsClinic },
    { id: 'hierarichical-manufacturer', label: annotationTool.string.TableSettingsManufacturer },
    { id: 'hierarichical-deviceModel', label: annotationTool.string.TableSettingsDeviceModel },
    { id: 'hierarichical-deviceLabel', label: annotationTool.string.TableSettingsDeviceLabel },
    { id: 'hierarichical-doctorLabel', label: annotationTool.string.TableSettingsDoctorLabel }
]

export const tableSettingsLeftColumnDistributorItems = [
    { id: 'leftColumnDistributor-episodeLength', label: annotationTool.string.LeftColumnDistributorEpisodeLength },
    { id: 'leftColumnDistributor-deviceLabel', label: annotationTool.string.LeftColumnDistributorDeviceLabel },
    { id: 'leftColumnDistributor-doctorLabel', label: annotationTool.string.LeftColumnDistributorDoctorLabel }
]