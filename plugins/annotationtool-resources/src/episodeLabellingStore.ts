import { writable } from 'svelte/store'
import { Episode, EpisodeLabellingConfig, StripLabellingConfig, LabelToSubmit, EpisodeLabellingStoreConfig } from './types'

export const createEpisodeLabellingStore = (episode: Episode, currentUserId: string, datasetID: string, addToSubmitQueue: (labelToSubmit: LabelToSubmit) => void, isConflictResolution: boolean = false): EpisodeLabellingStoreConfig => {    
    const episodeLabellingStore = writable<EpisodeLabellingConfig>({
        episodeId: '',
        episodeLabels: [],
        episodeApprovedLabels: [],
        episodeDisapprovedLabels: [],
        episodeTriage: '',
        isInterestingEpisode: false,
        isDoneEpisode: false,
        isConflictResolvedEpisode: false,
        strips: {}
    })
    
    // Initialize the episode labelling store with the episode data
    episodeLabellingStore.update(config => {
        config.episodeId = episode.episode_id
        if(isConflictResolution) {
            // Merge all labels from all users, keep them distinct
            config.episodeLabels = Object.values(episode.labels ?? {})
                .flat()
                .filter((label: {label: string, timestamp: number}) => label.label !== 'isInterestingFlag' && label.label !== 'isDoneFlag' && label.label !== 'isConflictResolvedFlag') as any ?? []
            config.episodeApprovedLabels = episode.approved_labels ?? []
            config.episodeDisapprovedLabels = episode.disapproved_labels ?? []
            config.isConflictResolvedEpisode = episode.approved_labels?.some((label: {label: string, timestamp: number}) => label.label === 'isConflictResolvedFlag') ?? false
        }
        else {
            config.episodeLabels = episode.labels?.[currentUserId]?.filter((label: {label: string, timestamp: number}) => label.label !== 'isInterestingFlag') as any ?? []
            config.episodeApprovedLabels = []
            config.episodeDisapprovedLabels = []
        }
        config.isInterestingEpisode = episode.labels?.[currentUserId]?.some((label: {label: string, timestamp: number}) => label.label === 'isInterestingFlag') ?? false
        config.isDoneEpisode = episode.labels?.[currentUserId]?.some((label: {label: string, timestamp: number}) => label.label === 'isDoneFlag') ?? false

        episode.strips.forEach((strip) => {
            const episodeStrips = config.strips

            if(!episodeStrips[strip.id]) {
                episodeStrips[strip.id] = { 
                    labels: isConflictResolution ? Object.values(strip.labels ?? {})    
                        .flat()
                        .filter((label: {label: string, timestamp: number}) => label.label !== 'isInterestingFlag' && label.label !== 'isDoneFlag' && label.label !== 'isConflictResolvedFlag') as any ?? []
                    : strip.labels?.[currentUserId]?.filter((label: {label: string, timestamp: number}) => label.label !== 'isInterestingFlag') as any ?? [],
                    allLabels: strip.labels ?? [],
                    approvedLabels: isConflictResolution ? strip.approved_labels ?? [] : [],
                    disapprovedLabels: isConflictResolution ? strip.disapproved_labels ?? [] : [],
                    triage: strip.triage ?? null,
                    isInterestingStrip: strip.labels?.[currentUserId]?.some((label: {label: string, timestamp: number}) => label.label === 'isInterestingFlag') ?? false,
                    startTimestamp: strip.timestamps[0] ?? 0,
                    endTimestamp: strip.timestamps[strip.timestamps.length - 1] ?? 0
                }
            }
        })

        return {
            ...config,
        }
    })

    const episodeLabellingStoreActions = {    
        addRemoveLabelStrip: (stripId: string, label: {label: string, timestamp: number, start: number, end: number}) => {
            episodeLabellingStore.update(config => {
                const episodeStrips = config.strips
    
                const strip = episodeStrips[stripId] as StripLabellingConfig
                let operationType: 'addition' | 'removal' | 'approval' | 'disapproval';
                if(strip) {
                    if(strip.labels.some(l => l.label === label.label && l.start === label.start && l.end === label.end)) {
                        const indexToRemove = strip.labels.findIndex(l => l.label === label.label && l.start === label.start && l.end === label.end)
                        strip.labels.splice(indexToRemove, 1)
                        operationType = isConflictResolution ? 'disapproval' : 'removal'
                        
                        // Also remove from allLabels to maintain reactivity
                        if (strip.allLabels[currentUserId]) {
                            const allLabelsIndexToRemove = strip.allLabels[currentUserId].findIndex(l => l.label === label.label && l.start === label.start && l.end === label.end)
                            if (allLabelsIndexToRemove !== -1) {
                                strip.allLabels[currentUserId].splice(allLabelsIndexToRemove, 1)
                            }
                        }
                    }
                    else {
                        strip.labels.push(label)
                        operationType = isConflictResolution ? 'approval' : 'addition'
                        
                        // Also add to allLabels to maintain reactivity
                        if (!strip.allLabels[currentUserId]) {
                            strip.allLabels[currentUserId] = []
                        }
                        strip.allLabels[currentUserId].push(label)
                    }
    
                    episodeStrips[stripId] = { ...strip }

                    addToSubmitQueue({
                        datasetID,
                        episodeID: config.episodeId,
                        stripID: stripId,
                        label: label.label,
                        operationType,
                        labelTimestamp: Date.now(),
                        start: label.start,
                        end: label.end,
                        submittedBy: currentUserId
                    })
                }
    
                return config;
            })
        },
    
        addRemoveTriageStrip: (stripId: string, triage: string) => {
            episodeLabellingStore.update(config => {
                const episodeStrips = config.strips
                const strip = episodeStrips[stripId] as StripLabellingConfig;
                if(strip) {
                    strip.triage = strip.triage === triage ? null : triage
    
                    episodeStrips[stripId] = { ...strip }
                }
    
                return config;
            })
        },
    
        addRemoveInterestingStrip: (stripId: string, isInteresting: boolean) => {
            episodeLabellingStore.update(config => {
                const episodeStrips = config.strips
                const strip = episodeStrips[stripId] as StripLabellingConfig;
                if(strip) {
                    strip.isInterestingStrip = isInteresting
    
                    addToSubmitQueue({
                        datasetID,
                        episodeID: config.episodeId,
                        stripID: stripId,
                        label: 'isInterestingFlag',
                        operationType: isInteresting ? 'addition' : 'removal',
                        labelTimestamp: Date.now(),
                        submittedBy: currentUserId
                    })

                    episodeStrips[stripId] = { ...strip }
                }

                return config;
            })
        },
    
        addRemoveEpisodeLabel: (label: {label: string, timestamp: number}) => {
            episodeLabellingStore.update(config => {
                let mode;
                if(config.episodeLabels.some(l => l.label === label.label)) {
                    config.episodeLabels = config.episodeLabels.filter(l => l.label !== label.label)
                    mode = 'remove'
                }
                else {
                    config.episodeLabels.push(label)
                    mode = 'add'
                }

                addToSubmitQueue({  
                    datasetID,
                    episodeID: config.episodeId,
                    label: label.label,
                    operationType: mode === 'add' ? 
                        (isConflictResolution ? 'approval' : 'addition') : 
                        (isConflictResolution ? 'disapproval' : 'removal'),
                    labelTimestamp: Date.now(),
                    submittedBy: currentUserId
                })
    
                const strips = config.strips
    
                Object.entries(strips).forEach(([stripId, strip]) => {
                    if(strip) {
                        if(mode === 'add' && !strip.labels.some(l => l.label === label.label && l.start === strip.startTimestamp && l.end === strip.endTimestamp)) {
                            const newLabel = {
                                ...label,
                                start: strip.startTimestamp,
                                end: strip.endTimestamp
                            }
                            strip.labels.push(newLabel)
                            
                            // Also add to allLabels to maintain reactivity
                            if (!strip.allLabels[currentUserId]) {
                                strip.allLabels[currentUserId] = []
                            }
                            strip.allLabels[currentUserId].push(newLabel)
                            
                            addToSubmitQueue({
                                datasetID,
                                episodeID: config.episodeId,
                                stripID: stripId,
                                label: label.label,
                                start: strip.startTimestamp,
                                end: strip.endTimestamp,
                                operationType: isConflictResolution ? 'approval' : 'addition',
                                labelTimestamp: Date.now(),
                                submittedBy: currentUserId
                            })
                        }
                        else if(mode === 'remove' && strip.labels.some(l => l.label === label.label)) {
                            strip.labels.forEach(l => {
                                if(l.label === label.label) {
                                    addToSubmitQueue({
                                        datasetID,
                                        episodeID: config.episodeId,
                                        stripID: stripId,
                                        label: label.label,
                                        start: l.start,
                                        end: l.end,
                                        operationType: isConflictResolution ? 'disapproval' : 'removal',
                                        labelTimestamp: Date.now(),
                                        submittedBy: currentUserId
                                    })
                                }
                            })

                            strip.labels = strip.labels.filter(l => l.label !== label.label)
                            
                            // Also remove from allLabels to maintain reactivity
                            if (strip.allLabels[currentUserId]) {
                                strip.allLabels[currentUserId] = strip.allLabels[currentUserId].filter(l => l.label !== label.label)
                            }
                        }
                    }
                })

                return {...config};
            })
        },
    
        addRemoveEpisodeTriage: (triage: string) => {
            episodeLabellingStore.update(config => {
                config.episodeTriage = config.episodeTriage === triage ? null : triage
    
                const strips = config.strips
    
                Object.values(strips).forEach((strip) => {
                    if(strip) {
                        strip.triage = config.episodeTriage
                    }
                })
    
                return {...config};
            })
        },
    
        addRemoveEpisodeInteresting: (isInteresting: boolean) => {
            episodeLabellingStore.update(config => {
                config.isInterestingEpisode = isInteresting
    
                addToSubmitQueue({
                    datasetID,
                    episodeID: config.episodeId,
                    label: 'isInterestingFlag',
                    operationType: isInteresting ? 'addition' : 'removal',
                    labelTimestamp: Date.now(),
                    submittedBy: currentUserId
                })

                return {...config};
            })
        },
    
        addRemoveEpisodeDone: (isDone: boolean) => {
            episodeLabellingStore.update(config => {
                config.isDoneEpisode = isDone

                addToSubmitQueue({
                    datasetID,
                    episodeID: config.episodeId,
                    label: 'isDoneFlag',
                    operationType: isDone ? 'addition' : 'removal',
                    labelTimestamp: Date.now(),
                    submittedBy: currentUserId
                })

                return {...config};
            })
        },

        approveEpisodeLabel: (label: {label: string, timestamp: number}) => {
            episodeLabellingStore.update(config => {
                config.episodeApprovedLabels.push(label)
                config.episodeDisapprovedLabels = config.episodeDisapprovedLabels.filter(l => l.label !== label.label)

                addToSubmitQueue({  
                    datasetID,
                    episodeID: config.episodeId,
                    label: label.label,
                    operationType: 'approval',
                    labelTimestamp: Date.now(),
                    submittedBy: currentUserId
                })
    
                const strips = config.strips
    
                Object.entries(strips).forEach(([stripId, strip]) => {
                    if(strip) {
                        strip.approvedLabels.push(label)
                        strip.disapprovedLabels = strip.disapprovedLabels.filter(l => l.label !== label.label)

                        addToSubmitQueue({
                            datasetID,
                            episodeID: config.episodeId,
                            stripID: stripId,
                            label: label.label,
                            operationType: 'approval',
                            labelTimestamp: Date.now(),
                            submittedBy: currentUserId
                        })
                    }
                })

                return {...config};
            })
        },

        disapproveEpisodeLabel: (label: {label: string, timestamp: number}) => {
            episodeLabellingStore.update(config => {
                config.episodeDisapprovedLabels.push(label)
                config.episodeApprovedLabels = config.episodeApprovedLabels.filter(l => l.label !== label.label)

                addToSubmitQueue({  
                    datasetID,
                    episodeID: config.episodeId,
                    label: label.label,
                    operationType: 'disapproval',
                    labelTimestamp: Date.now(),
                    submittedBy: currentUserId
                })

                const strips = config.strips
    
                Object.entries(strips).forEach(([stripId, strip]) => {
                    if(strip) {
                        strip.disapprovedLabels.push(label)
                        strip.approvedLabels = strip.approvedLabels.filter(l => l.label !== label.label)

                        addToSubmitQueue({
                            datasetID,
                            episodeID: config.episodeId,
                            stripID: stripId,
                            label: label.label,
                            operationType: 'disapproval',
                            labelTimestamp: Date.now(),
                            submittedBy: currentUserId
                        })
                    }
                })  

                return {...config};
            })
        },  

        approveStripLabel: (stripId: string, label: {label: string, timestamp: number}) => {
            episodeLabellingStore.update(config => {
                const strip = config.strips[stripId] as StripLabellingConfig
                if(strip) {
                    strip.approvedLabels.push(label)
                    strip.disapprovedLabels = strip.disapprovedLabels.filter(l => l.label !== label.label)

                    addToSubmitQueue({  
                        datasetID,
                        episodeID: config.episodeId,
                        stripID: stripId,
                        label: label.label,
                        operationType: 'approval',
                        labelTimestamp: Date.now(),
                        submittedBy: currentUserId
                    })
                }

                return {...config};
            })
        },

        disapproveStripLabel: (stripId: string, label: {label: string, timestamp: number}) => {
            episodeLabellingStore.update(config => {
                const strip = config.strips[stripId] as StripLabellingConfig
                if(strip) {
                    strip.disapprovedLabels.push(label)
                    strip.approvedLabels = strip.approvedLabels.filter(l => l.label !== label.label)

                    addToSubmitQueue({  
                        datasetID,
                        episodeID: config.episodeId,
                        stripID: stripId,
                        label: label.label,
                        operationType: 'disapproval',
                        labelTimestamp: Date.now(),
                        submittedBy: currentUserId
                    })
                }

                return {...config};
            })
        },
    
        addRemoveEpisodeConflictResolved: (isConflictResolved: boolean) => {
            episodeLabellingStore.update(config => {
                config.isConflictResolvedEpisode = isConflictResolved

                addToSubmitQueue({
                    datasetID,
                    episodeID: config.episodeId,
                    label: 'isConflictResolvedFlag',
                    operationType: isConflictResolved ? 'approval' : 'disapproval',
                    labelTimestamp: Date.now(),
                    submittedBy: currentUserId
                })

                return {...config};
            })
        },
    
        resetEpisodeLabelling: () => {
            episodeLabellingStore.update(config => {

                if (!isConflictResolution) {
                    config.episodeLabels.forEach(label => {
                        addToSubmitQueue({
                            datasetID,
                            episodeID: config.episodeId,
                            label: label.label,
                            operationType: 'removal',
                            labelTimestamp: Date.now(),
                            submittedBy: currentUserId
                        })
                    })
    
                    Object.entries(config.strips).forEach(([stripId, strip]) => {
                        strip.labels.forEach(label => {     
                            addToSubmitQueue({
                                datasetID,
                                episodeID: config.episodeId,
                                stripID: stripId,
                                label: label.label,
                                start: label.start,
                                end: label.end,
                                operationType: 'removal',
                                labelTimestamp: Date.now(),
                                submittedBy: currentUserId
                            })
                        })
                        config.strips[stripId].labels = []
                        // Also clear allLabels for current user to maintain reactivity
                        if (config.strips[stripId].allLabels[currentUserId]) {
                            config.strips[stripId].allLabels[currentUserId] = []
                        }
                    })

                    config.episodeLabels = []
                } else {
                    config.episodeApprovedLabels.forEach(label => {
                        addToSubmitQueue({
                            datasetID,
                            episodeID: config.episodeId,
                            label: label.label,
                            operationType: 'disapproval',
                            labelTimestamp: Date.now(),
                            submittedBy: currentUserId
                        })
                    })
                    config.episodeDisapprovedLabels.forEach(label => {
                        addToSubmitQueue({
                            datasetID,
                            episodeID: config.episodeId,
                            label: label.label,
                            operationType: 'approval',
                            labelTimestamp: Date.now(),
                            submittedBy: currentUserId
                        })
                    })
    
                    Object.entries(config.strips).forEach(([stripId, strip]) => {
                        strip.approvedLabels.forEach(label => {                                 
                            addToSubmitQueue({
                                datasetID,
                                episodeID: config.episodeId,
                                stripID: stripId,
                                label: label.label,
                                operationType: 'disapproval',
                                // start: label.start,
                                // end: label.end,
                                labelTimestamp: Date.now(),
                                submittedBy: currentUserId
                            })
                        })
                        config.strips[stripId].approvedLabels = []
                    })
                    Object.entries(config.strips).forEach(([stripId, strip]) => {
                        strip.disapprovedLabels.forEach(label => {                                 
                            addToSubmitQueue({
                                datasetID,
                                episodeID: config.episodeId,
                                stripID: stripId,
                                label: label.label,
                                // start: label.start,
                                // end: label.end,
                                operationType: 'disapproval',
                                labelTimestamp: Date.now(),
                                submittedBy: currentUserId
                            })
                        })
                        config.strips[stripId].approvedLabels = []
                    })

                    config.episodeApprovedLabels = []
                    config.episodeDisapprovedLabels = []

                    if (config.isConflictResolvedEpisode) {
                        addToSubmitQueue({
                            datasetID,
                            episodeID: config.episodeId,
                            label: 'isConflictResolvedFlag',
                            operationType: 'disapproval',
                            labelTimestamp: Date.now(),
                            submittedBy: currentUserId
                        })
                        config.isConflictResolvedEpisode = false
                    }
                }

                if (config.isDoneEpisode) {
                    addToSubmitQueue({
                        datasetID,
                        episodeID: config.episodeId,
                        label: 'isDoneFlag',
                        operationType: 'removal',
                        labelTimestamp: Date.now(),
                        submittedBy: currentUserId
                    })

                    config.isDoneEpisode = false
                }

                if (config.isInterestingEpisode) {
                    addToSubmitQueue({
                        datasetID,
                        episodeID: config.episodeId,
                        label: 'isInterestingFlag',
                        operationType: 'removal',
                        labelTimestamp: Date.now(),
                        submittedBy: currentUserId
                    })

                    Object.entries(config.strips).forEach(([stripId, strip]) => {
                        if (strip.isInterestingStrip) {
                            addToSubmitQueue({
                                datasetID,
                                episodeID: config.episodeId,
                                stripID: stripId,
                                label: 'isInterestingFlag',
                                operationType: 'removal',
                                labelTimestamp: Date.now(),
                                submittedBy: currentUserId
                            })
                        }
                        
                        config.strips[stripId].isInterestingStrip = false
                    })

                    config.isInterestingEpisode = false
                }

                return {...config}
            })
        }
    }

    return {
        episodeLabellingStore,
        episodeLabellingStoreActions
    }
}