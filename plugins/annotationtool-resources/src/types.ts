import { type Person } from "@hcengineering/contact"
import { Annotator } from "@hcengineering/annotationtool"
import { Writable } from "svelte/store"

export interface Annotator<PERSON>erson extends Person {
  annotatorAccessLevel: Annotator
  objectId?: string
  accountId?: string
}

export interface EpisodeLabellingConfig {
  episodeId: string,
  episodeLabels: {label: string, timestamp: number}[],
  episodeApprovedLabels: {label: string, timestamp: number}[],
  episodeDisapprovedLabels: {label: string, timestamp: number}[],
  episodeTriage: string | null,
  isInterestingEpisode: boolean,
  isDoneEpisode: boolean,
  isConflictResolvedEpisode?: boolean,
  strips: Record<string, StripLabellingConfig>
}

export interface StripLabellingConfig {
  labels: {label: string, timestamp: number, start?: number, end?: number}[],
  allLabels: Record<string, {label: string, timestamp: number, start?: number, end?: number}[]>
  approvedLabels: {label: string, timestamp: number}[],
  disapprovedLabels: {label: string, timestamp: number}[],
  triage: string | null,
  isInterestingStrip: boolean,
  startTimestamp: number,
  endTimestamp: number
}

export type SelectedFiltersConfig = Record<string, { value: string, label: string }>

export interface ClinicApiResponse {
  clinic_id: string,
  clinic_name: string
}

export interface PatientApiResponse {
  patient_id: string,
  patient_name: string
}

export interface LabelToSubmit {
  datasetID: string,
  episodeID: string,
  stripID?: string,
  label: string,
  labelTimestamp: number,
  start?: number,
  end?: number,
  operationType: 'addition' | 'removal' | 'approval' | 'disapproval'
  submittedBy: string
}

export interface EpisodeApiResponse {
  episodes: Episode[]
  total_count: number
  page: number
  limit: number
  total_pages: number
}

export interface Episode {
  id: string
  episode_id: string
  patient_id: string
  patient_name: string
  clinic_id: string
  clinic_name: string
  manufacturer: string
  device_label: string
  device_model: string
  report_time: string
  episode_length: string
  assessment: string
  labelling_status: string
  is_interesting: boolean
  interesting_description: string
  ecg_strip_ids: string[]
  created_at: string
  updated_at: string
  labelled_by?: string[]
  labels: Record<string, {label: string, timestamp: number}[]>
  approved_labels: {label: string, timestamp: number}[]
  disapproved_labels: {label: string, timestamp: number}[]
  strips: Strip[]
}

export interface Strip {
  id: string
  episode_id: string
  strip_number: number
  timestamps: number[]
  voltages: string[]
  image_urls: ImageUrl[]
  is_interesting: boolean
  interesting_description: string
  created_at: string
  updated_at: string
  labels: Record<string, {label: string, timestamp: number, start?: number, end?: number}[]>
  approved_labels: {label: string, timestamp: number}[]
  disapproved_labels: {label: string, timestamp: number}[]
  triage: string | null
}

export interface ImageUrl {
  url: string
  type: string
}

export interface LabellingHistory {
  labeller_id: string
  entries: {
    episode_id: string
    strip_id: string
    labeller_id: string
    labelled_at: number
    label: string
    operation: string
  }[]
  start_at: number
  end_at: number
}

export interface EpisodeLabellingStoreConfig {
  episodeLabellingStore: Writable<EpisodeLabellingConfig>
  episodeLabellingStoreActions: {
      addRemoveLabelStrip: (stripId: string, label: {label: string, timestamp: number, start: number, end: number}) => void
      addRemoveTriageStrip: (stripId: string, triage: string) => void
      addRemoveInterestingStrip: (stripId: string, isInteresting: boolean) => void
      addRemoveEpisodeLabel: (label: {label: string, timestamp: number}) => void
      addRemoveEpisodeTriage: (triage: string) => void
      addRemoveEpisodeInteresting: (isInteresting: boolean) => void
      addRemoveEpisodeDone: (isDone: boolean) => void
      approveEpisodeLabel: (label: {label: string, timestamp: number}) => void
      disapproveEpisodeLabel: (label: {label: string, timestamp: number}) => void
      approveStripLabel: (stripId: string, label: {label: string, timestamp: number}) => void
      disapproveStripLabel: (stripId: string, label: {label: string, timestamp: number}) => void
      addRemoveEpisodeConflictResolved: (isConflictResolved: boolean) => void
      resetEpisodeLabelling: () => void
  }
}

export interface LabellingStats {
  labeller_id: string
  [key: string]: number | string
}