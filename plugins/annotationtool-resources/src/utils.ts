import { get } from "svelte/store"
import annotationTool from "./plugin"
import { Episode } from "./types"
import { IntlString, translateCB } from "@hcengineering/platform"
import { themeStore } from "@hcengineering/theme"
import { personByIdStore, personAccountByIdStore } from "@hcengineering/contact-resources"

export const getEpisodeMetaItems = (episode: Episode) => {
    return [
        {
            icon: annotationTool.icon.Key,
            label: `${getEpisodeField(episode, 'episode_id')}`,
            tooltip: {label: annotationTool.string.TooltipEpisodeId}
        },
        {
            icon: annotationTool.icon.Patient,
            label: `${getEpisodeField(episode, 'patient_id')} ${getEpisodeField(episode, 'patient_age', '- ', ' y.o')}`,
            tooltip: {label: annotationTool.string.TooltipPatientDetails}
        },
        {
            icon: annotationTool.icon.Manufacturer,
            label: `${getEpisode<PERSON>ield(episode, 'manufacturer')} ${getEpisode<PERSON>ield(episode, 'device_type', '- ')} ${getEpisode<PERSON>ield(episode, 'device_name', ', ')}`,
            tooltip: {label: annotationTool.string.TooltipManufacturerDetails}
        },
        {
            icon: annotationTool.icon.Tag,
            label: `${getEpisodeField(episode, 'device_label')}`,
            tooltip: {label: annotationTool.string.TooltipDeviceLabel}
        },
        {
            icon: annotationTool.icon.Calendar,
            label: `${getEpisodeDate(episode, 'report_time')}`,
            tooltip: {label: annotationTool.string.TooltipReportTime}
        },
        {
            icon: annotationTool.icon.Timer,
            label: `${getEpisodeLength(episode, 'episode_length')}`,
            tooltip: {label: annotationTool.string.TooltipEpisodeLength}
        },
        {
            icon: annotationTool.icon.Ecg, 
            label: `${episode.strips?.length ?? 0} ${getIntlString(annotationTool.string.Strips as IntlString)}`,
            tooltip: {label: annotationTool.string.TooltipStrips}
        }
    ]
}

const getEpisodeField = (episode: Episode, field: string, prefixSeparator: string = '', suffix: string = '') => {
    const value = episode[field as keyof Episode] as string
    if(value) {
        return `${prefixSeparator}${value.length > 40 ? value.substring(0, 40) + '...' : value}${suffix}`
    }

    return ''
}

const getEpisodeDate = (episode: Episode, field: string) => {
    const raw = episode[field as keyof Episode] as string | undefined
    if (!raw) return ''

    const date = new Date(raw)
    if (isNaN(date.getTime())) return ''

    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        hourCycle: 'h12',
        timeZone: 'UTC',
        timeZoneName: 'short'
    })
}

export const getEpisodeLength = (episode: Episode, field: string) => {
    if(!episode[field as keyof Episode]) return getIntlString(annotationTool.string.NotSpecified as IntlString);

    try {
        const parts = (episode[field as keyof Episode] as string).split('.');
        const numParts = parts.length;
        const p1 = parts[0] ? parseInt(parts[0]) : 0;
        const p2 = parts[1] ? parseInt(parts[1]) : 0;
        const p3 = parts[2] ? parseInt(parts[2]) : 0;
        
        let formattedTime = '';
        if(numParts === 3) formattedTime+= `${p1}h `
        if(numParts === 3) formattedTime += `${p2}m `
        else formattedTime += `${p1}m `
        if(numParts === 3) formattedTime += `${p3}s `
        else if(numParts === 2) formattedTime += `${p2}s `
        else formattedTime += `${p1}s `

        return formattedTime;
    } catch (error) {
        return getIntlString(annotationTool.string.NotSpecified as IntlString);
    }
}

export const getLabellerName = (labellerId: string) => {
    const person = get(personByIdStore).get(get(personAccountByIdStore).get(labellerId as any)?.person as any) as any
    return person?.name?.split(',').reverse().join(' ') ?? 'Unknown Annotator'
}

export const getIntlString = (label: IntlString): string => {
    let _value: string | undefined
    translateCB(label, {}, get(themeStore).language, (r) => {
        _value = r
    })

    return _value ?? ''
}