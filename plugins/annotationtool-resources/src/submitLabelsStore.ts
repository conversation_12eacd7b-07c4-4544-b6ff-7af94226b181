
import { writable } from "svelte/store"
import type { LabelToSubmit } from "./types"
import { submitLabels } from "./api"

export const createSubmitLabelsStore = () => {
    const submitLabelsStore = writable<LabelToSubmit[]>([])
    let isSubmitting = false

    // Helper to process the queue serially
    const processQueue = async () => {
        if (isSubmitting) return
        isSubmitting = true

        while (true) {
            let nextLabel: LabelToSubmit | undefined
            // Get the next label to submit
            submitLabelsStore.update(state => {
                nextLabel = state[0]
                return state
            })

            if (!nextLabel) break

            try {
                await submitLabels(nextLabel)
            } catch (e) {
                // Optionally handle error, e.g. retry or log
            }

            // Remove the label that was just submitted
            submitLabelsStore.update(state => state.filter(label => label !== nextLabel))
        }

        isSubmitting = false
    }

    const addToSubmitQueue = (labelToSubmit: LabelToSubmit) => {
        submitLabelsStore.update(state => {
            // Prevent duplicate submissions for the same label/strip/operation
            if (!state.some(l =>
                l.datasetID === labelToSubmit.datasetID &&
                l.episodeID === labelToSubmit.episodeID &&
                l.stripID === labelToSubmit.stripID &&
                l.label === labelToSubmit.label &&
                l.start === labelToSubmit.start &&
                l.end === labelToSubmit.end &&
                l.operationType === labelToSubmit.operationType
            )) {
                return [...state, labelToSubmit]
            }
            return state
        })
        // Always trigger the queue processor after adding
        processQueue()
    }

    return {
        isSubmitting,
        addToSubmitQueue,
    }
}