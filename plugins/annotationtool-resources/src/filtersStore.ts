import { writable } from 'svelte/store'
import { SelectedFiltersConfig } from './types'

export const selectedFiltersStore = writable<SelectedFiltersConfig>({
  limit: { value: '2', label: '' },
  page: { value: '1', label: '' },
  sorting: { value: 'reporttime-asc', label: 'Time - Latest first' },
  labelling_status: { value: 'unlabelledEpisodes', label: 'Unlabelled Episodes' }
})

// Insertion methods for the store
export const filtersStoreActions = {
  setFilter: (name: string, value: string, label: string, selectedApplication: 'annotate' | 'dashboard' | 'conflict', projectId: string) => {
    if(value === 'none') {
      selectedFiltersStore.update(filters => {
        const newFilters = { ...filters }
        newFilters.page = { value: '1', label: '' }
        delete newFilters[name]
        
        if(name === 'labelling_status') {
          delete newFilters["maxLabellingTime"]
        }

        localStorage.setItem(`annotationtool-filters-${selectedApplication}-${projectId}`, JSON.stringify(newFilters))


        return newFilters
      })
    }
    else {
      selectedFiltersStore.update(filters => {
        const newFilters = {
          ...filters,
          [name]: { value, label },
          ...(name !== 'page' ? { page: { value: '1', label: '' } } : {}),
          ...(name === 'labelling_status' ? { maxLabellingTime: { value: Date.now().toString(), label: '' } } : {})
        }

        localStorage.setItem(`annotationtool-filters-${selectedApplication}-${projectId}`, JSON.stringify(newFilters))

        return newFilters
      })
    }
  },

  removeFilter: (name: string, selectedApplication: 'annotate' | 'dashboard' | 'conflict', projectId: string) => {
    selectedFiltersStore.update(filters => {
      const newFilters = { ...filters }
      newFilters.page = { value: '1', label: '' }
      delete newFilters[name]

      if(name === 'labelling_status') {
        delete newFilters["maxLabellingTime"]
      }

      localStorage.setItem(`annotationtool-filters-${selectedApplication}-${projectId}`, JSON.stringify(newFilters))

      return newFilters
    })
  },

  setFiltersToDefault: (selectedApplication: 'annotate' | 'dashboard' | 'conflict' | 'activity', projectId: string, currentFilters: SelectedFiltersConfig) => {
    const currentFiltersJson = JSON.stringify(currentFilters ?? 'null')

    try {
      const parsedFilters = JSON.parse(localStorage.getItem(`annotationtool-filters-${selectedApplication}-${projectId}`) ?? 'null')
      const jsonFilters = localStorage.getItem(`annotationtool-filters-${selectedApplication}-${projectId}`) ?? 'null'

      if(parsedFilters && jsonFilters !== currentFiltersJson) {
        selectedFiltersStore.set({
          ...parsedFilters,
          page: { value: '1', label: '' },
          ...(parsedFilters.labelling_status && parsedFilters.labelling_status.value !== 'none' ? { maxLabellingTime: { value: Date.now().toString(), label: '' } } : {})
        })
        return
      } else if (jsonFilters === currentFiltersJson) {
        return
      }
    } catch (error) {
      console.error('Error parsing saved filters:', error)
    }

    const restoredFilters = {
      limit: { value: '2', label: '' },
      page: { value: '1', label: '' },
      sorting: { value: 'reporttime-asc', label: 'Time - Latest first' },
      ...(selectedApplication !== 'conflict'
        ? { labelling_status: { value: 'unlabelledEpisodes', label: 'Unlabelled Episodes' } }
        : {})
  }

    if(JSON.stringify(restoredFilters) !== currentFiltersJson) {
      selectedFiltersStore.set(restoredFilters)
    }

    localStorage.setItem(`annotationtool-filters-${selectedApplication}-${projectId}`, JSON.stringify(restoredFilters))
  },

  clearAllFilters: (selectedApplication: 'annotate' | 'dashboard' | 'conflict', projectId: string) => {
    const clearedFilters = {
      limit: { value: '2', label: '' },
      page: { value: '1', label: '' },
      sorting: { value: 'reporttime-asc', label: 'Time - Latest first' }
    }
    selectedFiltersStore.set(clearedFilters)
    localStorage.setItem(`annotationtool-filters-${selectedApplication}-${projectId}`, JSON.stringify(clearedFilters))
  },

  getFilter: (name: string) => {
    let currentValue: { value: string, label: string } | undefined
    selectedFiltersStore.subscribe(filters => {
      currentValue = filters[name]
    })()
    return currentValue
  }
}
