//
// Copyright © 2025 Hardcore Engineering Inc.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import type { Class, Doc, Ref, Space } from '@hcengineering/core'
import type { Asset, IntlString, Plugin, Metadata, Resource } from '@hcengineering/platform'
import { plugin } from '@hcengineering/platform'
import type { AnyComponent } from '@hcengineering/ui/src/types'
import type { Action, ActionCategory, ViewAction, Viewlet } from '@hcengineering/view'

import type { RdDoc } from './types'

/**
 * @public
 */
export const rdId = 'rd' as Plugin

/**
 * @public
 */
export const rd = plugin(rdId, {
  app: {
    Rd: '' as Ref<Doc>
  },
  class: {
    RdDoc: '' as Ref<Class<RdDoc>>,
    Dataset: '' as Ref<Class<Doc>>,
    ModelRegistry: '' as Ref<Class<Doc>>,
    DatasetVersion: '' as Ref<Class<Doc>>,
    ModelRegistryVersions: '' as Ref<Class<Doc>>,
    AnnotationTask: '' as Ref<Class<Doc>>,
    Experiment: '' as Ref<Class<Doc>>,
    Model: '' as Ref<Class<Doc>>,
    ModelMonitoring: '' as Ref<Class<Doc>>
  },
  icon: {
    Rd: '' as Asset,
    Dataset: '' as Asset,
    Annotation: '' as Asset,
    Experiment: '' as Asset,
    Model: '' as Asset,
    Monitor: '' as Asset
  },
  string: {
    Rd: '' as IntlString,
    Name: '' as IntlString,
    Description: '' as IntlString,
    Datasets: '' as IntlString,
    CPU: '' as IntlString,
    Memory: '' as IntlString,
    ModelRegistry: '' as IntlString,
    DatasetVersions: '' as IntlString,
    ModelRegistryVersions: '' as IntlString,
    Dataset: '' as IntlString,
    Version: '' as IntlString,
    Path: '' as IntlString,
    Annotation: '' as IntlString,
    Experimentation: '' as IntlString,
    ModelMonitoring: '' as IntlString,
    Projects: '' as IntlString,
    CreateAnnotation: '' as IntlString,
    CreateExperiment: '' as IntlString,
    RegisterModel: '' as IntlString,
    CreateMonitoring: '' as IntlString,
    Notebooks: '' as IntlString,
    CreateRd: '' as IntlString,
    CreationTimestamp: '' as IntlString,
    Annotator: '' as IntlString,
    Status: '' as IntlString,
    Url: '' as IntlString,
    Connect: '' as IntlString
  },
  component: {
    CreateRd: '' as AnyComponent,
    RdBlank: '' as AnyComponent,
    RdView: '' as AnyComponent,
    ExperimentationView: '' as AnyComponent,
    DatasetsView: '' as AnyComponent,
    ModelRegistryView: '' as AnyComponent,
    EditDataset: '' as AnyComponent,
    EditModelRegistry: '' as AnyComponent,
    DatasetControlPanel: '' as AnyComponent,
    DatasetActivity: '' as AnyComponent,
    DatasetPresenter: '' as AnyComponent,
    ModelRegistryPresenter: '' as AnyComponent,
    ModelRegistryCharts: '' as AnyComponent,
    ModelRegistryChartsPanel: '' as AnyComponent,
    DatasetDatePresenter: '' as AnyComponent,
    ExperimentPresenter: '' as AnyComponent,
    ExperimentExpanded: '' as AnyComponent,
    ExperimentStatusPresenter: '' as AnyComponent,
    ConnectButton: '' as AnyComponent,
    ModelRegistryDatePresenter: '' as AnyComponent,
    ExperimentsView: '' as AnyComponent,
    NotebooksView: '' as AnyComponent,
    AnnotationView: '' as AnyComponent,
    CreateAnnotation: '' as AnyComponent,
    CreateExperiment: '' as AnyComponent,
    RegisterModel: '' as AnyComponent,
    CreateMonitoring: '' as AnyComponent
  },
  function: {
    IsRnDProject: '' as Resource<(space: Space) => Promise<boolean>>
  },
  action: {
    CreateRd: '' as Ref<Action>,
    EditRd: '' as Ref<Action>,
    DeleteRd: '' as Ref<Action<Doc, any>>
  },
  actionImpl: {
    CreateRd: '' as ViewAction,
    EditRd: '' as ViewAction
  },
  category: {
    Rd: '' as Ref<ActionCategory>
  },
  viewlet: {
    DatasetList: '' as Ref<Viewlet>,
    ExperimentList: '' as Ref<Viewlet>,
    ModelRegistryList: '' as Ref<Viewlet>
  },
  metadata: {
    RdUrl: '' as Metadata<string>,
    KubeflowUrl: '' as Metadata<string>,
    KubeflowApiKey: '' as Metadata<string>
  }
})

export default rd