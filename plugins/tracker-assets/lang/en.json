{"string": {"TrackerApplication": "Tracker", "Projects": "Your projects", "More": "More", "Default": "<PERSON><PERSON><PERSON>", "MakeDefault": "Make default", "Delete": "Delete", "Open": "Open", "Members": "Members", "Inbox": "Inbox", "MyIssues": "My issues", "ViewIssue": "View issue", "IssueCreated": "Issue Created", "Issues": "Issues", "Views": "Views", "Active": "Active", "AllIssues": "All issues", "ActiveIssues": "Active issues", "BacklogIssues": "Backlog", "Backlog": "Backlog", "Board": "Board", "Timeline": "Timeline", "Roadmaps": "Roadmaps", "GanttChart": "Gantt Chart", "Epics": "Epics", "Filter": "Filter", "Today": "Today", "ThreeMonths": "3 Months", "SixMonths": "6 Months", "TwelveMonths": "12 Months", "NoItemsAvailable": "No items available", "Type": "Type", "DateRangeTooltipStart": "Start Date", "DateRangeTooltipEnd": "End Date", "TimePeriod": "Time Period", "UserStories": "User Stories", "EditSegment": "Edit Segment", "TimeRange": "Time Range", "Components": "Components", "AllComponents": "All", "BacklogComponents": "Backlog", "ActiveComponents": "Active", "ClosedComponents": "Closed", "NewComponent": "New component", "CreateComponent": "Create component", "ComponentNamePlaceholder": "Component name", "ComponentDescriptionPlaceholder": "Description (optional)", "ComponentLead": "Lead", "ComponentMembers": "Members", "StartDate": "Start date", "TargetDate": "Target date", "Planned": "Planned", "InProgress": "In progress", "Paused": "Paused", "Completed": "Completed", "Canceled": "Canceled", "CreateProject": "Create project", "NewProject": "New project", "ProjectTitle": "Project title", "ProjectTitlePlaceholder": "New project", "UsedInIssueIDs": "Used in issue IDs", "Identifier": "Identifier", "Import": "Import", "ProjectIdentifier": "Project identifier", "IdentifierExists": "Project identifier already exists", "ProjectIdentifierPlaceholder": "PRJCT", "ChooseIcon": "Choose icon", "AddIssue": "Add issue", "NewIssue": "New issue", "NewIssuePlaceholder": "New", "ResumeDraft": "Resume draft", "SaveIssue": "Create issue", "SetPriority": "Set priority…", "SetStatus": "Set status…", "SelectIssue": "Select issue", "Priority": "Priority", "NoPriority": "No priority", "Urgent": "<PERSON><PERSON>", "High": "High", "Medium": "Medium", "Low": "Low", "Unassigned": "Unassigned", "Back": "Back", "List": "List", "NumberLabels": "{count, plural, =0 {no labels} =1 {1 label} other {# labels}}", "CategoryBacklog": "Backlog", "CategoryUnstarted": "Unstarted", "CategoryStarted": "Started", "CategoryCompleted": "Completed", "CategoryCanceled": "Canceled", "Title": "Title", "Name": "Name", "Description": "Description", "Status": "Status", "Number": "Number", "Assignee": "Assignee", "AssignTo": "Assign to...", "AssignedTo": "Assigned to {value}", "Parent": "Parent issue", "SetParent": "Set parent issue…", "ChangeParent": "Change parent issue…", "RemoveParent": "Remove parent issue", "OpenParent": "Open parent issue", "SubIssues": "Sub-issues", "SubIssuesList": "Sub-issues ({subIssues})", "OpenSubIssues": "Open sub-issues", "AddSubIssues": "Add sub-issue", "BlockedBy": "Blocked by", "RelatedTo": "Related to", "Comments": "Comments", "Attachments": "Attachments", "Labels": "Labels", "Component": "Component", "Space": "", "SetDueDate": "Set due date…", "ChangeDueDate": "Change due date…", "ModificationDate": "Updated {value}", "Project": "Project", "Issue": "Issue", "SubIssue": "Sub-issue", "Document": "", "DocumentIcon": "", "DocumentColor": "", "Rank": "Rank", "TypeIssuePriority": "Issue priority", "IssueTitlePlaceholder": "Issue title", "SubIssueTitlePlaceholder": "Sub-issue title", "IssueDescriptionPlaceholder": "Add description…", "SubIssueDescriptionPlaceholder": "Add sub-issue description", "JiraProjectUrlPlaceholder": "Add Jira project URL...", "GithubProjectUrlPlaceholder": "Add Github project URL...", "DatasetIDPlaceholder": "Add catalog", "NameSpacePlaceholder": "Add name space", "AddIssueTooltip": "Add issue...", "NewIssueDialogClose": "Do you want to close this dialog?", "NewIssueDialogCloseNote": "All changes will be lost", "RemoveComponentDialogClose": "Delete the component?", "RemoveComponentDialogCloseNote": "Are you sure you want to delete this component? This operation cannot be undone", "Grouping": "Grouping", "Ordering": "Ordering", "CompletedIssues": "Completed issues", "NoGrouping": "No grouping", "NoAssignee": "No assignee", "LastUpdated": "Last updated", "DueDate": "Due date", "Manual": "Manual", "All": "All", "PastWeek": "Past week", "PastMonth": "Past month", "CopyIssueUrl": "Copy issue URL to clipboard", "CopyIssueId": "Copy issue ID to clipboard", "CopyIssueBranch": "Copy Git branch name to clipboard", "CopyIssueTitle": "Copy issue title to clipboard", "AssetLabel": "<PERSON><PERSON>", "AddToComponent": "Add to component…", "MoveToComponent": "Move to component…", "NoComponent": "No component", "ComponentLeadTitle": "Component lead", "ComponentMembersTitle": "Component members", "ComponentLeadSearchPlaceholder": "Set component lead…", "ComponentMembersSearchPlaceholder": "Change component members…", "MoveToProject": "Move to project", "Duplicate": "Duplicate", "GotoIssues": "Go to issues", "GotoActive": "Go to active issues", "GotoBacklog": "Go to backlog", "GotoComponents": "Go to components", "GotoMyIssues": "Go to my issues", "GotoTrackerApplication": "Switch to Tracker Application", "CreatedOne": "Created", "MoveIssues": "Move issues", "MoveIssuesDescription": "Select the project you want to move issues to", "ManageAttributes": "Manage attributes", "KeepOriginalAttributes": "Keep original attributes", "KeepOriginalAttributesTooltip": "Original issue statuses and components will be kept in the new project", "SelectReplacement": "The following items are not available in the new project. Select a replacement.", "MissingItem": "MISSING ITEM", "Replacement": "REPLACEMENT", "Original": "ORIGINAL", "OriginalDescription": "Items from this section will be created in the new project", "Relations": "Relations", "RemoveRelation": "Remove relation...", "AddBlockedBy": "<PERSON> as blocked by...", "AddIsBlocking": "<PERSON> as blocking...", "AddRelatedIssue": "Reference another issue...", "RelatedIssue": "Related issue {id} - {title}", "BlockedIssue": "Blocked issue {id} - {title}", "BlockingIssue": "Blocking issue {id} - {title}", "BlockedBySearchPlaceholder": "Search for issue to mark blocked by...", "IsBlockingSearchPlaceholder": "Search for issue to mark as blocking...", "RelatedIssueSearchPlaceholder": "Search for issue to reference...", "Blocks": "Blocks", "Related": "Related", "RelatedIssues": "Related issues", "EditIssue": "Edit {title}", "EditWorkflowStatuses": "Edit issue statuses", "EditProject": "Edit project", "DeleteProject": "Delete project", "ArchiveProjectName": "Archive project {name}?", "ArchiveProjectConfirm": "Do you want to archive this project?", "DeleteProjectConfirm": "Do you want to delete this project and all issues?", "ProjectHasIssues": "There are existing issues in this project, are you sure that you want to archive?", "ManageWorkflowStatuses": "Manage project types", "AddWorkflowStatus": "Add issue status", "EditWorkflowStatus": "Edit issue status", "DeleteWorkflowStatus": "Delete issue status", "DeleteWorkflowStatusConfirm": "Do you want to delete the \"{status}\" status?", "DeleteWorkflowStatusErrorDescription": "The \"{status}\" status has {count, plural, =1 {1 issue} other {# issues}} assigned. Please select a status to move", "Save": "Save", "IncludeItemsThatMatch": "Include items that match", "AnyFilter": "any filter", "AllFilters": "all filters", "NoDescription": "No description", "SearchIssue": "Search for task...", "StatusHistory": "State History", "NewSubIssue": "Add sub-issue...", "AddLabel": "Add label", "DeleteIssue": "Delete {issueCount, plural, =1 {issue} other {# issues}}", "DeleteIssueConfirm": "Do you want to delete {issueCount, plural, =1 {issue} other {issues}}{subIssueCount, plural, =0 {?} =1 { and sub-issue?} other { and sub-issues?}}", "Milestone": "Milestone", "NoMilestone": "No Milestone", "MoveToMilestone": "Select Milestone", "Milestones": "Milestones", "AllMilestones": "All", "PlannedMilestones": "Planned", "ActiveMilestones": "Active", "ClosedMilestones": "Done", "AddToMilestone": "Add to Milestone", "MilestoneNamePlaceholder": "Milestone name", "NewMilestone": "New Milestone", "CreateMilestone": "Create", "MoveAndDeleteMilestone": "Move issues to {newMilestone} and delete {deleteMilestone}", "MoveAndDeleteMilestoneConfirm": "Do you want to delete milestone and move issues to another milestone?", "Estimation": "Estimation", "ReportedTime": "Spent time", "RemainingTime": "Remaining time", "TimeSpendReports": "Time spent reports", "TimeSpendReport": "Time", "TimeSpendReportAdd": "Add time report", "TimeSpendReportDate": "Date", "TimeSpendReportValue": "Spent time", "TimeSpendReportValueTooltip": "Spent time in hours", "TimeSpendReportDescription": "Description", "TimeSpendDays": "{value}d", "TimeSpendHours": "{value}h", "TimeSpendMinutes": "{value}m", "ChildEstimation": "Sub-issues estimation", "ChildReportedTime": "Sub-issues time", "CapacityValue": "of {value}d", "NewRelatedIssue": "New related issue", "RelatedIssuesNotFound": "Related issues not found", "AddedReference": "Added reference", "AddedAsBlocked": "Marked as blocked", "AddedAsBlocking": "Marked as blocking", "IssueTemplate": "Template", "IssueTemplates": "Templates", "NewProcess": "New template", "SaveProcess": "Save template", "NoIssueTemplate": "No template", "TemplateReplace": "Do you want to apply the new template?", "TemplateReplaceConfirm": "All fields will be overwritten by the new template values", "Apply": "Apply", "CurrentWorkDay": "Current Working Day", "PreviousWorkDay": "Previous Working Day", "TimeReportDayTypeLabel": "Select time report day type", "DefaultAssignee": "Default assignee for issues", "JiraProjectUrl": "Jira project URL", "GithubProjectUrl": "Github project URL", "DatasetID": "Catalog", "NameSpace": "Name space", "SevenHoursLength": "Seven Hours", "EightHoursLength": "Eight Hours", "HourLabel": "h", "Saved": "Saved...", "CreatedIssue": "Created issue", "CreatedSubIssue": "Created sub-issue", "ChangeStatus": "Change status", "ConfigLabel": "Tracker", "ConfigDescription": "Extension to manage work items and do all jobs done.", "NoStatusFound": "No matching status found", "CreateMissingStatus": "Create missing status", "UnsetParent": "Parent issue will be unset", "AllProjects": "All projects", "IssueNotificationTitle": "{issueTitle}", "IssueNotificationBody": "Updated by {senderName}", "IssueNotificationChanged": "{senderName} changed {property}", "IssueNotificationChangedProperty": "{senderName} changed {property} to \"{newValue}\"", "IssueNotificationMessage": "{senderName}: {message}", "PreviousAssigned": "Previously assigned", "IssueAssignedToYou": "Assigned to you", "RelatedIssueTargetDescription": "Related issue project target for Class or Space", "MapRelatedIssues": "Configure related issue default projects", "DefaultIssueStatus": "Default issue status", "IssueStatus": "Status", "Extensions": "Extensions", "RoleLabel": "Role: {role}", "UnsetParentIssue": "Unset parent issue"}, "status": {}}