{"string": {"AnnotationTool": "Annotation Tool", "AnnotationActivity": "Annotation Tool - Activity", "ConfigLabel": "Label", "ConfigDescription": "Label for annotating ECG episodes", "ConfigColor": "Label Color", "ConfigValue": "Label Value", "ConfigAccount": "Account ID", "ConfigAccessLevel": "Access Level", "ConfigEpisodeId": "Episode ID", "ConfigStripId": "Strip ID", "ConfigProject": "Project", "ConfigDatasetId": "Dataset ID", "ApplicationLabelAnnotationTool": "Annotation Tool", "Title": "Annotation Tool", "Annotate": "Annotate", "Dashboard": "Dashboard", "Activity": "Statistics", "Filter": "Filter", "FilterTooltip": "Select filters", "AnnotateFilters": "Annotate - Filter ECG Episodes", "CloseFilters": "Close filters", "ClearFilters": "Clear filters", "ClinicFilter": "Clinic filter", "SelectClinic": "Select clinic", "PatientFilter": "Patient filter", "SelectPatient": "Select patient", "SelectClinicFirst": "Please select a clinic first", "Manufacturer": "Manufacturer", "SelectManufacturer": "Select manufacturer", "LabelFilter": "Label filter", "EpisodeFilter": "Episode filter", "SelectLabel": "Select label", "SelectConflictStatus": "Select conflict status", "LabelledBy": "Labelled by", "SelectLabelledBy": "Select labelled by", "NotLabelledBy": "Not labelled by", "SelectNotLabelledBy": "Select not labelled by", "FromDate": "From Date", "SelectFromDate": "Select from date", "ToDate": "To Date", "SelectToDate": "Select to date", "EpisodeIDFilter": "Episode ID", "SelectEpisodeID": "Select episode ID", "NoOptionsAvailable": "No options available", "FirstPage": "First page", "PreviousPage": "Previous page", "NextPage": "Next page", "LastPage": "Last page", "Strips": "strips", "Strip": "Strip", "TooltipPatientDetails": "Patient details", "TooltipManufacturerDetails": "Device details", "TooltipEpisodeId": "Episode ID", "TooltipDeviceLabel": "Symptom detected by device", "TooltipReportTime": "Episode report time", "TooltipEpisodeLength": "Episode duration", "TooltipStrips": "Number of strips in the episode", "NotSpecified": "Not specified", "AnnotateWholeEpisode": "Annotate whole episode", "SettingsTitle": "Annotation Tool - Settings", "LabelsConfiguration": "Labels configuration", "TriageConfiguration": "Triage Labels configuration", "CreateLabel": "Create a new label for {project}", "CreateLabelButton": "Create a new label", "CreateTriageLabel": "Create a new triage label for {project}", "CreateTriageLabelButton": "Create a new triage label", "AddLabel": "Add label", "AddTriageLabel": "Add triage label", "NoLabels": "No labels found - start by creating a new label below", "NoTriageLabels": "No triage labels found - start by creating a new triage label below", "LabelPlaceholder": "Type label name here", "TriagePlaceholder": "Type triage label name here", "LabelColor": "Label color", "SelectLabels": "Select labels to apply to the whole episode", "SelectTriage": "Select triage to apply to the whole episode", "StripID": "Strip ID: {stripId}", "EpisodeID": "Episode {episodeId}", "Labels": "Strip labels", "Triage": "Strip triage", "EpisodeLabels": "Episode labels", "EpisodeTriage": "Episode triage", "IsInterestingEpisode": "<PERSON> as interesting", "IsInterestingStrip": "<PERSON> as interesting", "IsDoneEpisode": "Mark as done", "IsConflictResolvedEpisode": "<PERSON> as conflict resolved", "DescribeEpisode": "Describe what makes this episode interesting", "EpisodeActivity": "Episode activity - {episodeId}", "StripActivity": "Strip activity - {stripId}", "EpisodeViewMode": "Episode strips view", "EpisodeListView": "View strips as list", "EpisodeCardView": "View strips as cards", "NumberOfEpisodesPerPage": "Number of episodes per page", "Close": "Close", "CollapseEpisode": "Collapse episode", "ExpandEpisode": "Expand episode", "EpisodeIsCollapsed": "This episode and its strips have been collapsed", "Annotators": "Annotators Configuration", "AnnotatorAccessLevel": "Configure annotators access level", "AnnotatorLevelLabel": "Select annotator level", "AnnotatorLevel1": "Basic annotator", "AnnotatorLevel2": "Advanced annotator", "ConflictResolvingAnnotator": "Configure annotator to assign conflicting labellings", "TableSettingsTotalPatients": "Total patients", "TableSettingsTotalEpisodes": "Total episodes", "TableSettingsTotalEcgStrips": "Total ECG strips", "TableSettingsTooltip": "Configure table settings", "HierchicalSections": "Hierchical sections", "TableSettingsClinic": "Clinic", "TableSettingsManufacturer": "Manufacturer", "TableSettingsDeviceModel": "Device model", "TableSettingsDeviceLabel": "Device label", "TableSettingsDoctorLabel": "Doctor label", "LeftColumnDistributor": "Left column distributor", "LeftColumnDistributorEpisodeLength": "Episode length", "LeftColumnDistributorDeviceLabel": "Device label", "LeftColumnDistributorDoctorLabel": "Doctor label", "EpisodeLengthFrom": "Episode length from", "EpisodeLengthTo": "Episode length to", "SelectEpisodeLengthFrom": "Select episode length from", "SelectEpisodeLengthTo": "Select episode length to", "LessThan1Minute": "Less than 1 minute", "1Minute": "1 minute", "3Minutes": "3 minutes", "5Minutes": "5 minutes", "DashboardNoData": "There is no data to display for the selected filters and columns", "Sorting": "Episodes sorting", "SelectSorting": "Select patient episodes sorting", "SubmitLabels": "Submit Labels", "SubmittedLabels": "Labels submitted", "NotLabelledEpisodesWarning": "There are episodes which are not yet labelled above", "NoEpisodesFound": "No episodes found for the selected filters", "ErrorLoadingEpisodes": "Error loading episodes. Please try again later.", "PaginationInfo": "Showing episode {startItem} to {endItem} out of {totalItems} episodes", "SavingChanges": "Saving changes...", "ChangesSaved": "Changes saved", "None": "None", "LabelledOnly": "Doctor labelled only", "Conflict": "Conflict Resolver", "Approve": "Approve", "StripTitle": "Strip {stripId}", "Reject": "Reject", "Added": "+ Added", "Removed": "- Removed", "Approved": "+ Approved", "Disapproved": "- Rejected", "NoHistory": "No annotation history", "AnnotationActivityTitle": "Activity on {stripOrEpisode} {stripOrEpisodeId}", "AnnotationActivityNotificationBody": "New comment by {sender<PERSON>ame}", "AnnotationActivityNotificationTitle": "Activity in ", "AnnotationActivityNotificationSubtitle": "Comment on {stripOrEpisode} {stripOrEpisodeId}", "ByDay": "By day", "Week": "Last 7 days", "Total": "Total", "ShowingEpisodes": "Showing {currentEpisodes} out of {totalEpisodes} episodes", "LoadingEpisodes": "Loading episodes", "ClearAll": "Clear all", "NumDoneEpisodes": "{numDoneEpisodes} episode(s) done", "AcceptSegmentation": "Accept", "RejectSegmentation": "Reject", "DoneD": "Done with label (D)", "ClearX": "Clear label segments (X)", "ApplyToEntireStripE": "Apply to entire strip (E)", "MyAnnotations": "My annotations", "AllAnnotations": "All annotator annotations"}}