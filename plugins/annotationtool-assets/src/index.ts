//
// Copyright © 2020 Anticrm Platform Contributors.
//
// Licensed under the Eclipse Public License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License. You may
// obtain a copy of the License at https://www.eclipse.org/legal/epl-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//
// See the License for the specific language governing permissions and
// limitations under the License.
//

import annotationTool from '@hcengineering/annotationtool'
import { loadMetadata } from '@hcengineering/platform'

const icons = require('../assets/icons.svg') as string // eslint-disable-line
loadMetadata(annotationTool.icon, {
  AnnotationTool: `${icons}#annotationtool`,
  Lock: `${icons}#lock`,
  Copy: `${icons}#copy`,
  Bookmarks: `${icons}#bookmarks`,
  Delete: `${icons}#delete`,
  Failed: `${icons}#failed`,
  Edit: `${icons}#edit`,
  Dashboard: `${icons}#dashboard`,
  CheckmarkCircle: `${icons}#checkmark-circle`,
  X: `${icons}#x-icon`,
  XCircle: `${icons}#x-circle`,
  Calendar: `${icons}#calendar`,
  CardView: `${icons}#card-view`,
  ListView: `${icons}#list-view`,
  Hospital: `${icons}#hospital`,
  Patient: `${icons}#patient`,
  Manufacturer: `${icons}#manufacturer`,
  Label: `${icons}#label`,
  Doctor: `${icons}#doctor`,
  DateFrom: `${icons}#date-from`,
  DateTo: `${icons}#date-to`,
  Sorting: `${icons}#sorting`,
  Timer: `${icons}#timer`,
  Tag: `${icons}#tag`,
  TagAdd: `${icons}#tag-add`,
  Key: `${icons}#key`,
  Ecg: `${icons}#ecg`,
  ChevronDown: `${icons}#chevron-down`,
  ChevronUp: `${icons}#chevron-up`,
  ChevronLeft: `${icons}#chevron-left`,
  ChevronRight: `${icons}#chevron-right`,
  ChevronDoubleLeft: `${icons}#chevron-double-left`,
  ChevronDoubleRight: `${icons}#chevron-double-right`,
  ZoomOut: `${icons}#zoom-out`,
  Reply: `${icons}#reply`,
  ZoomIn: `${icons}#zoom-in`,
  Expand: `${icons}#expand`,
  History: `${icons}#history`,
  EpisodeCollapse: `${icons}#episode-collapse`,
  EpisodeExpand: `${icons}#episode-expand`,
  AnnotatorPermissions: `${icons}#annotator-permissions`,
  Warning: `${icons}#warning`,
  Conflict: `${icons}#conflict`,
  Activity: `${icons}#stats`,
  LoadMoreSeconds: `${icons}#load-5-more-seconds`,
  UnloadMoreSeconds: `${icons}#unload-5-seconds`,
  ApplyToWholeEpisode: `${icons}#apply-to-whole-episode`,
  MyAnnotations: `${icons}#my-annotations`,
  AllAnnotations: `${icons}#all-annotations`
})
