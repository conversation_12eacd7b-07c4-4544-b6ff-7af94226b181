{"string": {"Datacatalog": "Data Catalog", "Name": "Data Catalog name", "Description": "Data Catalog description", "ApplicationTitle": "Data Catalog", "DataAssets": "Data Assets", "DataLineage": "Data Lineage", "Observability": "Observability", "Domains": "Domains", "DataGovernance": "Data Governance", "LoadingDomains": "Loading domains...", "NoDomains": "No domains available", "Documentation": "Documentation", "SubDomains": "Sub Domains", "DataProducts": "Data Products", "Assets": "Assets", "CustomProperties": "Custom Properties", "Glossary": "Glossary", "Classification": "Classification", "SearchGlossary": "Search glossary", "SearchClassification": "Search classification", "Filter": "Filter", "Tag": "Tag", "TermForTable": "Term", "DescriptionForTable": "Description", "OwnersForTable": "Owners", "TagsForTable": "Tags", "DomainsForTable": "Domains", "Back": "Back", "AllTags": "All tags", "Status": "Status", "Active": "Active", "Disabled": "Disabled", "Approved": "Approved", "FullyQualifiedName": "Fully Qualified Name", "UsageCount": "Usage Count", "ChildrenCount": "Children Count", "Owners": "Owners", "Reviewers": "Reviewers", "Tags": "Tags", "TotalTags": "Total Tags", "Terms": "Terms", "All": "All", "NoDescriptionAvailable": "No description available", "Databases": "Databases", "Dashboards": "Dashboards", "Pipelines": "Pipelines", "Topics": "Topics", "Charts": "Charts", "Metrics": "Metrics", "Reports": "Reports", "MLModels": "ML Models", "Containers": "Containers", "ObservabilityPagination": "Showing page {page} out of {totalPages} total pages"}}