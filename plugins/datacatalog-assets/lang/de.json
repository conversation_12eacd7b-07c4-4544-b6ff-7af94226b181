{"string": {"Datacatalog": "Datenkatalog", "Name": "Datenkatalog Name", "Description": "Datenkatalog Beschreibung", "ApplicationTitle": "Datenkatalog", "NavItem1": "Datenassets", "NavItem2": "Datenherkunft", "NavItem3": "Beobachtbarkeit", "NavItem4": "<PERSON><PERSON><PERSON>", "NavItem5": "Data Governance", "Domains": "<PERSON><PERSON><PERSON>", "LoadingDomains": "Domänen werden geladen...", "NoDomains": "<PERSON><PERSON> ve<PERSON>", "Documentation": "Dokumentation", "SubDomains": "Unterdomänen", "DataProducts": "Datenprodukte", "Assets": "Assets", "CustomProperties": "Benutzerdefinierte Eigenschaften", "Glossary": "Glossar", "Classification": "Klassifizierung", "SearchGlossary": "Glossar durchsuchen", "SearchClassification": "Klassifizierung suchen", "Filter": "Filter", "Tag": "Tag", "TermForTable": "Begriff", "DescriptionForTable": "Beschreibung", "OwnersForTable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TagsForTable": "Tags", "DomainsForTable": "<PERSON><PERSON><PERSON>", "Back": "Zurück", "AllTags": "Alle Tags", "Status": "Status", "Active": "Aktiv", "Disabled": "Deaktiviert", "Approved": "<PERSON><PERSON><PERSON><PERSON>", "FullyQualifiedName": "Vollqualifizierter Name", "UsageCount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ChildrenCount": "<PERSON><PERSON><PERSON><PERSON>", "Owners": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reviewers": "<PERSON><PERSON><PERSON><PERSON>", "Tags": "Tags", "TotalTags": "Gesamte Tags", "Terms": "<PERSON><PERSON><PERSON><PERSON>", "All": "Alle", "NoDescriptionAvailable": "Keine Beschreibung verfügbar"}}