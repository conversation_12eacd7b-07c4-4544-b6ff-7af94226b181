services:
  # ZooKeeper (used by <PERSON><PERSON><PERSON> and ClickHouse replication)
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["C<PERSON>", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kafka (analytics microservice depends on it)
  kafka:
    image: wurstmeister/kafka:latest
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_ADVERTISED_HOST_NAME: kafka
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_CREATE_TOPICS: "webhook-events:1:1,notifications:1:1"
    ports:
      - "9092:9092"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    healthcheck:
      test: ["CMD", "kafka-topics.sh", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ClickHouse cluster: 3 shards x 3 replicas (9 nodes)
  ch1:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch1
    hostname: ch1
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node1/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    networks:
      default:
        aliases:
          - clickhouse
    ports:
      - "9010:9000"
      - "8123:8123"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch2:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch2
    hostname: ch2
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node2/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch3:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch3
    hostname: ch3
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node3/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch4:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch4
    hostname: ch4
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node4/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch5:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch5
    hostname: ch5
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node5/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch6:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch6
    hostname: ch6
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node6/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch7:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch7
    hostname: ch7
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node7/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch8:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch8
    hostname: ch8
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node8/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ch9:
    image: clickhouse/clickhouse-server:23.8
    container_name: ch9
    hostname: ch9
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    volumes:
      - ./clickhouse-cluster/config.d/clusters.xml:/etc/clickhouse-server/config.d/clusters.xml:ro
      - ./clickhouse-cluster/config.d/zookeeper.xml:/etc/clickhouse-server/config.d/zookeeper.xml:ro
      - ./clickhouse-cluster/config.d/listen.xml:/etc/clickhouse-server/config.d/listen.xml:ro
      - ./clickhouse-cluster/node9/config.d/macros.xml:/etc/clickhouse-server/config.d/macros.xml:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Analytics microservice (unchanged env & functionality)
  go-analytics-microservice:
    image: hardcoreeng/go-analytics-microservice
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    ports:
      - 2020:2020
    environment:
      ANALYTICS_HTTP_HOST:         "0.0.0.0"
      ANALYTICS_HTTP_PORT:         "2020"

      ANALYTICS_CLICKHOUSE_DSN:    "tcp://clickhouse:9000/analytics"

      ANALYTICS_DAPR_ENABLED:      "true"
      ANALYTICS_DAPR_APP_ID:       "analytics-microservice"
      ANALYTICS_DAPR_HTTP_PORT:    "3500"
      ANALYTICS_DAPR_GRPC_PORT:    "50001"

      DAPR_GRPC_PORT: "50001"
      DAPR_HTTP_PORT: "3500"
    depends_on:
      ch1:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:2020/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Dapr sidecar for analytics microservice
  analytics-dapr:
    image: daprio/daprd:1.15.6
    network_mode: "service:go-analytics-microservice"
    command: >
      /daprd
      --app-id go-analytics-microservice
      --app-port 2020
      --dapr-http-port 3500
      --dapr-grpc-port 50001
      --resources-path /components
    volumes:
      - ../go/go-microservices/analytics-microservice/components:/components:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3500/v1.0/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      go-analytics-microservice:
        condition: service_healthy


