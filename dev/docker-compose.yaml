services:
  # Go microservices
  go-analytics-microservice:
    image: hardcoreeng/go-analytics-microservice
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    ports:
      - 2020:2020
    environment:
      ANALYTICS_HTTP_HOST:         "0.0.0.0"
      ANALYTICS_HTTP_PORT:         "2020"

      ANALYTICS_CLICKHOUSE_DSN:    "tcp://clickhouse:9000/analytics"

      ANALYTICS_DAPR_ENABLED:      "true"
      ANALYTICS_DAPR_APP_ID:       "analytics-microservice"
      ANALYTICS_DAPR_HTTP_PORT:    "3500"
      ANALYTICS_DAPR_GRPC_PORT:    "50001"

       # Standard Dapr environment variables for the Dapr SDK
      DAPR_GRPC_PORT: "50001"  # For the daprclient.NewClient()
      DAPR_HTTP_PORT: "3500"   # For the daprclient.NewClient()
    depends_on:
      - clickhouse
      - kafka
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:2020/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
  go-webhook-microservice:
    image: hardcoreeng/go-webhook-microservice
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    ports:
      - 2022:2022
    environment:
      WEBHOOK_SERVER_HOST: "0.0.0.0"
      WEBHOOK_SERVER_PORT: "2022"
      
      WEBHOOK_ENVIRONMENT: "development"
      WEBHOOK_LOGGING_LEVEL: "info"
      
      WEBHOOK_OBSERVABILITY_PROMETHEUS_ENABLED: "false"
      WEBHOOK_OBSERVABILITY_TRACING_ENABLED: "false"
      
      # Dapr configuration - need BOTH sets of variables
      WEBHOOK_DAPR_ENABLED: "true"
      WEBHOOK_DAPR_APP_ID: "webhook-microservice"
      WEBHOOK_DAPR_HTTP_PORT: "3501"
      WEBHOOK_DAPR_GRPC_PORT: "50002"  # For the webhook microservice config
      
      # Standard Dapr environment variables for the Dapr SDK
      DAPR_GRPC_PORT: "50002"  # For the daprclient.NewClient()
      DAPR_HTTP_PORT: "3501"   # For the daprclient.NewClient()
      
      WEBHOOK_DAPR_STATE_STORE: "statestore"
      WEBHOOK_DAPR_PUBSUB_NAME: "kafka-pubsub"
      WEBHOOK_DAPR_SECRET_STORE: "config-secret-store"
      
      WEBHOOK_WEBHOOK_TOPIC_NAME: "webhook-events"
      WEBHOOK_WEBHOOK_RATE_LIMIT_RPM: "1000"
      WEBHOOK_WEBHOOK_WAF_ALLOW_LIST: "0.0.0.0/0"
    depends_on:
      - kafka
  go-annotationtool-microservice:
    image: hardcoreeng/go-annotationtool-microservice
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    ports:
      - 2021:2021
    environment:
      ANNOTATIONTOOL_HTTP_HOST:         "0.0.0.0"
      ANNOTATIONTOOL_HTTP_PORT:         "2021"

      # TODO:
      # - ANNOTATIONTOOL_CLICKHOUSE_DSN is not used, should be CLICKHOUSE_DSN, it currently falls back to 9002 in container.go
      # - conflict with minio port 9000
      # - repos expect host:port format, not tcp://host:port/db.
      CLICKHOUSE_DSN: "clickhouse:9000"
      CLICKHOUSE_DATABASE: "annotationtool"  
      CLICKHOUSE_USERNAME: "default"
      CLICKHOUSE_PASSWORD: ""

        # ADD THESE MINIO VARIABLES:
      MINIO_ENDPOINT: "minio:9000"        # Use Docker service name, not localhost
      MINIO_ACCESS_KEY: "minioadmin"
      MINIO_SECRET_KEY: "minioadmin"
      MINIO_BUCKET: "datasets"
      MINIO_USE_SSL: "false"
      MINIO_ROOT_PREFIX: "gold/rnd"
      MINIO_DEFAULT_PAGE_SIZE: "50"

      KAFKA_ENABLED: "false"
      KAFKA_BROKERS: "kafka:9092"
      KAFKA_TOPIC: "annotationtool-kafka-bus"
      KAFKA_CONSUMER_GROUP: "annotationtool-consumer-group"
      KAFKA_RETRY_COUNT: "3"
      KAFKA_RETRY_TOPIC: "annotationtool-retry"
      KAFKA_DEAD_LETTER_TOPIC: "annotationtool-dlq"
      
      # Exponential backoff configuration for retry mechanism
      KAFKA_RETRY_BASE_DELAY: "1s"          # Initial delay between retries
      KAFKA_RETRY_MAX_DELAY: "30s"          # Maximum delay cap for retries  
      KAFKA_RETRY_JITTER_ENABLED: "true"    # Add randomization to prevent thundering herd

      DAPR_ENABLED:      "true"
      DAPR_APP_ID:       "annotationtool-microservice"
      DAPR_HTTP_PORT:    "3502"
      DAPR_GRPC_PORT:    "50003"
    depends_on:
      - clickhouse
      - kafka
      - minio
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:2022/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
 
  # Dapr sidecars
  analytics-dapr:
    image: daprio/daprd:1.15.6
    network_mode: "service:go-analytics-microservice"

    command: >
      /daprd
      --app-id go-analytics-microservice
      --app-port 2020
      --dapr-http-port 3500
      --dapr-grpc-port 50001
      --resources-path /components
    volumes:
      - ../go/go-microservices/analytics-microservice/components:/components:ro  # Fixed path
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3500/v1.0/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      go-analytics-microservice: 
        condition: service_healthy
  webhook-dapr:
    image: daprio/daprd:1.15.6
    network_mode: "service:go-webhook-microservice"
    environment:
      WEBHOOK_POSTGRES_CONNECTION_STRING: "host=postgres port=5432 user=webhook password=webhook123 dbname=webhook_state sslmode=disable"
      WEBHOOK_POSTGRES_TABLE_NAME: "state"
      WEBHOOK_POSTGRES_METADATA_TABLE: "metadata"
      WEBHOOK_POSTGRES_SSLMODE: "false"

    command: >
      /daprd
      --app-id go-webhook-microservice
      --app-port 2022
      --dapr-http-port 3501
      --dapr-grpc-port 50002
      --resources-path /components
    volumes:
      - ../go/go-microservices/webhook-microservice/components:/components:ro # 
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3501/v1.0/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      go-webhook-microservice: 
        condition: service_healthy
  annotationtool-dapr:
    image: daprio/daprd:1.15.6
    network_mode: "service:go-annotationtool-microservice"

    command: >
      /daprd
      --app-id go-annotationtool-microservice
      --app-port 2021
      --dapr-http-port 3502
      --dapr-grpc-port 50003
      --resources-path /components
    volumes:
      - ..go/go-microservices/go-annotationtool-microservice/components:/components:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3502/v1.0/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

 # Zookeeper
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Kafka
  kafka:
    image: wurstmeister/kafka:latest
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_ADVERTISED_HOST_NAME: kafka
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_CREATE_TOPICS: "webhook-events:1:1,notifications:1:1"
    ports:
      - "9092:9092"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    healthcheck:
      test: ["CMD", "kafka-topics.sh", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Volumes
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: webhook_state
      POSTGRES_USER: webhook
      POSTGRES_PASSWORD: webhook123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U webhook -d webhook_state"]
      interval: 10s
      timeout: 5s
      retries: 5 
  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    ports:
      - "9010:9000"  
      - "8123:8123"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

# Huly legacy services
  mongodb:
    image: 'mongo:7-jammy'
    container_name: mongodb
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    healthcheck:
      test: echo "try { db.currentOp().ok } catch (err) { }" | mongosh --port 27017 --quiet
      interval: 5s
      timeout: 30s
      start_period: 0s
      start_interval: 1s
      retries: 30
    environment:
      - PUID=1000
      - PGID=1000
    volumes:
      - db:/data/db
    ports:
      - 27017:27017
    restart: unless-stopped
  minio:
    image: 'minio/minio'
    command: server /data --address ":9000" --console-address ":9001"
    ports:
      - 9000:9000
      - 9001:9001
    volumes:
      - files:/data
    restart: unless-stopped
  elastic:
    image: 'elasticsearch:7.14.2'
    command: |
      /bin/sh -c "./bin/elasticsearch-plugin list | grep -q ingest-attachment || yes | ./bin/elasticsearch-plugin install --silent ingest-attachment;      
      /usr/local/bin/docker-entrypoint.sh eswrapper"
    volumes:
      - elastic:/usr/share/elasticsearch/data
    ports:
      - 9200:9200
    environment:
      - ELASTICSEARCH_PORT_NUMBER=9200
      - BITNAMI_DEBUG=true
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1024m -Xmx1024m
      - http.cors.enabled=true
      - http.cors.allow-origin=http://localhost:8082
    healthcheck:
      interval: 20s
      retries: 10
      test: curl -s http://localhost:9200/_cluster/health | grep -vq '"status":"red"'
    restart: unless-stopped
  account:
    image: hardcoreeng/account
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    links:
      - mongodb
      - minio
      - stats
    ports:
      - 3000:3000
    volumes:
      - ./branding.json:/var/cfg/branding.json
    environment:
      - ACCOUNT_PORT=3000
      - SERVER_SECRET=secret
      - WORKSPACE_LIMIT_PER_USER=10000
      - STATS_URL=http://host.docker.internal:4900
      # - DB_URL=*******************************************
      - DB_URL=${MONGO_URL}
      # - DB_NS=account-2
      # Pass only one region to disallow selection for new workspaces.Ø
      - REGION_INFO=|Mongo;pg|Postgres;cockroach|CockroachDB
      # - REGION_INFO=cockroach|CockroachDB
      - TRANSACTOR_URL=ws://host.docker.internal:3333,ws://host.docker.internal:3331;;pg,ws://host.docker.internal:3332;;cockroach,
      - MAIL_URL=
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - FRONT_URL=http://host.docker.internal:8087
      - RESERVED_DB_NAMES=telegram,gmail,github
      - MODEL_ENABLED=*
      - LAST_NAME_FIRST=true
      # - WS_LIVENESS_DAYS=1
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - BRANDING_PATH=/var/cfg/branding.json
      # - DISABLE_SIGNUP=true
      # - INIT_SCRIPT_URL=https://raw.githubusercontent.com/hcengineering/init/main/script.yaml
      # - INIT_WORKSPACE=onboarding
    restart: unless-stopped
  stats:
    image: hardcoreeng/stats
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - 4900:4900
    environment:
      - PORT=4900
      - SERVER_SECRET=secret
    restart: unless-stopped
  workspace:
    image: hardcoreeng/workspace
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    links:
      - mongodb
      - minio
      - stats
    volumes:
      - ./branding.json:/var/cfg/branding.json
    environment:
      - WS_OPERATION=all+backup
      - SERVER_SECRET=secret
      - DB_URL=${MONGO_URL}
      - STATS_URL=http://host.docker.internal:4900
      - MAIL_URL=
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - RESERVED_DB_NAMES=telegram,gmail,github
      - MODEL_ENABLED=*
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - BRANDING_PATH=/var/cfg/branding.json
      # - PARALLEL=2
      - INIT_WORKSPACE=test
      - BACKUP_STORAGE=${BACKUP_STORAGE_CONFIG}
      - BACKUP_BUCKET=${BACKUP_BUCKET_NAME}
    restart: unless-stopped
  collaborator:
    image: hardcoreeng/collaborator
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    links:
      - mongodb
      - minio
      - transactor
      - stats
    ports:
      - 3078:3078
    environment:
      - COLLABORATOR_PORT=3078
      - SECRET=secret
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - STATS_URL=http://host.docker.internal:4900
    restart: unless-stopped
  front:
    image: hardcoreeng/front
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    links:
      - mongodb
      - minio
      - elastic
      - transactor
      - collaborator
      - stats
    ports:
      - 8087:8080
      - 8088:8080
    environment:
      - SERVER_PORT=8080
      - SERVER_SECRET=secret
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - STATS_URL=http://host.docker.internal:4900
      - UPLOAD_URL=/files
      - GMAIL_URL=http://host.docker.internal:8088
      - CALENDAR_URL=http://host.docker.internal:8095
      - TELEGRAM_URL=http://host.docker.internal:8086
      - REKONI_URL=http://host.docker.internal:4004
      - COLLABORATOR_URL=ws://host.docker.internal:3078
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - GITHUB_URL=http://host.docker.internal:3500
      - PRINT_URL=http://host.docker.internal:4005
      - SIGN_URL=http://host.docker.internal:4006
      - ANALYTICS_COLLECTOR_URL=http://host.docker.internal:4017
      - DESKTOP_UPDATES_URL=https://dist.huly.io
      - DESKTOP_UPDATES_CHANNEL=dev
      - BRANDING_URL=http://host.docker.internal:8087/branding.json
      # - DISABLE_SIGNUP=true
    restart: unless-stopped
  transactor:
    image: hardcoreeng/transactor
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    links:
      - mongodb
      - minio
      - account
      - stats
      # - apm-server
    ports:
      - 3333:3333
    volumes:
      - ./branding.json:/var/cfg/branding.json
    environment:
      # - SERVER_PROVIDER=uweb
      # - UWS_HTTP_MAX_HEADERS_SIZE="32768"
      - UV_THREADPOOL_SIZE=10
      - SERVER_PORT=3333
      - SERVER_SECRET=secret
      - ENABLE_COMPRESSION=false
      - STATS_URL=http://host.docker.internal:4900
      - FULLTEXT_URL=http://host.docker.internal:4700
      # - DB_URL=*******************************************
      - DB_URL=${MONGO_URL}
      - MONGO_URL=${MONGO_URL}
      - 'MONGO_OPTIONS={"appName": "transactor", "maxPoolSize": 10}'
      - METRICS_CONSOLE=false
      - METRICS_FILE=metrics.txt
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - FRONT_URL=http://host.docker.internal:8087
      # - APM_SERVER_URL=http://apm-server:8200
      - MAIL_URL=''
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - LAST_NAME_FIRST=true
      - BRANDING_PATH=/var/cfg/branding.json
      - SUPPORT_WORKSPACE=support
      - AI_BOT_URL=http://host.docker.internal:4010
    restart: unless-stopped 
  rekoni:
    image: hardcoreeng/rekoni-service
    restart: unless-stopped
    ports:
      - 4004:4004
  fulltext:
    image: hardcoreeng/fulltext
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    links:
      - elastic
      - mongodb
    ports:
      - 4700:4700
    environment:
      - SERVER_SECRET=secret
      - DB_URL=${MONGO_URL}
      - FULLTEXT_DB_URL=http://host.docker.internal:9200
      - ELASTIC_INDEX_NAME=local_storage_index
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - STATS_URL=http://host.docker.internal:4900
      - REKONI_URL=http://host.docker.internal:4004
      - ACCOUNTS_URL=http://host.docker.internal:3000
  print:
    image: hardcoreeng/print
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    ports:
      - 4005:4005
    environment:
      - SECRET=secret
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - STATS_URL=http://host.docker.internal:4900
  sign:
    image: hardcoreeng/sign
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    ports:
      - 4006:4006
    volumes:
      - ../services/sign/pod-sign/debug/certificate.p12:/var/cfg/certificate.p12
      - ../services/sign/pod-sign/debug/branding.json:/var/cfg/branding.json
    environment:
      - SECRET=secret
      - MINIO_ENDPOINT=minio
      - MINIO_ACCESS_KEY=minioadmin
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - MINIO_SECRET_KEY=minioadmin
      - CERTIFICATE_PATH=/var/cfg/certificate.p12
      - SERVICE_ID=sign-service
      - BRANDING_PATH=/var/cfg/branding.json
      - STATS_URL=http://host.docker.internal:4900
  analytics:
    image: hardcoreeng/analytics-collector
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    ports:
      - 4017:4017
    environment:
      - SECRET=secret
      - PORT=4017
      - MONGO_URL=${MONGO_URL}
      - 'MONGO_OPTIONS={"appName":"analytics","maxPoolSize":1}'
      - SERVICE_ID=analytics-collector-service
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - SUPPORT_WORKSPACE=support
      - STATS_URL=http://host.docker.internal:4900
  aiBot:
    image: hardcoreeng/ai-bot
    ports:
      - 4010:4010
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    restart: unless-stopped
    environment:
      - SERVER_SECRET=secret
      - MONGO_URL=${MONGO_URL}
      - ACCOUNTS_URL=http://host.docker.internal:3000
      - SUPPORT_WORKSPACE=support
      - STORAGE_CONFIG=${STORAGE_CONFIG}
      - FIRST_NAME=Jolie
      - LAST_NAME=AI
      - PASSWORD=password
      - AVATAR_PATH=./avatar.png
      - AVATAR_CONTENT_TYPE=.png
      - STATS_URL=http://host.docker.internal:4900
#      - LOVE_ENDPOINT=http://host.docker.internal:8096
#      - OPENAI_API_KEY=token
#  telegram-bot:
#    image: hardcoreeng/telegram-bot
#   extra_hosts:
#     - "host.docker.internal:host-gateway"
#    restart: unless-stopped
#    environment:
#      - PORT=4020
#      - BOT_TOKEN=token
#      - MONGO_URL=${MONGO_URL}
#      - MONGO_DB=telegram-bot
#      - SECRET=secret
#      - DOMAIN=domain
#      - ACCOUNTS_URL=http://host.docker.internal:3000
#      - SERVICE_ID=telegram-bot-service
#      - STATS_URL=http://host.docker.internal:4900
  # export:
  #   image: hardcoreeng/export
  #   extra_hosts:
  #     - 'host.docker.internal:host-gateway'
  #   links:
  #     - mongodb
  #     - minio
  #     - stats
  #   ports:
  #     - 4009:4009
  #   environment:
  #     - PORT=4009
  #     - SECRET=secret
  #     - SERVICE_ID=export-service
  #     - DB_URL=${MONGO_URL}
  #     - STATS_URL=http://host.docker.internal:4900
  #     - STORAGE_CONFIG=${STORAGE_CONFIG}
  #     - ACCOUNTS_URL=http://host.docker.internal:3000

volumes:
  db:
  files:
  elastic:
  clickhouse:
  postgres_data: