<yandex>
  <remote_servers>
    <default>
      <!-- 3 shards x 3 replicas -->
      <shard>
        <internal_replication>true</internal_replication>
        <replica>
          <host>ch1</host>
          <port>9000</port>
        </replica>
        <replica>
          <host>ch2</host>
          <port>9000</port>
        </replica>
        <replica>
          <host>ch3</host>
          <port>9000</port>
        </replica>
      </shard>

      <shard>
        <internal_replication>true</internal_replication>
        <replica>
          <host>ch4</host>
          <port>9000</port>
        </replica>
        <replica>
          <host>ch5</host>
          <port>9000</port>
        </replica>
        <replica>
          <host>ch6</host>
          <port>9000</port>
        </replica>
      </shard>

      <shard>
        <internal_replication>true</internal_replication>
        <replica>
          <host>ch7</host>
          <port>9000</port>
        </replica>
        <replica>
          <host>ch8</host>
          <port>9000</port>
        </replica>
        <replica>
          <host>ch9</host>
          <port>9000</port>
        </replica>
      </shard>
    </default>
  </remote_servers>

  <zookeeper>
    <node>
      <host>zookeeper</host>
      <port>2181</port>
    </node>
  </zookeeper>
</yandex>

