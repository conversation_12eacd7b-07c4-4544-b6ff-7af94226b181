//
// Copyright © 2023 Hardcore Engineering Inc.
//
import { serveAccount } from '@hcengineering/account-service'
import { Analytics } from '@hcengineering/analytics'
import { configureAnalytics, SplitLogger } from '@hcengineering/analytics-service'
import { MeasureMetricsContext, newMetrics } from '@hcengineering/core'
import { initStatisticsContext, loadBrandingMap } from '@hcengineering/server-core'
import { join } from 'path'

/**
 * Wait until a dependant HTTP service becomes reachable.
 * This is useful when our container may start faster than the
 * dependant one (e.g. stats).
 *
 * The function performs simple GET requests to the provided URL until
 * the connection succeeds or the timeout expires.
 */
async function waitForService (
  url: string | undefined,
  {
    timeoutMs = 30_000,
    intervalMs = 2_000
  }: { timeoutMs?: number, intervalMs?: number } = {}
): Promise<void> {
  if (url === undefined || url === '') {
    return
  }

  const start = Date.now()
  /* eslint-disable no-constant-condition */
  while (true) {
    try {
      // We don't care about response code here, a successful TCP connection
      // (i.e. fetch resolved) is enough to consider the service up.
      await fetch(url, { method: 'GET' })
      return
    } catch (err) {
      // Connection error – service is probably still starting.
    }

    if (Date.now() - start > timeoutMs) {
      console.warn(`Timed out waiting for service ${url}. Continuing startup.`)
      return
    }
    await new Promise((resolve) => setTimeout(resolve, intervalMs))
  }
  /* eslint-enable no-constant-condition */
}

configureAnalytics(process.env.SENTRY_DSN, {})
Analytics.setTag('application', 'account')

void (async () => {
  // Ensure stats service is reachable before we start sending metrics
  await waitForService(process.env.STATS_URL)

  const metricsContext = initStatisticsContext('account', {
    factory: () =>
      new MeasureMetricsContext(
        'account',
        {},
        {},
        newMetrics(),
        new SplitLogger('account', {
          root: join(process.cwd(), 'logs'),
          enableConsole: (process.env.ENABLE_CONSOLE ?? 'true') === 'true'
        })
      )
  })

  const brandingPath = process.env.BRANDING_PATH
  const brandings = loadBrandingMap(brandingPath)

  const { close, measureCtx } = serveAccount(metricsContext, brandings)

  process.on('uncaughtException', (e) => {
    measureCtx.error('uncaughtException', { error: e })
  })

  process.on('unhandledRejection', (reason, promise) => {
    measureCtx.error('Unhandled Rejection at:', { reason, promise })
  })

  process.on('SIGINT', () => {
    console.log('Received SIGINT. Shutting down gracefully...')
    close()
  })

  process.on('SIGTERM', () => {
    console.log('Received SIGTERM. Shutting down gracefully...')
    close()
  })
})()
