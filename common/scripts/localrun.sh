#!/usr/bin/env bash

set -euo pipefail

# Usage:
#  ./common/scripts/localrun.sh           # start 3 panes/windows
#  ./common/scripts/localrun.sh --update  # run rush update && rush validate first

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"

maybe_update() {
  if [[ "${1-}" == "--update" ]]; then
    echo "Running: rush update && rush validate"
    pushd "$ROOT_DIR" >/dev/null
    rush update
    rush validate
    popd >/dev/null
  fi
}

run_with_tmux() {
  local session_name="huly-local"

  # Kill existing session if it's stale
  if command -v tmux >/dev/null 2>&1 && tmux has-session -t "$session_name" 2>/dev/null; then
    echo "Existing tmux session '$session_name' detected. Attaching to it."
    tmux attach -t "$session_name"
    return 0
  fi

  echo "Starting tmux session '$session_name' with 3 windows..."

  tmux new-session -d -s "$session_name" -n account -c "$ROOT_DIR/pods/account" \
    'npm run run-local'

  tmux new-window -t "$session_name" -n server -c "$ROOT_DIR/pods/server" \
    'npm run run-local'

  tmux new-window -t "$session_name" -n prod -c "$ROOT_DIR/dev/prod" \
    'rushx dev-server'

  tmux select-window -t "$session_name:account"
  tmux attach -t "$session_name"
}

run_with_macos_terminal() {
  # Fallback for macOS if tmux is not available
  if [[ "$(uname -s)" != "Darwin" ]]; then
    return 1
  fi

  if ! command -v osascript >/dev/null 2>&1; then
    return 1
  fi

  echo "Launching three macOS Terminal windows..."
  osascript <<OSA
tell application "Terminal"
  do script "cd \"$ROOT_DIR/pods/account\"; npm run run-local"
  do script "cd \"$ROOT_DIR/pods/server\"; npm run run-local"
  do script "cd \"$ROOT_DIR/dev/prod\"; rushx dev-server"
  activate
end tell
OSA
}

main() {
  maybe_update "${1-}"

  if command -v tmux >/dev/null 2>&1; then
    run_with_tmux
    exit 0
  fi

  if run_with_macos_terminal; then
    exit 0
  fi

  echo "tmux not found and macOS Terminal fallback unavailable."
  echo "Running all processes in the current shell (use Ctrl-C to stop):"
  echo " - pods/account: npm run run-local"
  echo " - pods/server:  npm run run-local"
  echo " - dev/prod:     rushx dev-server"

  # As a last resort, run them sequentially in background; logs mixed in this shell.
  (cd "$ROOT_DIR/pods/account" && npm run run-local) &
  (cd "$ROOT_DIR/pods/server" && npm run run-local) &
  (cd "$ROOT_DIR/dev/prod" && rushx dev-server) &

  wait
}

main "$@"


