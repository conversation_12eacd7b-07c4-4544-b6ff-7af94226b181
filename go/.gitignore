# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with the
# -o option to specify an output file.
*.out

# Go workspace file
go.work
go.work.sum

# Local development environment files
.env
.env.*

# IDE settings
.vscode/
.idea/
*.swp
*~

# Compiled Go files
# This will ignore the bin directory in any of the microservices
**/bin
**/tmp

# Log files
*.log

# Coverage outputs
coverage.out
/coverage/

# Vendored dependencies
vendor/ 