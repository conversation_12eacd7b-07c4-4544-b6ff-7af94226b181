package tests

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"webhook_microservice/internal/api/middleware"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestValidatorMiddleware_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		config         middleware.ValidatorConfig
		headers        map[string]string
		method         string
		contentType    string
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Valid request with all required headers",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-Delivery-ID": "generic",
				},
				RequiredHeaders: []string{"Content-Type"},
			},
			headers: map[string]string{
				"X-Delivery-ID": "test-123",
				"Content-Type":  "application/json",
			},
			method:         "POST",
			expectedStatus: http.StatusOK,
		},
		{
			name: "Missing required header",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-Delivery-ID": "generic",
				},
				RequiredHeaders: []string{"Content-Type", "X-Source"},
			},
			headers: map[string]string{
				"X-Delivery-ID": "test-123",
				"Content-Type":  "application/json",
			},
			method:         "POST",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "A required header is missing",
		},
		{
			name: "Alternative delivery ID header (X-GitHub-Delivery)",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-GitHub-Delivery": "github",
				},
				RequiredHeaders: []string{"Content-Type"},
			},
			headers: map[string]string{
				"X-GitHub-Delivery": "github-123",
				"Content-Type":      "application/json",
			},
			method:         "POST",
			expectedStatus: http.StatusOK,
		},
		{
			name: "Alternative delivery ID header (X-Hook-ID)",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-Hook-ID": "gitlab",
				},
				RequiredHeaders: []string{"Content-Type"},
			},
			headers: map[string]string{
				"X-Hook-ID":    "hook-123",
				"Content-Type": "application/json",
			},
			method:         "POST",
			expectedStatus: http.StatusOK,
		},
		{
			name: "Missing Content-Type for POST",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-Delivery-ID": "generic",
				},
				RequiredHeaders: []string{"Content-Type"},
			},
			headers: map[string]string{
				"X-Delivery-ID": "test-123",
			},
			method:         "POST",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "A required header is missing",
		},
		{
			name: "Unsupported Content-Type",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-Delivery-ID": "generic",
				},
				RequiredHeaders: []string{},
			},
			headers: map[string]string{
				"X-Delivery-ID": "test-123",
				"Content-Type":  "application/xml",
			},
			method:         "POST",
			expectedStatus: http.StatusUnsupportedMediaType,
			expectedError:  "application/xml",
		},
		{
			name: "Valid Content-Type variations",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-Delivery-ID": "generic",
				},
				RequiredHeaders: []string{},
			},
			headers: map[string]string{
				"X-Delivery-ID": "test-123",
				"Content-Type":  "application/json; charset=utf-8",
			},
			method:         "POST",
			expectedStatus: http.StatusOK,
		},
		{
			name: "GET request without Content-Type (should pass)",
			config: middleware.ValidatorConfig{
				DeliveryIDHeaders: map[string]string{
					"X-Delivery-ID": "generic",
				},
				RequiredHeaders: []string{},
			},
			headers: map[string]string{
				"X-Delivery-ID": "test-123",
			},
			method:         "GET",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create router with middleware
			router := gin.New()
			router.Use(middleware.ValidatorMiddleware(tt.config))
			router.Any("/test", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"status": "ok"})
			})

			// Create request
			req := httptest.NewRequest(tt.method, "/test", strings.NewReader("{}"))
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// Execute request
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert status
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Assert error message if expected
			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestSignatureValidatorMiddleware_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name      string
		headers   map[string]string
		expectSet bool
	}{
		{
			name: "X-Signature header",
			headers: map[string]string{
				"X-Signature": "sha256=abc123",
			},
			expectSet: true,
		},
		{
			name: "X-Hub-Signature header",
			headers: map[string]string{
				"X-Hub-Signature": "sha1=def456",
			},
			expectSet: true,
		},
		{
			name: "X-Hub-Signature-256 header",
			headers: map[string]string{
				"X-Hub-Signature-256": "sha256=ghi789",
			},
			expectSet: true,
		},
		{
			name:      "No signature headers",
			headers:   map[string]string{},
			expectSet: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create router with middleware
			router := gin.New()
			router.Use(middleware.SignatureValidatorMiddleware())
			router.POST("/test", func(c *gin.Context) {
				signature, exists := c.Get("webhook_signature")
				if tt.expectSet {
					assert.True(t, exists)
					assert.NotEmpty(t, signature)
				} else {
					assert.False(t, exists)
				}
				c.JSON(http.StatusOK, gin.H{"status": "ok"})
			})

			// Create request
			req := httptest.NewRequest("POST", "/test", strings.NewReader("{}"))
			for key, value := range tt.headers {
				req.Header.Set(key, value)
			}

			// Execute request
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Should always pass through
			assert.Equal(t, http.StatusOK, w.Code)
		})
	}
}

func TestWAFMiddleware_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		config         middleware.WAFConfig
		clientIP       string
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Empty allow list (allows all)",
			config: middleware.WAFConfig{
				AllowList:    []string{},
				RateLimitRPM: 60,
			},
			clientIP:       "***********",
			expectedStatus: http.StatusOK,
		},
		{
			name: "IP in allow list",
			config: middleware.WAFConfig{
				AllowList:    []string{"***********", "********"},
				RateLimitRPM: 60,
			},
			clientIP:       "***********",
			expectedStatus: http.StatusOK,
		},
		{
			name: "IP not in allow list",
			config: middleware.WAFConfig{
				AllowList:    []string{"***********", "********"},
				RateLimitRPM: 60,
			},
			clientIP:       "***********",
			expectedStatus: http.StatusForbidden,
			expectedError:  "IP not allowed",
		},
		{
			name: "CIDR range allows IP",
			config: middleware.WAFConfig{
				AllowList:    []string{"***********/24"},
				RateLimitRPM: 60,
			},
			clientIP:       "***********00",
			expectedStatus: http.StatusOK,
		},
		{
			name: "CIDR range blocks IP",
			config: middleware.WAFConfig{
				AllowList:    []string{"***********/24"},
				RateLimitRPM: 60,
			},
			clientIP:       "***********",
			expectedStatus: http.StatusForbidden,
			expectedError:  "IP not allowed",
		},
		{
			name: "Invalid CIDR (falls back to exact match)",
			config: middleware.WAFConfig{
				AllowList:    []string{"***********/invalid"},
				RateLimitRPM: 60,
			},
			clientIP:       "***********",
			expectedStatus: http.StatusForbidden,
			expectedError:  "IP not allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create router with middleware
			router := gin.New()
			router.Use(middleware.WAFMiddleware(tt.config))
			router.GET("/test", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"status": "ok"})
			})

			// Create request
			req := httptest.NewRequest("GET", "/test", nil)
			req.RemoteAddr = tt.clientIP + ":12345" // Add port to simulate real request

			// Execute request
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert status
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Assert error message if expected
			if tt.expectedError != "" {
				assert.Contains(t, w.Body.String(), tt.expectedError)
			}
		})
	}
}

func TestWAFMiddleware_RateLimit_Unit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create WAF with very low rate limit for testing
	config := middleware.WAFConfig{
		AllowList:    []string{}, // Allow all IPs
		RateLimitRPM: 1,          // 1 request per minute
	}

	// Create router with middleware
	router := gin.New()
	router.Use(middleware.WAFMiddleware(config))
	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// First request should succeed
	req1 := httptest.NewRequest("GET", "/test", nil)
	req1.RemoteAddr = "***********:12345"
	w1 := httptest.NewRecorder()
	router.ServeHTTP(w1, req1)
	assert.Equal(t, http.StatusOK, w1.Code)

	// Second request should be rate limited
	req2 := httptest.NewRequest("GET", "/test", nil)
	req2.RemoteAddr = "***********:12345"
	w2 := httptest.NewRecorder()
	router.ServeHTTP(w2, req2)
	assert.Equal(t, http.StatusTooManyRequests, w2.Code)
	assert.Contains(t, w2.Body.String(), "Rate limit exceeded")
}
