package tests

import (
	"os"
	"strings"
	"testing"
	"time"

	"webhook_microservice/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// clearAllEnv clears all webhook-related environment variables
func clearAllEnv() {
	envVars := []string{
		// Server
		"WEBHOOK_SERVER_PORT",
		"WEBHOOK_SERVER_HOST",
		// Environment
		"WEBHOOK_ENVIRONMENT",
		// Logging
		"WEBHOOK_LOGGING_LEVEL",
		// Observability
		"WEBHOOK_OBSERVABILITY_PROMETHEUS_ENABLED",
		"WEBHOOK_OBSERVABILITY_PROMETHEUS_PATH",
		"WEBHOOK_OBSERVABILITY_TRACING_ENABLED",
		"WEBHOOK_OBSERVABILITY_TRACING_JAEGER",
		// Dapr
		"WEBHOOK_DAPR_ENABLED",
		"WEBHOOK_DAPR_APP_ID",
		"WEBHOOK_DAPR_HTTP_PORT",
		"WEBHOOK_DAPR_GRPC_PORT",
		"WEBHOOK_DAPR_STATE_STORE",
		"WEBHOOK_DAPR_PUBSUB_NAME",
		"WEBHOOK_DAPR_SECRET_STORE",
		// Webhook
		"WEBHOOK_WEBHOOK_TOPIC_NAME",
		"WEBHOOK_WEBHOOK_RATE_LIMIT_RPM",
		"WEBHOOK_WEBHOOK_WAF_ALLOW_LIST",
		"WEBHOOK_WEBHOOK_REQUIRED_HEADERS",
		// Redis
		"WEBHOOK_REDIS_ADDR",
		"WEBHOOK_REDIS_PASSWORD",
		"WEBHOOK_REDIS_DB",
		"WEBHOOK_REDIS_POOL_SIZE",
		"WEBHOOK_REDIS_MIN_IDLE_CONNS",
		"WEBHOOK_REDIS_MAX_RETRIES",
		"WEBHOOK_REDIS_KEY_PREFIX",
		"WEBHOOK_REDIS_DEFAULT_TTL",
		// BambooHR
		"WEBHOOK_BAMBOO_HR_API_KEY",
		"WEBHOOK_BAMBOO_HR_COMPANY_DOMAIN",
		"WEBHOOK_BAMBOO_HR_API_VERSION",
		"WEBHOOK_BAMBOO_HR_ROUTES_ENABLED",
		"WEBHOOK_BAMBOO_HR_ROUTES_PREFIX",
		"WEBHOOK_BAMBOO_HR_ROUTES_RATE_LIMIT_RPM",
		"WEBHOOK_BAMBOO_HR_ROUTES_WAF_ALLOW_LIST",
		// Auth
		"WEBHOOK_AUTH_ENABLED",
		"WEBHOOK_AUTH_ACCOUNT_SERVICE_URL",
		"WEBHOOK_AUTH_TIMEOUT",
		// Legacy variables for backward compatibility
		"WEBHOOK_PORT",
		"WEBHOOK_PUBSUB_NAME",
		"WEBHOOK_TOPIC_NAME",
		"WEBHOOK_STATE_STORE",
		"WEBHOOK_RATE_LIMIT_RPM",
		"WEBHOOK_WAF_ALLOW_LIST",
	}

	for _, envVar := range envVars {
		os.Unsetenv(envVar)
	}
}

func TestLoadConfig_StructuredConfiguration_Unit(t *testing.T) {
	// Clear environment first
	clearAllEnv()

	// Set structured environment variables
	envVars := map[string]string{
		"WEBHOOK_SERVER_PORT":            "9090",
		"WEBHOOK_SERVER_HOST":            "127.0.0.1",
		"WEBHOOK_ENVIRONMENT":            "production",
		"WEBHOOK_LOGGING_LEVEL":          "warn",
		"WEBHOOK_DAPR_ENABLED":           "false",
		"WEBHOOK_WEBHOOK_RATE_LIMIT_RPM": "500",
		// Auth configuration (enabled with service URL)
		"WEBHOOK_AUTH_ENABLED":             "true",
		"WEBHOOK_AUTH_ACCOUNT_SERVICE_URL": "http://localhost:3000",
		// BambooHR configuration
		"WEBHOOK_BAMBOO_HR_API_KEY":               "test-api-key-12345",
		"WEBHOOK_BAMBOO_HR_COMPANY_DOMAIN":        "testcompany",
		"WEBHOOK_BAMBOO_HR_API_VERSION":           "v1",
		"WEBHOOK_BAMBOO_HR_ROUTES_ENABLED":        "true",
		"WEBHOOK_BAMBOO_HR_ROUTES_PREFIX":         "/api/bamboo",
		"WEBHOOK_BAMBOO_HR_ROUTES_RATE_LIMIT_RPM": "100",
	}

	for key, value := range envVars {
		os.Setenv(key, value)
	}
	defer clearAllEnv()

	cfg, err := config.LoadConfig()
	require.NoError(t, err)

	// Assert structured configuration
	assert.Equal(t, 9090, cfg.Server.Port)
	assert.Equal(t, "127.0.0.1", cfg.Server.Host)
	assert.Equal(t, "production", cfg.Environment)
	assert.Equal(t, "warn", cfg.Logging.Level)
	assert.Equal(t, false, cfg.Dapr.Enabled)
	assert.Equal(t, 500, cfg.Webhook.RateLimitRPM)

	// Assert BambooHR configuration
	assert.Equal(t, "test-api-key-12345", cfg.BambooHR.APIKey)
	assert.Equal(t, "testcompany", cfg.BambooHR.CompanyDomain)
	assert.Equal(t, "v1", cfg.BambooHR.APIVersion)
	assert.Equal(t, true, cfg.BambooHR.Routes.Enabled)
	assert.Equal(t, "/api/bamboo", cfg.BambooHR.Routes.Prefix)
	assert.Equal(t, 100, cfg.BambooHR.Routes.RateLimitRPM)

	// Assert Auth configuration
	assert.Equal(t, true, cfg.Auth.Enabled)
	assert.Equal(t, "http://localhost:3000", cfg.Auth.AccountServiceURL)
}

func TestConfigValidation_Unit(t *testing.T) {
	tests := []struct {
		name    string
		config  config.Config
		wantErr bool
		errMsg  string
	}{
		{
			name: "Valid configuration",
			config: config.Config{
				Server: config.ServerConfig{Port: 8080, Host: "0.0.0.0"},
				Webhook: config.WebhookConfig{
					TopicName:    "test-topic",
					RateLimitRPM: 1000,
					WAFAllowList: []string{"***********/24"},
				},
				Redis: config.RedisConfig{
					Addr:     "localhost:6379",
					PoolSize: 10,
				},
				BambooHR: config.BambooHRConfig{
					APIKey:        "test-api-key",
					CompanyDomain: "testcompany",
					APIVersion:    "v1",
					Routes: config.BambooHRRoutesConfig{
						Enabled:      true,
						Prefix:       "/api/bamboo",
						RateLimitRPM: 100,
					},
				},
				Auth: config.AuthConfig{
					Enabled:           true,
					AccountServiceURL: "http://localhost:3000",
				},
			},
			wantErr: false,
		},
		{
			name: "Invalid port",
			config: config.Config{
				Server:  config.ServerConfig{Port: 99, Host: "0.0.0.0"},
				Webhook: config.WebhookConfig{TopicName: "test", RateLimitRPM: 1000},
				Redis:   config.RedisConfig{Addr: "localhost:6379", PoolSize: 10},
				BambooHR: config.BambooHRConfig{
					APIKey:        "test-api-key",
					CompanyDomain: "testcompany",
					APIVersion:    "v1",
					Routes: config.BambooHRRoutesConfig{
						Enabled:      true,
						Prefix:       "/api/bamboo",
						RateLimitRPM: 100,
					},
				},
				Auth: config.AuthConfig{
					Enabled:           true,
					AccountServiceURL: "http://localhost:3000",
				},
			},
			wantErr: true,
			errMsg:  "invalid port 99",
		},
		{
			name: "Invalid CIDR",
			config: config.Config{
				Server: config.ServerConfig{Port: 8080, Host: "0.0.0.0"},
				Webhook: config.WebhookConfig{
					TopicName:    "test",
					RateLimitRPM: 1000,
					WAFAllowList: []string{"invalid-cidr"},
				},
				Redis: config.RedisConfig{Addr: "localhost:6379", PoolSize: 10},
				BambooHR: config.BambooHRConfig{
					APIKey:        "test-api-key",
					CompanyDomain: "testcompany",
					APIVersion:    "v1",
					Routes: config.BambooHRRoutesConfig{
						Enabled:      true,
						Prefix:       "/api/bamboo",
						RateLimitRPM: 100,
					},
				},
				Auth: config.AuthConfig{
					Enabled:           true,
					AccountServiceURL: "http://localhost:3000",
				},
			},
			wantErr: true,
			errMsg:  "invalid IP in waf_allow_list",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConfig_DefaultValues_Unit(t *testing.T) {
	// Clear all environment variables
	clearAllEnv()
	defer clearAllEnv()

	// Enable auth and provide required config for this test
	os.Setenv("WEBHOOK_AUTH_ENABLED", "true")
	os.Setenv("WEBHOOK_AUTH_ACCOUNT_SERVICE_URL", "http://localhost:3000")
	os.Setenv("WEBHOOK_BAMBOO_HR_API_KEY", "test-key")

	cfg, err := config.LoadConfig()
	require.NoError(t, err)

	// Assert default values
	assert.Equal(t, 2021, cfg.Server.Port)
	assert.Equal(t, "0.0.0.0", cfg.Server.Host)
	assert.Equal(t, "development", cfg.Environment)
	assert.Equal(t, "info", cfg.Logging.Level)
	assert.True(t, cfg.Observability.Prometheus.Enabled)
	assert.Equal(t, "/metrics", cfg.Observability.Prometheus.Path)
	assert.False(t, cfg.Observability.Tracing.Enabled)
	assert.Equal(t, "http://localhost:14268/api/traces", cfg.Observability.Tracing.Jaeger)
	assert.True(t, cfg.Dapr.Enabled)
	assert.Equal(t, "webhook-microservice", cfg.Dapr.AppID)
	assert.Equal(t, 3500, cfg.Dapr.HTTPPort)
	assert.Equal(t, 50001, cfg.Dapr.GRPCPort)
	assert.Equal(t, "statestore", cfg.Dapr.StateStore)
	assert.Equal(t, "kafka-pubsub", cfg.Dapr.PubSubName)
	assert.Equal(t, "config-secret-store", cfg.Dapr.SecretStore)
	assert.Equal(t, "webhook-events", cfg.Webhook.TopicName)
	assert.Equal(t, 1000, cfg.Webhook.RateLimitRPM)
	assert.Equal(t, []string{"0.0.0.0/0"}, cfg.Webhook.WAFAllowList)
	assert.Equal(t, []string{"Content-Type"}, cfg.Webhook.Validator.RequiredHeaders)
	// Assert Auth configuration defaults
	assert.True(t, cfg.Auth.Enabled)
	assert.Equal(t, "http://localhost:3000", cfg.Auth.AccountServiceURL)
}

func TestConfig_EnvironmentOverrides_Unit(t *testing.T) {
	// Clear environment first
	clearAllEnv()

	// Set specific environment variables
	os.Setenv("WEBHOOK_WEBHOOK_WAF_ALLOW_LIST", "***********,10.0.0.0/8")
	os.Setenv("WEBHOOK_WEBHOOK_REQUIRED_HEADERS", "X-Custom-Header,X-Another-Header")
	os.Setenv("WEBHOOK_REDIS_DEFAULT_TTL", "12h")
	// Enable auth and provide required config for this test
	os.Setenv("WEBHOOK_AUTH_ENABLED", "true")
	os.Setenv("WEBHOOK_AUTH_ACCOUNT_SERVICE_URL", "http://localhost:3000")
	os.Setenv("WEBHOOK_BAMBOO_HR_API_KEY", "test-key")
	defer clearAllEnv()

	cfg, err := config.LoadConfig()
	require.NoError(t, err)

	// Assert overridden values
	assert.Equal(t, []string{"***********", "10.0.0.0/8"}, cfg.Webhook.WAFAllowList)
	assert.Equal(t, []string{"X-Custom-Header", "X-Another-Header"}, cfg.Webhook.Validator.RequiredHeaders)
	assert.Equal(t, 12*time.Hour, cfg.Redis.DefaultTTL)

	// Other values should be defaults
	assert.Equal(t, 2021, cfg.Server.Port)
	assert.Equal(t, "0.0.0.0", cfg.Server.Host)
	assert.Equal(t, "development", cfg.Environment)
}

func TestConfig_BackwardCompatibility_Unit(t *testing.T) {
	// Clear environment first
	clearAllEnv()

	// Set old environment variables
	os.Setenv("WEBHOOK_PORT", "9090")
	os.Setenv("WEBHOOK_PUBSUB_NAME", "legacy-pubsub")
	os.Setenv("WEBHOOK_TOPIC_NAME", "legacy-topic")
	os.Setenv("WEBHOOK_STATE_STORE", "legacy-store")
	os.Setenv("WEBHOOK_RATE_LIMIT_RPM", "500")
	os.Setenv("WEBHOOK_WAF_ALLOW_LIST", "***********,10.0.0.0/8")
	// Enable auth and provide required config for this test
	os.Setenv("WEBHOOK_AUTH_ENABLED", "true")
	os.Setenv("WEBHOOK_AUTH_ACCOUNT_SERVICE_URL", "http://localhost:3000")
	os.Setenv("WEBHOOK_BAMBOO_HR_API_KEY", "test-key")
	defer clearAllEnv()

	cfg, err := config.LoadConfig()
	require.NoError(t, err)

	// Assert that old variables are mapped to new structure
	assert.Equal(t, 9090, cfg.Server.Port)
	assert.Equal(t, "legacy-pubsub", cfg.Dapr.PubSubName)
	assert.Equal(t, "legacy-topic", cfg.Webhook.TopicName)
	assert.Equal(t, "legacy-store", cfg.Dapr.StateStore)
	assert.Equal(t, 500, cfg.Webhook.RateLimitRPM)
	assert.Equal(t, []string{"***********", "10.0.0.0/8"}, cfg.Webhook.WAFAllowList)
}

func TestConfig_InvalidDuration_Unit(t *testing.T) {
	// Clear environment first
	clearAllEnv()

	// Set invalid duration
	os.Setenv("WEBHOOK_REDIS_DEFAULT_TTL", "invalid")
	// Enable auth and provide required config for this test
	os.Setenv("WEBHOOK_AUTH_ENABLED", "true")
	os.Setenv("WEBHOOK_AUTH_ACCOUNT_SERVICE_URL", "http://localhost:3000")
	os.Setenv("WEBHOOK_BAMBOO_HR_API_KEY", "test-key")
	defer clearAllEnv()

	cfg, err := config.LoadConfig()
	// This test expects an error during config loading due to invalid duration
	if err != nil {
		assert.Contains(t, err.Error(), "time: invalid duration")
		return
	}

	// If no error during loading, check that default is used
	require.NoError(t, err)

	// Should use default duration
	assert.Equal(t, 24*time.Hour, cfg.Redis.DefaultTTL)
}

func TestConfig_InvalidPort_Unit(t *testing.T) {
	// Clear environment first
	clearAllEnv()

	// Test cases for invalid ports
	invalidPorts := []string{
		"0",      // Too low
		"1023",   // Below minimum
		"65536",  // Above maximum
		"abc",    // Not a number
		"-1",     // Negative
		"999999", // Way too high
	}

	for _, port := range invalidPorts {
		t.Run("Invalid port "+port, func(t *testing.T) {
			// Clear environment and set up required configs
			clearAllEnv()
			os.Setenv("WEBHOOK_SERVER_PORT", port)
			os.Setenv("WEBHOOK_AUTH_ENABLED", "true")
			os.Setenv("WEBHOOK_AUTH_ACCOUNT_SERVICE_URL", "http://localhost:3000")
			os.Setenv("WEBHOOK_BAMBOO_HR_API_KEY", "test-key")

			cfg, err := config.LoadConfig()
			if err == nil {
				err = cfg.Validate()
			}
			assert.Error(t, err)
			// Accept either configuration parsing errors or validation errors
			errorMsg := err.Error()
			hasInvalidPort := strings.Contains(errorMsg, "invalid port") ||
				strings.Contains(errorMsg, "cannot parse") ||
				strings.Contains(errorMsg, "invalid syntax")
			assert.True(t, hasInvalidPort, "Expected error about invalid port, got: %s", errorMsg)
		})
	}
}
