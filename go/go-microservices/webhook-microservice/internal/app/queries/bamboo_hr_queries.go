package queries

import (
	"context"
	"log/slog"
	"webhook_microservice/internal/app"
	"webhook_microservice/internal/domain"
)

// BambooHRQuery is the base interface for all BambooHR queries
type BambooHRQuery interface {
	QueryType() string
}

// GetWhosOutQuery represents a query to get all time off information
type GetWhosOutQuery struct {
	// Future: could add filtering options like date range, employee IDs, etc.
}

func (q GetWhosOutQuery) QueryType() string { return "whos_out" }

// GetCurrentlyOutQuery represents a query to get only currently out employees
type GetCurrentlyOutQuery struct {
	// Future: could add filtering options like date range, employee IDs, etc.
}

func (q GetCurrentlyOutQuery) QueryType() string { return "currently_out" }

// BambooHRQueryHandler handles BambooHR-related queries using the service layer
type BambooHRQueryHandler struct {
	bambooHRService *app.BambooHRService
	logger          *slog.Logger
}

// NewBambooHRQueryHandler creates a new BambooHRQueryHandler
func NewBambooHRQueryHandler(
	bambooHRService *app.BambooHRService,
	logger *slog.Logger,
) *BambooHRQueryHandler {
	return &BambooHRQueryHandler{
		bambooHRService: bambooHRService,
		logger:          logger,
	}
}

// Handle processes any BambooHR query and decides behavior based on query type
func (h *BambooHRQueryHandler) Handle(ctx context.Context, query BambooHRQuery) (*domain.EnrichedWhosOutResponse, error) {
	h.logger.Info("Processing BambooHR query", "query_type", query.QueryType())

	// Decide behavior based on query type using type switch
	switch query.(type) {
	case GetWhosOutQuery:
		return h.handleWhosOut(ctx)
	case GetCurrentlyOutQuery:
		return h.handleCurrentlyOut(ctx)
	default:
		h.logger.Error("Unknown query type", "query_type", query.QueryType())
		return nil, app.NewBambooHRError("unsupported query type", nil)
	}
}

// handleWhosOut processes the GetWhosOutQuery - returns ALL enriched time off data
func (h *BambooHRQueryHandler) handleWhosOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, error) {
	h.logger.Info("Processing GetWhosOut query - fetching all enriched time off data")

	// Use service to get enriched data
	response, err := h.bambooHRService.GetEnrichedWhosOut(ctx)
	if err != nil {
		h.logger.Error("Failed to process GetWhosOut query", "error", err)
		return nil, app.NewBambooHRError("failed to fetch enriched who's out data", err)
	}

	if response != nil {
		h.logger.Info("GetWhosOut query processed successfully", "total_records", len(*response))
	}
	return response, nil
}

// handleCurrentlyOut processes the GetCurrentlyOutQuery - returns enriched currently out data
func (h *BambooHRQueryHandler) handleCurrentlyOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, error) {
	h.logger.Info("Processing GetCurrentlyOut query - fetching enriched currently out data")

	// Use service to get enriched currently out data
	response, err := h.bambooHRService.GetEnrichedCurrentlyOut(ctx)
	if err != nil {
		h.logger.Error("Failed to process GetCurrentlyOut query", "error", err)
		return nil, app.NewBambooHRError("failed to fetch enriched currently out data", err)
	}

	if response != nil {
		h.logger.Info("GetCurrentlyOut query processed successfully", "total_records", len(*response))
	}
	return response, nil
}
