package app

import (
	"context"
	"webhook_microservice/internal/domain"
)

// BambooHRClient defines the interface for BambooHR API operations
type BambooHRClient interface {
	GetWhosOut(ctx context.Context) (*domain.WhosOutResponse, error)
	GetUsers(ctx context.Context) (*domain.UsersResponse, error)
	GetTimeOffRequests(ctx context.Context, start, end string) (*domain.TimeOffRequestsResponse, error)
}
