package app

import (
	"context"
	"webhook_microservice/internal/domain"
)

// CacheService defines the interface for caching enriched BambooHR data
type CacheService interface {
	// GetEnrichedWhosOut retrieves cached enriched who's out data
	GetEnrichedWhosOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, bool, error)
	
	// SetEnrichedWhosOut caches enriched who's out data with TTL
	SetEnrichedWhosOut(ctx context.Context, data *domain.EnrichedWhosOutResponse) error
	
	// GetEnrichedCurrentlyOut retrieves cached enriched currently out data
	GetEnrichedCurrentlyOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, bool, error)
	
	// SetEnrichedCurrentlyOut caches enriched currently out data with TTL
	SetEnrichedCurrentlyOut(ctx context.Context, data *domain.EnrichedWhosOutResponse) error
	
	// InvalidateCache removes all cached BambooHR data
	InvalidateCache(ctx context.Context) error
} 