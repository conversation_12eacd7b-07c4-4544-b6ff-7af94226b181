package app

import (
	"context"
	"log/slog"
	"strconv"
	"time"
	"webhook_microservice/internal/domain"
)

// RequestTypeInfo holds type name and icon information from requests
type RequestTypeInfo struct {
	Name string
	Icon string
}

// BambooHRService provides enriched BambooHR data operations with caching
type BambooHRService struct {
	bambooHRClient BambooHRClient
	cacheService   CacheService
	logger         *slog.Logger
}

// NewBambooHRService creates a new BambooHRService
func NewBambooHRService(bambooHRClient BambooHRClient, cacheService CacheService, logger *slog.Logger) *BambooHRService {
	return &BambooHRService{
		bambooHRClient: bambooHRClient,
		cacheService:   cacheService,
		logger:         logger,
	}
}

// GetEnrichedWhosOut fetches who's out data and enriches it with email addresses from users data
// and substitutes type field with approved request types from time_off/requests API
// Uses cache-aside pattern with 10-minute TTL
func (s *BambooHRService) GetEnrichedWhosOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, error) {
	s.logger.Info("Starting enriched who's out data fetch")

	// Try to get from cache first
	if s.cacheService != nil {
		cachedData, found, err := s.cacheService.GetEnrichedWhosOut(ctx)
		if err != nil {
			s.logger.Warn("Cache retrieval failed, proceeding with API call", "error", err)
		} else if found {
			s.logger.Info("Cache hit for enriched who's out data", "record_count", len(*cachedData))
			return cachedData, nil
		} else {
			s.logger.Debug("Cache miss for enriched who's out data")
		}
	}

	// Cache miss or cache unavailable - fetch from APIs
	s.logger.Info("Fetching fresh data from BambooHR APIs")

	// Get today's date for requests API
	today := time.Now().Format("2006-01-02")

	// Fetch all three datasets concurrently for better performance
	whosOutChan := make(chan *domain.WhosOutResponse, 1)
	usersChan := make(chan *domain.UsersResponse, 1)
	requestsChan := make(chan *domain.TimeOffRequestsResponse, 1)
	errChan := make(chan error, 3)

	// Fetch who's out data
	go func() {
		whosOut, err := s.bambooHRClient.GetWhosOut(ctx)
		if err != nil {
			errChan <- err
			return
		}
		whosOutChan <- whosOut
	}()

	// Fetch users data
	go func() {
		users, err := s.bambooHRClient.GetUsers(ctx)
		if err != nil {
			errChan <- err
			return
		}
		usersChan <- users
	}()

	// Fetch time off requests for today
	go func() {
		requests, err := s.bambooHRClient.GetTimeOffRequests(ctx, today, today)
		if err != nil {
			errChan <- err
			return
		}
		requestsChan <- requests
	}()

	// Wait for all three operations to complete
	var whosOutData *domain.WhosOutResponse
	var usersData *domain.UsersResponse
	var requestsData *domain.TimeOffRequestsResponse
	for i := 0; i < 3; i++ {
		select {
		case whosOut := <-whosOutChan:
			whosOutData = whosOut
		case users := <-usersChan:
			usersData = users
		case requests := <-requestsChan:
			requestsData = requests
		case err := <-errChan:
			s.logger.Error("Failed to fetch BambooHR data", "error", err)
			return nil, NewBambooHRError("failed to fetch required data", err)
		}
	}

	s.logger.Info("Successfully fetched all datasets",
		"whos_out_count", len(*whosOutData),
		"users_count", len(*usersData),
		"requests_count", len(*requestsData))

	// Create email lookup map for efficient matching
	emailLookup := s.createEmailLookup(*usersData)
	s.logger.Debug("Created email lookup map", "lookup_size", len(emailLookup))

	// Create requests lookup map for type substitution
	requestsLookup := s.createRequestsLookup(*requestsData)
	s.logger.Debug("Created requests lookup map", "lookup_size", len(requestsLookup))

	// Enrich who's out data with emails and substitute types from approved requests
	enrichedResponse := s.enrichWithEmailsAndTypes(*whosOutData, emailLookup, requestsLookup)

	s.logger.Info("Successfully enriched who's out data",
		"total_records", len(enrichedResponse),
		"enriched_records", s.countEnrichedRecords(enrichedResponse))

	// Cache the enriched data (fire and forget - don't fail if caching fails)
	if s.cacheService != nil {
		go func() {
			if err := s.cacheService.SetEnrichedWhosOut(context.Background(), &enrichedResponse); err != nil {
				s.logger.Warn("Failed to cache enriched who's out data", "error", err)
			}
		}()
	}

	return &enrichedResponse, nil
}

// GetEnrichedCurrentlyOut fetches currently out data and enriches it with email addresses
// Uses cache-aside pattern with 10-minute TTL
func (s *BambooHRService) GetEnrichedCurrentlyOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, error) {
	s.logger.Info("Starting enriched currently out data fetch")

	// Try to get from cache first
	if s.cacheService != nil {
		cachedData, found, err := s.cacheService.GetEnrichedCurrentlyOut(ctx)
		if err != nil {
			s.logger.Warn("Cache retrieval failed, proceeding with API call", "error", err)
		} else if found {
			s.logger.Info("Cache hit for enriched currently out data", "record_count", len(*cachedData))
			return cachedData, nil
		} else {
			s.logger.Debug("Cache miss for enriched currently out data")
		}
	}

	// Cache miss or cache unavailable - get all enriched data first
	allEnriched, err := s.GetEnrichedWhosOut(ctx)
	if err != nil {
		return nil, err
	}

	// Filter to currently out only
	var currentlyOut domain.EnrichedWhosOutResponse
	for _, timeOff := range *allEnriched {
		if timeOff.IsCurrentlyOut() {
			currentlyOut = append(currentlyOut, timeOff)
		}
	}

	s.logger.Info("Filtered to currently out employees",
		"total_records", len(*allEnriched),
		"currently_out", len(currentlyOut))

	// Cache the filtered data (fire and forget - don't fail if caching fails)
	if s.cacheService != nil {
		go func() {
			if err := s.cacheService.SetEnrichedCurrentlyOut(context.Background(), &currentlyOut); err != nil {
				s.logger.Warn("Failed to cache enriched currently out data", "error", err)
			}
		}()
	}

	return &currentlyOut, nil
}

// createEmailLookup creates a map from employee ID to email for efficient lookups
func (s *BambooHRService) createEmailLookup(users domain.UsersResponse) map[int]string {
	emailLookup := make(map[int]string)
	for _, user := range users {
		emailLookup[user.EmployeeID] = user.Email
	}
	return emailLookup
}

// createRequestsLookup creates a map from employee ID to type for efficient lookups
func (s *BambooHRService) createRequestsLookup(requests domain.TimeOffRequestsResponse) map[int]RequestTypeInfo {
	requestsLookup := make(map[int]RequestTypeInfo)
	for _, request := range requests {
		// Only include approved requests for type substitution
		if request.IsApproved() {
			// Convert employeeId string to int for matching
			if employeeID, err := strconv.Atoi(request.EmployeeID); err == nil {
				requestsLookup[employeeID] = RequestTypeInfo{
					Name: request.Type.Name,
					Icon: request.Type.Icon,
				}
				s.logger.Debug("Added approved request to lookup",
					"employee_id", employeeID,
					"request_id", request.ID,
					"type", request.Type.Name,
					"icon", request.Type.Icon)
			}
		}
	}
	return requestsLookup
}

// enrichWithEmailsAndTypes enriches time off data with email addresses and substitutes types
func (s *BambooHRService) enrichWithEmailsAndTypes(whosOut domain.WhosOutResponse, emailLookup map[int]string, requestsLookup map[int]RequestTypeInfo) domain.EnrichedWhosOutResponse {
	enriched := make(domain.EnrichedWhosOutResponse, 0, len(whosOut))

	for _, timeOff := range whosOut {
		email, found := emailLookup[timeOff.EmployeeID]
		if !found {
			s.logger.Warn("No email found for employee",
				"employee_id", timeOff.EmployeeID,
				"employee_name", timeOff.Name)
			email = "" // Set empty string if no email found
		}

		// Attempt to substitute type from requests lookup if available
		substitutedType := timeOff.Type
		substitutedIcon := "" // Default empty icon for original who's out data
		if requestTypeInfo, ok := requestsLookup[timeOff.EmployeeID]; ok {
			substitutedType = requestTypeInfo.Name
			substitutedIcon = requestTypeInfo.Icon
			s.logger.Debug("Substituted type and icon from request",
				"timeoff_id", timeOff.ID,
				"employee_id", timeOff.EmployeeID,
				"original_type", timeOff.Type,
				"substituted_type", requestTypeInfo.Name,
				"substituted_icon", requestTypeInfo.Icon)
		}

		enrichedTimeOff := domain.EnrichedTimeOff{
			ID:         timeOff.ID,
			Type:       substitutedType,
			Icon:       substitutedIcon,
			EmployeeID: timeOff.EmployeeID,
			Name:       timeOff.Name,
			Email:      email,
			Start:      timeOff.Start,
			End:        timeOff.End,
		}

		enriched = append(enriched, enrichedTimeOff)
	}

	return enriched
}

// countEnrichedRecords counts how many records have email addresses
func (s *BambooHRService) countEnrichedRecords(enriched domain.EnrichedWhosOutResponse) int {
	count := 0
	for _, timeOff := range enriched {
		if timeOff.Email != "" {
			count++
		}
	}
	return count
}
