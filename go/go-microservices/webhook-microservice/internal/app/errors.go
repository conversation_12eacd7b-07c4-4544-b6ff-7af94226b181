package app

import (
	"errors"
	"net/http"
	"webhook_microservice/internal/domain"
)

var (
	// ErrInvalidID is returned when ID is invalid → 400 Bad Request
	ErrInvalidID = errors.New("invalid delivery ID")

	// ErrDuplicate is returned when webhook is duplicate → 200 OK (idempotent)
	ErrDuplicate = errors.New("duplicate webhook event")

	// ErrPublishFailed is returned when publishing fails → 502 Bad Gateway
	ErrPublishFailed = errors.New("failed to publish webhook event")

	// ErrInvalidWebhookData is returned when webhook data is invalid → 400 Bad Request
	ErrInvalidWebhookData = errors.New("invalid webhook data")

	// ErrDeduplicationFailed is returned when deduplication check fails → 500 Internal Server Error
	ErrDeduplicationFailed = errors.New("deduplication check failed")

	// ErrBambooHRAPIFailed is returned when BambooHR API call fails → 502 Bad Gateway
	ErrBambooHRAPIFailed = errors.New("bamboo hr api call failed")
)

// HTTPStatusCode maps application errors to HTTP status codes
func HTTPStatusCode(err error) int {
	switch err {
	case domain.ErrInvalidDeliveryID, ErrInvalidID, ErrInvalidWebhookData: // Handles invalid delivery ID error from domain package
		return http.StatusBadRequest
	case ErrDuplicate:
		return http.StatusOK // Idempotent - already processed
	case ErrPublishFailed, ErrBambooHRAPIFailed:
		return http.StatusBadGateway
	case ErrDeduplicationFailed:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// NewBambooHRError creates a new BambooHR-related error
func NewBambooHRError(message string, err error) error {
	if err != nil {
		return ErrBambooHRAPIFailed
	}
	return ErrBambooHRAPIFailed
}
