package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthTokenRequest represents the request structure for getAccountInfoByToken
type AuthTokenRequest struct {
	Method string        `json:"method"`
	Params []interface{} `json:"params"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	AccountServiceURL string
	Enabled           bool
}

// AuthenticationMiddleware creates a middleware for JWT token validation
func AuthenticationMiddleware(config AuthConfig, logger *slog.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip authentication if disabled
		if !config.Enabled {
			logger.Debug("Authentication middleware disabled")
			c.Next()
			return
		}

		// Extract Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.Warn("Missing Authorization header", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authorization header is required",
			})
			c.<PERSON>bort()
			return
		}

		logger.Info("Received Bearer token", "token", authHeader)

		// Check if it's a Bearer token
		if !strings.HasPrefix(authHeader, "Bearer ") {
			logger.Warn("Invalid Authorization header format", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authorization header must be a Bearer token",
			})
			c.Abort()
			return
		}

		// Extract token (remove "Bearer " prefix)
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			logger.Warn("Empty token in Authorization header", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Bearer token cannot be empty",
			})
			c.Abort()
			return
		}

		// Validate token with account service
		if !validateTokenWithAccountService(token, config.AccountServiceURL, logger) {
			logger.Warn("Token validation failed", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// Token is valid, continue with request
		logger.Info("Token validation successful", "path", c.Request.URL.Path)
		c.Next()
	}
}

// validateTokenWithAccountService makes a POST call to the account service to validate the token
func validateTokenWithAccountService(token, accountServiceURL string, logger *slog.Logger) bool {
	// Prepare request payload
	requestPayload := AuthTokenRequest{
		Method: "getAccountInfoByToken",
		Params: []interface{}{},
	}

	jsonData, err := json.Marshal(requestPayload)
	if err != nil {
		logger.Error("Failed to marshal auth request", "error", err)
		return false
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", accountServiceURL, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("Failed to create auth request", "error", err)
		return false
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	req.Body = io.NopCloser(bytes.NewBuffer(jsonData))

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make the request
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("Failed to call account service", "error", err, "url", accountServiceURL)
		return false
	}
	defer resp.Body.Close()

	// Read response body for logging
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Failed to read auth response body", "error", err)
		return false
	}

	// Log the response body for debugging
	logger.Info("Account service response received",
		"status_code", resp.StatusCode,
		"response_body", string(body),
		"url", accountServiceURL)

	// Check if response is 200 OK
	if resp.StatusCode != http.StatusOK {
		logger.Warn("Account service returned non-200 response",
			"status_code", resp.StatusCode,
			"response_body", string(body),
			"url", accountServiceURL)
		return false
	}

	// Parse response body to check for error field
	var responseBody map[string]interface{}
	if err := json.Unmarshal(body, &responseBody); err != nil {
		logger.Error("Failed to parse auth response body", "error", err, "body", string(body))
		return false
	}

	// Check if response contains an error field
	if errorField, exists := responseBody["error"]; exists && errorField != nil {
		logger.Warn("Account service returned error in response",
			"error", errorField,
			"response_body", string(body))
		return false
	}

	logger.Info("Token validation successful",
		"status_code", resp.StatusCode,
		"response_body", string(body))
	return true
}

// TimeoutMiddleware creates a middleware for request timeout
func TimeoutMiddleware(timeout time.Duration, logger *slog.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		// Replace the request context
		c.Request = c.Request.WithContext(ctx)

		// Create a channel to signal completion
		done := make(chan struct{})

		// Run the request in a goroutine
		go func() {
			defer close(done)
			c.Next()
		}()

		// Wait for completion or timeout
		select {
		case <-done:
			// Request completed normally
			return
		case <-ctx.Done():
			// Request timed out
			logger.Error("Request timeout",
				"path", c.Request.URL.Path,
				"timeout", timeout)
			c.JSON(http.StatusRequestTimeout, gin.H{
				"success": false,
				"error":   "Request timeout",
			})
			c.Abort()
		}
	}
}

// SecurityHeadersMiddleware adds security headers to all responses
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")

		// Remove server information
		c.Header("Server", "")

		c.Next()
	}
}
