package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// ValidatorConfig holds validation configuration
type ValidatorConfig struct {
	// A map: header name → source identifier
	DeliveryIDHeaders map[string]string `json:"deliveryIDHeaders"`
	// Other headers you always want present (e.g. Content-Type)
	RequiredHeaders []string `json:"requiredHeaders"`
}

// ValidatorMiddleware validates required headers and basic webhook structure
func ValidatorMiddleware(config ValidatorConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1) Find a known delivery-ID header
		var found bool
		for hdr, src := range config.DeliveryIDHeaders {
			if val := c.GetHeader(hdr); val != "" {
				c.Set("delivery_id", val)
				c.Set("source", src)
				found = true
				break
			}
			// Also try lowercased version
			if val := c.GetHeader(strings.ToLower(hdr)); val != "" {
				c.Set("delivery_id", val)
				c.Set("source", src)
				found = true
				break
			}
		}
		if !found {
			// none of the known headers were found → error
			var hdrs []string
			for hdr := range config.DeliveryIDHeaders {
				hdrs = append(hdrs, hdr)
			}
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "MISSING_DELIVERY_ID_HEADER",
				"detail": "A delivery ID header is required. Please check the headers and try again.",
			})
			c.Abort()
			return
		}

		// 2) Check if at least one of the required headers is present
		found = false
		for _, hdr := range config.RequiredHeaders {
			if c.GetHeader(hdr) != "" {
				found = true
				break
			}
		}
		if !found && len(config.RequiredHeaders) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "MISSING_REQUIRED_HEADER",
				"detail": "A required header is missing. Please check the headers and try again.",
			})
			c.Abort()
			return
		}

		// 3) Validate Content-Type for POST
		if c.Request.Method == http.MethodPost {
			ct := strings.ToLower(c.GetHeader("Content-Type"))
			if !strings.Contains(ct, "application/json") &&
				!strings.Contains(ct, "application/x-www-form-urlencoded") &&
				!strings.Contains(ct, "text/plain") {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error": "UNSUPPORTED_CONTENT_TYPE",
					"detail": c.GetHeader("Content-Type"),
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// SignatureValidatorMiddleware validates webhook signatures (placeholder for future implementation)
func SignatureValidatorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement signature validation based on webhook source
		// For now, just log the signature headers
		signature := c.GetHeader("X-Signature")
		if signature == "" {
			signature = c.GetHeader("X-Hub-Signature")
		}
		if signature == "" {
			signature = c.GetHeader("X-Hub-Signature-256")
		}
		
		// Store signature for potential validation in handler
		if signature != "" {
			c.Set("webhook_signature", signature)
		}
		
		c.Next()
	}
}