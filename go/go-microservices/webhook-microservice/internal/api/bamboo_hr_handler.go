package api

import (
	"log/slog"

	"webhook_microservice/internal/app"
	"webhook_microservice/internal/app/queries"
	"webhook_microservice/internal/config"

	"github.com/gin-gonic/gin"
)

// BambooHRHandler handles HTTP requests for BambooHR operations
type BambooHRHandler struct {
	bambooHRQueryHandler *queries.BambooHRQueryHandler
	cacheService         app.CacheService
	logger               *slog.Logger
}

// NewBambooHRHandler creates a new BambooHRHandler
func NewBambooHRHandler(
	bambooHRQueryHandler *queries.BambooHRQueryHandler,
	cacheService app.CacheService,
	logger *slog.Logger,
) *BambooHRHandler {
	return &BambooHRHandler{
		bambooHRQueryHandler: bambooHRQueryHandler,
		cacheService:         cacheService,
		logger:               logger,
	}
}

// RegisterRoutes registers the BambooHR routes using configuration
func (h *BambooHRHandler) RegisterRoutes(router *gin.Engine, config *config.Config) {
	// Check if BambooHR routes are enabled
	if !config.BambooHR.Routes.Enabled {
		h.logger.Info("BambooHR routes disabled in configuration")
		return
	}

	// ✅ Use configuration for route prefix
	routePrefix := config.BambooHR.Routes.Prefix
	if routePrefix == "" {
		routePrefix = "/api/bamboo-hr" // fallback default
	}

	// Commented out the authentication middlware while we
	// address the account service token validation problems

	// // ✅ Use configuration for authentication middleware
	// authConfig := middleware.AuthConfig{
	// 	AccountServiceURL: config.Auth.AccountServiceURL,
	// 	Enabled:           config.Auth.Enabled,
	// }

	// Add BambooHR-specific middleware (authentication only)
	bambooHRGroup := router.Group(routePrefix)
	// bambooHRGroup.Use(
	// 	middleware.AuthenticationMiddleware(authConfig, h.logger),
	// )
	{
		bambooHRGroup.GET("/whos-out", h.GetWhosOut)
		bambooHRGroup.GET("/whos-out/current", h.GetCurrentlyOut)
		bambooHRGroup.DELETE("/cache", h.InvalidateCache) // Cache invalidation endpoint
	}

	h.logger.Info("BambooHR routes registered successfully",
		"prefix", routePrefix,
		"auth_enabled", config.Auth.Enabled,
		"cors_applied_globally", true,
	)
}

// GetWhosOut handles GET /api/bamboo-hr/whos-out
func (h *BambooHRHandler) GetWhosOut(c *gin.Context) {
	h.logger.Info("Received request for who's out", "path", c.Request.URL.Path)

	query := queries.GetWhosOutQuery{}
	response, err := h.bambooHRQueryHandler.Handle(c.Request.Context(), query)
	if err != nil {
		h.logger.Error("Failed to get who's out", "error", err)

		// Map error to appropriate HTTP response based on error type
		BadGateway(c, "Failed to fetch data from BambooHR")
		return
	}

	h.logger.Info("Successfully retrieved who's out data", "count", len(*response))
	Success(c, response)
}

// GetCurrentlyOut handles GET /api/bamboo-hr/whos-out/current
func (h *BambooHRHandler) GetCurrentlyOut(c *gin.Context) {
	h.logger.Info("Received request for currently out", "path", c.Request.URL.Path)

	query := queries.GetCurrentlyOutQuery{}
	response, err := h.bambooHRQueryHandler.Handle(c.Request.Context(), query)
	if err != nil {
		h.logger.Error("Failed to get currently out", "error", err)

		// Map error to appropriate HTTP response based on error type
		BadGateway(c, "Failed to fetch data from BambooHR")
		return
	}

	h.logger.Info("Successfully retrieved currently out data", "count", len(*response))
	Success(c, response)
}

// InvalidateCache handles DELETE /api/bamboo-hr/cache
func (h *BambooHRHandler) InvalidateCache(c *gin.Context) {
	h.logger.Info("Received request to invalidate BambooHR cache", "path", c.Request.URL.Path)

	if h.cacheService == nil {
		h.logger.Warn("Cache service not available")
		BadRequest(c, "Cache service not available")
		return
	}

	err := h.cacheService.InvalidateCache(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to invalidate cache", "error", err)
		InternalError(c, "Failed to invalidate cache")
		return
	}

	h.logger.Info("Successfully invalidated BambooHR cache")
	Success(c, gin.H{"message": "Cache invalidated successfully"})
}
