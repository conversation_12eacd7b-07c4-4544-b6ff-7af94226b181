package api

import (
	"time"

	"webhook_microservice/internal/api/middleware"
	"webhook_microservice/internal/container"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes registers all API routes
func RegisterRoutes(router *gin.Engine, container *container.Container) {
	// Get components from container
	metricsComp := container.MetricsComponents()
	tracingComp := container.TracingComponents()
	config := container.Config() // ✅ Get config from container

	// Use container-provided components
	router.Use(gin.Logger())
	router.Use(tracingComp.Middleware())
	router.Use(metricsComp.PrometheusMiddleware())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware()) // ✅ Add CORS middleware globally

	// Add request logging middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		c.Next()

		latency := time.Since(start)
		status := c.Writer.Status()
		clientIP := c.ClientIP()

		container.Logger().Info("Request processed",
			"method", method,
			"path", path,
			"status", status,
			"latency", latency,
			"client_ip", clientIP,
		)
	})

	// Register health endpoints
	router.GET("/healthz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ok"})
	})

	router.GET("/readyz", func(c *gin.Context) {
		if err := container.HealthCheck(c.Request.Context()); err != nil {
			container.Logger().Error("Service not ready", "error", err)
			InternalError(c, "Service not ready")
			return
		}
		Success(c, gin.H{"status": "ready"})
	})

	// Register metrics endpoint through container
	if container.PrometheusEnabled() {
		metricsComp.RegisterEndpoint(router)
	}

	// ✅ Register webhook routes with configuration
	appHandler := container.AppWebhookHandler()
	webhookHandler := NewWebhookHandler(appHandler, container.Logger())
	webhookHandler.RegisterRoutes(router, config) // ✅ Pass config

	// ✅ Register BambooHR routes using CQRS pattern
	bambooHRQueryHandler := container.BambooHRQueryHandler()
	bambooHRCacheService := container.BambooHRCacheService()
	bambooHRHandler := NewBambooHRHandler(bambooHRQueryHandler, bambooHRCacheService, container.Logger())
	bambooHRHandler.RegisterRoutes(router, config) // ✅ Pass config
}

// RegisterTestRoutes registers routes for testing without middleware
func RegisterTestRoutes(router *gin.Engine, container *container.Container) {
	// Get components from container
	metricsComp := container.MetricsComponents()
	config := container.Config() // ✅ Get config from container

	// Add minimal middleware for tests
	router.Use(gin.Recovery())

	// Register health endpoints
	router.GET("/healthz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ok"})
	})

	router.GET("/readyz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ready"})
	})

	// Register metrics endpoint through container
	if container.PrometheusEnabled() {
		metricsComp.RegisterEndpoint(router)
	}

	// ✅ Register webhook routes with configuration for tests
	appHandler := container.AppWebhookHandler()
	webhookHandler := NewWebhookHandler(appHandler, container.Logger())
	webhookHandler.RegisterRoutes(router, config) // Use the same registration method

	// ✅ Register BambooHR routes for tests using CQRS pattern
	bambooHRQueryHandler := container.BambooHRQueryHandler()
	bambooHRCacheService := container.BambooHRCacheService()
	bambooHRHandler := NewBambooHRHandler(bambooHRQueryHandler, bambooHRCacheService, container.Logger())
	bambooHRHandler.RegisterRoutes(router, config) // ✅ Pass config
}
