package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response represents a standard API response
type Response struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   *Error      `json:"error,omitempty"`
}

// Error represents an API error
type Error struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// NewSuccessResponse creates a new success response
func NewSuccessResponse(data interface{}) *Response {
	return &Response{
		Success: true,
		Data:    data,
	}
}

// NewErrorResponse creates a new error response
func NewErrorResponse(code string, message string) *Response {
	return &Response{
		Success: false,
		Error: &Error{
			Code:    code,
			Message: message,
		},
	}
}

// JSON sends a JSON response
func JSON(c *gin.Context, status int, response *Response) {
	c.JSON(status, response)
}

// Success sends a success response
func Success(c *gin.Context, data interface{}) {
	JSON(c, http.StatusOK, NewSuccessResponse(data))
}

// Created sends a created response
func Created(c *gin.Context, data interface{}) {
	JSON(c, http.StatusCreated, NewSuccessResponse(data))
}

// BadRequest sends a bad request response
func BadRequest(c *gin.Context, message string) {
	JSON(c, http.StatusBadRequest, NewErrorResponse("BAD_REQUEST", message))
}

// NotFound sends a not found response
func NotFound(c *gin.Context, message string) {
	JSON(c, http.StatusNotFound, NewErrorResponse("NOT_FOUND", message))
}

// InternalError sends an internal server error response
func InternalError(c *gin.Context, message string) {
	JSON(c, http.StatusInternalServerError, NewErrorResponse("INTERNAL_ERROR", message))
}

// BadGateway sends a bad gateway response
func BadGateway(c *gin.Context, message string) {
	JSON(c, http.StatusBadGateway, NewErrorResponse("BAD_GATEWAY", message))
}
