package domain

import (
	"time"
)

// Employee represents an employee in BambooHR
type Employee struct {
	ID        int    `json:"id"` // BambooHR returns numeric IDs
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	Email     string `json:"email"`
}

// TimeOff represents time off information for an employee from who's out API
type TimeOff struct {
	ID         int    `json:"id"`         // 4381
	Type       string `json:"type"`       // "timeOff"
	EmployeeID int    `json:"employeeId"` // 233
	Name       string `json:"name"`       // "Erdin <PERSON>"
	Start      string `json:"start"`      // "2025-07-21"
	End        string `json:"end"`        // "2025-08-01"
}

// WhosOutResponse represents the response from BambooHR who's out API
type WhosOutResponse []TimeOff

// EnrichedTimeOff represents time off information enriched with user email
type EnrichedTimeOff struct {
	ID         int    `json:"id"`         // 4381
	Type       string `json:"type"`       // "timeOff"
	Icon       string `json:"icon"`       // "palm-trees", "home", "first-aid-kit" (enriched field)
	EmployeeID int    `json:"employeeId"` // 233
	Name       string `json:"name"`       // "Erdin Osmani"
	Email      string `json:"email"`      // "<EMAIL>" (enriched field)
	Start      string `json:"start"`      // "2025-07-21"
	End        string `json:"end"`        // "2025-08-01"
}

// EnrichedWhosOutResponse represents the enriched response with email addresses
type EnrichedWhosOutResponse []EnrichedTimeOff

// User represents a user in BambooHR meta/users endpoint
type User struct {
	ID         int    `json:"id"`
	EmployeeID int    `json:"employeeId"`
	FirstName  string `json:"firstName"`
	LastName   string `json:"lastName"`
	Email      string `json:"email"`
	Status     string `json:"status"`
	LastLogin  string `json:"lastLogin"`
}

// UsersResponse represents the response from BambooHR meta/users API
// The API returns a map with user IDs as keys
type UsersResponse map[string]User

// GetFullName returns the full name of the user
func (u *User) GetFullName() string {
	return u.FirstName + " " + u.LastName
}

// GetEmployeeName returns the employee name from TimeOff
func (t *TimeOff) GetEmployeeName() string {
	return t.Name
}

// GetEmployeeName returns the employee name from EnrichedTimeOff
func (e *EnrichedTimeOff) GetEmployeeName() string {
	return e.Name
}

// IsCurrentlyOut checks if the time off is currently active
func (t *TimeOff) IsCurrentlyOut() bool {
	now := time.Now()

	// Parse start and end dates
	startDate, err := time.Parse("2006-01-02", t.Start)
	if err != nil {
		return false
	}

	endDate, err := time.Parse("2006-01-02", t.End)
	if err != nil {
		return false
	}

	// Check if current date is within the time off period
	currentDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	return (currentDate.Equal(startDate) || currentDate.After(startDate)) &&
		(currentDate.Equal(endDate) || currentDate.Before(endDate))
}

// IsCurrentlyOut checks if the time off is currently active for EnrichedTimeOff
func (e *EnrichedTimeOff) IsCurrentlyOut() bool {
	now := time.Now()

	// Parse start and end dates
	startDate, err := time.Parse("2006-01-02", e.Start)
	if err != nil {
		return false
	}

	endDate, err := time.Parse("2006-01-02", e.End)
	if err != nil {
		return false
	}

	// Check if current date is within the time off period
	currentDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	return (currentDate.Equal(startDate) || currentDate.After(startDate)) &&
		(currentDate.Equal(endDate) || currentDate.Before(endDate))
}

// GetDurationDays calculates the duration of time off in days
func (t *TimeOff) GetDurationDays() (int, error) {
	startDate, err := time.Parse("2006-01-02", t.Start)
	if err != nil {
		return 0, err
	}

	endDate, err := time.Parse("2006-01-02", t.End)
	if err != nil {
		return 0, err
	}

	duration := endDate.Sub(startDate)
	return int(duration.Hours()/24) + 1, nil // +1 to include the start day
}

// GetDurationDays calculates the duration of time off in days for EnrichedTimeOff
func (e *EnrichedTimeOff) GetDurationDays() (int, error) {
	startDate, err := time.Parse("2006-01-02", e.Start)
	if err != nil {
		return 0, err
	}

	endDate, err := time.Parse("2006-01-02", e.End)
	if err != nil {
		return 0, err
	}

	duration := endDate.Sub(startDate)
	return int(duration.Hours()/24) + 1, nil // +1 to include the start day
}

// TimeOffRequestStatus represents the status information of a time off request
type TimeOffRequestStatus struct {
	LastChanged         string `json:"lastChanged"`
	LastChangedByUserId string `json:"lastChangedByUserId"`
	Status              string `json:"status"` // "approved", "canceled", "requested"
}

// TimeOffRequestType represents the type information of a time off request
type TimeOffRequestType struct {
	ID   string `json:"id"`
	Name string `json:"name"` // "Vacation - Prishtina", "Work From Home", "Sick Leave"
	Icon string `json:"icon"`
}

// TimeOffRequest represents a time off request from the requests API
type TimeOffRequest struct {
	ID         string               `json:"id"`         // "4783"
	EmployeeID string               `json:"employeeId"` // "153"
	Status     TimeOffRequestStatus `json:"status"`
	Name       string               `json:"name"`
	Start      string               `json:"start"`
	End        string               `json:"end"`
	Created    string               `json:"created"`
	Type       TimeOffRequestType   `json:"type"`
}

// TimeOffRequestsResponse represents the response from BambooHR time_off/requests API
type TimeOffRequestsResponse []TimeOffRequest

// IsApproved checks if the time off request is approved
func (r *TimeOffRequest) IsApproved() bool {
	return r.Status.Status == "approved"
}

// GetTypeName returns the type name from the request
func (r *TimeOffRequest) GetTypeName() string {
	return r.Type.Name
}

// GetEmployeeName returns the full name of the employee
func (e *Employee) GetEmployeeName() string {
	return e.FirstName + " " + e.LastName
}
