package dapr

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"time"

	"webhook_microservice/internal/domain"

	daprclient "github.com/dapr/go-sdk/client"
)

// CacheService provides caching functionality using Dapr state store
type CacheService struct {
	daprClient daprclient.Client
	storeName  string
	logger     *slog.Logger
}

// NewCacheService creates a new cache service
func NewCacheService(daprClient daprclient.Client, storeName string, logger *slog.Logger) *CacheService {
	return &CacheService{
		daprClient: daprClient,
		storeName:  storeName,
		logger:     logger,
	}
}

// CacheItem represents a cached item with TTL
type CacheItem struct {
	Data      interface{} `json:"data"`
	ExpiresAt time.Time   `json:"expires_at"`
}

// SetEnrichedWhosOut caches enriched who's out data with 10-minute TTL
func (c *CacheService) SetEnrichedWhosOut(ctx context.Context, data *domain.EnrichedWhosOutResponse) error {
	if c.daprClient == nil {
		c.logger.Debug("Dapr client not available, skipping cache set")
		return nil
	}

	key := "enriched_whos_out"
	expiresAt := time.Now().Add(10 * time.Minute)

	cacheItem := CacheItem{
		Data:      data,
		ExpiresAt: expiresAt,
	}

	// Convert to JSON for storage
	jsonData, err := json.Marshal(cacheItem)
	if err != nil {
		c.logger.Error("Failed to marshal cache item", "error", err, "key", key)
		return fmt.Errorf("failed to marshal cache item: %w", err)
	}

	// Store in Dapr state store
	err = c.daprClient.SaveState(ctx, c.storeName, key, jsonData, nil)
	if err != nil {
		c.logger.Error("Failed to save to cache", "error", err, "key", key)
		return fmt.Errorf("failed to save to cache: %w", err)
	}

	c.logger.Info("Cached enriched who's out data",
		"key", key,
		"expires_at", expiresAt,
		"record_count", len(*data))

	return nil
}

// GetEnrichedWhosOut retrieves cached enriched who's out data if not expired
func (c *CacheService) GetEnrichedWhosOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, bool, error) {
	if c.daprClient == nil {
		c.logger.Debug("Dapr client not available, skipping cache get")
		return nil, false, nil
	}

	key := "enriched_whos_out"

	// Get from Dapr state store
	result, err := c.daprClient.GetState(ctx, c.storeName, key, nil)
	if err != nil {
		c.logger.Error("Failed to get from cache", "error", err, "key", key)
		return nil, false, fmt.Errorf("failed to get from cache: %w", err)
	}

	// Check if key exists
	if len(result.Value) == 0 {
		c.logger.Debug("Cache miss", "key", key)
		return nil, false, nil
	}

	// Unmarshal cache item
	var cacheItem CacheItem
	if err := json.Unmarshal(result.Value, &cacheItem); err != nil {
		c.logger.Error("Failed to unmarshal cache item", "error", err, "key", key)
		// Delete corrupted cache entry
		_ = c.daprClient.DeleteState(ctx, c.storeName, key, nil)
		return nil, false, nil
	}

	// Check if expired
	if time.Now().After(cacheItem.ExpiresAt) {
		c.logger.Debug("Cache expired", "key", key, "expired_at", cacheItem.ExpiresAt)
		// Delete expired cache entry
		_ = c.daprClient.DeleteState(ctx, c.storeName, key, nil)
		return nil, false, nil
	}

	// Convert data back to proper type
	dataBytes, err := json.Marshal(cacheItem.Data)
	if err != nil {
		c.logger.Error("Failed to marshal cached data", "error", err, "key", key)
		return nil, false, nil
	}

	var enrichedData domain.EnrichedWhosOutResponse
	if err := json.Unmarshal(dataBytes, &enrichedData); err != nil {
		c.logger.Error("Failed to unmarshal cached data", "error", err, "key", key)
		// Delete corrupted cache entry
		_ = c.daprClient.DeleteState(ctx, c.storeName, key, nil)
		return nil, false, nil
	}

	c.logger.Info("Cache hit",
		"key", key,
		"expires_at", cacheItem.ExpiresAt,
		"record_count", len(enrichedData))

	return &enrichedData, true, nil
}

// SetEnrichedCurrentlyOut caches enriched currently out data with 10-minute TTL
func (c *CacheService) SetEnrichedCurrentlyOut(ctx context.Context, data *domain.EnrichedWhosOutResponse) error {
	if c.daprClient == nil {
		c.logger.Debug("Dapr client not available, skipping cache set")
		return nil
	}

	key := "enriched_currently_out"
	expiresAt := time.Now().Add(10 * time.Minute)

	cacheItem := CacheItem{
		Data:      data,
		ExpiresAt: expiresAt,
	}

	jsonData, err := json.Marshal(cacheItem)
	if err != nil {
		c.logger.Error("Failed to marshal cache item", "error", err, "key", key)
		return fmt.Errorf("failed to marshal cache item: %w", err)
	}

	err = c.daprClient.SaveState(ctx, c.storeName, key, jsonData, nil)
	if err != nil {
		c.logger.Error("Failed to save to cache", "error", err, "key", key)
		return fmt.Errorf("failed to save to cache: %w", err)
	}

	c.logger.Info("Cached enriched currently out data",
		"key", key,
		"expires_at", expiresAt,
		"record_count", len(*data))

	return nil
}

// GetEnrichedCurrentlyOut retrieves cached enriched currently out data if not expired
func (c *CacheService) GetEnrichedCurrentlyOut(ctx context.Context) (*domain.EnrichedWhosOutResponse, bool, error) {
	if c.daprClient == nil {
		c.logger.Debug("Dapr client not available, skipping cache get")
		return nil, false, nil
	}

	key := "enriched_currently_out"

	result, err := c.daprClient.GetState(ctx, c.storeName, key, nil)
	if err != nil {
		c.logger.Error("Failed to get from cache", "error", err, "key", key)
		return nil, false, fmt.Errorf("failed to get from cache: %w", err)
	}

	if len(result.Value) == 0 {
		c.logger.Debug("Cache miss", "key", key)
		return nil, false, nil
	}

	var cacheItem CacheItem
	if err := json.Unmarshal(result.Value, &cacheItem); err != nil {
		c.logger.Error("Failed to unmarshal cache item", "error", err, "key", key)
		_ = c.daprClient.DeleteState(ctx, c.storeName, key, nil)
		return nil, false, nil
	}

	if time.Now().After(cacheItem.ExpiresAt) {
		c.logger.Debug("Cache expired", "key", key, "expired_at", cacheItem.ExpiresAt)
		_ = c.daprClient.DeleteState(ctx, c.storeName, key, nil)
		return nil, false, nil
	}

	dataBytes, err := json.Marshal(cacheItem.Data)
	if err != nil {
		c.logger.Error("Failed to marshal cached data", "error", err, "key", key)
		return nil, false, nil
	}

	var enrichedData domain.EnrichedWhosOutResponse
	if err := json.Unmarshal(dataBytes, &enrichedData); err != nil {
		c.logger.Error("Failed to unmarshal cached data", "error", err, "key", key)
		_ = c.daprClient.DeleteState(ctx, c.storeName, key, nil)
		return nil, false, nil
	}

	c.logger.Info("Cache hit",
		"key", key,
		"expires_at", cacheItem.ExpiresAt,
		"record_count", len(enrichedData))

	return &enrichedData, true, nil
}

// InvalidateCache removes all cached BambooHR data
func (c *CacheService) InvalidateCache(ctx context.Context) error {
	if c.daprClient == nil {
		c.logger.Debug("Dapr client not available, skipping cache invalidation")
		return nil
	}

	keys := []string{"enriched_whos_out", "enriched_currently_out"}

	for _, key := range keys {
		err := c.daprClient.DeleteState(ctx, c.storeName, key, nil)
		if err != nil {
			c.logger.Error("Failed to delete cache key", "error", err, "key", key)
			return fmt.Errorf("failed to delete cache key %s: %w", key, err)
		}
		c.logger.Debug("Invalidated cache key", "key", key)
	}

	c.logger.Info("Cache invalidated", "keys", keys)
	return nil
}
