package infra

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"
	"webhook_microservice/internal/config"
	"webhook_microservice/internal/domain"
)

// BambooHRClient implements the BambooHRClient interface
type BambooHRClient struct {
	config     *config.BambooHRConfig
	httpClient *http.Client
	logger     *slog.Logger
}

// NewBambooHRClient creates a new BambooHR client
func NewBambooHRClient(config *config.BambooHRConfig, logger *slog.Logger) *BambooHRClient {
	return &BambooHRClient{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// GetWhosOut fetches who's out data from BambooHR API
func (c *BambooHRClient) GetWhosOut(ctx context.Context) (*domain.WhosOutResponse, error) {
	// Construct the API URL
	url := fmt.Sprintf("%s/time_off/whos_out", c.config.GetBaseURL())

	c.logger.Info("Making request to BambooHR API", "url", url)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		c.logger.Error("Failed to create HTTP request", "error", err)
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Add("Accept", "application/json")
	req.Header.Add("AcceptHeaderParameter", "application/json")

	// Add authorization header with API key
	req.SetBasicAuth(c.config.APIKey, "x")

	// Make the HTTP request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error("HTTP request failed", "error", err)
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		c.logger.Error("BambooHR API returned error status", "status", resp.StatusCode)
		return nil, fmt.Errorf("api returned status %d", resp.StatusCode)
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.logger.Error("Failed to read response body", "error", err)
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	c.logger.Debug("BambooHR API response", "body", string(body))

	// Parse JSON response
	var whosOutData domain.WhosOutResponse
	if err := json.Unmarshal(body, &whosOutData); err != nil {
		c.logger.Error("Failed to parse JSON response", "error", err)
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Log the actual parsed data received
	c.logger.Info("Parsed BambooHR data successfully",
		"total_employees", len(whosOutData),
		"response_data", whosOutData,
	)

	c.logger.Info("Successfully fetched who's out data", "count", len(whosOutData))
	return &whosOutData, nil
}

// GetUsers fetches user metadata from BambooHR API
func (c *BambooHRClient) GetUsers(ctx context.Context) (*domain.UsersResponse, error) {
	// Construct the API URL
	url := fmt.Sprintf("%s/meta/users", c.config.GetBaseURL())

	c.logger.Info("Making request to BambooHR users API", "url", url)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		c.logger.Error("Failed to create HTTP request", "error", err)
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Add("Accept", "application/json")
	req.Header.Add("AcceptHeaderParameter", "application/json")

	// Add authorization header with API key
	req.SetBasicAuth(c.config.APIKey, "x")

	// Make the HTTP request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error("HTTP request failed", "error", err)
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		c.logger.Error("BambooHR users API returned error status", "status", resp.StatusCode)
		return nil, fmt.Errorf("api returned status %d", resp.StatusCode)
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.logger.Error("Failed to read response body", "error", err)
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	c.logger.Debug("BambooHR users API response", "body", string(body))

	// Parse JSON response
	var usersData domain.UsersResponse
	if err := json.Unmarshal(body, &usersData); err != nil {
		c.logger.Error("Failed to parse JSON response", "error", err)
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	c.logger.Info("Successfully fetched users data", "count", len(usersData))
	return &usersData, nil
}

// GetTimeOffRequests fetches time off requests from BambooHR API for given date range
func (c *BambooHRClient) GetTimeOffRequests(ctx context.Context, start, end string) (*domain.TimeOffRequestsResponse, error) {
	// Construct the API URL with date parameters
	url := fmt.Sprintf("%s/time_off/requests?start=%s&end=%s", c.config.GetBaseURL(), start, end)

	c.logger.Info("Making request to BambooHR time_off/requests API", "url", url, "start", start, "end", end)

	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		c.logger.Error("Failed to create HTTP request", "error", err)
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Add("Accept", "application/json")
	req.Header.Add("AcceptHeaderParameter", "application/json")

	// Add authorization header with API key
	req.SetBasicAuth(c.config.APIKey, "x")

	// Make the HTTP request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		c.logger.Error("HTTP request failed", "error", err)
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		c.logger.Error("BambooHR time_off/requests API returned error status", "status", resp.StatusCode)
		return nil, fmt.Errorf("api returned status %d", resp.StatusCode)
	}

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		c.logger.Error("Failed to read response body", "error", err)
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	c.logger.Debug("BambooHR time_off/requests API response", "body", string(body))

	// Parse JSON response
	var requestsData domain.TimeOffRequestsResponse
	if err := json.Unmarshal(body, &requestsData); err != nil {
		c.logger.Error("Failed to parse JSON response", "error", err)
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	c.logger.Info("Successfully fetched time off requests data", "count", len(requestsData))
	return &requestsData, nil
}

// getUserIDs extracts user IDs from the users map for logging
func getUserIDs(users domain.UsersResponse) []string {
	ids := make([]string, 0, len(users))
	for id := range users {
		ids = append(ids, id)
	}
	return ids
}
