package config

import (
	"fmt"
	"log/slog"
	"net"
	"os"
	"strings"

	"github.com/spf13/viper"
)

// Config represents the webhook microservice configuration
type Config struct {
	Server        ServerConfig        `yaml:"server" mapstructure:"server"`
	Environment   string              `yaml:"environment" mapstructure:"environment"`
	Logging       LoggingConfig       `yaml:"logging" mapstructure:"logging"`
	Observability ObservabilityConfig `yaml:"observability" mapstructure:"observability"`
	Dapr          DaprConfig          `yaml:"dapr" mapstructure:"dapr"`
	Webhook       WebhookConfig       `yaml:"webhook" mapstructure:"webhook"`
	BambooHR      BambooHRConfig      `yaml:"bamboo_hr" mapstructure:"bamboo_hr"`
	Auth          AuthConfig          `yaml:"auth" mapstructure:"auth"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port int    `yaml:"port" mapstructure:"port"`
	Host string `yaml:"host" mapstructure:"host"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level string `yaml:"level" mapstructure:"level"`
}

// ObservabilityConfig holds observability configuration
type ObservabilityConfig struct {
	Prometheus PrometheusConfig `yaml:"prometheus" mapstructure:"prometheus"`
	Tracing    TracingConfig    `yaml:"tracing" mapstructure:"tracing"`
}

// PrometheusConfig holds Prometheus configuration
type PrometheusConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	Path    string `yaml:"path" mapstructure:"path"`
}

// TracingConfig holds tracing configuration
type TracingConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	Jaeger  string `yaml:"jaeger" mapstructure:"jaeger"`
}

// DaprConfig holds Dapr configuration
type DaprConfig struct {
	Enabled     bool   `yaml:"enabled" mapstructure:"enabled"`
	AppID       string `yaml:"app_id" mapstructure:"app_id"`
	HTTPPort    int    `yaml:"http_port" mapstructure:"http_port"`
	GRPCPort    int    `yaml:"grpc_port" mapstructure:"grpc_port"`
	StateStore  string `yaml:"state_store" mapstructure:"state_store"`
	PubSubName  string `yaml:"pubsub_name" mapstructure:"pubsub_name"`
	SecretStore string `yaml:"secret_store" mapstructure:"secret_store"`
}

// WebhookConfig holds webhook-specific configuration
type WebhookConfig struct {
	TopicName       string          `yaml:"topic_name" mapstructure:"topic_name"`
	RateLimitRPM    int             `yaml:"rate_limit_rpm" mapstructure:"rate_limit_rpm"`
	WAFAllowList    []string        `yaml:"waf_allow_list" mapstructure:"waf_allow_list"`
	Validator       ValidatorConfig `yaml:"validator" mapstructure:"validator"`
	RequiredHeaders []string        `yaml:"required_headers" mapstructure:"required_headers"`
}

// ValidatorConfig holds validator configuration
type ValidatorConfig struct {
	DeliveryIDHeaders map[string]string `yaml:"deliveryIDHeaders" mapstructure:"deliveryIDHeaders"`
	RequiredHeaders   []string          `yaml:"requiredHeaders" mapstructure:"requiredHeaders"`
}

// BambooHRConfig holds BambooHR configuration
type BambooHRConfig struct {
	APIKey        string               `yaml:"api_key" mapstructure:"api_key"`
	CompanyDomain string               `yaml:"company_domain" mapstructure:"company_domain"`
	APIVersion    string               `yaml:"api_version" mapstructure:"api_version"`
	Routes        BambooHRRoutesConfig `yaml:"routes" mapstructure:"routes"`
}

// BambooHRRoutesConfig holds route-specific configuration for BambooHR endpoints
type BambooHRRoutesConfig struct {
	Prefix       string   `yaml:"prefix" mapstructure:"prefix"`
	RateLimitRPM int      `yaml:"rate_limit_rpm" mapstructure:"rate_limit_rpm"`
	WAFAllowList []string `yaml:"waf_allow_list" mapstructure:"waf_allow_list"`
	Enabled      bool     `yaml:"enabled" mapstructure:"enabled"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	Enabled           bool   `yaml:"enabled" mapstructure:"enabled"`
	AccountServiceURL string `yaml:"account_service_url" mapstructure:"account_service_url"`
	Timeout           string `yaml:"timeout" mapstructure:"timeout"`
}

// GetBaseURL constructs the full BambooHR API URL
func (b *BambooHRConfig) GetBaseURL() string {
	return fmt.Sprintf("https://%s.bamboohr.com/api/%s", b.CompanyDomain, b.APIVersion)
}

// Validate validates the entire configuration
func (c *Config) Validate() error {
	if err := c.Server.Validate(); err != nil {
		return fmt.Errorf("server config: %w", err)
	}
	if err := c.Webhook.Validate(); err != nil {
		return fmt.Errorf("webhook config: %w", err)
	}
	if err := c.BambooHR.Validate(); err != nil {
		return fmt.Errorf("bamboo_hr config: %w", err)
	}
	if err := c.Auth.Validate(); err != nil {
		return fmt.Errorf("auth config: %w", err)
	}
	return nil
}

// Validate server configuration
func (s *ServerConfig) Validate() error {
	if s.Port < 1024 || s.Port > 65535 {
		return fmt.Errorf("invalid port %d: must be between 1024-65535", s.Port)
	}
	if s.Host == "" {
		return fmt.Errorf("host cannot be empty")
	}
	return nil
}

// Validate webhook configuration
func (w *WebhookConfig) Validate() error {
	if w.TopicName == "" {
		return fmt.Errorf("topic_name cannot be empty")
	}
	if w.RateLimitRPM <= 0 {
		return fmt.Errorf("rate_limit_rpm must be positive, got %d", w.RateLimitRPM)
	}

	// Validate CIDR notation in WAF allow list
	for _, cidr := range w.WAFAllowList {
		if strings.Contains(cidr, "/") {
			_, _, err := net.ParseCIDR(cidr)
			if err != nil {
				return fmt.Errorf("invalid CIDR in waf_allow_list: %s", cidr)
			}
		} else {
			// Validate as IP address
			if net.ParseIP(cidr) == nil {
				return fmt.Errorf("invalid IP in waf_allow_list: %s", cidr)
			}
		}
	}
	return nil
}

// Validate BambooHR configuration
func (b *BambooHRConfig) Validate() error {
	if b.APIKey == "" {
		return fmt.Errorf("bamboo_hr api_key cannot be empty")
	}
	if b.CompanyDomain == "" {
		return fmt.Errorf("bamboo_hr company_domain cannot be empty")
	}
	if b.APIVersion == "" {
		return fmt.Errorf("bamboo_hr api_version cannot be empty")
	}

	// Validate routes configuration
	if err := b.Routes.Validate(); err != nil {
		return fmt.Errorf("bamboo_hr routes: %w", err)
	}

	return nil
}

// Validate BambooHR routes configuration
func (r *BambooHRRoutesConfig) Validate() error {
	if r.RateLimitRPM <= 0 {
		return fmt.Errorf("rate_limit_rpm must be positive, got %d", r.RateLimitRPM)
	}

	// Validate CIDR notation in WAF allow list
	for _, cidr := range r.WAFAllowList {
		if strings.Contains(cidr, "/") {
			_, _, err := net.ParseCIDR(cidr)
			if err != nil {
				return fmt.Errorf("invalid CIDR in waf_allow_list: %s", cidr)
			}
		} else {
			// Validate as IP address
			if net.ParseIP(cidr) == nil {
				return fmt.Errorf("invalid IP in waf_allow_list: %s", cidr)
			}
		}
	}
	return nil
}

// Validate Auth configuration
func (a *AuthConfig) Validate() error {
	if a.Enabled {
		if a.AccountServiceURL == "" {
			return fmt.Errorf("account_service_url cannot be empty when auth is enabled")
		}
	}
	return nil
}

// LoadConfig loads and validates the webhook microservice configuration
func LoadConfig() (*Config, error) {
	v := viper.New()

	// Set config file path
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "configs/config.yaml"
	}

	v.SetConfigFile(configPath)
	v.SetConfigType("yaml")

	// Configure environment variable handling
	v.SetEnvPrefix("WEBHOOK")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set comprehensive defaults matching YAML structure
	setDefaults(v)

	// Try to read config file
	if err := v.ReadInConfig(); err != nil {
		slog.Warn("Config file not found, using environment variables and defaults", "file", configPath, "error", err)
	} else {
		slog.Info("Configuration loaded from file", "file", configPath)
	}

	// Handle special environment variable overrides
	handleSpecialEnvVars(v)

	// Unmarshal configuration
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// ✅ CRITICAL: Validate configuration and fail fast
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	slog.Info("Configuration loaded and validated successfully")
	return &config, nil
}

// setDefaults sets all default values matching YAML structure
func setDefaults(v *viper.Viper) {
	// Server defaults
	v.SetDefault("server.port", 2021)
	v.SetDefault("server.host", "0.0.0.0")

	// Environment default
	v.SetDefault("environment", "development")

	// Logging defaults
	v.SetDefault("logging.level", "info")

	// Observability defaults
	v.SetDefault("observability.prometheus.enabled", true)
	v.SetDefault("observability.prometheus.path", "/metrics")
	v.SetDefault("observability.tracing.enabled", false)
	v.SetDefault("observability.tracing.jaeger", "http://localhost:14268/api/traces")

	// Dapr defaults
	v.SetDefault("dapr.enabled", true)
	v.SetDefault("dapr.app_id", "webhook-microservice")
	v.SetDefault("dapr.http_port", 3500)
	v.SetDefault("dapr.grpc_port", 50001)
	v.SetDefault("dapr.state_store", "statestore")
	v.SetDefault("dapr.pubsub_name", "kafka-pubsub")
	v.SetDefault("dapr.secret_store", "config-secret-store")

	// Webhook defaults
	v.SetDefault("webhook.topic_name", "webhook-events")
	v.SetDefault("webhook.rate_limit_rpm", 1000)
	v.SetDefault("webhook.waf_allow_list", []string{"0.0.0.0/0"})
	v.SetDefault("webhook.validator.deliveryIDHeaders", map[string]string{
		"X-Delivery-ID":                  "generic",
		"X-GitHub-Delivery":              "github",
		"X-Atlassian-Webhook-Identifier": "jira",
		"X-Hook-ID":                      "gitlab",
		"requestId":                      "crosschex",
	})
	v.SetDefault("webhook.validator.requiredHeaders", []string{"Content-Type"})

	// BambooHR defaults
	v.SetDefault("bamboo_hr.api_key", "")
	v.SetDefault("bamboo_hr.company_domain", "91life")
	v.SetDefault("bamboo_hr.api_version", "v1")
	v.SetDefault("bamboo_hr.routes.prefix", "/api/bamboo-hr")
	v.SetDefault("bamboo_hr.routes.rate_limit_rpm", 1000)
	v.SetDefault("bamboo_hr.routes.waf_allow_list", []string{"0.0.0.0/0"})
	v.SetDefault("bamboo_hr.routes.enabled", true)

	// Auth defaults
	v.SetDefault("auth.enabled", true)
	v.SetDefault("auth.account_service_url", "http://localhost:3000")
	v.SetDefault("auth.timeout", "10s")
}

// handleSpecialEnvVars handles special cases for environment variables
func handleSpecialEnvVars(v *viper.Viper) {
	// Handle comma-separated WAF allow list
	if allowList := os.Getenv("WEBHOOK_WEBHOOK_WAF_ALLOW_LIST"); allowList != "" {
		v.Set("webhook.waf_allow_list", strings.Split(allowList, ","))
	}

	// Handle comma-separated required headers
	if headers := os.Getenv("WEBHOOK_WEBHOOK_REQUIRED_HEADERS"); headers != "" {
		v.Set("webhook.validator.requiredHeaders", strings.Split(headers, ","))
	}
}
