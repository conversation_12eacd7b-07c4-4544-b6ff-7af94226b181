apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: statestore
spec:
  type: state.postgresql
  version: v1
  metadata:
  - name: connectionString
    secretKeyRef:
      name: WEBHOOK_POSTGRES_CONNECTION_STRING
      key: WEBH<PERSON><PERSON>_POSTGRES_CONNECTION_STRING
  - name: tableName
    secretKeyRef:
      name: WEBHOOK_POSTGRES_TABLE_NAME
      key: WEBHOOK_POSTGRES_TABLE_NAME
  - name: metadataTableName
    secretKeyRef:
      name: WEBHOOK_POSTGRES_METADATA_TABLE
      key: WEBHOOK_POSTGRES_METADATA_TABLE
  - name: actorStateStore
    value: "true"
auth:
  secretStore: config-secret-store
