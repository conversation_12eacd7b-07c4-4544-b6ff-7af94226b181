########################################################
## MAIN CONFIGURATION FILE
########################################################

server:
  port: 2021 # overwritten by env vars (docker-compose locally)
  host: 0.0.0.0

environment: development

logging:
  level: info  # debug, info, warn, error

observability:
  prometheus:
    enabled: false
    path: /metrics
  tracing:
    enabled: false
    jaeger: http://localhost:14268/api/traces

# Dapr configuration for webhook microservice
dapr:
  enabled: true
  app_id: webhook-microservice
  http_port: 3500
  grpc_port: 50001
  state_store: statestore # references /components/postgresql-statestore.yaml
  pubsub_name: kafka-pubsub # references /components/kafka-pubsub.yaml
  secret_store: config-secret-store # references /components/config-secret-store.yaml

# Webhook-specific configuration
webhook:
  topic_name: "webhook-events"
  rate_limit_rpm: 1000 # requests per minute
  waf_allow_list:
    - "0.0.0.0/0"
  validator:
    deliveryIDHeaders:
      "X-GitHub-Delivery": "github"
      "X-Atlassian-Webhook-Identifier": "jira"
      "requestId": "crosschex"
      "X-Delivery-ID": "generic"
    requiredHeaders:
      - "Content-Type"

bamboo_hr:
  api_key: "api-key"
  company_domain: "91life"
  api_version: "v1"
  routes:
    prefix: "/api/bamboo-hr"
    rate_limit_rpm: 1000

auth:
  enabled: true
  account_service_url: "http://localhost:3000"
  timeout: "10s"