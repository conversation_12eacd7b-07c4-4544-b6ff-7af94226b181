#!/bin/bash

# Test runner script for webhook microservice
# Supports both unit tests and integration tests with beautiful colored output

set -e

# Enhanced colors and formatting
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;90m'
BOLD='\033[1m'
DIM='\033[2m'
NC='\033[0m' # No Color

# Unicode symbols for better visual appeal
CHECK_MARK="✅"
CROSS_MARK="❌"
WARNING="⚠️"
INFO="ℹ️"
ROCKET="🚀"
GEAR="⚙️"
MAGNIFYING_GLASS="🔍"
STOPWATCH="⏱️"
TROPHY="🏆"

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
TEST_TIMEOUT="60s"
SERVICE_PORT="2023"
DAPR_PORT="3500"

# Enhanced printing functions with better formatting
print_header() {
    echo ""
    echo -e "${BOLD}${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BOLD}${CYAN}║${NC} ${WHITE}${BOLD}$1${NC}${CYAN}${BOLD}║${NC}"
    echo -e "${BOLD}${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_section() {
    echo ""
    echo -e "${BOLD}${PURPLE}▶ $1${NC}"
    echo -e "${GRAY}────────────────────────────────────────────────────────────────${NC}"
}

print_status() {
    echo -e "${BLUE}${INFO} ${BOLD}$1${NC}"
}

print_success() {
    echo -e "${GREEN}${CHECK_MARK} ${BOLD}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}${WARNING} ${BOLD}$1${NC}"
}

print_error() {
    echo -e "${RED}${CROSS_MARK} ${BOLD}$1${NC}"
}

print_info() {
    echo -e "${CYAN}${MAGNIFYING_GLASS} $1${NC}"
}

print_step() {
    echo -e "${WHITE}${GEAR} $1${NC}"
}

# Function to check if service is running
is_service_running() {
    curl -s -f "http://localhost:${SERVICE_PORT}/healthz" > /dev/null 2>&1
}

# Function to check if Dapr is running
is_dapr_running() {
    curl -s -f "http://localhost:${DAPR_PORT}/v1.0/healthz" > /dev/null 2>&1
}

# Function to run unit tests with enhanced output
run_unit_tests() {
    print_section "Unit Tests ${STOPWATCH}"
    print_step "Setting up test environment..."
    cd "$PROJECT_DIR"

    # Set test environment
    export GIN_MODE=test
    export WEBHOOK_PORT="$SERVICE_PORT"
    export WEBHOOK_DAPR_HTTP_PORT="$DAPR_PORT"

    print_step "Running unit tests (isolated, fast)..."
    echo -e "${DIM}Running: go test -v -timeout=$TEST_TIMEOUT -run='.*_Unit' ./tests/${NC}"
    echo ""

    # Run unit tests with beautiful formatted output
    go test -v -timeout="$TEST_TIMEOUT" -run=".*_Unit" ./tests/ 2>&1 | bash "$SCRIPT_DIR/test-formatter.sh"

    local exit_code=${PIPESTATUS[0]}
    echo ""

    if [ $exit_code -eq 0 ]; then
        print_success "Unit tests completed successfully!"
        return 0
    else
        print_error "Unit tests failed!"
        return 1
    fi
}

# Function to run integration tests with enhanced output
run_integration_tests() {
    print_section "Integration Tests ${ROCKET}"
    print_step "Checking service dependencies..."

    # Check if service is running
    if ! is_service_running; then
        print_error "Webhook service is not running on port $SERVICE_PORT"
        print_info "Start the service with: ${BOLD}bash scripts/run-local.sh${NC}"
        return 1
    fi
    print_success "Webhook service is running on port $SERVICE_PORT"

    # Check if Dapr is running
    if ! is_dapr_running; then
        print_warning "Dapr is not running on port $DAPR_PORT"
        print_info "Some integration tests may fail without Dapr"
    else
        print_success "Dapr is running and healthy on port $DAPR_PORT"
    fi

    cd "$PROJECT_DIR"

    print_step "Running integration tests (requires running service)..."
    echo -e "${DIM}Running: go test -v -timeout=$TEST_TIMEOUT -run='.*_Integration' ./tests/${NC}"
    echo ""

    # Run integration tests with beautiful formatted output
    go test -v -timeout="$TEST_TIMEOUT" -run=".*_Integration" ./tests/ 2>&1 | bash "$SCRIPT_DIR/test-formatter.sh"

    local exit_code=${PIPESTATUS[0]}
    echo ""

    if [ $exit_code -eq 0 ]; then
        print_success "Integration tests completed successfully!"
        return 0
    else
        print_error "Integration tests failed!"
        return 1
    fi
}

# Function to run all tests with beautiful summary
run_all_tests() {
    print_header "Webhook Microservice Test Suite ${ROCKET}"

    local unit_result=0
    local integration_result=0
    local start_time=$(date +%s)

    # Run unit tests
    run_unit_tests
    unit_result=$?

    echo ""

    # Run integration tests
    run_integration_tests
    integration_result=$?

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # Beautiful test summary
    echo ""
    print_section "Test Results Summary ${TROPHY}"

    echo -e "${BOLD}${WHITE}┌─────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BOLD}${WHITE}│                        TEST RESULTS                         │${NC}"
    echo -e "${BOLD}${WHITE}├─────────────────────────────────────────────────────────────┤${NC}"

    if [ $unit_result -eq 0 ]; then
        echo -e "${BOLD}${WHITE}│${NC} ${GREEN}${CHECK_MARK} Unit Tests:        ${BOLD}PASSED${NC}                        ${BOLD}${WHITE}│${NC}"
    else
        echo -e "${BOLD}${WHITE}│${NC} ${RED}${CROSS_MARK} Unit Tests:        ${BOLD}FAILED${NC}                        ${BOLD}${WHITE}│${NC}"
    fi

    if [ $integration_result -eq 0 ]; then
        echo -e "${BOLD}${WHITE}│${NC} ${GREEN}${CHECK_MARK} Integration Tests: ${BOLD}PASSED${NC}                        ${BOLD}${WHITE}│${NC}"
    else
        echo -e "${BOLD}${WHITE}│${NC} ${RED}${CROSS_MARK} Integration Tests: ${BOLD}FAILED${NC}                        ${BOLD}${WHITE}│${NC}"
    fi

    echo -e "${BOLD}${WHITE}├─────────────────────────────────────────────────────────────┤${NC}"
    echo -e "${BOLD}${WHITE}│${NC} ${STOPWATCH} Total Duration:    ${BOLD}${duration}s${NC}                             ${BOLD}${WHITE}│${NC}"
    echo -e "${BOLD}${WHITE}└─────────────────────────────────────────────────────────────┘${NC}"

    echo ""

    if [ $unit_result -eq 0 ] && [ $integration_result -eq 0 ]; then
        print_success "All tests passed! ${TROPHY} Ready for deployment!"
        echo -e "${GREEN}${BOLD}🎉 Congratulations! Your webhook microservice is rock solid! 🎉${NC}"
        return 0
    else
        print_error "Some tests failed! Please review the output above."
        echo -e "${RED}${BOLD}💥 Fix the failing tests before proceeding! 💥${NC}"
        return 1
    fi
}

# Function to show beautiful usage information
show_usage() {
    print_header "Webhook Microservice Test Runner ${GEAR}"

    echo -e "${BOLD}${WHITE}USAGE:${NC}"
    echo -e "  ${CYAN}$0${NC} ${YELLOW}[OPTION]${NC}"
    echo ""

    echo -e "${BOLD}${WHITE}OPTIONS:${NC}"
    echo -e "  ${GREEN}unit${NC}         ${GRAY}Run unit tests only (fast, isolated)${NC}"
    echo -e "  ${GREEN}integration${NC}  ${GRAY}Run integration tests only (requires running service)${NC}"
    echo -e "  ${GREEN}all${NC}          ${GRAY}Run all tests (default)${NC}"
    echo -e "  ${GREEN}coverage${NC}     ${GRAY}Run tests with coverage report${NC}"
    echo -e "  ${GREEN}help${NC}         ${GRAY}Show this help message${NC}"
    echo ""

    echo -e "${BOLD}${WHITE}EXAMPLES:${NC}"
    echo -e "  ${CYAN}$0${NC}                    ${GRAY}# Run all tests${NC}"
    echo -e "  ${CYAN}$0${NC} ${YELLOW}unit${NC}              ${GRAY}# Run unit tests only${NC}"
    echo -e "  ${CYAN}$0${NC} ${YELLOW}integration${NC}       ${GRAY}# Run integration tests only${NC}"
    echo -e "  ${CYAN}$0${NC} ${YELLOW}coverage${NC}          ${GRAY}# Run tests with coverage report${NC}"
    echo ""

    echo -e "${BOLD}${WHITE}PREREQUISITES FOR INTEGRATION TESTS:${NC}"
    echo -e "  ${YELLOW}${WARNING}${NC} Start the service: ${CYAN}bash scripts/run-local.sh${NC}"
    echo -e "  ${YELLOW}${WARNING}${NC} Service should be running on port ${BOLD}$SERVICE_PORT${NC}"
    echo -e "  ${YELLOW}${WARNING}${NC} Dapr should be running on port ${BOLD}$DAPR_PORT${NC}"
    echo ""

    echo -e "${BOLD}${WHITE}TEST TYPES:${NC}"
    echo -e "  ${GREEN}${CHECK_MARK} Unit Tests${NC}        ${GRAY}Fast, isolated, no external dependencies${NC}"
    echo -e "  ${GREEN}${CHECK_MARK} Integration Tests${NC} ${GRAY}End-to-end testing with running service${NC}"
    echo -e "  ${GREEN}${CHECK_MARK} Coverage Report${NC}   ${GRAY}Detailed code coverage analysis${NC}"
}

# Function to run tests with coverage
run_coverage() {
    print_section "Test Coverage Analysis ${MAGNIFYING_GLASS}"
    print_step "Running tests with coverage analysis..."
    cd "$PROJECT_DIR"

    # Set test environment
    export GIN_MODE=test
    export WEBHOOK_PORT="$SERVICE_PORT"
    export WEBHOOK_DAPR_HTTP_PORT="$DAPR_PORT"

    echo -e "${DIM}Running: go test -coverprofile=coverage.out ./tests/${NC}"
    echo ""

    # Run tests with coverage
    if go test -coverprofile=coverage.out ./tests/ 2>/dev/null; then
        print_success "Tests completed, generating coverage report..."

        # Generate coverage report
        echo ""
        print_step "Coverage by package:"
        go tool cover -func=coverage.out | while IFS= read -r line; do
            if [[ $line == *"total:"* ]]; then
                echo -e "${BOLD}${WHITE}${line}${NC}"
            elif [[ $line == *"100.0%"* ]]; then
                echo -e "${GREEN}${line}${NC}"
            elif [[ $line == *"0.0%"* ]]; then
                echo -e "${RED}${line}${NC}"
            else
                echo -e "${YELLOW}${line}${NC}"
            fi
        done

        # Generate HTML report
        if command -v go &> /dev/null; then
            go tool cover -html=coverage.out -o coverage.html
            print_success "HTML coverage report generated: coverage.html"
            print_info "Open coverage.html in your browser to view detailed coverage"
        fi

        return 0
    else
        print_error "Tests failed during coverage analysis!"
        return 1
    fi
}

# Main script logic with enhanced handling
main() {
    # Clear screen for better readability
    clear

    case "${1:-all}" in
        "unit")
            print_header "Unit Tests Only ${GEAR}"
            run_unit_tests
            ;;
        "integration")
            print_header "Integration Tests Only ${ROCKET}"
            run_integration_tests
            ;;
        "all")
            run_all_tests
            ;;
        "coverage")
            print_header "Test Coverage Analysis ${MAGNIFYING_GLASS}"
            run_coverage
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown option: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac

    local exit_code=$?
    echo ""

    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}${BOLD}🎉 Test execution completed successfully! 🎉${NC}"
    else
        echo -e "${RED}${BOLD}💥 Test execution failed! Please review the output above. 💥${NC}"
    fi

    exit $exit_code
}

# Run main function with all arguments
main "$@"
