#!/usr/bin/env bash
set -euo pipefail

# Determine project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

echo "🚀 Starting Webhook Microservice..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Ensure Go modules
echo "📦 Installing dependencies..."
go mod tidy

# Start infrastructure services (PostgreSQL, Jaeger and OTel Collector)
echo "🐳 Starting infrastructure services..."
docker-compose up -d postgres jaeger otel-collector
sleep 10  # Wait longer for PostgreSQL to start

echo "ℹ️  Using PostgreSQL for Dapr state store and Dapr's Redis for pub/sub"

echo "🔧 Setting up Dapr..."

# Install Dapr CLI if missing
if ! command -v dapr &>/dev/null; then
  echo "📥 Installing Dapr CLI..."
  OS_NAME="$(uname -s)"
  if [ "$OS_NAME" = "Linux" ]; then
    wget -q https://raw.githubusercontent.com/dapr/cli/master/install/install.sh -O - | bash
    # Add to PATH for current session
    export PATH="$HOME/.dapr/bin:$PATH"
  elif [ "$OS_NAME" = "Darwin" ]; then
    brew install dapr/tap/dapr-cli
  else
    echo "❌ Unsupported OS. Please install Dapr manually from https://docs.dapr.io/getting-started/install-dapr-cli/"
    exit 1
  fi
fi

# Check if Dapr runtime is initialized
if [ ! -f "$HOME/.dapr/bin/daprd" ]; then
  echo "🔧 Initializing Dapr runtime..."
  dapr init --wait
  echo "✅ Dapr runtime initialized successfully."
else
  echo "✅ Dapr runtime already initialized."
fi

# Ensure Dapr is in PATH
export PATH="$HOME/.dapr/bin:$PATH"

# Verify Dapr is working
echo "🔍 Verifying Dapr installation..."
if ! dapr --version >/dev/null 2>&1; then
  echo "❌ Dapr CLI is not working properly"
  exit 1
fi

if [ ! -f "$HOME/.dapr/bin/daprd" ]; then
  echo "❌ Dapr runtime (daprd) is not installed"
  echo "🔧 Try running: dapr init"
  exit 1
fi

echo "✅ Dapr is ready!"

# Set webhook configuration
echo "🔧 Setting webhook configuration..."
export WEBHOOK_ENVIRONMENT="development"
export WEBHOOK_LOGGING_LEVEL="debug"
export WEBHOOK_OBSERVABILITY_PROMETHEUS_ENABLED="true"
export WEBHOOK_OBSERVABILITY_TRACING_ENABLED="true"
export WEBHOOK_OBSERVABILITY_TRACING_JAEGER="http://localhost:14268/api/traces"

export WEBHOOK_SERVER_PORT="2023"
export WEBHOOK_SERVER_HOST="0.0.0.0"

export WEBHOOK_DAPR_ENABLED="true"
export WEBHOOK_DAPR_APP_ID="webhook-microservice"
export WEBHOOK_DAPR_HTTP_PORT="3500"
export WEBHOOK_DAPR_GRPC_PORT="50001"
export WEBHOOK_DAPR_STATE_STORE="statestore"
export WEBHOOK_DAPR_PUBSUB_NAME="redis-pubsub"

export WEBHOOK_WEBHOOK_TOPIC_NAME="webhook-events"
export WEBHOOK_WEBHOOK_RATE_LIMIT_RPM="1000"
export WEBHOOK_WEBHOOK_WAF_ALLOW_LIST="0.0.0.0/0,::/0"

# BambooHR configuration
export WEBHOOK_BAMBOO_HR_API_KEY="api-key"
export WEBHOOK_BAMBOO_HR_COMPANY_DOMAIN="91life"
export WEBHOOK_BAMBOO_HR_API_VERSION="v1"
export WEBHOOK_BAMBOO_HR_ROUTES_ENABLED="true"
export WEBHOOK_BAMBOO_HR_ROUTES_PREFIX="/api/bamboo-hr"
export WEBHOOK_BAMBOO_HR_ROUTES_RATE_LIMIT_RPM="1000"

# Auth configuration
export WEBHOOK_AUTH_ENABLED="true"
export WEBHOOK_AUTH_ACCOUNT_SERVICE_URL="http://localhost:3000"
export WEBHOOK_AUTH_TIMEOUT="10s"

# Redis environment variables removed - not used by application code
# State store now uses PostgreSQL via Dapr component configuration

# Cleanup on exit
cleanup() {
  echo "🛑 Shutting down..."
  kill "$DAPR_PID" 2>/dev/null || true
  docker-compose down
  echo "✅ Cleanup complete"
}
trap cleanup SIGINT SIGTERM

# Run service with Dapr sidecar
echo "🚀 Starting webhook service with Dapr sidecar..."
dapr run \
  --app-id "$WEBHOOK_DAPR_APP_ID" \
  --app-port "$WEBHOOK_SERVER_PORT" \
  --dapr-http-port "$WEBHOOK_DAPR_HTTP_PORT" \
  --metrics-port 9090 \
  --resources-path ./components \
  -- go run ./cmd/server/main.go &

DAPR_PID=$!

echo "✅ Webhook microservice is running!"
echo "📡 Endpoints:"
echo "  - Webhook: http://$WEBHOOK_SERVER_HOST:$WEBHOOK_SERVER_PORT/webhook"
echo "  - Health: http://$WEBHOOK_SERVER_HOST:$WEBHOOK_SERVER_PORT/healthz"
echo "  - Metrics: http://$WEBHOOK_SERVER_HOST:$WEBHOOK_SERVER_PORT/metrics"
echo "  - Dapr Health: http://localhost:$WEBHOOK_DAPR_HTTP_PORT/v1.0/healthz"
echo "  - Dapr Metadata: http://localhost:$WEBHOOK_DAPR_HTTP_PORT/v1.0/metadata"
echo "  - Jaeger UI: http://localhost:16686"
echo ""
echo "📝 Test webhook:"
echo "  curl -X POST http://$WEBHOOK_SERVER_HOST:$WEBHOOK_SERVER_PORT/webhook \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -H 'X-Delivery-ID: test-123' \\"
echo "    -H 'X-GitHub-Delivery: test-123' \\"
echo "    -H 'X-Hook-ID: test-123' \\"
echo "    -H 'X-Source: github.com' \\"
echo "    -d '{\"test\": \"data\"}'"
echo ""
echo "�� Test Dapr:"
echo "  curl http://localhost:$WEBHOOK_DAPR_HTTP_PORT/v1.0/healthz"
echo "  curl http://localhost:$WEBHOOK_DAPR_HTTP_PORT/v1.0/metadata"
echo ""
echo "Press Ctrl+C to stop..."

wait "$DAPR_PID"