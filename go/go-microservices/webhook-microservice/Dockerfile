# Multi-stage Dockerfile for Webhook Microservice
# Build stage
FROM golang:1.23.9-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /app

# Copy shared module first (needed for replace directive)
COPY shared ./shared

# Set working directory for the service
WORKDIR /app/webhook-microservice

# Copy dependency files first for better layer caching
COPY go-microservices/webhook-microservice/go.mod go-microservices/webhook-microservice/go.sum ./

# Fix the replace directive to use the correct path in Docker build context
RUN sed -i 's|replace github.com/Matrics-io/the-manhattan-project-huly/go/shared => ../../shared|replace github.com/Matrics-io/the-manhattan-project-huly/go/shared => ../shared|' go.mod

# Download dependencies (cached unless go.mod/go.sum changes)
RUN go mod download

# Copy source code (this layer rebuilds when source changes, but deps are cached)
COPY go-microservices/webhook-microservice/cmd ./cmd
COPY go-microservices/webhook-microservice/internal ./internal
COPY go-microservices/webhook-microservice/components ./components
COPY go-microservices/webhook-microservice/configs ./configs

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/server/main.go

# Runtime stage
FROM alpine:3.18

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1000 -S webhook && \
    adduser -u 1000 -S webhook -G webhook

# Create directories
RUN mkdir -p /app/configs /app/components /app/internal/config

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/webhook-microservice/main .

# Copy configuration files
COPY --chown=webhook:webhook go-microservices/webhook-microservice/configs/ ./configs/
COPY --chown=webhook:webhook go-microservices/webhook-microservice/components/ ./components/
COPY --chown=webhook:webhook go-microservices/webhook-microservice/internal/config/ ./internal/config/

# Set ownership
RUN chown -R webhook:webhook /app

# Switch to non-root user
USER webhook

# Expose port
EXPOSE 2021

# Set default environment variables
# ENV WEBHOOK_SERVER_PORT=2021 \
#     WEBHOOK_SERVER_HOST=0.0.0.0 \
#     WEBHOOK_ENVIRONMENT=production \
#     WEBHOOK_LOGGING_LEVEL=info \
#     WEBHOOK_OBSERVABILITY_PROMETHEUS_ENABLED=true \
#     WEBHOOK_OBSERVABILITY_TRACING_ENABLED=false \
#     WEBHOOK_DAPR_ENABLED=true \
#     WEBHOOK_DAPR_APP_ID=webhook-microservice \
#     WEBHOOK_DAPR_HTTP_PORT=3500 \
#     WEBHOOK_DAPR_GRPC_PORT=50001 \
#     WEBHOOK_DAPR_STATE_STORE=statestore \
#     WEBHOOK_DAPR_PUBSUB_NAME=redis-pubsub \
#     WEBHOOK_DAPR_SECRET_STORE=config-secret-store \
#     WEBHOOK_WEBHOOK_TOPIC_NAME=webhook-events \
#     WEBHOOK_WEBHOOK_RATE_LIMIT_RPM=1000 \
#     WEBHOOK_WEBHOOK_WAF_ALLOW_LIST=0.0.0.0/0 \
#     WEBHOOK_REDIS_ADDR=localhost:6379 \
#     WEBHOOK_REDIS_PASSWORD="" \
#     WEBHOOK_REDIS_DB=0 \
#     WEBHOOK_REDIS_POOL_SIZE=10 \
#     WEBHOOK_REDIS_MIN_IDLE_CONNS=5 \
#     WEBHOOK_REDIS_MAX_RETRIES=3 \
#     WEBHOOK_REDIS_KEY_PREFIX=webhook: \
#     WEBHOOK_REDIS_DEFAULT_TTL=24h

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:${WEBHOOK_SERVER_PORT}/healthz || exit 1

# Command to run the application
CMD ["./main"] 