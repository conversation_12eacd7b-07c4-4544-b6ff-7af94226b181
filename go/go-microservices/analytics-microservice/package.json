{"name": "@hcengineering/go-analytics-microservice", "version": "0.1.0", "author": "Matics", "license": "EPL-2.0", "scripts": {"_phase:docker-build": "rushx docker:build", "_phase:docker-staging": "rushx docker:staging", "docker:build": "../../../common/scripts/docker_build.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice", "docker:staging": "../../../common/scripts/docker_tag.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice staging", "docker:push": "../../../common/scripts/docker_tag.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice", "docker:tbuild": "docker build -t us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice . --platform=linux/amd64 && ../../../common/scripts/docker_tag_push.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice", "docker:abuild": "docker build -t us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice . --platform=linux/arm64 && ../../../common/scripts/docker_tag_push.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice"}}