-- =====================================================
-- 
-- Tables
-- 
-- =====================================================

-- =====================================================
-- <PERSON>ra Issues Fact Local and Distributed Tables 
-- =====================================================
CREATE TABLE IF NOT EXISTS analytics.issue_fact_local ON CLUSTER 'default' (
    key String,
    labels Array(String),
    story_points Nullable(Int64),
    time_spent Nullable(Int64),
    original_estimate Nullable(String),
    progress Nullable(Int64),
    remaining_estimate Nullable(Int64),
    aggregated_time_spent Nullable(Int64),
    type Nullable(String),
    summary Nullable(String),
    parent Nullable(String),
    priority Nullable(String),
    status Nullable(String),
    creator Nullable(String),
    assignee Nullable(String),
    green_date Nullable(Int64),
    blue_date Nullable(Int64),
    red_date Nullable(Int64),
    created_at Nullable(Int64),
    due_date Nullable(Int64),
    updated_at Nullable(DateTime),
    project Nullable(String),
    fix_versions Array(String),
    affected_versions Array(String),
    sprint Nullable(Int64)
) ENGINE = MergeTree()
ORDER BY key;

CREATE TABLE IF NOT EXISTS analytics.issue_fact ON CLUSTER 'default'
AS analytics.issue_fact_local
ENGINE = Distributed('default', 'analytics', 'issue_fact_local', cityHash64(key));


-- =====================================================
-- User Dimension Local and Distributed Tables
-- =====================================================
CREATE TABLE IF NOT EXISTS analytics.user_dim_local ON CLUSTER 'default' (
    user_id String,
    name Nullable(String),
    email Nullable(String),
    updated_at DateTime
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY user_id;

CREATE TABLE IF NOT EXISTS analytics.user_dim ON CLUSTER 'default'
AS analytics.user_dim_local
ENGINE = Distributed('default', 'analytics', 'user_dim_local', cityHash64(user_id));


-- =====================================================
-- Project Dimension Local and Distributed Tables
-- =====================================================
CREATE TABLE IF NOT EXISTS analytics.project_dim_local ON CLUSTER 'default' (
    project_key String,
    project_id Nullable(Int64),
    name Nullable(String),
    updated_at DateTime
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY project_key;

CREATE TABLE IF NOT EXISTS analytics.project_dim ON CLUSTER 'default'
AS analytics.project_dim_local
ENGINE = Distributed('default', 'analytics', 'project_dim_local', cityHash64(project_key));


-- =====================================================
-- Release Dimension Local and Distributed Tables
-- =====================================================
CREATE TABLE IF NOT EXISTS analytics.release_dim_local ON CLUSTER 'default' (
    release_id String,
    version Nullable(String),
    release_date Nullable(Int64),
    project_id Nullable(Int64),
    updated_at DateTime
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY release_id;

CREATE TABLE IF NOT EXISTS analytics.release_dim ON CLUSTER 'default'
AS analytics.release_dim_local
ENGINE = Distributed('default', 'analytics', 'release_dim_local', cityHash64(release_id));


-- =====================================================
-- Sprint Dimension Local and Distributed Tables
-- =====================================================
CREATE TABLE IF NOT EXISTS analytics.sprint_dim_local ON CLUSTER 'default' (
    sprint_id Int64,
    state Nullable(String),
    name Nullable(String),
    created_at Nullable(Int64),
    start_date Nullable(Int64),
    end_date Nullable(Int64),
    project_key Nullable(String),
    updated_at DateTime
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY sprint_id;

CREATE TABLE IF NOT EXISTS analytics.sprint_dim ON CLUSTER 'default'
AS analytics.sprint_dim_local
ENGINE = Distributed('default', 'analytics', 'sprint_dim_local', cityHash64(sprint_id));


-- =====================================================
-- Jira Raw Events Local and Distributed Tables
-- =====================================================
CREATE TABLE IF NOT EXISTS analytics.jira_raw_events_local ON CLUSTER 'default' (
    payload String,
    created_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.jira_raw_events ON CLUSTER 'default'
AS analytics.jira_raw_events_local
ENGINE = Distributed('default', 'analytics', 'jira_raw_events_local', cityHash64(payload));


-- =====================================================
-- Issue Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.issue_dim_local ON CLUSTER 'default' (
    key String,
    type String,
    summary String,
    parent String,
    updated_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.issue_dim ON CLUSTER 'default'
AS analytics.issue_dim_local
ENGINE = Distributed('default', 'analytics', 'issue_dim_local', cityHash64(key));


-- =====================================================
-- Status Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.status_dim_local ON CLUSTER 'default' (
    status_id String,
    status String,
    resolution String,
    updated_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.status_dim ON CLUSTER 'default'
AS analytics.status_dim_local
ENGINE = Distributed('default', 'analytics', 'status_dim_local', cityHash64(status_id));


-- =====================================================
-- Priority Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.priority_dim_local ON CLUSTER 'default' (
    priority_id String,
    priority String,
    updated_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.priority_dim ON CLUSTER 'default'
AS analytics.priority_dim_local
ENGINE = Distributed('default', 'analytics', 'priority_dim_local', cityHash64(priority_id));


-- =====================================================
-- Pull Request Fact Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.pull_request_fact_local ON CLUSTER 'default' (
    id Int64,
    title String,
    commits Int64,
    repository_id Int64,
    user_id Int64,
    changed_files Int64,
    state Int64,
    draft Int64,
    merged Int64,
    comments Int64,
    created_at Int64,
    closed_at Int64,
    merged_at Int64,
    updated_at Int64
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.pull_request_fact ON CLUSTER 'default'
AS analytics.pull_request_fact_local
ENGINE = Distributed('default', 'analytics', 'pull_request_fact_local', cityHash64(id));


-- =====================================================
-- State Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.state_dim_local ON CLUSTER 'default' (
    id Int64,
    state String,
    draft Bool,
    merged Bool,
    comments Int64,
    updated_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.state_dim ON CLUSTER 'default'
AS analytics.state_dim_local
ENGINE = Distributed('default', 'analytics', 'state_dim_local', cityHash64(id));


-- =====================================================
-- GitHub Raw Events Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.github_raw_events_local ON CLUSTER 'default' (
    payload String,
    created_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.github_raw_events ON CLUSTER 'default'
AS analytics.github_raw_events_local
ENGINE = Distributed('default', 'analytics', 'github_raw_events_local', cityHash64(payload));

-- =====================================================
-- Repo Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.repo_dim_local ON CLUSTER 'default' (
    repo_sk Int64,
    repo_id Int64,
    owner_login String,
    repo_name String,
    default_branch String,
    is_private Bool,
    created_at DateTime,
    updated_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.repo_dim ON CLUSTER 'default'
AS analytics.repo_dim_local
ENGINE = Distributed('default', 'analytics', 'repo_dim_local', cityHash64(repo_sk));

-- =====================================================
-- Branch Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.branch_dim_local ON CLUSTER 'default' (
    branch_sk Int64,
    repo_sk Int64,
    branch_name String,
    branch_type String,
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.branch_dim ON CLUSTER 'default'
AS analytics.branch_dim_local
ENGINE = Distributed('default', 'analytics', 'branch_dim_local', cityHash64(branch_sk));

-- =====================================================
-- Team Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.team_dim_local ON CLUSTER 'default' (
    team_sk Int64,
    team_id Int64,
    team_name String,
    description String
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.team_dim ON CLUSTER 'default'
AS analytics.team_dim_local
ENGINE = Distributed('default', 'analytics', 'team_dim_local', cityHash64(team_sk));

-- =====================================================
-- Environment Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.environment_dim_local ON CLUSTER 'default' (
    env_sk Int64,
    env_name String
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.environment_dim ON CLUSTER 'default'
AS analytics.environment_dim_local
ENGINE = Distributed('default', 'analytics', 'environment_dim_local', cityHash64(env_sk));

-- =====================================================
-- Issue Type Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.issue_type_dim_local ON CLUSTER 'default' (
    issue_type_sk Int64,
    issue_type_key String
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.issue_type_dim ON CLUSTER 'default'
AS analytics.issue_type_dim_local
ENGINE = Distributed('default', 'analytics', 'issue_type_dim_local', cityHash64(issue_type_sk));

-- =====================================================
-- Severity Dimension Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.severity_dim_local ON CLUSTER 'default' (
    severity_sk Int64,
    severity_name String
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.severity_dim ON CLUSTER 'default'
AS analytics.severity_dim_local
ENGINE = Distributed('default', 'analytics', 'severity_dim_local', cityHash64(severity_sk));

-- =====================================================
-- Commit Fact Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.fact_commit_local ON CLUSTER 'default' (
    commit_sk Int64,
    repo_sk Int64,
    branch_sk Int64,
    author_user_id String,
    committer_user_id String,
    commit_sha String,
    message String,
    authored_at DateTime,
    committed_at DateTime,
    is_merge_commit Bool
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.fact_commit ON CLUSTER 'default'
AS analytics.fact_commit_local
ENGINE = Distributed('default', 'analytics', 'fact_commit_local', cityHash64(commit_sk));

-- =====================================================
-- Merge Fact Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.fact_merge_local ON CLUSTER 'default' (
    merge_sk Int64,
    repo_sk Int64,
    source_branch_sk Int64,
    target_branch_sk Int64,
    pr_number Int64,
    merged_by_user_id String,
    merge_sha String,
    merged_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.fact_merge ON CLUSTER 'default'
AS analytics.fact_merge_local
ENGINE = Distributed('default', 'analytics', 'fact_merge_local', cityHash64(merge_sk));

-- =====================================================
-- Workflow Run Fact Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.fact_workflow_run_local ON CLUSTER 'default' (
    workflow_run_sk Int64,
    repo_sk Int64,
    workflow_name String,
    status String,
    conclusion String,
    started_at DateTime,
    completed_at DateTime,
    duration_sec Int64
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.fact_workflow_run ON CLUSTER 'default'
AS analytics.fact_workflow_run_local
ENGINE = Distributed('default', 'analytics', 'fact_workflow_run_local', cityHash64(workflow_run_sk));

-- =====================================================
-- Deployment Fact Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.fact_deployment_local ON CLUSTER 'default' (
    deployment_sk Int64,
    repo_sk Int64,
    env_sk Int64,
    run_sk Int64,
    deployment_id Int64,
    main_commit_sha String,
    status String,
    deployed_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.fact_deployment ON CLUSTER 'default'
AS analytics.fact_deployment_local
ENGINE = Distributed('default', 'analytics', 'fact_deployment_local', cityHash64(deployment_sk));

-- =====================================================
-- Incident Fact Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.fact_incident_local ON CLUSTER 'default' (
    incident_sk Int64,
    repo_sk Int64,
    jira_issue_id String,
    issue_type_sk Int64,
    severity_sk Int64,
    created_at DateTime,
    resolved_at DateTime,
    linked_deployment_sk Int64
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.fact_incident ON CLUSTER 'default'
AS analytics.fact_incident_local
ENGINE = Distributed('default', 'analytics', 'fact_incident_local', cityHash64(incident_sk));

-- =====================================================
-- Bridge Commit Deployment Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.bridge_commit_deployment_local ON CLUSTER 'default' (
    commit_sk Int64,
    deployment_sk Int64
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.bridge_commit_deployment ON CLUSTER 'default'
AS analytics.bridge_commit_deployment_local
ENGINE = Distributed('default', 'analytics', 'bridge_commit_deployment_local', cityHash64(commit_sk, deployment_sk));

-- =====================================================
-- Bridge Incident Deployment Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.bridge_incident_deployment_local ON CLUSTER 'default' (
    incident_sk Int64,
    deployment_sk Int64
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.bridge_incident_deployment ON CLUSTER 'default'
AS analytics.bridge_incident_deployment_local
ENGINE = Distributed('default', 'analytics', 'bridge_incident_deployment_local', cityHash64(incident_sk, deployment_sk));

-- =====================================================
-- Bridge User Team Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.bridge_user_team_local ON CLUSTER 'default' (
    user_id String,
    team_sk Int64,
    joined_at DateTime
) ENGINE = MergeTree()
ORDER BY tuple();

CREATE TABLE IF NOT EXISTS analytics.bridge_user_team ON CLUSTER 'default'
AS analytics.bridge_user_team_local
ENGINE = Distributed('default', 'analytics', 'bridge_user_team_local', cityHash64(user_id, team_sk));


-- =====================================================
-- RAW EVENT LANDING ZONE
-- =====================================================

-- GitHub Raw Events Table (simplified – aligns with Jira pattern)
CREATE TABLE IF NOT EXISTS analytics.github_raw_events_local ON CLUSTER 'default' (
    payload String,
    created_at String
) ENGINE = MergeTree()
ORDER BY created_at;

CREATE TABLE IF NOT EXISTS analytics.github_raw_events ON CLUSTER 'default'
AS analytics.github_raw_events_local
ENGINE = Distributed('default', 'analytics', 'github_raw_events_local', cityHash64(payload));

-- =====================================================
-- CORE DIMENSIONS
-- =====================================================

-- Repository Dimension
CREATE TABLE IF NOT EXISTS analytics.repo_dim_local ON CLUSTER 'default' (
    repo_sk UInt64,
    repo_id UInt64,
    owner_login String,
    repo_name String,
    default_branch String,
    is_private Bool,
    created_at DateTime,
    updated_at DateTime
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY repo_sk;

CREATE TABLE IF NOT EXISTS analytics.repo_dim ON CLUSTER 'default'
AS analytics.repo_dim_local
ENGINE = Distributed('default', 'analytics', 'repo_dim_local', cityHash64(repo_sk));

-- Branch Dimension
CREATE TABLE IF NOT EXISTS analytics.branch_dim_local ON CLUSTER 'default' (
    branch_sk UInt64,
    repo_sk UInt64,
    branch_name String,
    branch_type String
) ENGINE = ReplacingMergeTree()
ORDER BY branch_sk;

CREATE TABLE IF NOT EXISTS analytics.branch_dim ON CLUSTER 'default'
AS analytics.branch_dim_local
ENGINE = Distributed('default', 'analytics', 'branch_dim_local', cityHash64(branch_sk));


-- Note: Reusing existing user_dim table from Jira implementation
-- No need to create a separate user dimension for GitHub

-- Team Dimension
CREATE TABLE IF NOT EXISTS analytics.team_dim_local ON CLUSTER 'default' (
    team_sk UInt64,
    team_id UInt64,
    team_name String,
    description String
) ENGINE = ReplacingMergeTree()
ORDER BY team_sk;

CREATE TABLE IF NOT EXISTS analytics.team_dim ON CLUSTER 'default'
AS analytics.team_dim_local
ENGINE = Distributed('default', 'analytics', 'team_dim_local', cityHash64(team_sk));



-- Environment Dimension
CREATE TABLE IF NOT EXISTS analytics.environment_dim_local ON CLUSTER 'default' (
    env_sk UInt8,
    env_name String
) ENGINE = ReplacingMergeTree()
ORDER BY env_sk;

CREATE TABLE IF NOT EXISTS analytics.environment_dim ON CLUSTER 'default'
AS analytics.environment_dim_local
ENGINE = Distributed('default', 'analytics', 'environment_dim_local', cityHash64(env_sk));

-- Issue Type Dimension
CREATE TABLE IF NOT EXISTS analytics.issue_type_dim_local ON CLUSTER 'default' (
    issue_type_sk UInt16,
    issue_type_key String
) ENGINE = ReplacingMergeTree()
ORDER BY issue_type_sk;

CREATE TABLE IF NOT EXISTS analytics.issue_type_dim ON CLUSTER 'default'
AS analytics.issue_type_dim_local
ENGINE = Distributed('default', 'analytics', 'issue_type_dim_local', cityHash64(issue_type_sk));

-- Severity Dimension
CREATE TABLE IF NOT EXISTS analytics.severity_dim_local ON CLUSTER 'default' (
    severity_sk UInt16,
    severity_name String
) ENGINE = ReplacingMergeTree()
ORDER BY severity_sk;

-- Date Dimension
CREATE TABLE IF NOT EXISTS analytics.date_dim_local ON CLUSTER 'default' (
    date_sk UInt32,
    date Date,
    year UInt16,
    quarter UInt8,
    month UInt8,
    day UInt8,
    day_of_week UInt8,
    is_weekend UInt8
) ENGINE = ReplacingMergeTree()
ORDER BY date_sk;

CREATE TABLE IF NOT EXISTS analytics.date_dim ON CLUSTER 'default'
AS analytics.date_dim_local
ENGINE = Distributed('default', 'analytics', 'date_dim_local', cityHash64(date_sk));


-- =====================================================
-- FACT TABLES
-- =====================================================

-- Commit Fact Table
CREATE TABLE IF NOT EXISTS analytics.fact_commit_local ON CLUSTER 'default' (
    commit_sk UInt64,
    repo_sk UInt64,
    branch_sk UInt64,
    author_user_id String,
    committer_user_id String,
    commit_sha String,
    message String,
    authored_at DateTime,
    committed_at DateTime,
    is_merge_commit Bool
) ENGINE = MergeTree()
ORDER BY (repo_sk, committed_at);

CREATE TABLE IF NOT EXISTS analytics.fact_commit ON CLUSTER 'default'
AS analytics.fact_commit_local
ENGINE = Distributed('default', 'analytics', 'fact_commit_local', cityHash64(commit_sk));

-- Merge Fact Table
CREATE TABLE IF NOT EXISTS analytics.fact_merge_local (
    merge_sk UInt64,
    repo_sk UInt64,
    source_branch_sk UInt64,
    target_branch_sk UInt64,
    pr_number UInt64,
    merged_by_user_id String,
    merge_sha String,
    merged_at DateTime
) ENGINE = MergeTree()
ORDER BY (repo_sk, merged_at);

CREATE TABLE IF NOT EXISTS analytics.fact_merge ON CLUSTER 'default'
AS analytics.fact_merge_local
ENGINE = Distributed('default', 'analytics', 'fact_merge_local', cityHash64(merge_sk));

-- Workflow Run Fact Table
CREATE TABLE IF NOT EXISTS analytics.fact_workflow_run_local ON CLUSTER 'default' (
    run_sk UInt64,
    repo_sk UInt64,
    run_id UInt64,
    workflow_name String,
    status String,
    conclusion String,
    started_at DateTime,
    completed_at DateTime,
    duration_sec UInt32,
    triggered_by_user_id String
) ENGINE = MergeTree()
ORDER BY (repo_sk, started_at);

CREATE TABLE IF NOT EXISTS analytics.fact_workflow_run ON CLUSTER 'default'
AS analytics.fact_workflow_run_local
ENGINE = Distributed('default', 'analytics', 'fact_workflow_run_local', cityHash64(run_sk));

-- Deployment Fact Table
CREATE TABLE IF NOT EXISTS analytics.fact_deployment_local ON CLUSTER 'default' (
    deployment_sk UInt64,
    repo_sk UInt64,
    env_sk UInt8,
    run_sk UInt64,
    deployment_id UInt64,
    main_commit_sha String,
    status String,
    deployed_at DateTime
) ENGINE = MergeTree()
ORDER BY (repo_sk, env_sk, deployed_at);

CREATE TABLE IF NOT EXISTS analytics.fact_deployment ON CLUSTER 'default'
AS analytics.fact_deployment_local
ENGINE = Distributed('default', 'analytics', 'fact_deployment_local', cityHash64(deployment_sk));

-- Incident Fact Table
CREATE TABLE IF NOT EXISTS analytics.fact_incident_local ON CLUSTER 'default' (
    incident_sk UInt64,
    repo_sk UInt64,
    jira_issue_id String,
    issue_type_sk UInt16,
    severity_sk UInt16,
    created_at DateTime,
    resolved_at DateTime,
    linked_deployment_sk UInt64
) ENGINE = MergeTree()
ORDER BY (repo_sk, created_at);

CREATE TABLE IF NOT EXISTS analytics.fact_incident ON CLUSTER 'default'
AS analytics.fact_incident_local
ENGINE = Distributed('default', 'analytics', 'fact_incident_local', cityHash64(incident_sk));

-- =====================================================
-- BRIDGE (Many-to-Many) TABLES
-- =====================================================

-- Commit-Deployment Bridge
CREATE TABLE IF NOT EXISTS analytics.bridge_commit_deployment_local ON CLUSTER 'default' (
    commit_sk UInt64,
    deployment_sk UInt64
) ENGINE = MergeTree()
ORDER BY (commit_sk, deployment_sk);

CREATE TABLE IF NOT EXISTS analytics.bridge_commit_deployment ON CLUSTER 'default'
AS analytics.bridge_commit_deployment_local
ENGINE = Distributed('default', 'analytics', 'bridge_commit_deployment_local', cityHash64(commit_sk));

-- Incident-Deployment Bridge
CREATE TABLE IF NOT EXISTS analytics.bridge_incident_deployment_local ON CLUSTER 'default' (
    incident_sk UInt64,
    deployment_sk UInt64
) ENGINE = MergeTree()
ORDER BY (incident_sk, deployment_sk);

CREATE TABLE IF NOT EXISTS analytics.bridge_incident_deployment ON CLUSTER 'default'
AS analytics.bridge_incident_deployment_local
ENGINE = Distributed('default', 'analytics', 'bridge_incident_deployment_local', cityHash64(incident_sk));

-- User-Team Bridge
CREATE TABLE IF NOT EXISTS analytics.bridge_user_team_local ON CLUSTER 'default' (
    user_id String,
    team_sk UInt64,
    joined_at DateTime
) ENGINE = MergeTree()
ORDER BY (user_id, team_sk);

CREATE TABLE IF NOT EXISTS analytics.bridge_user_team ON CLUSTER 'default'
AS analytics.bridge_user_team_local
ENGINE = Distributed('default', 'analytics', 'bridge_user_team_local', cityHash64(user_id));

-- =====================================================
-- Seed Jobs Local and Distributed Tables
-- =====================================================

CREATE TABLE IF NOT EXISTS analytics.seed_jobs_local ON CLUSTER 'default' (
    job_id String,
    platform String,
    category String,
    endpoint String,
    project String,
    status String,

    -- Authentication fields (stored as JSON for flexibility)
    jira_base_url Nullable(String),
    jira_email Nullable(String),
    jira_token Nullable(String),
    github_base_url Nullable(String),
    github_token Nullable(String),

    -- Timestamps
    started_at DateTime,
    updated_at DateTime,

    -- Error information
    error_message Nullable(String),

    -- Optional earliest date for bulk fetching
    earliest_date Nullable(DateTime)
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY job_id;

CREATE TABLE IF NOT EXISTS analytics.seed_jobs ON CLUSTER 'default'
AS analytics.seed_jobs_local
ENGINE = Distributed('default', 'analytics', 'seed_jobs_local', cityHash64(job_id));
