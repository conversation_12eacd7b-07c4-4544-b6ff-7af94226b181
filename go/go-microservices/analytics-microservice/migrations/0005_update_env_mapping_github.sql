-- =============================================================
--  FIX ENVIRONMENT MAPPING FOR GITHUB DEPLOYMENTS (0005)
--  • Adds canonical environment names (production, staging, …)
--  • Maps common aliases (main, master, canary, github-pages, etc.)
--  • Re-creates mv_fact_deployment to use the new dimension instead
-- =============================================================

-- -----------------------------------------------------------------
-- DIMENSION WITH CANONICAL ENVIRONMENT KEYS
-- -----------------------------------------------------------------
DROP TABLE IF EXISTS analytics.environment_dim ON CLUSTER 'default';
DROP TABLE IF EXISTS analytics.environment_dim_local ON CLUSTER 'default';
CREATE TABLE analytics.environment_dim_local ON CLUSTER 'default'
(
    env_sk   UInt8   COMMENT '1=prod,2=staging,3=dev,4=test',
    env_name String  COMMENT 'lower-case canonical or alias name'
) ENGINE = ReplacingMergeTree()
ORDER BY (env_name);

CREATE TABLE IF NOT EXISTS analytics.environment_dim ON CLUSTER 'default'
AS analytics.environment_dim_local
ENGINE = Distributed('default', 'analytics', 'environment_dim_local', cityHash64(env_sk)); 


-- Insert / upsert canonical names and aliases
INSERT INTO analytics.environment_dim (env_sk, env_name) VALUES
    -- Production aliases
    (1,'production'),(1,'prod'),(1,'prd'),(1,'main'),(1,'master'),
    (1,'release'),(1,'stable'),(1,'trunk'),(1,'live'),
    (1,'canary'),(1,'blue'),(1,'green'),(1,'edge'),
    -- GitHub-Pages / Vercel
    (1,'github-pages'),(1,'gh-pages'),(1,'preview'),(1,'vercel-prod'),
    -- Regional prod
    (1,'production-us'),(1,'production-eu'),(1,'prod-us'),(1,'prod-eu'),

    -- Staging / pre-prod aliases
    (2,'staging'),(2,'stage'),(2,'uat'),(2,'preprod'),(2,'pre-production'),(2,'qa'),

    -- Development / integration aliases
    (3,'development'),(3,'dev'),(3,'sandbox'),(3,'alpha'),(3,'beta'),(3,'local'),(3,'integration'),(3,'int'),

    -- Test / CI aliases
    (4,'test'),(4,'testing'),(4,'ci'),(4,'continuous-integration');

-- -----------------------------------------------------------------
-- REPLACE EXISTING MATERIALISED VIEW FOR DEPLOYMENTS
-- -----------------------------------------------------------------
DROP VIEW IF EXISTS analytics.mv_fact_deployment ON CLUSTER 'default';

CREATE MATERIALIZED VIEW analytics.mv_fact_deployment ON CLUSTER 'default'
TO analytics.fact_deployment_local
AS
SELECT
    JSONExtractUInt(payload,'deployment','id')                  AS deployment_sk,
    JSONExtractUInt(payload,'repository','id')                  AS repo_sk,
    coalesce(env.env_sk, 1)                                     AS env_sk,
    0                                                          AS run_sk,
    JSONExtractUInt(payload,'deployment','id')                  AS deployment_id,
    JSONExtractString(payload,'deployment','sha')               AS main_commit_sha,
    JSONExtractString(payload,'deployment_status','state')      AS status,
    parseDateTimeBestEffort(JSONExtractString(payload,'deployment_status','created_at')) AS deployed_at
FROM analytics.github_raw_events_local gr
LEFT JOIN analytics.environment_dim_local env ON lower(JSONExtractString(gr.payload,'deployment_status','environment')) = env.env_name
WHERE JSONHas(gr.payload, 'deployment_status'); 