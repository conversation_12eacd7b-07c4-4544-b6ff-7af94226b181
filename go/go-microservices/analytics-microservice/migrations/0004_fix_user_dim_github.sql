-- =============================================================
-- 0004 - FIX USER DIM MATERIALIZED VIEW FOR GITHUB EVENTS
-- Re-creates mv_user_dim_github so that user_id is extracted as UInt64
-- and cast to String, preventing empty values when sender.id is numeric.
-- =============================================================

-- Drop the old materialized view if it exists
DROP VIEW IF EXISTS analytics.mv_user_dim_github ON CLUSTER 'default';

-- Recreate the materialized view with the correct extraction logic
CREATE MATERIALIZED VIEW analytics.mv_user_dim_github ON CLUSTER 'default'
TO analytics.user_dim_local
AS SELECT
    toString(JSONExtractUInt(payload, 'sender', 'id')) AS user_id,
    JSONExtractString(payload, 'sender', 'login') AS name,
    JSONExtractString(payload, 'sender', 'email') AS email
FROM analytics.github_raw_events
WHERE JSONExtractString(payload, 'sender', 'login') != ''; 