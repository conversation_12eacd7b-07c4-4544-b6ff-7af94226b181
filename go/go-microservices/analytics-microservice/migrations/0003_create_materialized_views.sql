-- =====================================================
-- 
-- Materialized Views
-- 
-- =====================================================

-- =====================================================
-- Issues Materialized View
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_issue_fact ON CLUSTER 'default'
TO analytics.issue_fact_local
AS
SELECT
    JSONExtractString(payload,'issue', 'key') as key,
    JSONExtractArrayRaw(payload,'issue', 'fields', 'labels') as labels,
    JSONExtractInt(payload,'issue', 'fields', 'customfield_10004') as story_points,
    JSONExtractInt(payload,'issue', 'fields', 'timespent') as time_spent,
    JSONExtractString(payload,'issue', 'fields', 'timeoriginalestimate') as original_estimate,
    JSONExtractInt(payload,'issue', 'fields', 'progress', 'progress') as progress,
    JSONExtractInt(payload,'issue', 'fields', 'timeestimate') as remaining_estimate,
    JSONExtractInt(payload,'issue', 'fields', 'aggregatetimespent') as aggregated_time_spent,
    JSONExtractString(payload,'issue', 'key') as type,
    JSONExtractString(payload,'issue', 'key') as summary,
    JSONExtractString(payload,'issue', 'key') as parent,
    JSONExtractString(payload,'issue', 'fields', 'priority', 'id') as priority,
    JSONExtractString(payload,'issue', 'fields', 'status', 'id') as status,
    JSONExtractString(payload,'issue', 'fields', 'creator', 'accountId') as creator,
    JSONExtractString(payload,'issue', 'fields', 'assignee', 'accountId') as assignee,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload,'issue', 'fields', 'customfield_11397'))
            )
        ) AS Nullable(UInt64)
    ) AS green_date,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload,'issue', 'fields', 'customfield_11398'))
            )
        ) AS Nullable(UInt64)
    ) AS blue_date,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload,'issue', 'fields', 'customfield_11399'))
            )
        ) AS Nullable(UInt64)
    ) AS red_date,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload,'issue', 'fields', 'created'))
            )
        ) AS Nullable(UInt64)
    ) AS created_at,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload,'issue', 'fields', 'duedate'))
            )
        ) AS Nullable(UInt64)
    ) AS due_date,
    now() AS updated_at,
    JSONExtractString(payload,'issue', 'fields', 'project', 'key') as project,
    arrayMap(x -> JSONExtractString(x, 'id'), JSONExtractArrayRaw(payload, 'issue', 'fields', 'fixVersions')) AS fix_versions,
    arrayMap(x -> JSONExtractString(x, 'id'), JSONExtractArrayRaw(payload, 'issue', 'fields', 'versions')) AS affected_versions,
    JSONExtractInt(sprint_raw, 'id') AS sprint
FROM analytics.jira_raw_events_local
ARRAY JOIN
  JSONExtractArrayRaw(payload, 'issue', 'fields', 'customfield_10007') AS sprint_raw

WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
    OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created')
  AND (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created'
    OR arrayExists(item -> JSONExtractString(item, 'fieldId') IN (
      'labels', 'customfield_10004', 'timespent', 'timeoriginalestimate', 
      'progress', 'timeestimate', 'aggregatetimespent', 'priority', 'status', 
      'assignee', 'customfield_11397', 'customfield_11398', 'customfield_11399', 
      'duedate', 'updated', 'fixVersions', 'versions', 'customfield_10007', 
      'issuetype', 'summary', 'parent'
    ), JSONExtractArrayRaw(payload, 'changelog', 'items'))
  );

-- =====================================================
-- Issues Dimension Materialized View
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_issue_dim ON CLUSTER 'default'
TO analytics.issue_dim_local
AS SELECT
    JSONExtractString(payload, 'issue', 'key') as key,
    JSONExtractString(payload, 'issue','fields', 'issuetype', 'name') as type,
    JSONExtractString(payload, 'issue','fields', 'summary') as summary,
    JSONExtractString(payload, 'issue','fields', 'parent', 'key') as parent,
    now() AS updated_at
FROM analytics.jira_raw_events_local
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
    OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created')
  AND (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created'
    OR arrayExists(item -> JSONExtractString(item, 'fieldId') IN ( 
      'issuetype', 'summary', 'parent'
    ), JSONExtractArrayRaw(payload, 'changelog', 'items'))
  );

-- =====================================================
-- Status Dimension Materialized View
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_status_dim ON CLUSTER 'default'
TO analytics.status_dim_local
AS SELECT
    JSONExtractString(payload, 'issue','fields', 'status', 'id') as status_id,
    JSONExtractString(payload, 'issue','fields', 'status', 'name') as status,
    JSONExtractString(payload, 'issue','fields', 'resolution', 'name') as resolution,
    now() AS updated_at
FROM analytics.jira_raw_events_local
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
    OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created')
  AND (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created'
    OR arrayExists(item -> JSONExtractString(item, 'fieldId') IN ('status'), JSONExtractArrayRaw(payload, 'changelog', 'items'))
  );

-- =====================================================
-- Priority Dimension Materialized View
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_priority_dim ON CLUSTER 'default'
TO analytics.priority_dim_local
AS SELECT
    JSONExtractInt(payload, 'issue','fields', 'priority', 'id') as priority_id,
    JSONExtractString(payload, 'issue','fields', 'priority', 'name') as priority,
    now() AS updated_at
FROM analytics.jira_raw_events_local
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
    OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created')
  AND (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created'
    OR arrayExists(item -> JSONExtractString(item, 'fieldId') IN ('priority'), JSONExtractArrayRaw(payload, 'changelog', 'items'))
  );

-- =====================================================
-- Project Dimension Materialized View
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_project_dim_local ON CLUSTER 'default'
TO analytics.project_dim_local
AS SELECT
    JSONExtractString(payload, 'issue','fields', 'project', 'key') as project_key,
    JSONExtractInt(payload, 'issue','fields', 'project', 'id') as project_id,
    JSONExtractString(payload, 'issue','fields', 'project', 'name') as name,
    now() as updated_at
FROM analytics.jira_raw_events_local
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
  OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created';

-- =====================================================
-- User Dimension Materialized View for assignee
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_assignee_dim ON CLUSTER 'default'
TO analytics.user_dim_local
AS SELECT
    JSONExtractString(payload, 'issue','fields', 'assignee', 'displayName') as name,
    JSONExtractString(payload, 'issue','fields', 'assignee', 'accountId') as user_id,
    JSONExtractString(payload, 'issue','fields', 'assignee', 'emailAddress') as email,
    now() as updated_at
FROM analytics.jira_raw_events_local
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
    OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created')
  AND (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created'
    OR arrayExists(item -> JSONExtractString(item, 'fieldId') IN ( 'assignee'), JSONExtractArrayRaw(payload, 'changelog', 'items'))
  );

-- =====================================================
-- User Dimension Materialized View for creator
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_creator_dim ON CLUSTER 'default'
TO analytics.user_dim_local
AS SELECT
    JSONExtractString(payload, 'issue','fields', 'creator', 'displayName') as name,
    JSONExtractString(payload, 'issue','fields', 'creator', 'accountId') as user_id,
    JSONExtractString(payload, 'issue','fields', 'creator', 'emailAddress') as email,
    now() as updated_at
FROM analytics.jira_raw_events_local
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
  OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created';

-- =====================================================
-- Sprints Materialized View from Issues
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_issue_sprint_dim_local ON CLUSTER 'default'
TO analytics.sprint_dim_local
AS
SELECT
    JSONExtractString(sprint_raw, 'id') AS sprint_id,
    JSONExtractString(sprint_raw, 'state') AS state,
    JSONExtractString(sprint_raw, 'name') AS name,
    JSONExtractString(payload, 'issue','fields', 'project', 'key') AS project_key,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(sprint_raw, 'startDate'))
            )
        ) AS Nullable(UInt64)
    ) AS start_date,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(sprint_raw, 'endDate'))
            )
        ) AS Nullable(UInt64)
    ) AS end_date,
    now() as updated_at
FROM analytics.jira_raw_events_local
ARRAY JOIN JSONExtractArrayRaw(payload, 'issue','fields', 'customfield_10007') AS sprint_raw
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_updated'
    OR JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created')
  AND (
    JSONExtractString(payload, 'webhookEvent') = 'jira:issue_created'
    OR arrayExists(item -> JSONExtractString(item, 'fieldId') IN ('customfield_10007'), JSONExtractArrayRaw(payload, 'changelog', 'items'))
  );

-- =====================================================
-- Sprints Materialized View
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_sprint_dim_local ON CLUSTER 'default'
TO analytics.sprint_dim_local
AS SELECT
    JSONExtractInt(payload, 'sprint', 'id') as sprint_id,
    JSONExtractString(payload, 'sprint', 'state') as state,
    JSONExtractString(payload, 'sprint', 'name') as name,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload, 'sprint', 'createdDate'))
            )
        ) AS Nullable(UInt64)
    ) AS created_at,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload, 'sprint', 'startDate'))
            )
        ) AS Nullable(UInt64)
    ) AS start_date,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload, 'sprint', 'endDate'))
            )
        ) AS Nullable(UInt64)
    ) AS end_date,
    now() as updated_at
FROM analytics.jira_raw_events_local  
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'sprint_started'
    OR JSONExtractString(payload, 'webhookEvent') = 'sprint_closed'
    OR JSONExtractString(payload, 'webhookEvent') = 'sprint_updated')
  ;

-- =====================================================
-- Releases Materialized View
-- =====================================================
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_release_dim_local ON CLUSTER 'default'
TO analytics.release_dim_local
AS SELECT
    JSONExtractString(payload, 'version', 'id') as release_id,
    JSONExtractString(payload, 'version', 'name') as version,
    CAST(
        toYYYYMMDD(
            toDate(
                parseDateTimeBestEffortOrNull(JSONExtractString(payload, 'version', 'releaseDate'))
            )
        ) AS Nullable(UInt64)
    ) AS release_date,
    JSONExtractInt(payload, 'version', 'projectId') as project_id,
    now() as updated_at
FROM analytics.jira_raw_events_local
WHERE
  NOT JSONHas(payload, 'webhookEvent')
  OR (
    JSONExtractString(payload, 'webhookEvent') = 'jira:version_released'
    OR JSONExtractString(payload, 'webhookEvent') = 'jira:version_updated')
  ;


-- =====================================================
-- MATERIALIZED VIEWS FOR GITHUB EVENTS
-- =====================================================

-- Repository Dimension from GitHub Events
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_repo_dim
TO analytics.repo_dim_local
AS SELECT
    JSONExtractUInt(payload, 'repository', 'id') as repo_sk,
    JSONExtractUInt(payload, 'repository', 'id') as repo_id,
    JSONExtractString(payload, 'repository', 'owner', 'login') as owner_login,
    JSONExtractString(payload, 'repository', 'name') as repo_name,
    JSONExtractString(payload, 'repository', 'default_branch') as default_branch,
    JSONExtractBool(payload, 'repository', 'private') as is_private,
    parseDateTimeBestEffort(JSONExtractString(payload, 'repository', 'created_at')) as created_at,
    parseDateTimeBestEffort(JSONExtractString(payload, 'repository', 'updated_at')) as updated_at
FROM analytics.github_raw_events_local
WHERE JSONHas(payload, 'repository');

-- User Dimension from GitHub Events (reusing existing user_dim)
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_user_dim_github
TO analytics.user_dim_local
AS SELECT
    JSONExtractString(payload, 'sender', 'id') as user_id,
    JSONExtractString(payload, 'sender', 'login') as name,
    JSONExtractString(payload, 'sender', 'email') as email
FROM analytics.github_raw_events_local
WHERE JSONExtractString(payload, 'sender', 'login') != '';

-- Commit Fact from Push Events
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_fact_commit
TO analytics.fact_commit_local
AS SELECT
    cityHash64(concat(
        toString(JSONExtractUInt(payload, 'repository', 'id')),
        JSONExtractString(commit_json, 'id')
    )) as commit_sk,
    JSONExtractUInt(payload, 'repository', 'id') as repo_sk,
    0 as branch_sk, -- Will be populated separately
    JSONExtractString(commit_json, 'author', 'username') as author_user_id,
    JSONExtractString(commit_json, 'committer', 'username') as committer_user_id,
    JSONExtractString(commit_json, 'id') as commit_sha,
    JSONExtractString(commit_json, 'message') as message,
    parseDateTimeBestEffort(JSONExtractString(commit_json, 'timestamp')) as authored_at,
    parseDateTimeBestEffort(JSONExtractString(commit_json, 'timestamp')) as committed_at,
    false as is_merge_commit
FROM (
    SELECT
        payload,
        arrayJoin(JSONExtractArrayRaw(payload, 'commits')) as commit_json
    FROM analytics.github_raw_events_local
    WHERE JSONHas(payload, 'commits')
);

-- Workflow Run Fact from Workflow Events
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_fact_workflow_run
TO analytics.fact_workflow_run_local
AS SELECT
    JSONExtractUInt(payload, 'workflow_run', 'id') as run_sk,
    JSONExtractUInt(payload, 'repository', 'id') as repo_sk,
    JSONExtractUInt(payload, 'workflow_run', 'id') as run_id,
    JSONExtractString(payload, 'workflow_run', 'name') as workflow_name,
    JSONExtractString(payload, 'workflow_run', 'status') as status,
    JSONExtractString(payload, 'workflow_run', 'conclusion') as conclusion,
    parseDateTimeBestEffort(JSONExtractString(payload, 'workflow_run', 'run_started_at')) as started_at,
    parseDateTimeBestEffort(JSONExtractString(payload, 'workflow_run', 'updated_at')) as completed_at,
    dateDiff('second', 
        parseDateTimeBestEffort(JSONExtractString(payload, 'workflow_run', 'run_started_at')),
        parseDateTimeBestEffort(JSONExtractString(payload, 'workflow_run', 'updated_at'))
    ) as duration_sec,
    JSONExtractString(payload, 'workflow_run', 'actor', 'id') as triggered_by_user_id
FROM analytics.github_raw_events_local
WHERE JSONHas(payload, 'workflow_run');

-- Deployment Fact from Deployment Events
CREATE MATERIALIZED VIEW IF NOT EXISTS analytics.mv_fact_deployment
TO analytics.fact_deployment_local
AS SELECT
    JSONExtractUInt(payload, 'deployment', 'id') as deployment_sk,
    JSONExtractUInt(payload, 'repository', 'id') as repo_sk,
    CASE 
        WHEN JSONExtractString(payload, 'deployment', 'environment') = 'production' THEN 1
        WHEN JSONExtractString(payload, 'deployment', 'environment') = 'staging' THEN 2
        WHEN JSONExtractString(payload, 'deployment', 'environment') = 'development' THEN 3
        ELSE 4
    END as env_sk,
    0 as run_sk, -- To be linked separately
    JSONExtractUInt(payload, 'deployment', 'id') as deployment_id,
    JSONExtractString(payload, 'deployment', 'sha') as main_commit_sha,
    JSONExtractString(payload, 'deployment_status', 'state') as status,
    parseDateTimeBestEffort(JSONExtractString(payload, 'deployment_status', 'created_at')) as deployed_at
FROM analytics.github_raw_events_local
WHERE JSONHas(payload, 'deployment_status');

-- =====================================================
-- DORA METRICS CALCULATION VIEWS
-- =====================================================

-- Deployment Frequency View
CREATE VIEW IF NOT EXISTS analytics.v_deployment_frequency ON CLUSTER 'default' AS
SELECT 
    r.repo_name,
    r.owner_login,
    toYYYYMM(d.deployed_at) as period,
    count(*) as deployment_count,
    count(*) / 30.0 as deployments_per_day -- Approximate monthly average
FROM analytics.fact_deployment d
JOIN analytics.repo_dim_local r ON d.repo_sk = r.repo_sk
WHERE d.env_sk = 1 -- Production only
  AND d.status = 'success'
GROUP BY r.repo_name, r.owner_login, toYYYYMM(d.deployed_at)
ORDER BY period DESC;

-- Lead Time for Changes View
CREATE VIEW IF NOT EXISTS analytics.v_lead_time_for_changes ON CLUSTER 'default' AS
SELECT 
    r.repo_name,
    r.owner_login,
    toYYYYMM(d.deployed_at) as period,
    median(dateDiff('hour', m.merged_at, d.deployed_at)) as median_lead_time_hours
FROM analytics.fact_deployment d
JOIN analytics.bridge_commit_deployment_local bcd ON d.deployment_sk = bcd.deployment_sk
JOIN analytics.fact_commit_local c ON bcd.commit_sk = c.commit_sk
JOIN analytics.fact_merge_local m ON c.commit_sha = m.merge_sha
JOIN analytics.repo_dim_local r ON d.repo_sk = r.repo_sk
WHERE d.env_sk = 1 -- Production only
  AND d.status = 'success'
GROUP BY r.repo_name, r.owner_login, toYYYYMM(d.deployed_at)
ORDER BY period DESC;

-- Mean Time to Restore View
CREATE VIEW IF NOT EXISTS analytics.v_mean_time_to_restore ON CLUSTER 'default' AS
SELECT 
    r.repo_name,
    r.owner_login,
    toYYYYMM(i.created_at) as period,
    median(dateDiff('minute', i.created_at, i.resolved_at)) as median_mttr_minutes
FROM analytics.fact_incident i
JOIN analytics.repo_dim_local r ON i.repo_sk = r.repo_sk
WHERE i.resolved_at IS NOT NULL
GROUP BY r.repo_name, r.owner_login, toYYYYMM(i.created_at)
ORDER BY period DESC;

-- Change Failure Rate View
CREATE VIEW IF NOT EXISTS analytics.v_change_failure_rate ON CLUSTER 'default' AS
SELECT 
    r.repo_name,
    r.owner_login,
    toYYYYMM(d.deployed_at) as period,
    count(*) as total_deployments,
    count(bid.deployment_sk) as failed_deployments,
    (count(bid.deployment_sk) * 100.0) / count(*) as failure_rate_percent
FROM analytics.fact_deployment d
JOIN analytics.repo_dim_local r ON d.repo_sk = r.repo_sk
LEFT JOIN analytics.bridge_incident_deployment_local bid ON d.deployment_sk = bid.deployment_sk
WHERE d.env_sk = 1 -- Production only
  AND d.status = 'success'
GROUP BY r.repo_name, r.owner_login, toYYYYMM(d.deployed_at)
ORDER BY period DESC; 