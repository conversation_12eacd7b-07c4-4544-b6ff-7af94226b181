package app

import (
	"analytics-microservice/internal/domain"
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"log/slog"
)

// SeedRequest represents a request to seed a specific category for a project
type SeedRequest struct {
	Source   string `json:"source"`   // "jira" or "github"
	Category string `json:"category"` // "issues", "sprints", "pull_requests", etc.
	Endpoint string `json:"endpoint"` // API endpoint like "/issues", "/sprints"
	Project  string `json:"project"`  // Jira project key or GitHub repo name

	// Optional earliest date - won't fetch data older than this (RFC3339 format)
	EarliestDate string `json:"earliest_date,omitempty"`

	// Jira Authentication (required when source is "jira")
	JiraBaseURL string `json:"jira_base_url,omitempty"`
	JiraEmail   string `json:"jira_email,omitempty"`
	JiraToken   string `json:"jira_token,omitempty"`

	// GitHub Authentication (required when source is "github")
	GitHubBaseURL string `json:"github_base_url,omitempty"`
	GitHubToken   string `json:"github_token,omitempty"`
}

// SeedResponse represents the response from a seed request
type SeedResponse struct {
	JobID  string `json:"job_id"`
	Status string `json:"status"`
}

type SeedService struct {
	clickhouseClient ClickHouseClient
	jobRepository    SeedJobRepository
	genericSeeder    GenericSeeder
	logger           *slog.Logger
}

func NewSeedService(clickhouseClient ClickHouseClient, jobRepository SeedJobRepository, genericSeeder GenericSeeder, logger *slog.Logger) *SeedService {
	return &SeedService{
		clickhouseClient: clickhouseClient,
		jobRepository:    jobRepository,
		genericSeeder:    genericSeeder,
		logger:           logger,
	}
}

// SeedJob creates a new seeding job and processes it asynchronously
func (s *SeedService) SeedJob(ctx context.Context, request SeedRequest) (*SeedResponse, error) {
	s.logger.Info("Creating new seed job",
		"source", request.Source,
		"category", request.Category,
		"project", request.Project)

	// Validate request
	if err := s.validateSeedRequest(request); err != nil {
		return nil, fmt.Errorf("invalid seed request: %w", err)
	}

	// Convert source to platform
	platform, err := s.convertSourceToPlatform(request.Source)
	if err != nil {
		return nil, fmt.Errorf("invalid source: %w", err)
	}

	// Create authentication object
	githubBaseURL := request.GitHubBaseURL
	if githubBaseURL == "" {
		githubBaseURL = "https://api.github.com"
	}

	auth := domain.SeedJobAuth{
		JiraBaseURL:   request.JiraBaseURL,
		JiraEmail:     request.JiraEmail,
		JiraToken:     request.JiraToken,
		GitHubBaseURL: githubBaseURL,
		GitHubToken:   request.GitHubToken,
	}

	// Parse earliest date if provided
	var earliestDate *time.Time
	if request.EarliestDate != "" {
		parsed, err := time.Parse(time.RFC3339, request.EarliestDate)
		if err != nil {
			return nil, fmt.Errorf("invalid earliest_date format, expected RFC3339: %w", err)
		}
		earliestDate = &parsed
	}

	// Create domain job (ID is auto-generated)
	job, err := domain.NewSeedJob(request.Category, request.Endpoint, request.Project, platform, auth, earliestDate)
	if err != nil {
		return nil, fmt.Errorf("failed to create seed job: %w", err)
	}

	// Store job
	if err := s.jobRepository.Store(ctx, job); err != nil {
		return nil, fmt.Errorf("failed to store seed job: %w", err)
	}

	// Start processing asynchronously
	go s.processSeedJob(context.Background(), job)

	return &SeedResponse{
		JobID:  job.ID(),
		Status: string(job.Status()),
	}, nil
}

func (s *SeedService) GetTableNames() ([]string, error) {
	s.logger.Info("Getting table names from analytics database")

	// Get table names using the injected client
	tableNames, err := s.clickhouseClient.GetTableNames()
	if err != nil {
		s.logger.Error("Failed to get table names via ClickHouse client", "error", err)
		return nil, fmt.Errorf("failed to get table names via ClickHouse client: %w", err)
	}

	s.logger.Info("Successfully retrieved table names", "count", len(tableNames))
	return tableNames, nil
}

func (s *SeedService) RunMigrations() error {
	s.logger.Info("Running database migrations")

	// Run migrations using the injected client
	if err := s.clickhouseClient.AutoMigrate(); err != nil {
		s.logger.Error("Failed to run migrations via ClickHouse client", "error", err)
		return fmt.Errorf("failed to run migrations via ClickHouse client: %w", err)
	}

	s.logger.Info("Migrations completed successfully")
	return nil
}

func (s *SeedService) DropAllTables() error {
	s.logger.Info("Dropping all tables in analytics database")

	// Drop all tables using the injected client
	if err := s.clickhouseClient.DropAllTables(); err != nil {
		s.logger.Error("Failed to drop all tables via ClickHouse client", "error", err)
		return fmt.Errorf("failed to drop all tables via ClickHouse client: %w", err)
	}

	s.logger.Info("All tables dropped successfully")
	return nil
}

func (s *SeedService) PopulateDateDimension() error {
	s.logger.Info("Populating date dimension table")

	// Populate date dimension using the injected client
	if err := s.clickhouseClient.PopulateDateDimension(); err != nil {
		s.logger.Error("Failed to populate date dimension via ClickHouse client", "error", err)
		return fmt.Errorf("failed to populate date dimension via ClickHouse client: %w", err)
	}

	s.logger.Info("Date dimension population completed successfully")
	return nil
}

// GetSeedJobStatus retrieves the status of a seed job by ID
func (s *SeedService) GetSeedJobStatus(ctx context.Context, jobID string) (*domain.SeedJob, error) {
	job, err := s.jobRepository.GetByID(ctx, jobID)
	if err != nil {
		return nil, fmt.Errorf("failed to get seed job: %w", err)
	}
	return job, nil
}

// GetAllSeedJobs retrieves all seed jobs
func (s *SeedService) GetAllSeedJobs(ctx context.Context) ([]*domain.SeedJob, error) {
	jobs, err := s.jobRepository.GetJobs(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get seed jobs: %w", err)
	}
	return jobs, nil
}

// Helper methods
func (s *SeedService) validateSeedRequest(request SeedRequest) error {
	if request.Source == "" {
		return fmt.Errorf("source cannot be empty")
	}
	if request.Category == "" {
		return fmt.Errorf("category cannot be empty")
	}
	if request.Endpoint == "" {
		return fmt.Errorf("endpoint cannot be empty")
	}
	if request.Project == "" {
		return fmt.Errorf("project cannot be empty")
	}

	// Validate source
	if request.Source != "jira" && request.Source != "github" {
		return fmt.Errorf("source must be either 'jira' or 'github'")
	}

	// Validate authentication fields based on source
	if request.Source == "jira" {
		if request.JiraBaseURL == "" {
			return fmt.Errorf("jira_base_url is required when source is 'jira'")
		}
		if request.JiraEmail == "" {
			return fmt.Errorf("jira_email is required when source is 'jira'")
		}
		if request.JiraToken == "" {
			return fmt.Errorf("jira_token is required when source is 'jira'")
		}
	}

	if request.Source == "github" {
		if request.GitHubToken == "" {
			return fmt.Errorf("github_token is required when source is 'github'")
		}
		// Note: GitHubBaseURL will default to "https://api.github.com" if empty in the auth object
	}

	return nil
}

func (s *SeedService) convertSourceToPlatform(source string) (domain.SeedJobPlatform, error) {
	switch strings.ToLower(source) {
	case "jira":
		return domain.SeedJobPlatformJira, nil
	case "github":
		return domain.SeedJobPlatformGitHub, nil
	default:
		return "", fmt.Errorf("unsupported source: %s", source)
	}
}

func (s *SeedService) processSeedJob(ctx context.Context, job *domain.SeedJob) {
	s.logger.Info("Starting seed job processing", "job_id", job.ID(), "category", job.Category(), "project", job.Project())

	// Start the job
	if err := job.Start(); err != nil {
		s.logger.Error("Failed to start job", "job_id", job.ID(), "error", err)
		return
	}

	// Update job status in repository
	if err := s.jobRepository.Update(ctx, job); err != nil {
		s.logger.Error("Failed to update job status to running", "job_id", job.ID(), "error", err)
		return
	}

	// Process the actual seeding based on job category and platform
	if err := s.executeSeeding(ctx, job); err != nil {
		s.logger.Error("Seeding failed", "job_id", job.ID(), "error", err)

		// Mark job as failed
		if failErr := job.Fail(err.Error()); failErr != nil {
			s.logger.Error("Failed to mark job as failed", "job_id", job.ID(), "error", failErr)
		}

		// Update job status in repository
		if updateErr := s.jobRepository.Update(ctx, job); updateErr != nil {
			s.logger.Error("Failed to update job status to failed", "job_id", job.ID(), "error", updateErr)
		}
		return
	}

	// Complete the job
	if err := job.Complete(); err != nil {
		s.logger.Error("Failed to complete job", "job_id", job.ID(), "error", err)
		return
	}

	// Update job status in repository
	if err := s.jobRepository.Update(ctx, job); err != nil {
		s.logger.Error("Failed to update job status to completed", "job_id", job.ID(), "error", err)
		return
	}

	s.logger.Info("Seed job completed successfully", "job_id", job.ID())
}

// executeSeeding performs the actual seeding based on job parameters
func (s *SeedService) executeSeeding(ctx context.Context, job *domain.SeedJob) error {
	s.logger.Info("Executing seeding",
		"job_id", job.ID(),
		"platform", job.Platform(),
		"category", job.Category(),
		"project", job.Project(),
		"endpoint", job.Endpoint())

	// Execute generic seeding with retry logic
	return s.retryWithBackoff(func() error {
		return s.genericSeeder.Seed(ctx, job, job.Endpoint(), job.Project())
	})
}

// retryWithBackoff implements exponential backoff retry logic
func (s *SeedService) retryWithBackoff(operation func() error) error {
	maxRetries := 3
	baseDelay := time.Second * 2

	for attempt := 0; attempt < maxRetries; attempt++ {
		err := operation()
		if err == nil {
			return nil
		}

		if attempt == maxRetries-1 {
			return fmt.Errorf("operation failed after %d attempts: %w", maxRetries, err)
		}

		// Calculate exponential backoff delay
		delay := time.Duration(float64(baseDelay) * math.Pow(2, float64(attempt)))
		s.logger.Warn("Operation failed, retrying",
			"attempt", attempt+1,
			"max_retries", maxRetries,
			"delay", delay,
			"error", err)

		time.Sleep(delay)
	}

	return fmt.Errorf("unexpected end of retry loop")
}
