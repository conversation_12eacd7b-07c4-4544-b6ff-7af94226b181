package app

import (
	"context"
	"fmt"
	"time"

	"analytics-microservice/internal/domain"
)

type GetProjectSprintsQuery struct {
	ProjectKey string
}

type GetProjectReleasesQuery struct {
	ProjectKey string
}

// New query for project data
type GetProjectDataQuery struct {
	ProjectIds []string
	StartDate  time.Time
	EndDate    time.Time
	IssueType  string // story, task, sub-task, bug, epic
}

type JiraService struct {
	jiraRepo JiraRepository
}

func NewJiraService(repo JiraRepository) *JiraService {
	return &JiraService{jiraRepo: repo}
}

func (s *JiraService) HandleGetProjectSprints(
	ctx context.Context,
	q GetProjectSprintsQuery,
) ([]*domain.Sprint, error) {
	if q.ProjectKey == "" {
		return nil, fmt.Errorf("projectKey is required")
	}
	return s.jiraRepo.GetProjectSprints(ctx, q.ProjectKey)
}

func (s *JiraService) HandleGetProjectReleases(
	ctx context.Context,
	q GetProjectReleasesQuery,
) ([]*domain.Release, error) {
	if q.ProjectKey == "" {
		return nil, fmt.Errorf("projectKey is required")
	}
	return s.jiraRepo.GetProjectReleases(ctx, q.ProjectKey)
}

func (s *JiraService) HandleGetIssueData(ctx context.Context, q GetProjectDataQuery) ([]*domain.Issue, error) {
	return s.jiraRepo.GetProjectIssues(ctx, q.ProjectIds, q.StartDate, q.EndDate, q.IssueType)
}
