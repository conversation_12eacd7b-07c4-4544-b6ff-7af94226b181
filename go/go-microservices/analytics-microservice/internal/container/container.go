package container

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"strings"
	"sync"

	daprclient "github.com/dapr/go-sdk/client"
	"go.opentelemetry.io/otel/trace"

	"analytics-microservice/internal/app"
	"analytics-microservice/internal/config"
	"analytics-microservice/internal/infra/clickhouse"
	kpiConfig "analytics-microservice/internal/infra/config"
	"analytics-microservice/internal/infra/dapr"
	"analytics-microservice/internal/infra/messaging"
	"analytics-microservice/internal/infra/seeding"
	"analytics-microservice/internal/logging"
	"analytics-microservice/internal/observability"
)

// Container holds all the application dependencies
type Container struct {
	// Configuration embedded directly
	config config.Config

	// Core infrastructure
	logger *slog.Logger
	tracer trace.Tracer

	// Database clients
	clickhouseClient *clickhouse.Client

	// Dapr clients (individual fields)
	daprClient       daprclient.Client
	stateClient      *dapr.StateClient
	pubsubClient     *dapr.PubSubClient
	invocationClient *dapr.InvocationClient
	secretsClient    *dapr.SecretsClient

	// Repositories as individual fields
	kpiConfigRepo app.KPIConfigRepository
	githubRepo    *clickhouse.GitHubRepo
	jiraRepo      *clickhouse.JiraRepo
	seedJobRepo   app.SeedJobRepository

	// Seeders
	genericSeeder app.GenericSeeder

	// Application services as individual fields
	kpiDataService *app.KPIDataService
	jiraService    *app.JiraService
	webhookHandler *app.WebhookHandler
	epicService    *app.EpicService
	seedService    *app.SeedService

	// Infrastructure services
	eventPublisher      app.EventPublisher
	webhookEventHandler *dapr.WebhookEventHandler

	// Initialization control
	initOnce sync.Once
	err      error

	// Cleanup functions
	tracerCleanup func()
}

// New creates a new container with all dependencies wired together
func New() (*Container, error) {
	cfg, err := config.Load("")
	fmt.Println("cfg", cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// 2) embed it on your Container
	c := &Container{
		config: *cfg,
	}
	c.initLogger()

	c.initOnce.Do(func() {
		c.initTracer()
		c.initDatabaseClients()
		c.initDaprClients()
		c.initRepositories()
		c.initServices()
		c.initEventPublisher()
		c.initWebhookComponents()
	})

	if c.err != nil {
		c.logger.Error("Container initialization failed", "error", c.err)
		return nil, c.err
	}

	c.logger.Info("Container initialized successfully")
	return c, nil
}

// initLogger initializes the structured logger
func (c *Container) initLogger() {
	opts := &slog.HandlerOptions{
		Level:     slog.LevelInfo,
		AddSource: c.config.Environment == "development",
	}
	c.logger = slog.New(slog.NewJSONHandler(os.Stdout, opts)).With("service", "template-microservice")
	slog.SetDefault(c.logger)
}

// initTracer initializes OpenTelemetry tracing if enabled
func (c *Container) initTracer() {
	if c.err != nil {
		return
	}

	if !c.config.Observability.Tracing.Enabled || c.config.Observability.Tracing.Jaeger == "" {
		c.logger.Info("Tracing disabled or no Jaeger endpoint configured")
		return
	}

	cleanup, err := observability.InitTracer("template-microservice", c.config.Observability.Tracing.Jaeger)
	if err != nil {
		c.logger.Warn("Failed to initialize tracer, continuing without tracing", "error", err)
		return
	}

	c.tracer = observability.GetTracer("template-microservice")
	c.tracerCleanup = cleanup
	c.logger.Info("Tracer initialized successfully")
}

// initDatabaseClients initializes database clients with graceful degradation
func (c *Container) initDatabaseClients() {
	if c.err != nil {
		return
	}

	// Initialize ClickHouse
	if c.config.ClickHouse.DSN != "" {
		chClient, err := clickhouse.NewClient(c.config.ClickHouse.DSN)
		if err != nil {
			c.logger.Warn("Failed to initialize ClickHouse client", "error", err)
		} else {
			c.clickhouseClient = chClient

			// Perform automatic schema migration
			if err := chClient.AutoMigrate(); err != nil {
				c.logger.Warn("ClickHouse automigrate failed", "error", err)
			} else {
				c.logger.Info("ClickHouse client initialized and schema migrated successfully")
			}
		}
	}

}

// initDaprClients initializes Dapr clients with graceful degradation
func (c *Container) initDaprClients() {
	if c.err != nil {
		return
	}

	if !c.config.Dapr.Enabled {
		c.logger.Info("Dapr disabled in configuration")
		return
	}

	var err error
	c.daprClient, err = daprclient.NewClient()
	if err != nil {
		c.logger.Warn("Failed to initialize Dapr client, continuing without Dapr", "error", err)
		return
	}

	// Create a zap logger for Dapr clients (they expect zap.Logger)
	zapLogger, err := logging.NewLogger(strings.ToLower(c.config.Logging.Level))
	if err != nil {
		c.logger.Warn("Failed to create zap logger for Dapr clients", "error", err)
		return
	}

	// Initialize individual Dapr clients
	c.stateClient = dapr.NewStateClient(c.daprClient)
	c.pubsubClient = dapr.NewPubSubClient(c.daprClient, zapLogger.WithGroup("PubSub").Logger)
	c.invocationClient = dapr.NewInvocationClient(c.daprClient, zapLogger.WithGroup("Invocation").Logger)

	// Create a temporary config for secrets client
	tempConfig := &config.Config{}
	tempConfig.Dapr.SecretStore = c.config.Dapr.SecretStore
	c.secretsClient = dapr.NewSecretsClient(c.daprClient, tempConfig, zapLogger.WithGroup("Secrets").Logger)

	c.logger.Info("Dapr clients initialized successfully")
}

// initRepositories initializes repositories if database clients are available
func (c *Container) initRepositories() {
	if c.err != nil {
		return
	}

	if c.clickhouseClient != nil { // Initialize GitHub repository for DORA metrics
		c.githubRepo = clickhouse.NewGitHubRepo(c.clickhouseClient.Gorm())
		c.logger.Info("GitHub repository initialized", "component", "GitHubRepo")
	}

	// Jira repository
	c.jiraRepo = clickhouse.NewJiraRepo(c.clickhouseClient.Gorm())
	c.logger.Info("Jira repository initialized", "component", "JiraRepo")

	// Seed job repository
	c.seedJobRepo = clickhouse.NewSeedJobRepository(c.clickhouseClient.Gorm(), c.logger)
	c.logger.Info("Seed job repository initialized", "component", "SeedJobRepo")

	// Generic seeder
	c.genericSeeder = seeding.NewGenericSeeder(c.jiraRepo, c.githubRepo, c.logger)
	c.logger.Info("Generic seeder initialized", "component", "GenericSeeder")

	jsonPath := c.config.KPIConfig.Path
	c.kpiConfigRepo = kpiConfig.NewKPIConfigRepository(jsonPath, c.logger)
}

// initServices initializes application services
func (c *Container) initServices() {
	if c.err != nil {
		return
	}

	if c.kpiConfigRepo != nil {
		c.kpiDataService = app.NewKPIDataService(
			c.kpiConfigRepo,
			c.jiraRepo,
			c.logger.WithGroup("KPIDataService"),
		)
		c.logger.Info("Static KPI service initialized")
	} else {
		c.logger.Warn("Static KPI repo missing, skipping static KPI service")
	}

	if c.jiraRepo != nil {
		c.jiraService = app.NewJiraService(c.jiraRepo)
		c.logger.Info("Jira service initialized")
	} else {
		c.logger.Warn("Jira repository missing, skipping Jira service")
	}
	if c.jiraRepo != nil {
		c.epicService = app.NewEpicService(c.jiraRepo)
		c.logger.Info("Epic service initialized")
	}

	if c.clickhouseClient != nil {
		// Create seed service with injected dependencies
		c.seedService = app.NewSeedService(
			c.clickhouseClient,
			c.seedJobRepo,
			c.genericSeeder,
			c.logger.WithGroup("SeedService"),
		)
		c.logger.Info("Seed service initialized")
	}
}

// initEventPublisher initializes event publisher if Dapr pub/sub is available
func (c *Container) initEventPublisher() {
	if c.err != nil {
		return
	}

	if c.daprClient != nil && c.config.Dapr.PubSubName != "" {
		publisher, err := messaging.NewDaprEventPublisher(
			c.daprClient,
			c.config.Dapr.PubSubName,
			c.config.Dapr.AppID,
			c.logger.WithGroup("EventPublisher"),
		)
		if err != nil {
			c.logger.Warn("Failed to create event publisher", "error", err)
		} else {
			c.eventPublisher = publisher
			c.logger.Info("Event publisher initialized successfully")
		}
	} else {
		c.logger.Info("Event publisher not configured (Dapr pub/sub not available)")
	}
}

// initWebhookComponents initializes webhook processing components
func (c *Container) initWebhookComponents() {
	if c.err != nil {
		return
	}

	// Initialize webhook handler (app layer)
	if c.jiraRepo != nil && c.githubRepo != nil {
		c.webhookHandler = app.NewWebhookHandler(
			c.jiraRepo,
			c.githubRepo,
			c.logger,
		)
		c.logger.Info("Webhook handler initialized")

		// Initialize webhook event handler (infrastructure layer)
		if c.config.Dapr.Enabled {
			// Create zap logger for webhook event handler (since it requires *zap.Logger)
			zapLogger, err := logging.NewLogger(strings.ToLower(c.config.Logging.Level))
			if err != nil {
				c.err = fmt.Errorf("failed to create zap logger for webhook event handler: %w", err)
				return
			}

			c.webhookEventHandler = dapr.NewWebhookEventHandler(
				c.webhookHandler,
				zapLogger.WithGroup("WebhookEventHandler").Logger,
			)
			c.logger.Info("Webhook event handler initialized",
				"pubsub", "redis-pubsub",
				"topic", "webhook-events")
		} else {
			c.logger.Warn("Webhook event handler not initialized - Dapr disabled")
		}
	} else {
		c.logger.Warn("Webhook handler not initialized - missing repositories")
	}
}

// Accessor methods
func (c *Container) Logger() *slog.Logger                           { return c.logger }
func (c *Container) Config() config.Config                          { return c.config }
func (c *Container) DaprClient() daprclient.Client                  { return c.daprClient }
func (c *Container) StateClient() *dapr.StateClient                 { return c.stateClient }
func (c *Container) PubSubClient() *dapr.PubSubClient               { return c.pubsubClient }
func (c *Container) InvocationClient() *dapr.InvocationClient       { return c.invocationClient }
func (c *Container) SecretsClient() *dapr.SecretsClient             { return c.secretsClient }
func (c *Container) EventPublisher() app.EventPublisher             { return c.eventPublisher }
func (c *Container) KPIDataService() *app.KPIDataService            { return c.kpiDataService }
func (c *Container) GitHubRepo() *clickhouse.GitHubRepo             { return c.githubRepo }
func (c *Container) JiraService() *app.JiraService                  { return c.jiraService }
func (c *Container) WebhookHandler() *app.WebhookHandler            { return c.webhookHandler }
func (c *Container) WebhookEventHandler() *dapr.WebhookEventHandler { return c.webhookEventHandler }
func (c *Container) EpicService() *app.EpicService                  { return c.epicService }
func (c *Container) SeedService() *app.SeedService                  { return c.seedService }

// HealthCheck performs a health check on all available components
func (c *Container) HealthCheck(ctx context.Context) error {
	var errors []error

	// Check ClickHouse
	if c.clickhouseClient != nil {
		if err := c.clickhouseClient.DB().PingContext(ctx); err != nil {
			errors = append(errors, fmt.Errorf("ClickHouse health check failed: %w", err))
		}
	}

	// Check Dapr
	if c.daprClient != nil && c.stateClient != nil {
		if _, err := c.stateClient.GetState(ctx, c.config.Dapr.StateStore, "health-check"); err != nil {
			// This is expected if the key doesn't exist, but it means Dapr is responding
			c.logger.Debug("Dapr health check completed", "error", err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("health check failures: %v", errors)
	}

	return nil
}

// Close closes all connections and resources
func (c *Container) Close(ctx context.Context) error {
	var errors []error

	// Close tracer
	if c.tracerCleanup != nil {
		c.tracerCleanup()
	}

	// Close ClickHouse connection
	if c.clickhouseClient != nil {
		if err := c.clickhouseClient.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close ClickHouse connection: %w", err))
		}
	}

	// Close Dapr client
	if c.daprClient != nil {
		c.daprClient.Close()
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors during close: %v", errors)
	}

	c.logger.Info("Container closed successfully")
	return nil
}
