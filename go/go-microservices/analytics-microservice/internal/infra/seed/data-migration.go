// =====================================================
// #####################################################
// ############# CURRENTLY OUT OF USE ##################
// #####################################################
// =====================================================

package seeder

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	_ "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/joho/godotenv"
	"gorm.io/driver/clickhouse"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// =====================================================
//
// STRUCTS
//
// =====================================================

type Config struct {
	JiraBaseURL   string
	JiraEmail     string
	JiraToken     string
	JiraProjects  []string
	ClickHouseDSN string

	GitHubToken      string
	GitHubRepos      []string
	GitHubBaseURL    string
	GitHubEventTypes []string
}

type RawJiraEvent struct {
	Payload   string `gorm:"column:payload;type:String" json:"payload"`
	CreatedAt string `gorm:"column:created_at;type:String" json:"created_at"`
}

func (RawJiraEvent) TableName() string {
	return "jira_raw_events"
}

type GitHubEventRaw struct {
	Payload   string `gorm:"column:payload;type:String" json:"payload"`
	CreatedAt string `gorm:"column:created_at;type:String" json:"created_at"`
}

func (GitHubEventRaw) TableName() string {
	return "github_raw_events"
}

type JiraSearchResponse struct {
	Expand     string        `json:"expand"`
	StartAt    int           `json:"startAt"`
	MaxResults int           `json:"maxResults"`
	Total      int           `json:"total"`
	Issues     []interface{} `json:"issues"`
}

type JiraSprintResponse struct {
	MaxResults int           `json:"maxResults"`
	StartAt    int           `json:"startAt"`
	Total      int           `json:"total"`
	IsLast     bool          `json:"isLast"`
	Values     []interface{} `json:"values"`
}

type GitHubRepo struct {
	ID       uint64 `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name"`
	Owner    struct {
		Login string `json:"login"`
	} `json:"owner"`
}

type GitHubEvent struct {
	Type       string                 `json:"type"`
	ID         string                 `json:"id"`
	Created_at string                 `json:"created_at"`
	Repo       GitHubRepo             `json:"repo"`
	Payload    map[string]interface{} `json:"payload"`
}

// Seeder handles data migration operations
type Seeder struct {
	config *Config
	db     *gorm.DB
	client *http.Client
}

func NewSeeder(config *Config) (*Seeder, error) {
	// Initialize ClickHouse connection
	db, err := initClickHouse(config.ClickHouseDSN)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %v", err)
	}

	client := &http.Client{Timeout: 30 * time.Second}

	return &Seeder{
		config: config,
		db:     db,
		client: client,
	}, nil
}

// Close closes the database connection
func (s *Seeder) Close() error {
	if s.db != nil {
		sqlDB, err := s.db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// Run executes the complete data migration process
func (s *Seeder) Run() error {
	// If Jira credentials are present but no projects specified, fetch all accessible projects automatically
	if len(s.config.JiraProjects) == 0 && s.config.JiraBaseURL != "" && s.config.JiraEmail != "" && s.config.JiraToken != "" {
		log.Println("No JIRA_PROJECTS provided – attempting to fetch all accessible projects from Jira ...")
		projects, err := s.fetchAllJiraProjects()
		if err != nil {
			log.Printf("Warning: failed to auto-discover projects: %v", err)
		} else {
			log.Printf("Discovered %d projects: %v", len(projects), projects)
			s.config.JiraProjects = projects
		}
	}

	// Process Jira data if projects are specified
	if len(s.config.JiraProjects) > 0 {
		if err := s.processJiraData(); err != nil {
			return fmt.Errorf("failed to process Jira data: %w", err)
		}
	}

	// Process GitHub data if repos are specified
	if len(s.config.GitHubRepos) > 0 {
		if err := s.processGitHubData(); err != nil {
			return fmt.Errorf("failed to process GitHub data: %w", err)
		}
	}

	return nil
}

// processJiraData handles all Jira data processing
func (s *Seeder) processJiraData() error {
	log.Println("Starting Jira data fetch process...")
	log.Printf("Processing %d projects: %v", len(s.config.JiraProjects), s.config.JiraProjects)

	// Process each project
	for i, project := range s.config.JiraProjects {
		log.Printf("\n=== Processing Project %d/%d: %s ===", i+1, len(s.config.JiraProjects), project)

		// 1. Fetch and save Jira issues for this project
		log.Printf("Fetching Jira issues for project %s...", project)
		if err := s.fetchAndSaveIssues(project); err != nil {
			log.Printf("Failed to fetch issues for project %s: %v", project, err)
			continue // Continue with next project instead of failing completely
		}

		// 2. Fetch and save Jira sprints for this project
		log.Printf("Fetching Jira sprints for project %s...", project)
		if err := s.fetchAndSaveSprints(project); err != nil {
			log.Printf("Failed to fetch sprints for project %s: %v", project, err)
			continue
		}

		// 3. Fetch and save Jira releases for this project
		log.Printf("Fetching Jira releases for project %s...", project)
		if err := s.fetchAndSaveReleases(project); err != nil {
			log.Printf("Failed to fetch releases for project %s: %v", project, err)
			continue
		}

		log.Printf("Completed processing project %s", project)

		// Add delay between projects to be respectful to the API
		if i < len(s.config.JiraProjects)-1 {
			log.Println("Waiting 2 seconds before processing next project...")
			time.Sleep(2 * time.Second)
		}
	}

	log.Println("\nJira data fetch process completed successfully for all projects!")
	return nil
}

// processGitHubData handles all GitHub data processing
func (s *Seeder) processGitHubData() error {
	log.Println("Starting GitHub data fetch process...")
	log.Printf("Processing %d repositories: %v", len(s.config.GitHubRepos), s.config.GitHubRepos)

	// Process each repo
	for i, repo := range s.config.GitHubRepos {
		log.Printf("\n=== Processing Repository %d/%d: %s ===", i+1, len(s.config.GitHubRepos), repo)

		// Fetch and save GitHub events for this repo
		log.Printf("Fetching GitHub events for repo %s...", repo)
		if err := s.fetchAndSaveGitHubEvents(repo); err != nil {
			log.Printf("Failed to fetch events for repo %s: %v", repo, err)
			continue // Continue with next repo instead of failing completely
		}

		log.Printf("Completed processing repository %s", repo)

		// Add delay between repositories to be respectful to the API
		if i < len(s.config.GitHubRepos)-1 {
			log.Println("Waiting 2 seconds before processing next repository...")
			time.Sleep(2 * time.Second)
		}
	}

	log.Println("\nGitHub data fetch process completed successfully for all repositories!")
	return nil
}

// =====================================================
//
// JIRA FETCHING FUNCTIONS
//
// =====================================================

func (s *Seeder) fetchAllJiraProjects() ([]string, error) {
	startAt := 0
	maxResults := 1000
	var allProjects []string

	for {
		endpoint := fmt.Sprintf("%s/rest/api/3/project/search?startAt=%d&maxResults=%d", s.config.JiraBaseURL, startAt, maxResults)

		resp, err := s.makeJiraRequest(endpoint)
		if err != nil {
			return nil, fmt.Errorf("request failed: %w", err)
		}

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			return nil, fmt.Errorf("Jira project search failed with status %d: %s", resp.StatusCode, string(body))
		}

		var searchResp struct {
			StartAt    int `json:"startAt"`
			MaxResults int `json:"maxResults"`
			Total      int `json:"total"`
			Values     []struct {
				Key string `json:"key"`
			} `json:"values"`
		}

		if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
			resp.Body.Close()
			return nil, fmt.Errorf("decode failed: %w", err)
		}
		resp.Body.Close()

		for _, v := range searchResp.Values {
			if v.Key != "" {
				allProjects = append(allProjects, v.Key)
			}
		}

		if startAt+len(searchResp.Values) >= searchResp.Total {
			break
		}
		startAt += maxResults
	}

	// Deduplicate just in case
	seen := make(map[string]struct{})
	var unique []string
	for _, p := range allProjects {
		if _, ok := seen[p]; !ok {
			seen[p] = struct{}{}
			unique = append(unique, p)
		}
	}
	return unique, nil
}

func (s *Seeder) fetchAndSaveIssues(project string) error {
	startAt := 0
	maxResults := 1000
	totalIssuesSaved := 0

	for {
		// Build JQL query for issues created from 2025-01-01 onwards
		jql := fmt.Sprintf("project = \"%s\" AND created >= \"2025-01-01\" ORDER BY created ASC", project)

		// Prepare the request URL
		baseUrl := fmt.Sprintf("%s/rest/api/3/search", s.config.JiraBaseURL)
		params := url.Values{}
		params.Add("jql", jql)
		params.Add("startAt", fmt.Sprintf("%d", startAt))
		params.Add("maxResults", fmt.Sprintf("%d", maxResults))
		params.Add("expand", "changelog")

		endpoint := fmt.Sprintf("%s?%s", baseUrl, params.Encode())

		log.Printf("  Fetching issues for %s from 2025-01-01 onwards: startAt=%d, maxResults=%d", project, startAt, maxResults)
		resp, err := s.makeJiraRequest(endpoint)
		if err != nil {
			return fmt.Errorf("failed to make request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			resp.Body.Close()
			return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
		}

		var searchResp JiraSearchResponse
		if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
			resp.Body.Close()
			return fmt.Errorf("failed to decode response: %w", err)
		}

		log.Printf("  Retrieved %d issues for %s (total: %d)", len(searchResp.Issues), project, searchResp.Total)

		// Save each issue to the database
		for _, issue := range searchResp.Issues {
			wrapped := map[string]interface{}{"issue": issue}
			issueJSON, err := json.Marshal(wrapped)
			if err != nil {
				log.Printf("  Failed to marshal issue: %v", err)
				continue
			}

			// Check if issue already exists
			if s.checkJiraIssueExists(string(issueJSON)) {
				log.Printf("  Skipping issue (already exists)")
				continue
			}

			rawIssue := RawJiraEvent{
				Payload:   string(issueJSON),
				CreatedAt: time.Now().Format(time.RFC3339),
			}

			if err := s.db.Create(&rawIssue).Error; err != nil {
				log.Printf("  Failed to save issue: %v", err)
				continue
			}
			totalIssuesSaved++
		}

		// Check if we've fetched all issues
		if startAt+len(searchResp.Issues) >= searchResp.Total {
			break
		}

		// Advance by the number of issues actually returned to avoid gaps if API caps page size
		startAt += len(searchResp.Issues)

		// Add a small delay to avoid rate limiting
		time.Sleep(1 * time.Second)
	}

	log.Printf("  Saved %d issues for project %s (from 2025-01-01 onwards)", totalIssuesSaved, project)
	return nil
}

func (s *Seeder) fetchAndSaveSprints(project string) error {
	// First, get all boards for the project
	boardsEndpoint := fmt.Sprintf("%s/rest/agile/1.0/board?projectKeyOrId=%s&maxResults=%d", s.config.JiraBaseURL, project, 1000)

	resp, err := s.makeJiraRequest(boardsEndpoint)
	if err != nil {
		return fmt.Errorf("failed to fetch boards: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		return fmt.Errorf("boards API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var boardsResp struct {
		Values []struct {
			ID   int    `json:"id"`
			Name string `json:"name"`
		} `json:"values"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&boardsResp); err != nil {
		resp.Body.Close()
		return fmt.Errorf("failed to decode boards response: %w", err)
	}

	log.Printf("  Found %d boards for project %s", len(boardsResp.Values), project)
	totalSprintsSaved := 0

	// Fetch sprints for each board
	for _, board := range boardsResp.Values {
		log.Printf("  Fetching sprints for board: %s (ID: %d) in project %s", board.Name, board.ID, project)

		startAt := 0
		maxResults := 1000

		for {
			sprintsEndpoint := fmt.Sprintf("%s/rest/agile/1.0/board/%d/sprint?startAt=%d&maxResults=%d",
				s.config.JiraBaseURL, board.ID, startAt, maxResults)

			resp, err := s.makeJiraRequest(sprintsEndpoint)
			if err != nil {
				log.Printf("  Failed to fetch sprints for board %d: %v", board.ID, err)
				break
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				body, _ := io.ReadAll(resp.Body)
				log.Printf("  Sprints API request failed for board %d with status %d: %s", board.ID, resp.StatusCode, string(body))
				break
			}

			var sprintResp JiraSprintResponse
			if err := json.NewDecoder(resp.Body).Decode(&sprintResp); err != nil {
				log.Printf("  Failed to decode sprints response for board %d: %v", board.ID, err)
				break
			}

			log.Printf("  Retrieved %d sprints for board %s", len(sprintResp.Values), board.Name)

			// Save each sprint to the database
			for _, sprint := range sprintResp.Values {
				sprintJSON, err := json.Marshal(map[string]interface{}{
					"sprint": sprint,
				})
				if err != nil {
					log.Printf("  Failed to marshal sprint: %v", err)
					continue
				}

				// Check if sprint already exists
				if s.checkJiraSprintExists(string(sprintJSON)) {
					log.Printf("  Skipping sprint (already exists)")
					continue
				}

				rawSprint := RawJiraEvent{
					Payload:   string(sprintJSON),
					CreatedAt: time.Now().Format(time.RFC3339),
				}

				if err := s.db.Create(&rawSprint).Error; err != nil {
					log.Printf("  Failed to save sprint: %v", err)
					continue
				}
				totalSprintsSaved++
			}

			// Check if we've fetched all sprints
			if sprintResp.IsLast || len(sprintResp.Values) == 0 {
				break
			}

			// Advance by the actual number returned to avoid skipping data
			startAt += len(sprintResp.Values)
			time.Sleep(500 * time.Millisecond)
		}
	}

	log.Printf("  Saved %d sprints for project %s", totalSprintsSaved, project)
	return nil
}

func (s *Seeder) fetchAndSaveReleases(project string) error {
	// First, get project details to get project ID
	projectEndpoint := fmt.Sprintf("%s/rest/api/3/project/%s", s.config.JiraBaseURL, project)

	resp, err := s.makeJiraRequest(projectEndpoint)
	if err != nil {
		return fmt.Errorf("failed to fetch project details: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		return fmt.Errorf("project API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var projectResp struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&projectResp); err != nil {
		resp.Body.Close()
		return fmt.Errorf("failed to decode project response: %w", err)
	}

	// Fetch versions/releases for the project
	versionsEndpoint := fmt.Sprintf("%s/rest/api/3/project/%s/versions", s.config.JiraBaseURL, projectResp.ID)

	resp, err = s.makeJiraRequest(versionsEndpoint)
	if err != nil {
		return fmt.Errorf("failed to fetch versions: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		resp.Body.Close()
		return fmt.Errorf("versions API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var versions JiraVersionResponse
	if err := json.NewDecoder(resp.Body).Decode(&versions); err != nil {
		resp.Body.Close()
		return fmt.Errorf("failed to decode versions response: %w", err)
	}

	log.Printf("  Retrieved %d versions/releases for project %s", len(versions), project)
	releasesSaved := 0

	// Save each version/release to the database
	for _, version := range versions {
		releaseJSON, err := json.Marshal(map[string]interface{}{
			"version": version,
		})
		if err != nil {
			log.Printf("  Failed to marshal release: %v", err)
			continue
		}

		// Check if release already exists
		if s.checkJiraReleaseExists(string(releaseJSON)) {
			log.Printf("  Skipping release (already exists)")
			continue
		}

		rawRelease := RawJiraEvent{
			Payload:   string(releaseJSON),
			CreatedAt: time.Now().Format(time.RFC3339),
		}

		if err := s.db.Create(&rawRelease).Error; err != nil {
			log.Printf("  Failed to save release: %v", err)
			continue
		}
		releasesSaved++
	}

	log.Printf("  Saved %d releases for project %s", releasesSaved, project)
	return nil
}

// =====================================================
//
// GITHUB FETCHING FUNCTIONS
//
// =====================================================

func (s *Seeder) fetchAndSaveGitHubEvents(repo string) error {
	// Ensure repo format is correct (owner/repo)
	if !strings.Contains(repo, "/") {
		return fmt.Errorf("invalid repository format, expected 'owner/repo': %s", repo)
	}

	// Get repository details first to extract repo ID and other metadata
	repoEndpoint := fmt.Sprintf("%s/repos/%s", s.config.GitHubBaseURL, repo)

	resp, err := s.makeGitHubRequest(repoEndpoint)
	if err != nil {
		return fmt.Errorf("failed to fetch repository details: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("repository API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Store the full repository details as map to extract all fields
	var repoDetailsMap map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&repoDetailsMap); err != nil {
		return fmt.Errorf("failed to decode repository response: %w", err)
	}

	// Also decode into our struct for easier access to basic fields
	respBytes, _ := json.Marshal(repoDetailsMap)
	var repoDetails GitHubRepo
	json.Unmarshal(respBytes, &repoDetails)

	log.Printf("  Found repository: %s (ID: %d)", repoDetails.FullName, repoDetails.ID)
	totalSaved := 0

	// 0. Fetch and save branches
	log.Printf("  Fetching branches for repo %s...", repo)
	if err := s.fetchAndSaveBranches(repo, repoDetails.ID, repoDetailsMap); err != nil {
		log.Printf("  Warning: Failed to fetch branches: %v", err)
	} else {
		totalSaved++
	}

	// 1. Fetch and save commits
	log.Printf("  Fetching commits for repo %s...", repo)
	if err := s.fetchAndSaveCommits(repo, repoDetails.ID, repoDetailsMap); err != nil {
		log.Printf("  Warning: Failed to fetch commits: %v", err)
	} else {
		totalSaved++
	}

	// 2. Fetch and save pull requests
	log.Printf("  Fetching pull requests for repo %s...", repo)
	if err := s.fetchAndSavePullRequests(repo, repoDetails.ID, repoDetailsMap); err != nil {
		log.Printf("  Warning: Failed to fetch pull requests: %v", err)
	} else {
		totalSaved++
	}

	// 3. Fetch and save workflow runs
	log.Printf("  Fetching workflow runs for repo %s...", repo)
	if err := s.fetchAndSaveWorkflowRuns(repo, repoDetails.ID, repoDetailsMap); err != nil {
		log.Printf("  Warning: Failed to fetch workflow runs: %v", err)
	} else {
		totalSaved++
	}

	// 4. Fetch and save releases
	log.Printf("  Fetching releases for repo %s...", repo)
	if err := s.fetchAndSaveGitHubReleases(repo, repoDetails.ID, repoDetailsMap); err != nil {
		log.Printf("  Warning: Failed to fetch releases: %v", err)
	} else {
		totalSaved++
	}

	// 5. Fetch and save deployments
	log.Printf("  Fetching deployments for repo %s...", repo)
	if err := s.fetchAndSaveDeployments(repo, repoDetails.ID, repoDetailsMap); err != nil {
		log.Printf("  Warning: Failed to fetch deployments: %v", err)
	} else {
		totalSaved++
	}

	// 6. Also fetch general events (as before)
	log.Printf("  Fetching general events for repo %s...", repo)
	if err := s.fetchAndSaveRepoEvents(repo, repoDetails.ID, repoDetailsMap); err != nil {
		log.Printf("  Warning: Failed to fetch general events: %v", err)
	} else {
		totalSaved++
	}

	log.Printf("  Completed data collection for repository %s with %d data types", repo, totalSaved)
	return nil
}

func (s *Seeder) fetchAndSaveBranches(repo string, repoID uint64, repoDetailsMap map[string]interface{}) error {
	totalBranchesSaved := 0
	page := 1
	perPage := 100
	maxPages := 5 // Limit to avoid rate limiting

	for page <= maxPages {
		// GitHub branches API endpoint
		branchesEndpoint := fmt.Sprintf("%s/repos/%s/branches?page=%d&per_page=%d",
			s.config.GitHubBaseURL, repo, page, perPage)

		log.Printf("    Fetching branches: page=%d", page)
		resp, err := s.makeGitHubRequest(branchesEndpoint)
		if err != nil {
			return fmt.Errorf("failed to make branches request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			return fmt.Errorf("branches API request failed with status %d: %s", resp.StatusCode, string(body))
		}

		var branches []interface{}
		if err := json.NewDecoder(resp.Body).Decode(&branches); err != nil {
			return fmt.Errorf("failed to decode branches response: %w", err)
		}

		log.Printf("    Retrieved %d branches (page %d)", len(branches), page)

		if len(branches) == 0 {
			break // No more branches
		}

		// Save each branch with proper event structure to match webhook format
		for _, branch := range branches {
			branchJSON, err := json.Marshal(branch)
			if err != nil {
				log.Printf("    Failed to marshal branch: %v", err)
				continue
			}

			// Get branch name
			branchName := "unknown"
			if branchMap, ok := branch.(map[string]interface{}); ok {
				if name, ok := branchMap["name"].(string); ok {
					branchName = name
				}
			}

			// Check if branch already exists
			if s.checkGitHubBranchExists(repo, branchName) {
				log.Printf("    Skipping branch %s (already exists)", branchName)
				continue
			}

			// Determine branch type
			branchType := "feature"
			if branchName == "main" || branchName == "master" {
				branchType = "main"
			} else if strings.HasPrefix(branchName, "release") {
				branchType = "release"
			} else if strings.HasPrefix(branchName, "hotfix") {
				branchType = "hotfix"
			} else if branchName == "develop" || branchName == "development" {
				branchType = "develop"
			}

			// Create a branch create event payload
			branchEvent := map[string]interface{}{
				"repository":    repoDetailsMap,
				"ref":           fmt.Sprintf("refs/heads/%s", branchName),
				"ref_type":      "branch",
				"branch":        branchName,
				"branch_type":   branchType,
				"master_branch": repoDetailsMap["default_branch"],
				"event_type":    "create",
				"webhookEvent":  "create",
				"branch_data":   json.RawMessage(branchJSON),
			}

			payloadJSON, err := json.Marshal(branchEvent)
			if err != nil {
				log.Printf("    Failed to marshal branch event: %v", err)
				continue
			}

			// Save as a create event
			rawEvent := GitHubEventRaw{
				Payload:   string(payloadJSON),
				CreatedAt: time.Now().Format(time.RFC3339),
			}

			if err := s.db.Create(&rawEvent).Error; err != nil {
				log.Printf("    Failed to save branch event: %v", err)
				continue
			}
			totalBranchesSaved++

			// Also save direct branch dimension entry to ensure it's populated
			// This helps in case the materialized view doesn't pick it up
			s.saveBranchDirectly(repoID, branchName, branchType)
		}

		// Check for pagination
		linkHeader := resp.Header.Get("Link")
		hasNextPage := strings.Contains(linkHeader, `rel="next"`)
		if !hasNextPage {
			break
		}

		page++
		time.Sleep(1 * time.Second) // Respect rate limiting
	}

	log.Printf("    Saved %d branches", totalBranchesSaved)
	return nil
}

func (s *Seeder) fetchAndSaveCommits(repo string, repoID uint64, repoDetailsMap map[string]interface{}) error {
	totalCommitsSaved := 0
	page := 1
	perPage := 100
	maxPages := 5 // Limit to avoid rate limiting

	for page <= maxPages {
		// GitHub commits API endpoint with since parameter for date filtering
		commitsEndpoint := fmt.Sprintf("%s/repos/%s/commits?page=%d&per_page=%d&since=2025-01-01T00:00:00Z",
			s.config.GitHubBaseURL, repo, page, perPage)

		log.Printf("    Fetching commits from 2025-01-01 onwards: page=%d", page)
		resp, err := s.makeGitHubRequest(commitsEndpoint)
		if err != nil {
			return fmt.Errorf("failed to make commits request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			return fmt.Errorf("commits API request failed with status %d: %s", resp.StatusCode, string(body))
		}

		var commits []map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&commits); err != nil {
			return fmt.Errorf("failed to decode commits response: %w", err)
		}

		log.Printf("    Retrieved %d commits (page %d)", len(commits), page)

		if len(commits) == 0 {
			break // No more commits
		}

		// Process commits in groups to simulate push events (to match webhook format)
		// The materialized view mv_fact_commit expects a 'commits' array in a push event
		batchSize := 10
		for i := 0; i < len(commits); i += batchSize {
			end := i + batchSize
			if end > len(commits) {
				end = len(commits)
			}

			// Process each commit to ensure it has the required fields
			commitBatch := commits[i:end]
			processedBatch := make([]map[string]interface{}, 0, len(commitBatch))

			for _, commit := range commitBatch {
				// Process commit to ensure it has all required fields
				processedCommit := processCommitForFactTable(commit, repoDetailsMap)

				// Check if commit already exists
				if commitSHA, ok := processedCommit["id"].(string); ok && commitSHA != "" {
					if s.checkGitHubCommitExists(commitSHA) {
						log.Printf("    Skipping commit %s (already exists)", commitSHA[:8])
						continue
					}
				}

				processedBatch = append(processedBatch, processedCommit)

				// Also insert directly into fact_commit table
				s.saveCommitDirectly(repoID, processedCommit)
			}

			// Format like a push webhook event
			pushEvent := map[string]interface{}{
				"repository": repoDetailsMap,
				"commits":    processedBatch,
				"ref":        fmt.Sprintf("refs/heads/%s", repoDetailsMap["default_branch"]),
				"after":      "", // Would be the latest commit SHA
				"before":     "", // Would be the previous HEAD
				"pusher": map[string]interface{}{
					"name":  "data-migration",
					"email": "<EMAIL>",
				},
			}

			// Set the after/before if we have commit data
			if len(processedBatch) > 0 {
				if sha, ok := processedBatch[0]["id"].(string); ok {
					pushEvent["after"] = sha
				}
			}

			payloadJSON, err := json.Marshal(pushEvent)
			if err != nil {
				log.Printf("    Failed to marshal push event: %v", err)
				continue
			}

			// Save as a push event to match webhook format
			rawEvent := GitHubEventRaw{
				Payload:   string(payloadJSON),
				CreatedAt: time.Now().Format(time.RFC3339),
			}

			if err := s.db.Create(&rawEvent).Error; err != nil {
				log.Printf("    Failed to save push event: %v", err)
				continue
			}
			totalCommitsSaved += len(processedBatch)
		}

		// Check for pagination
		linkHeader := resp.Header.Get("Link")
		hasNextPage := strings.Contains(linkHeader, `rel="next"`)
		if !hasNextPage {
			break
		}

		page++
		time.Sleep(1 * time.Second) // Respect rate limiting
	}

	log.Printf("    Saved %d commits formatted as push events", totalCommitsSaved)
	return nil
}

func (s *Seeder) fetchAndSavePullRequests(repo string, repoID uint64, repoDetailsMap map[string]interface{}) error {
	totalPRsSaved := 0
	totalMergesSaved := 0

	// Fetch open and closed PRs separately
	states := []string{"open", "closed"}

	for _, state := range states {
		page := 1
		perPage := 100
		maxPages := 5 // Limit to avoid rate limiting

		for page <= maxPages {
			// GitHub PRs API endpoint with since parameter for date filtering
			prsEndpoint := fmt.Sprintf("%s/repos/%s/pulls?state=%s&page=%d&per_page=%d&since=2025-01-01T00:00:00Z",
				s.config.GitHubBaseURL, repo, state, page, perPage)

			log.Printf("    Fetching %s pull requests from 2025-01-01 onwards: page=%d", state, page)
			resp, err := s.makeGitHubRequest(prsEndpoint)
			if err != nil {
				return fmt.Errorf("failed to make PRs request: %w", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				body, _ := io.ReadAll(resp.Body)
				return fmt.Errorf("PRs API request failed with status %d: %s", resp.StatusCode, string(body))
			}

			var pullRequests []map[string]interface{}
			if err := json.NewDecoder(resp.Body).Decode(&pullRequests); err != nil {
				return fmt.Errorf("failed to decode PRs response: %w", err)
			}

			log.Printf("    Retrieved %d %s PRs (page %d)", len(pullRequests), state, page)

			if len(pullRequests) == 0 {
				break // No more PRs
			}

			// Save each PR to the database with webhook-like format
			for _, pr := range pullRequests {
				// First, get detailed PR info including reviews
				prNumber, _ := pr["number"].(float64)
				if prNumber > 0 {
					detailedPR := s.fetchPRDetails(repo, int(prNumber))
					if detailedPR != nil {
						// Use the detailed PR instead
						pr = detailedPR
					}
				}

				// Check if PR already exists
				if prNumber > 0 && s.checkGitHubPRExists(repo, int(prNumber)) {
					log.Printf("    Skipping PR #%d (already exists)", int(prNumber))
					continue
				}

				prJSON, err := json.Marshal(pr)
				if err != nil {
					log.Printf("    Failed to marshal PR: %v", err)
					continue
				}

				// Create a payload similar to a webhook payload
				action := "closed"
				if state == "open" {
					action = "opened"
				}

				payload := map[string]interface{}{
					"repository":   repoDetailsMap, // Use complete repo details
					"pull_request": json.RawMessage(prJSON),
					"action":       action,
					"event_type":   "pull_request",
					"webhookEvent": "pull_request",
				}

				payloadJSON, err := json.Marshal(payload)
				if err != nil {
					log.Printf("    Failed to marshal PR payload: %v", err)
					continue
				}

				// Save to github_event_raw
				rawEvent := GitHubEventRaw{
					Payload:   string(payloadJSON),
					CreatedAt: time.Now().Format(time.RFC3339),
				}

				if err := s.db.Create(&rawEvent).Error; err != nil {
					log.Printf("    Failed to save PR: %v", err)
					continue
				}
				totalPRsSaved++

				// Check if this PR was merged, and if so, create a separate merge event
				merged, _ := pr["merged"].(bool)
				mergedAt, hasmergedAt := pr["merged_at"].(string)

				if merged && hasmergedAt && mergedAt != "" {
					// For merged PRs, create a pull_request.merged event
					mergePayload := map[string]interface{}{
						"repository":   repoDetailsMap,
						"pull_request": json.RawMessage(prJSON),
						"action":       "merged", // Custom action for merged PRs
						"event_type":   "pull_request",
						"webhookEvent": "pull_request",
					}

					mergePayloadJSON, err := json.Marshal(mergePayload)
					if err != nil {
						log.Printf("    Failed to marshal PR merge payload: %v", err)
						continue
					}

					// Save the merge event
					mergeEvent := GitHubEventRaw{
						Payload:   string(mergePayloadJSON),
						CreatedAt: time.Now().Format(time.RFC3339),
					}

					if err := s.db.Create(&mergeEvent).Error; err != nil {
						log.Printf("    Failed to save PR merge event: %v", err)
						continue
					}
					totalMergesSaved++

					// Also directly save to fact_merge table for DORA metrics
					s.saveMergeDirectly(repoID, pr)
				}
			}

			// Check for pagination
			linkHeader := resp.Header.Get("Link")
			hasNextPage := strings.Contains(linkHeader, `rel="next"`)
			if !hasNextPage {
				break
			}

			page++
			time.Sleep(1 * time.Second) // Respect rate limiting
		}
	}

	log.Printf("    Saved %d pull requests (including %d merges)", totalPRsSaved, totalMergesSaved)
	return nil
}

func (s *Seeder) fetchAndSaveWorkflowRuns(repo string, repoID uint64, repoDetailsMap map[string]interface{}) error {
	totalRunsSaved := 0

	// GitHub workflow runs API endpoint
	runsEndpoint := fmt.Sprintf("%s/repos/%s/actions/runs", s.config.GitHubBaseURL, repo)

	log.Printf("    Fetching workflow runs")
	resp, err := s.makeGitHubRequest(runsEndpoint)
	if err != nil {
		return fmt.Errorf("failed to make workflow runs request: %w", err)
	}
	defer resp.Body.Close()

	// Some repositories might not have GitHub Actions enabled
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusNotFound {
			log.Printf("    No workflow runs found (GitHub Actions may not be enabled)")
			return nil
		}
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("workflow runs API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var workflowRunsResponse struct {
		TotalCount   int           `json:"total_count"`
		WorkflowRuns []interface{} `json:"workflow_runs"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&workflowRunsResponse); err != nil {
		return fmt.Errorf("failed to decode workflow runs response: %w", err)
	}

	log.Printf("    Retrieved %d workflow runs", workflowRunsResponse.TotalCount)

	// Save each workflow run to the database with webhook-like format
	for _, run := range workflowRunsResponse.WorkflowRuns {
		// Extract run ID for existence check
		runMap, ok := run.(map[string]interface{})
		if !ok {
			log.Printf("    Warning: Invalid workflow run data format")
			continue
		}

		runID, _ := runMap["id"].(float64)
		if runID > 0 && s.checkGitHubWorkflowRunExists(int64(runID)) {
			log.Printf("    Skipping workflow run %d (already exists)", int64(runID))
			continue
		}

		runJSON, err := json.Marshal(run)
		if err != nil {
			log.Printf("    Failed to marshal workflow run: %v", err)
			continue
		}

		// Create a payload similar to a webhook payload
		payload := map[string]interface{}{
			"repository":   repoDetailsMap, // Use the full repository details
			"workflow_run": json.RawMessage(runJSON),
			"action":       "completed",
			"event_type":   "workflow_run",
			"webhookEvent": "workflow_run",
		}

		payloadJSON, err := json.Marshal(payload)
		if err != nil {
			log.Printf("    Failed to marshal workflow run payload: %v", err)
			continue
		}

		// Save to github_event_raw
		rawEvent := GitHubEventRaw{
			Payload:   string(payloadJSON),
			CreatedAt: time.Now().Format(time.RFC3339),
		}

		if err := s.db.Create(&rawEvent).Error; err != nil {
			log.Printf("    Failed to save workflow run: %v", err)
			continue
		}
		totalRunsSaved++
	}

	log.Printf("    Saved %d workflow runs", totalRunsSaved)
	return nil
}

func (s *Seeder) fetchAndSaveGitHubReleases(repo string, repoID uint64, repoDetailsMap map[string]interface{}) error {
	totalReleasesSaved := 0

	// GitHub releases API endpoint
	releasesEndpoint := fmt.Sprintf("%s/repos/%s/releases", s.config.GitHubBaseURL, repo)

	log.Printf("    Fetching releases")
	resp, err := s.makeGitHubRequest(releasesEndpoint)
	if err != nil {
		return fmt.Errorf("failed to make releases request: %w", err)
	}
	defer resp.Body.Close()

	// Some repositories might not have any releases
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusNotFound {
			log.Printf("    No releases found")
			return nil
		}
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("releases API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var releases []interface{}
	if err := json.NewDecoder(resp.Body).Decode(&releases); err != nil {
		return fmt.Errorf("failed to decode releases response: %w", err)
	}

	log.Printf("    Retrieved %d releases", len(releases))

	// Save each release to the database with webhook-like format
	for _, release := range releases {
		// Extract release ID for existence check
		releaseMap, ok := release.(map[string]interface{})
		if !ok {
			log.Printf("    Warning: Invalid release data format")
			continue
		}

		releaseID, _ := releaseMap["id"].(float64)
		if releaseID > 0 && s.checkGitHubReleaseExists(repo, int64(releaseID)) {
			log.Printf("    Skipping release %d (already exists)", int64(releaseID))
			continue
		}

		releaseJSON, err := json.Marshal(release)
		if err != nil {
			log.Printf("    Failed to marshal release: %v", err)
			continue
		}

		// Create a payload similar to a webhook payload
		payload := map[string]interface{}{
			"repository":   repoDetailsMap, // Use complete repo details
			"release":      json.RawMessage(releaseJSON),
			"action":       "published",
			"event_type":   "release",
			"webhookEvent": "release",
		}

		payloadJSON, err := json.Marshal(payload)
		if err != nil {
			log.Printf("    Failed to marshal release payload: %v", err)
			continue
		}

		// Save to github_event_raw
		rawEvent := GitHubEventRaw{
			Payload:   string(payloadJSON),
			CreatedAt: time.Now().Format(time.RFC3339),
		}

		if err := s.db.Create(&rawEvent).Error; err != nil {
			log.Printf("    Failed to save release: %v", err)
			continue
		}
		totalReleasesSaved++
	}

	log.Printf("    Saved %d releases", totalReleasesSaved)
	return nil
}

func (s *Seeder) fetchAndSaveDeployments(repo string, repoID uint64, repoDetailsMap map[string]interface{}) error {
	totalDeploymentsSaved := 0

	// GitHub deployments API endpoint
	deploymentsEndpoint := fmt.Sprintf("%s/repos/%s/deployments", s.config.GitHubBaseURL, repo)

	log.Printf("    Fetching deployments")
	resp, err := s.makeGitHubRequest(deploymentsEndpoint)
	if err != nil {
		return fmt.Errorf("failed to make deployments request: %w", err)
	}
	defer resp.Body.Close()

	// Some repositories might not have any deployments
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusNotFound {
			log.Printf("    No deployments found")
			return nil
		}
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("deployments API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var deployments []map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&deployments); err != nil {
		return fmt.Errorf("failed to decode deployments response: %w", err)
	}

	log.Printf("    Retrieved %d deployments", len(deployments))

	// Save each deployment with its statuses
	for _, deployment := range deployments {
		deploymentID, ok := deployment["id"].(float64)
		if !ok || deploymentID == 0 {
			log.Printf("    Invalid deployment ID, skipping")
			continue
		}

		// Check if deployment already exists
		if s.checkGitHubDeploymentExists(int64(deploymentID)) {
			log.Printf("    Skipping deployment %d (already exists)", int64(deploymentID))
			continue
		}

		// Fetch deployment statuses
		statuses, err := s.fetchDeploymentStatuses(repo, int(deploymentID))
		if err != nil {
			log.Printf("    Failed to fetch statuses for deployment %d: %v", int(deploymentID), err)
		}

		// Include statuses in the deployment payload
		deployment["statuses"] = statuses

		// Create a payload similar to a webhook payload
		deploymentJSON, err := json.Marshal(deployment)
		if err != nil {
			log.Printf("    Failed to marshal deployment: %v", err)
			continue
		}

		// Create deployment event payload
		payload := map[string]interface{}{
			"repository":          repoDetailsMap, // Use complete repo details
			"deployment":          json.RawMessage(deploymentJSON),
			"action":              "created",
			"event_type":          "deployment",
			"webhookEvent":        "deployment",
			"deployment_statuses": statuses,
		}

		payloadJSON, err := json.Marshal(payload)
		if err != nil {
			log.Printf("    Failed to marshal deployment payload: %v", err)
			continue
		}

		// Save to github_event_raw
		rawEvent := GitHubEventRaw{
			Payload:   string(payloadJSON),
			CreatedAt: time.Now().Format(time.RFC3339),
		}

		if err := s.db.Create(&rawEvent).Error; err != nil {
			log.Printf("    Failed to save deployment: %v", err)
			continue
		}
		totalDeploymentsSaved++

		// Also create deployment_status events for each status
		for _, status := range statuses {
			// Create a status event
			statusPayload := map[string]interface{}{
				"repository":        repoDetailsMap,
				"deployment":        json.RawMessage(deploymentJSON),
				"deployment_status": status,
				"action":            "created",
				"event_type":        "deployment_status",
				"webhookEvent":      "deployment_status",
			}

			statusJSON, err := json.Marshal(statusPayload)
			if err != nil {
				log.Printf("    Failed to marshal status payload: %v", err)
				continue
			}

			// Save status event
			statusEvent := GitHubEventRaw{
				Payload:   string(statusJSON),
				CreatedAt: time.Now().Format(time.RFC3339),
			}

			if err := s.db.Create(&statusEvent).Error; err != nil {
				log.Printf("    Failed to save deployment status: %v", err)
				continue
			}

			// Save successful deployments directly to fact_deployment table
			statusState, _ := status["state"].(string)
			if statusState == "success" {
				s.saveDeploymentDirectly(repoID, deployment, status)
			}
		}
	}

	log.Printf("    Saved %d deployments with their statuses", totalDeploymentsSaved)
	return nil
}

func (s *Seeder) fetchAndSaveRepoEvents(repo string, repoID uint64, repoDetailsMap map[string]interface{}) error {
	totalEventsSaved := 0

	// Fetch events for the repository
	page := 1
	perPage := 100
	maxPages := 5 // Limit to avoid rate limiting

	for page <= maxPages {
		// GitHub events endpoint
		eventsEndpoint := fmt.Sprintf("%s/repos/%s/events?page=%d&per_page=%d",
			s.config.GitHubBaseURL, repo, page, perPage)

		log.Printf("    Fetching GitHub events: page=%d", page)
		resp, err := s.makeGitHubRequest(eventsEndpoint)
		if err != nil {
			return fmt.Errorf("failed to make events request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			return fmt.Errorf("events API request failed with status %d: %s", resp.StatusCode, string(body))
		}

		var events []GitHubEvent
		if err := json.NewDecoder(resp.Body).Decode(&events); err != nil {
			return fmt.Errorf("failed to decode events response: %w", err)
		}

		log.Printf("    Retrieved %d events", len(events))

		if len(events) == 0 {
			break // No more events to process
		}

		// Filter and save each event to the database
		for _, event := range events {
			// Skip if the event type is not in our target list
			if !containsEventType(s.config.GitHubEventTypes, event.Type) {
				continue
			}

			// Check if event already exists
			if s.checkGitHubEventExists(event.ID) {
				log.Printf("    Skipping event %s (already exists)", event.ID)
				continue
			}

			// Create full payload with event metadata
			fullPayload := map[string]interface{}{
				"id":         event.ID,
				"type":       event.Type,
				"created_at": event.Created_at,
				"repo":       event.Repo,
				"repository": repoDetailsMap, // Add complete repository details
				"payload":    event.Payload,
			}

			payloadJSON, err := json.Marshal(fullPayload)
			if err != nil {
				log.Printf("    Failed to marshal event: %v", err)
				continue
			}

			createdAt, err := time.Parse(time.RFC3339, event.Created_at)
			if err != nil {
				createdAt = time.Now() // Use current time as fallback
			}

			rawEvent := GitHubEventRaw{
				Payload:   string(payloadJSON),
				CreatedAt: createdAt.Format(time.RFC3339),
			}

			if err := s.db.Create(&rawEvent).Error; err != nil {
				log.Printf("    Failed to save event: %v", err)
				continue
			}
			totalEventsSaved++
		}

		// Check if Link header exists and has next page
		linkHeader := resp.Header.Get("Link")
		hasNextPage := strings.Contains(linkHeader, `rel="next"`)
		if !hasNextPage {
			break
		}

		page++
		time.Sleep(1 * time.Second) // Respect rate limiting
	}

	log.Printf("    Saved %d events from events stream", totalEventsSaved)
	return nil
}

func (s *Seeder) fetchDeploymentStatuses(repo string, deploymentID int) ([]map[string]interface{}, error) {
	// GitHub deployment statuses API endpoint
	statusesEndpoint := fmt.Sprintf("%s/repos/%s/deployments/%d/statuses",
		s.config.GitHubBaseURL, repo, deploymentID)

	resp, err := s.makeGitHubRequest(statusesEndpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to make deployment statuses request: %w", err)
	}
	defer resp.Body.Close()

	// Check response
	if resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusNotFound {
			return []map[string]interface{}{}, nil
		}
		return nil, fmt.Errorf("deployment statuses API request failed with status %d", resp.StatusCode)
	}

	var statuses []map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&statuses); err != nil {
		return nil, fmt.Errorf("failed to decode deployment statuses: %w", err)
	}

	return statuses, nil
}

func (s *Seeder) fetchPRDetails(repo string, prNumber int) map[string]interface{} {
	// GitHub PR details API endpoint
	prEndpoint := fmt.Sprintf("%s/repos/%s/pulls/%d", s.config.GitHubBaseURL, repo, prNumber)

	resp, err := s.makeGitHubRequest(prEndpoint)
	if err != nil {
		log.Printf("    Failed to fetch PR details: %v", err)
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Printf("    PR details API request failed with status %d", resp.StatusCode)
		return nil
	}

	var prDetails map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&prDetails); err != nil {
		log.Printf("    Failed to decode PR details: %v", err)
		return nil
	}

	return prDetails
}

// =====================================================
//
// EXISTENCE CHECK FUNCTIONS
//
// =====================================================

func (s *Seeder) checkJiraIssueExists(payload string) bool {
	// Extract issue key from Jira payload
	var payloadMap map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &payloadMap); err != nil {
		log.Printf("    Warning: Failed to parse Jira payload for existence check: %v", err)
		return false
	}

	// Navigate to the issue data
	issueData, ok := payloadMap["issue"].(map[string]interface{})
	if !ok {
		return false
	}

	issueKey, ok := issueData["key"].(string)
	if !ok || issueKey == "" {
		return false
	}

	// Check if this issue already exists in the database
	var count int64
	sql := `SELECT count() FROM jira_raw_events WHERE JSONExtractString(payload, 'issue', 'key') = ?`

	if err := s.db.Raw(sql, issueKey).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check Jira issue existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkJiraSprintExists(payload string) bool {
	// Extract sprint ID from Jira payload
	var payloadMap map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &payloadMap); err != nil {
		log.Printf("    Warning: Failed to parse Jira sprint payload for existence check: %v", err)
		return false
	}

	// Navigate to the sprint data
	sprintData, ok := payloadMap["sprint"].(map[string]interface{})
	if !ok {
		return false
	}

	sprintID, ok := sprintData["id"].(float64)
	if !ok || sprintID == 0 {
		return false
	}

	// Check if this sprint already exists in the database
	var count int64
	sql := `SELECT count() FROM jira_raw_events WHERE JSONExtractFloat(payload, 'sprint', 'id') = ?`

	if err := s.db.Raw(sql, sprintID).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check Jira sprint existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkJiraReleaseExists(payload string) bool {
	// Extract version ID from Jira payload
	var payloadMap map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &payloadMap); err != nil {
		log.Printf("    Warning: Failed to parse Jira release payload for existence check: %v", err)
		return false
	}

	// Navigate to the version data
	versionData, ok := payloadMap["version"].(map[string]interface{})
	if !ok {
		return false
	}

	versionID, ok := versionData["id"].(string)
	if !ok || versionID == "" {
		return false
	}

	// Check if this version already exists in the database
	var count int64
	sql := `SELECT count() FROM jira_raw_events WHERE JSONExtractString(payload, 'version', 'id') = ?`

	if err := s.db.Raw(sql, versionID).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check Jira release existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkGitHubBranchExists(repo string, branchName string) bool {
	// Check if this branch already exists in the database
	var count int64
	sql := `SELECT count() FROM github_raw_events WHERE JSONExtractString(payload, 'branch') = ? AND JSONExtractString(payload, 'repository', 'full_name') = ?`

	if err := s.db.Raw(sql, branchName, repo).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check GitHub branch existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkGitHubCommitExists(commitSHA string) bool {
	// Check if this commit already exists in the database
	var count int64
	sql := `SELECT count() FROM github_raw_events WHERE JSONExtractString(payload, 'commits', 'id') = ?`

	if err := s.db.Raw(sql, commitSHA).Scan(&count).Error; err != nil {
		// Try alternative path for single commit
		sql = `SELECT count() FROM github_raw_events WHERE JSONExtractString(payload, 'after') = ?`
		if err := s.db.Raw(sql, commitSHA).Scan(&count).Error; err != nil {
			log.Printf("    Warning: Failed to check GitHub commit existence: %v", err)
			return false
		}
	}

	return count > 0
}

func (s *Seeder) checkGitHubPRExists(repo string, prNumber int) bool {
	// Check if this PR already exists in the database
	var count int64
	sql := `SELECT count() FROM github_raw_events WHERE JSONExtractString(payload, 'pull_request', 'number') = ? AND JSONExtractString(payload, 'repository', 'full_name') = ?`

	if err := s.db.Raw(sql, prNumber, repo).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check GitHub PR existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkGitHubWorkflowRunExists(runID int64) bool {
	// Check if this workflow run already exists in the database
	var count int64
	sql := `SELECT count() FROM github_raw_events WHERE JSONExtractFloat(payload, 'workflow_run', 'id') = ?`

	if err := s.db.Raw(sql, runID).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check GitHub workflow run existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkGitHubReleaseExists(repo string, releaseID int64) bool {
	// Check if this release already exists in the database
	var count int64
	sql := `SELECT count() FROM github_raw_events WHERE JSONExtractFloat(payload, 'release', 'id') = ? AND JSONExtractString(payload, 'repository', 'full_name') = ?`

	if err := s.db.Raw(sql, releaseID, repo).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check GitHub release existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkGitHubDeploymentExists(deploymentID int64) bool {
	// Check if this deployment already exists in the database
	var count int64
	sql := `SELECT count() FROM github_raw_events WHERE JSONExtractFloat(payload, 'deployment', 'id') = ?`

	if err := s.db.Raw(sql, deploymentID).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check GitHub deployment existence: %v", err)
		return false
	}

	return false
}

func (s *Seeder) checkGitHubEventExists(eventID string) bool {
	// Check if this event already exists in the database
	var count int64
	sql := `SELECT count() FROM github_raw_events WHERE JSONExtractString(payload, 'id') = ?`

	if err := s.db.Raw(sql, eventID).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check GitHub event existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkEnvironmentExists(envName string) bool {
	var count int64
	sql := `SELECT count() FROM analytics.environment_dim WHERE env_name = ?`

	if err := s.db.Raw(sql, envName).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check environment existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkDeploymentExists(deploymentID, statusID uint64) bool {
	var count int64
	sql := `SELECT count() FROM analytics.fact_deployment WHERE deployment_id = ? AND run_sk = ?`

	if err := s.db.Raw(sql, deploymentID, statusID).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check deployment existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkMergeExists(mergeSHA string) bool {
	var count int64
	sql := `SELECT count() FROM analytics.fact_merge WHERE merge_sha = ?`

	if err := s.db.Raw(sql, mergeSHA).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check merge existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkBranchExists(repoID uint64, branchName string) bool {
	var count int64
	sql := `SELECT count() FROM analytics.branch_dim WHERE repo_sk = ? AND branch_name = ?`

	if err := s.db.Raw(sql, repoID, branchName).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check branch existence: %v", err)
		return false
	}

	return count > 0
}

func (s *Seeder) checkCommitExists(commitSHA string) bool {
	var count int64
	sql := `SELECT count() FROM analytics.fact_commit WHERE commit_sha = ?`

	if err := s.db.Raw(sql, commitSHA).Scan(&count).Error; err != nil {
		log.Printf("    Warning: Failed to check commit existence: %v", err)
		return false
	}

	return count > 0
}

// =====================================================
//
// SAVING FUNCTIONS (EXCLUSIVELY RUNNING SQL)
//
// =====================================================

func (s *Seeder) saveEnvironmentDirectly(environmentName string) {
	// Check if environment already exists
	if s.checkEnvironmentExists(environmentName) {
		log.Printf("    Skipping environment %s (already exists)", environmentName)
		return
	}

	// Determine environment surrogate key
	var envSK uint8 = 0 // Default unknown environment
	if environmentName == "production" || environmentName == "prod" {
		envSK = 1
	} else if environmentName == "staging" || environmentName == "stage" {
		envSK = 2
	} else if environmentName == "development" || environmentName == "dev" {
		envSK = 3
	} else if environmentName == "test" || environmentName == "testing" {
		envSK = 4
	}

	// Create SQL to insert environment
	sql := `
		INSERT INTO analytics.environment_dim (
			env_sk,
			env_name
		) VALUES (
			?,
			?
		)
	`

	err := s.db.Exec(sql, envSK, environmentName).Error
	if err != nil {
		// Ignore errors, likely just duplicate key
		// log.Printf("    Note: Environment insert result: %v", err)
	}
}

func (s *Seeder) saveDeploymentDirectly(repoID uint64, deployment map[string]interface{}, status map[string]interface{}) {
	// Extract needed fields
	deploymentID, _ := deployment["id"].(float64)
	statusID, _ := status["id"].(float64)

	if deploymentID == 0 || statusID == 0 {
		log.Printf("    Warning: Missing required deployment/status IDs")
		return
	}

	// Check if deployment already exists
	if s.checkDeploymentExists(uint64(deploymentID), uint64(statusID)) {
		log.Printf("    Skipping deployment %d (already exists)", uint64(deploymentID))
		return
	}

	// Extract environment
	environment := "unknown"
	if env, ok := deployment["environment"].(string); ok && env != "" {
		environment = env
	}

	// Insert the environment to environment_dim if it doesn't exist
	s.saveEnvironmentDirectly(environment)

	// Calculate environment surrogate key
	var envSK uint8 = 0 // Default unknown environment
	if environment == "production" || environment == "prod" {
		envSK = 1
	} else if environment == "staging" || environment == "stage" {
		envSK = 2
	} else if environment == "development" || environment == "dev" || environment == "develop" {
		envSK = 3
	} else if environment == "test" || environment == "testing" {
		envSK = 4
	}

	// Extract created_at timestamp
	createdAtStr, _ := status["created_at"].(string)
	createdAt, err := time.Parse(time.RFC3339, createdAtStr)
	if err != nil {
		log.Printf("    Warning: Failed to parse created_at: %v", err)
		createdAt = time.Now()
	}

	// Extract deployment SHA
	sha := ""
	if ref, ok := deployment["sha"].(string); ok && ref != "" {
		sha = ref
	}

	// Extract status
	deploymentStatus := "unknown"
	if state, ok := status["state"].(string); ok && state != "" {
		deploymentStatus = state
	}

	// Use combined IDs as deployment key
	deploymentKey := fmt.Sprintf("%d-%d", uint64(deploymentID), uint64(statusID))

	// Create SQL to insert deployment directly
	sql := `
		INSERT INTO analytics.fact_deployment (
			deployment_sk,
			repo_sk,
			env_sk,
			run_sk,
			deployment_id,
			main_commit_sha,
			status,
			deployed_at
		) VALUES (
			cityHash64(?),
			?,
			?,
			0,
			?,
			?,
			?,
			?
		)
	`

	err = s.db.Exec(sql,
		deploymentKey,
		repoID,
		envSK,
		uint64(deploymentID),
		sha,
		deploymentStatus,
		createdAt,
	).Error

	if err != nil {
		log.Printf("    Warning: Failed to directly insert deployment: %v", err)
	}
}

func (s *Seeder) saveMergeDirectly(repoID uint64, pr map[string]interface{}) {
	// Extract needed fields from PR
	prNumber, _ := pr["number"].(float64)
	mergeSha, _ := pr["merge_commit_sha"].(string)
	mergedByRaw, hasmergedBy := pr["merged_by"].(map[string]interface{})
	mergedAt, hasmergedAt := pr["merged_at"].(string)

	if prNumber == 0 || mergeSha == "" || !hasmergedBy || !hasmergedAt {
		log.Printf("    Warning: Missing required fields for fact_merge")
		return
	}

	// Check if merge already exists
	if s.checkMergeExists(mergeSha) {
		log.Printf("    Skipping merge for PR #%d (already exists)", int(prNumber))
		return
	}

	// Parse mergedAt date
	parsedMergedAt, err := time.Parse(time.RFC3339, mergedAt)
	if err != nil {
		log.Printf("    Warning: Failed to parse merged_at date: %v", err)
		parsedMergedAt = time.Now() // Fallback
	}

	// Extract user ID who merged
	var mergedByUserID string
	if mergedByLogin, ok := mergedByRaw["login"].(string); ok {
		mergedByUserID = mergedByLogin
	} else {
		mergedByUserID = "unknown"
	}

	// Extract source and target branch
	var sourceBranch, targetBranch string
	if headRef, ok := pr["head"].(map[string]interface{}); ok {
		if ref, ok := headRef["ref"].(string); ok {
			sourceBranch = ref
		}
	}
	if baseRef, ok := pr["base"].(map[string]interface{}); ok {
		if ref, ok := baseRef["ref"].(string); ok {
			targetBranch = ref
		}
	}

	// Create SQL to insert merge directly
	sql := `
		INSERT INTO analytics.fact_merge (
			merge_sk,
			repo_sk,
			source_branch_sk,
			target_branch_sk,
			pr_number,
			merged_by_user_id,
			merge_sha,
			merged_at
		) VALUES (
			cityHash64(?),
			?,
			cityHash64(concat(toString(?), ?)),
			cityHash64(concat(toString(?), ?)),
			?,
			?,
			?,
			?
		)
	`

	err = s.db.Exec(sql,
		mergeSha,
		repoID,
		repoID, sourceBranch,
		repoID, targetBranch,
		uint64(prNumber),
		mergedByUserID,
		mergeSha,
		parsedMergedAt,
	).Error

	if err != nil {
		log.Printf("    Warning: Failed to directly insert merge: %v", err)
	}
}

func (s *Seeder) saveBranchDirectly(repoID uint64, branchName, branchType string) {
	// Check if branch already exists
	if s.checkBranchExists(repoID, branchName) {
		log.Printf("    Skipping branch %s (already exists)", branchName)
		return
	}

	// Create SQL to insert branch directly, with ON CONFLICT to handle duplicates
	sql := `
		INSERT INTO analytics.branch_dim (
			branch_sk, repo_sk, branch_name, branch_type
		) VALUES (
			cityHash64(concat(toString(?), ?)), ?, ?, ?
		)
	`

	err := s.db.Exec(sql, repoID, branchName, repoID, branchName, branchType).Error
	if err != nil {
		log.Printf("    Warning: Failed to directly insert branch: %v", err)
	}
}

func (s *Seeder) saveCommitDirectly(repoID uint64, commit map[string]interface{}) {
	// Extract needed fields
	commitID, _ := commit["id"].(string)
	if commitID == "" {
		log.Printf("    Warning: Missing commit ID, skipping direct insert")
		return
	}

	// Check if commit already exists
	if s.checkCommitExists(commitID) {
		log.Printf("    Skipping commit %s (already exists)", commitID[:8])
		return
	}

	// Extract timestamp
	timestamp := time.Now() // Default
	if ts, ok := commit["timestamp"].(string); ok {
		parsed, err := time.Parse(time.RFC3339, ts)
		if err == nil {
			timestamp = parsed
		}
	}

	// Extract author info
	authorUserID := ""
	committerUserID := ""

	if authorMap, ok := commit["author"].(map[string]interface{}); ok {
		if username, ok := authorMap["username"].(string); ok && username != "" {
			authorUserID = username
		} else if login, ok := authorMap["login"].(string); ok && login != "" {
			authorUserID = login
		} else if name, ok := authorMap["name"].(string); ok && name != "" {
			authorUserID = name
		}
	}

	if committerMap, ok := commit["committer"].(map[string]interface{}); ok {
		if username, ok := committerMap["username"].(string); ok && username != "" {
			committerUserID = username
		} else if login, ok := committerMap["login"].(string); ok && login != "" {
			committerUserID = login
		} else if name, ok := committerMap["name"].(string); ok && name != "" {
			committerUserID = name
		}
	}

	// Extract message
	message := ""
	if msg, ok := commit["message"].(string); ok {
		message = msg
	}

	// Determine if it's a merge commit
	isMergeCommit := false
	if message != "" && strings.HasPrefix(message, "Merge") {
		isMergeCommit = true
	}

	// Create SQL to insert commit directly
	sql := `
		INSERT INTO analytics.fact_commit (
			commit_sk,
			repo_sk,
			branch_sk,
			author_user_id,
			committer_user_id,
			commit_sha,
			message,
			authored_at,
			committed_at,
			is_merge_commit
		) VALUES (
			cityHash64(concat(toString(?), ?)),
			?,
			0,
			?,
			?,
			?,
			?,
			?,
			?,
			?
		)
	`

	err := s.db.Exec(sql,
		repoID,
		commitID,
		repoID,
		authorUserID,
		committerUserID,
		commitID,
		message,
		timestamp,
		timestamp,
		isMergeCommit,
	).Error

	if err != nil {
		log.Printf("    Warning: Failed to directly insert commit: %v", err)
	}
}

// =====================================================
//
// HELPER FUNCTIONS / UTILITIES
//
// =====================================================

func loadConfig() (*Config, error) {
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	projectsEnv := os.Getenv("JIRA_PROJECTS")
	if projectsEnv == "" {
		projectsEnv = os.Getenv("JIRA_PROJECT")
	}

	// Parse projects from comma-separated string
	var jiraProjects []string
	if projectsEnv != "" {
		projectsList := strings.Split(projectsEnv, ",")
		for _, p := range projectsList {
			trimmed := strings.TrimSpace(p)
			if trimmed != "" {
				jiraProjects = append(jiraProjects, trimmed)
			}
		}
	}

	// Parse GitHub repos from comma-separated string
	reposEnv := os.Getenv("GITHUB_REPOS")
	var githubRepos []string
	if reposEnv != "" {
		reposList := strings.Split(reposEnv, ",")
		for _, r := range reposList {
			trimmed := strings.TrimSpace(r)
			if trimmed != "" {
				githubRepos = append(githubRepos, trimmed)
			}
		}
	}

	// Parse GitHub event types from comma-separated string
	eventTypesEnv := os.Getenv("GITHUB_EVENT_TYPES")
	var eventTypes []string
	if eventTypesEnv != "" {
		eventTypesList := strings.Split(eventTypesEnv, ",")
		for _, e := range eventTypesList {
			trimmed := strings.TrimSpace(e)
			if trimmed != "" {
				eventTypes = append(eventTypes, trimmed)
			}
		}
	} else {
		// Default event types if none specified
		eventTypes = []string{"push", "pull_request", "workflow_run", "deployment", "deployment_status"}
	}

	config := &Config{
		JiraBaseURL:      os.Getenv("JIRA_BASE_URL"),
		JiraEmail:        os.Getenv("JIRA_EMAIL"),
		JiraToken:        os.Getenv("JIRA_TOKEN"),
		JiraProjects:     jiraProjects,
		ClickHouseDSN:    os.Getenv("CLICKHOUSE_DSN"),
		GitHubToken:      os.Getenv("GITHUB_TOKEN"),
		GitHubRepos:      githubRepos,
		GitHubBaseURL:    os.Getenv("GITHUB_API_URL"),
		GitHubEventTypes: eventTypes,
	}

	// Set default ClickHouse DSN if not provided
	if config.ClickHouseDSN == "" {
		config.ClickHouseDSN = "clickhouse://default:@localhost:9000/analytics"
	}

	// Set default GitHub API URL if not provided
	if config.GitHubBaseURL == "" {
		config.GitHubBaseURL = "https://api.github.com"
	}

	// Validate required fields for Jira
	if len(jiraProjects) > 0 && (config.JiraBaseURL == "" || config.JiraEmail == "" || config.JiraToken == "") {
		return nil, fmt.Errorf("missing required environment variables for Jira: JIRA_BASE_URL, JIRA_EMAIL, JIRA_TOKEN")
	}

	// GitHub token is optional for public repositories; warn if absent
	if len(githubRepos) > 0 && config.GitHubToken == "" {
		log.Println("Warning: GITHUB_TOKEN not provided – proceeding unauthenticated; rate limits will be very low (60 req/hr)")
	}

	return config, nil
}

func initClickHouse(dsn string) (*gorm.DB, error) {
	fmt.Printf("Seeder: Attempting to connect to ClickHouse with DSN: %s\n", dsn)

	db, err := gorm.Open(clickhouse.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
	})
	if err != nil {
		fmt.Printf("Seeder: Failed to open ClickHouse connection: %v\n", err)
		return nil, fmt.Errorf("failed to connect to ClickHouse via GORM: %w", err)
	}

	fmt.Printf("Seeder: ClickHouse connection opened successfully, testing with ping...\n")

	// Test connection
	var one int
	if err := db.Raw("SELECT 1").Scan(&one).Error; err != nil {
		fmt.Printf("Seeder: Failed to ping ClickHouse: %v\n", err)
		return nil, fmt.Errorf("failed to ping ClickHouse: %w", err)
	}

	fmt.Printf("Seeder: ClickHouse ping successful, connection established\n")
	return db, nil
}

func containsEventType(eventTypes []string, eventType string) bool {
	for _, t := range eventTypes {
		if t == eventType {
			return true
		}
	}
	return false
}

func processCommitForFactTable(commit map[string]interface{}, repoDetailsMap map[string]interface{}) map[string]interface{} {
	processedCommit := make(map[string]interface{})

	// Copy original commit data
	for k, v := range commit {
		processedCommit[k] = v
	}

	// Extract commit SHA and use it as ID
	if sha, ok := commit["sha"].(string); ok {
		processedCommit["id"] = sha
	}

	// Extract the actual commit data which is nested
	if commitData, ok := commit["commit"].(map[string]interface{}); ok {
		// Extract committer info
		if committer, ok := commitData["committer"].(map[string]interface{}); ok {
			processedCommit["committer"] = committer

			// Extract and format timestamp (critical for the ClickHouse view)
			if date, ok := committer["date"].(string); ok {
				// GitHub date format is already ISO-8601, ClickHouse should parse it
				processedCommit["timestamp"] = date
			} else {
				// Fallback to current time in ISO-8601 format
				processedCommit["timestamp"] = time.Now().Format(time.RFC3339)
			}
		}

		// Extract author info
		if author, ok := commitData["author"].(map[string]interface{}); ok {
			processedCommit["author"] = author
		}

		// Extract message
		if message, ok := commitData["message"].(string); ok {
			processedCommit["message"] = message
		}
	}

	return processedCommit
}

// =====================================================
//
// REQUEST FUNCTIONS
//
// =====================================================

func (s *Seeder) makeJiraRequest(endpoint string) (*http.Response, error) {
	req, err := http.NewRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	req.SetBasicAuth(s.config.JiraEmail, s.config.JiraToken)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	return s.client.Do(req)
}

func (s *Seeder) makeGitHubRequest(endpoint string) (*http.Response, error) {
	req, err := http.NewRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	if s.config.GitHubToken != "" {
		req.Header.Set("Authorization", "token "+s.config.GitHubToken)
	}
	req.Header.Set("Accept", "application/vnd.github.v3+json")
	req.Header.Set("User-Agent", "GitHub-Data-Migration-Script")

	return s.client.Do(req)
}

// =====================================================
//
// INTERFACES
//
// =====================================================

type JiraVersionResponse []interface{}
