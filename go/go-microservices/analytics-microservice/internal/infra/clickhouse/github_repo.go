package clickhouse

import (
	"context"
	"fmt"
	"time"

	"analytics-microservice/internal/domain"

	"gorm.io/gorm"
)

// GitHubRepo provides data access for GitHub-related models
type GitHubRepo struct {
	db *gorm.DB
}

// NewGitHubRepo creates a new GitHub repository
func NewGitHubRepo(db *gorm.DB) *GitHubRepo {
	return &GitHubRepo{db: db}
}

// =============================================================
//  RAW EVENT OPERATIONS
// =============================================================

// StoreWebhookEvent stores a webhook event in GitHubRawEvents table (same schema as Jira)
func (r *GitHubRepo) StoreWebhookEvent(ctx context.Context, event *domain.WebhookEvent) error {
	// Convert domain entity to GitHubRawEvents structure (same as Jira pattern)
	rawEvent := r.toGitHubRawEvents(event)

	// Store in github_raw_events table
	if err := r.db.WithContext(ctx).Create(rawEvent).Error; err != nil {
		return fmt.Errorf("failed to store GitHub webhook event: %w", err)
	}

	return nil
}

// toGitHubRawEvents converts domain entity to GitHubRawEvents (same pattern as Jira)
func (r *GitHubRepo) toGitHubRawEvents(event *domain.WebhookEvent) *domain.GitHubRawEvents {
	return &domain.GitHubRawEvents{
		Payload:   string(event.Payload()),
		CreatedAt: event.ReceivedAt(),
	}
}

// GetGitHubEventsByType retrieves events by inferred type (e.g. push, workflow_run) by inspecting JSON content
func (r *GitHubRepo) GetGitHubEventsByType(ctx context.Context, eventType string, limit int) ([]domain.GitHubRawEvents, error) {
	var events []domain.GitHubRawEvents

	// Map high-level GitHub event types to a JSON key that should be present in the payload
	jsonKey := ""
	switch eventType {
	case "push":
		jsonKey = "commits"
	case "workflow_run":
		jsonKey = "workflow_run"
	case "deployment_status", "deployment":
		jsonKey = "deployment_status"
	case "pull_request":
		jsonKey = "pull_request"
	default:
		jsonKey = ""
	}

	query := r.db.WithContext(ctx).Table((domain.GitHubRawEvents{}).TableName())
	if jsonKey != "" {
		query = query.Where("JSONHas(payload, ?)", jsonKey)
	}

	err := query.Order("created_at DESC").Limit(limit).Find(&events).Error
	return events, err
}

// =============================================================
//  DIMENSION OPERATIONS
// =============================================================

// UpsertRepo creates or updates a repository dimension
func (r *GitHubRepo) UpsertRepo(ctx context.Context, repo *domain.RepoDim) error {
	var existing domain.RepoDim
	err := r.db.WithContext(ctx).Where("repo_id = ?", repo.RepoID).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		return r.db.WithContext(ctx).Create(repo).Error
	} else if err != nil {
		return err
	}

	// Update existing repo
	repo.RepoSK = existing.RepoSK
	return r.db.WithContext(ctx).Save(repo).Error
}

// GetRepoByID retrieves a repository by GitHub repo ID
func (r *GitHubRepo) GetRepoByID(ctx context.Context, repoID uint64) (*domain.RepoDim, error) {
	var repo domain.RepoDim
	err := r.db.WithContext(ctx).Where("repo_id = ?", repoID).First(&repo).Error
	if err != nil {
		return nil, err
	}
	return &repo, nil
}

// UpsertUser creates or updates a user dimension (using shared UserDim)
func (r *GitHubRepo) UpsertUser(ctx context.Context, user *domain.UserDim) error {
	var existing domain.UserDim
	err := r.db.WithContext(ctx).Where("user_id = ?", user.UserID).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		return r.db.WithContext(ctx).Create(user).Error
	} else if err != nil {
		return err
	}

	// Update existing user
	return r.db.WithContext(ctx).Save(user).Error
}

// GetUserByID retrieves a user by GitHub user ID (using shared UserDim)
func (r *GitHubRepo) GetUserByID(ctx context.Context, userID string) (*domain.UserDim, error) {
	var user domain.UserDim
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// UpsertBranch creates or updates a branch dimension
func (r *GitHubRepo) UpsertBranch(ctx context.Context, branch *domain.BranchDim) error {
	var existing domain.BranchDim
	err := r.db.WithContext(ctx).
		Where("repo_sk = ? AND branch_name = ?", branch.RepoSK, branch.BranchName).
		First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		return r.db.WithContext(ctx).Create(branch).Error
	} else if err != nil {
		return err
	}

	// Update existing branch
	branch.BranchSK = existing.BranchSK
	return r.db.WithContext(ctx).Save(branch).Error
}

// =============================================================
//  FACT TABLE OPERATIONS
// =============================================================

// CreateCommit stores a commit fact
func (r *GitHubRepo) CreateCommit(ctx context.Context, commit *domain.FactCommit) error {
	return r.db.WithContext(ctx).Create(commit).Error
}

// GetCommitsBySHA retrieves commits by SHA
func (r *GitHubRepo) GetCommitsBySHA(ctx context.Context, sha string) (*domain.FactCommit, error) {
	var commit domain.FactCommit
	err := r.db.WithContext(ctx).Where("commit_sha = ?", sha).First(&commit).Error
	if err != nil {
		return nil, err
	}
	return &commit, nil
}

// CreateMerge stores a merge fact
func (r *GitHubRepo) CreateMerge(ctx context.Context, merge *domain.FactMerge) error {
	return r.db.WithContext(ctx).Create(merge).Error
}

// CreateWorkflowRun stores a workflow run fact
func (r *GitHubRepo) CreateWorkflowRun(ctx context.Context, run *domain.FactWorkflowRun) error {
	return r.db.WithContext(ctx).Create(run).Error
}

// CreateDeployment stores a deployment fact
func (r *GitHubRepo) CreateDeployment(ctx context.Context, deployment *domain.FactDeployment) error {
	return r.db.WithContext(ctx).Create(deployment).Error
}

// GetDeploymentsByRepo retrieves deployments for a repository
func (r *GitHubRepo) GetDeploymentsByRepo(ctx context.Context, repoSK uint64, envSK uint8, limit int) ([]domain.FactDeployment, error) {
	var deployments []domain.FactDeployment
	query := r.db.WithContext(ctx).Where("repo_sk = ?", repoSK)

	if envSK > 0 {
		query = query.Where("env_sk = ?", envSK)
	}

	err := query.Order("deployed_at DESC").Limit(limit).Find(&deployments).Error
	return deployments, err
}

// CreateIncident stores an incident fact
func (r *GitHubRepo) CreateIncident(ctx context.Context, incident *domain.FactIncident) error {
	return r.db.WithContext(ctx).Create(incident).Error
}

// =============================================================
//  BRIDGE TABLE OPERATIONS
// =============================================================

// LinkCommitToDeployment creates a commit-deployment bridge
func (r *GitHubRepo) LinkCommitToDeployment(ctx context.Context, commitSK, deploymentSK uint64) error {
	bridge := &domain.BridgeCommitDeployment{
		CommitSK:     int64(commitSK),
		DeploymentSK: int64(deploymentSK),
	}
	return r.db.WithContext(ctx).Create(bridge).Error
}

// LinkIncidentToDeployment creates an incident-deployment bridge
func (r *GitHubRepo) LinkIncidentToDeployment(ctx context.Context, incidentSK, deploymentSK uint64) error {
	bridge := &domain.BridgeIncidentDeployment{
		IncidentSK:   int64(incidentSK),
		DeploymentSK: int64(deploymentSK),
	}
	return r.db.WithContext(ctx).Create(bridge).Error
}

// =============================================================
//  DORA METRICS QUERIES
// =============================================================

// DORAMetrics represents DORA metrics for a repository
type DORAMetrics struct {
	RepoName            string    `json:"repo_name"`
	OwnerLogin          string    `json:"owner_login"`
	Period              string    `json:"period"`
	DeploymentFrequency float64   `json:"deployment_frequency"`
	LeadTimeHours       float64   `json:"lead_time_hours"`
	MTTRMinutes         float64   `json:"mttr_minutes"`
	ChangeFailureRate   float64   `json:"change_failure_rate"`
	CalculatedAt        time.Time `json:"calculated_at"`
}

// GetDeploymentFrequency calculates deployment frequency for repositories
func (r *GitHubRepo) GetDeploymentFrequency(ctx context.Context, repoID uint64, fromDate, toDate time.Time) ([]DORAMetrics, error) {
	var metrics []DORAMetrics

	// Derive period boundaries in YYYYMM format (UInt32 in ClickHouse views)
	startPeriod := fromDate.Year()*100 + int(fromDate.Month())
	endPeriod := toDate.Year()*100 + int(toDate.Month())

	baseQuery := `
		SELECT
			df.repo_name,
			df.owner_login,
			toString(df.period) AS period,
			df.deployments_per_day AS deployment_frequency
		FROM analytics.v_deployment_frequency df
	`

	// When repoID is provided we join repo_dim to filter accurately
	if repoID > 0 {
		baseQuery += `JOIN analytics.repo_dim r 
						ON r.repo_name = df.repo_name 
					   AND r.owner_login = df.owner_login 
					   AND r.repo_id = ?
					  WHERE df.period BETWEEN ? AND ? 
					  ORDER BY period DESC`
		err := r.db.WithContext(ctx).Raw(baseQuery, repoID, startPeriod, endPeriod).Scan(&metrics).Error
		return metrics, err
	}

	// All repositories
	baseQuery += `WHERE df.period BETWEEN ? AND ? ORDER BY period DESC`
	err := r.db.WithContext(ctx).Raw(baseQuery, startPeriod, endPeriod).Scan(&metrics).Error
	return metrics, err
}

// GetLeadTimeForChanges calculates lead time for changes
func (r *GitHubRepo) GetLeadTimeForChanges(ctx context.Context, repoID uint64, fromDate, toDate time.Time) ([]DORAMetrics, error) {
	var metrics []DORAMetrics
	startPeriod := fromDate.Year()*100 + int(fromDate.Month())
	endPeriod := toDate.Year()*100 + int(toDate.Month())

	baseQuery := `
		SELECT
			lt.repo_name,
			lt.owner_login,
			toString(lt.period) AS period,
			lt.median_lead_time_hours AS lead_time_hours
		FROM analytics.v_lead_time_for_changes lt
	`

	if repoID > 0 {
		baseQuery += `JOIN analytics.repo_dim r 
						ON r.repo_name = lt.repo_name 
					   AND r.owner_login = lt.owner_login 
					   AND r.repo_id = ?
					  WHERE lt.period BETWEEN ? AND ? 
					  ORDER BY period DESC`
		err := r.db.WithContext(ctx).Raw(baseQuery, repoID, startPeriod, endPeriod).Scan(&metrics).Error
		return metrics, err
	}

	baseQuery += `WHERE lt.period BETWEEN ? AND ? ORDER BY period DESC`
	err := r.db.WithContext(ctx).Raw(baseQuery, startPeriod, endPeriod).Scan(&metrics).Error
	return metrics, err
}

// GetMeanTimeToRestore calculates mean time to restore
func (r *GitHubRepo) GetMeanTimeToRestore(ctx context.Context, repoID uint64, fromDate, toDate time.Time) ([]DORAMetrics, error) {
	var metrics []DORAMetrics
	startPeriod := fromDate.Year()*100 + int(fromDate.Month())
	endPeriod := toDate.Year()*100 + int(toDate.Month())

	baseQuery := `
		SELECT
			mt.repo_name,
			mt.owner_login,
			toString(mt.period) AS period,
			mt.median_mttr_minutes AS mttr_minutes
		FROM analytics.v_mean_time_to_restore mt
	`

	if repoID > 0 {
		baseQuery += `JOIN analytics.repo_dim r 
						ON r.repo_name = mt.repo_name 
					   AND r.owner_login = mt.owner_login 
					   AND r.repo_id = ?
					  WHERE mt.period BETWEEN ? AND ? 
					  ORDER BY period DESC`
		err := r.db.WithContext(ctx).Raw(baseQuery, repoID, startPeriod, endPeriod).Scan(&metrics).Error
		return metrics, err
	}

	baseQuery += `WHERE mt.period BETWEEN ? AND ? ORDER BY period DESC`
	err := r.db.WithContext(ctx).Raw(baseQuery, startPeriod, endPeriod).Scan(&metrics).Error
	return metrics, err
}

// GetChangeFailureRate calculates change failure rate
func (r *GitHubRepo) GetChangeFailureRate(ctx context.Context, repoID uint64, fromDate, toDate time.Time) ([]DORAMetrics, error) {
	var metrics []DORAMetrics
	startPeriod := fromDate.Year()*100 + int(fromDate.Month())
	endPeriod := toDate.Year()*100 + int(toDate.Month())

	baseQuery := `
		SELECT
			cfr.repo_name,
			cfr.owner_login,
			toString(cfr.period) AS period,
			cfr.failure_rate_percent AS change_failure_rate
		FROM analytics.v_change_failure_rate cfr
	`

	if repoID > 0 {
		baseQuery += `JOIN analytics.repo_dim r 
						ON r.repo_name = cfr.repo_name 
					   AND r.owner_login = cfr.owner_login 
					   AND r.repo_id = ?
					  WHERE cfr.period BETWEEN ? AND ? 
					  ORDER BY period DESC`
		err := r.db.WithContext(ctx).Raw(baseQuery, repoID, startPeriod, endPeriod).Scan(&metrics).Error
		return metrics, err
	}

	baseQuery += `WHERE cfr.period BETWEEN ? AND ? ORDER BY period DESC`
	err := r.db.WithContext(ctx).Raw(baseQuery, startPeriod, endPeriod).Scan(&metrics).Error
	return metrics, err
}

// GetAllDORAMetrics retrieves all DORA metrics for a repository in a single call
func (r *GitHubRepo) GetAllDORAMetrics(ctx context.Context, repoID uint64, fromDate, toDate time.Time) (*DORAMetrics, error) {
	var result DORAMetrics

	// Get repository info first
	repo, err := r.GetRepoByID(ctx, repoID)
	if err != nil {
		return nil, fmt.Errorf("failed to get repository: %w", err)
	}

	result.RepoName = repo.RepoName
	result.OwnerLogin = repo.OwnerLogin
	result.Period = fmt.Sprintf("%d-%d", fromDate.Year(), fromDate.Month())
	result.CalculatedAt = time.Now()

	// Calculate each metric
	deploymentFreq, err := r.GetDeploymentFrequency(ctx, repoID, fromDate, toDate)
	if err == nil && len(deploymentFreq) > 0 {
		result.DeploymentFrequency = deploymentFreq[0].DeploymentFrequency
	}

	leadTime, err := r.GetLeadTimeForChanges(ctx, repoID, fromDate, toDate)
	if err == nil && len(leadTime) > 0 {
		result.LeadTimeHours = leadTime[0].LeadTimeHours
	}

	mttr, err := r.GetMeanTimeToRestore(ctx, repoID, fromDate, toDate)
	if err == nil && len(mttr) > 0 {
		result.MTTRMinutes = mttr[0].MTTRMinutes
	}

	cfr, err := r.GetChangeFailureRate(ctx, repoID, fromDate, toDate)
	if err == nil && len(cfr) > 0 {
		result.ChangeFailureRate = cfr[0].ChangeFailureRate
	}

	return &result, nil
}
