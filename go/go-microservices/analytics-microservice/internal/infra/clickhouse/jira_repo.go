package clickhouse

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"strings"
	"time"

	"analytics-microservice/internal/domain"

	"gorm.io/gorm"
)

// JiraRepo provides data access for Jira-related models
type JiraRepo struct {
	db *gorm.DB
}

// TableManager returns a table manager for ClickHouse operations
func (r *JiraRepo) TableManager() *TableManager {
	return NewTableManager(r.db)
}

// NewJiraRepo creates a new Jira repository
func NewJiraRepo(db *gorm.DB) *JiraRepo {
	return &JiraRepo{db: db}
}

type Sprint struct {
	SprintName string    `gorm:"column:name"`
	StartDate  time.Time `gorm:"column:start_date"`
	EndDate    time.Time `gorm:"column:end_date"`
}

type Release struct {
	ReleaseName string    `gorm:"column:version"`
	StartDate   time.Time `gorm:"column:start_date"`
	EndDate     time.Time `gorm:"column:end_date"`
}

type IssueData struct {
	Key         string     `gorm:"column:key"`
	StoryPoints int64      `gorm:"column:story_points"`
	Type        string     `gorm:"column:type"`
	Summary     string     `gorm:"column:summary"`
	Parent      string     `gorm:"column:parent"`
	Priority    string     `gorm:"column:priority"`
	Assignee    string     `gorm:"column:assignee"`
	DueDate     *time.Time `gorm:"column:due_date"`
	GreenDate   *time.Time `gorm:"column:green_date"`
	BlueDate    *time.Time `gorm:"column:blue_date"`
	RedDate     *time.Time `gorm:"column:red_date"`
}

// StoreWebhookEvent stores a webhook event in GitHubRawEvents table (same schema as Jira)
func (r *JiraRepo) StoreWebhookEvent(ctx context.Context, event *domain.WebhookEvent) error {
	// Convert domain entity to JiraRawEvents structure (same as Jira pattern)
	rawEvent := r.toJiraRawEvents(event)

	// Store in github_raw_events table
	if err := r.db.WithContext(ctx).Create(rawEvent).Error; err != nil {
		return fmt.Errorf("failed to store Jira webhook event: %w", err)
	}

	return nil
}

// toJiraRawEvents converts domain entity to JiraRawEvents
func (r *JiraRepo) toJiraRawEvents(event *domain.WebhookEvent) *domain.JiraRawEvent {
	return &domain.JiraRawEvent{
		Payload:   string(event.Payload()),
		CreatedAt: event.ReceivedAt(),
	}
}

func (r *JiraRepo) GetCalculatedKPIs(ctx context.Context, queries []string) (domain.KPIAggregatedValues, error) {
	if len(queries) < 2 {
		return domain.KPIAggregatedValues{}, fmt.Errorf("GetCalculatedKPIs expects at least 2 queries (aggregate + timeseries), got %d", len(queries))
	}

	// 1) Run aggregate query
	var aggregatedValue sql.NullFloat64
	if err := r.db.WithContext(ctx).Raw(queries[0]).Scan(&aggregatedValue).Error; err != nil {
		return domain.KPIAggregatedValues{}, fmt.Errorf("executing aggregate query: %w", err)
	}
	aggVal := 0.0
	if aggregatedValue.Valid && !math.IsNaN(aggregatedValue.Float64) {
		aggVal = aggregatedValue.Float64
	}

	// 2) Run timeseries query
	rows, err := r.db.WithContext(ctx).Raw(queries[1]).Rows()
	if err != nil {
		return domain.KPIAggregatedValues{}, err
	}
	defer rows.Close()

	cols, _ := rows.Columns()
	var series []domain.HistoricalData

	for rows.Next() {

		var xs sql.NullString
		nfs := make([]sql.NullFloat64, len(cols))

		scanArgs := make([]interface{}, len(cols))
		for i, col := range cols {
			if col == "x" {
				scanArgs[i] = &xs
			} else {
				scanArgs[i] = &nfs[i]
			}
		}

		// Do the scan
		if err := rows.Scan(scanArgs...); err != nil {
			return domain.KPIAggregatedValues{}, err
		}

		// Extract the slice of values, guarding against NULL/NaN/Inf
		var values []float64
		for i, col := range cols {
			if col == "x" {
				continue
			}
			if nfs[i].Valid && !math.IsNaN(nfs[i].Float64) && !math.IsInf(nfs[i].Float64, 0) {
				values = append(values, nfs[i].Float64)
			} else {
				values = append(values, 0)
			}
		}

		series = append(series, domain.HistoricalData{
			X:      xs.String,
			Values: values,
		})
	}

	return domain.KPIAggregatedValues{
		AggregatedValue: aggVal,
		HistoricalData:  series,
	}, nil
}

func (r *JiraRepo) GetProjectSprints(ctx context.Context, projectKey string) ([]*domain.Sprint, error) {
	query := `SELECT DISTINCT sp.name AS name, dd.date AS start_date, dd2.date AS end_date
		FROM analytics.issue_fact f
		INNER JOIN analytics.sprint_dim sp ON f.sprint = sp.sprint_id
		INNER JOIN analytics.project_dim p ON f.project = p.project_key
		INNER JOIN analytics.date_dim dd ON sp.start_date = dd.id
		INNER JOIN analytics.date_dim dd2 ON sp.end_date = dd2.id
		WHERE p.project_key = ?;`

	var rows []Sprint
	if err := r.db.WithContext(ctx).Raw(query, projectKey).Scan(&rows).Error; err != nil {
		return nil, fmt.Errorf("fetching sprints for project %s: %w", projectKey, err)
	}

	var sprints []*domain.Sprint
	for _, s := range rows {
		sprints = append(sprints, toDomainSprint(s))
	}

	return sprints, nil
}

func (r *JiraRepo) GetProjectReleases(ctx context.Context, projectKey string) ([]*domain.Release, error) {
	query := `SELECT DISTINCT r.version AS version, dd.date AS end_date, dd.date as start_date
        FROM analytics.release_dim r 
        INNER JOIN analytics.project_dim p ON r.project_id = p.project_id
        INNER JOIN analytics.date_dim dd ON r.release_date = dd.id
        WHERE p.project_key = ?;`

	var rows []Release
	if err := r.db.WithContext(ctx).Raw(query, projectKey).Scan(&rows).Error; err != nil {
		return nil, fmt.Errorf("fetching releases for project %s: %w", projectKey, err)
	}

	if len(rows) == 0 {
		return []*domain.Release{}, nil
	}

	var releases []*domain.Release
	for _, rel := range rows {
		releases = append(releases, toDomainRelease(rel))
	}

	return releases, nil
}

func (r *JiraRepo) GetEpicHealthRaw(
	ctx context.Context,
	projectKey string,
	start, end time.Time,
) ([]*domain.RawEpicHealth, error) {
	const epicHealthQuery = `
WITH
    toDate(?) AS start_dt,
    toDate(?)   AS end_dt,

    toUInt64(formatDateTime(start_dt, '%Y%m%d')) AS start_int,
    toUInt64(formatDateTime(end_dt,   '%Y%m%d')) AS end_int,

    epic_base AS (
        SELECT
            id.key          AS epic_key,
            id.summary      AS summary,
            id.updated_at   AS epic_updated_at
        FROM analytics.issue_dim AS id
        WHERE id.type = 'Epic'
          AND id.key LIKE ?
          AND toDate(id.updated_at) BETWEEN start_dt AND end_dt
    ),

    initial_issue_points AS (
        SELECT
            f.key,
            d.parent                            AS epic_key,
            argMin(f.story_points, f.updated_at) AS story_points_initial
        FROM analytics.issue_fact AS f
        INNER JOIN analytics.issue_dim AS d ON d.key = f.key
        WHERE d.parent IN (SELECT epic_key FROM epic_base)
        GROUP BY f.key, d.parent
    ),
    child_created AS (
        SELECT epic_key, sum(ifNull(story_points_initial,0)) AS story_points_planned
        FROM initial_issue_points
        GROUP BY epic_key
    ),

    latest_issue_points AS (
        SELECT
            f.key,
            d.parent                             AS epic_key,
            argMax(f.story_points, f.updated_at) AS story_points_latest,
            argMax(f.status,       f.updated_at) AS status_id_latest
        FROM analytics.issue_fact AS f
        INNER JOIN analytics.issue_dim AS d ON d.key = f.key
        WHERE d.parent IN (SELECT epic_key FROM epic_base)
        GROUP BY f.key, d.parent
    ),

    status_map AS (
        SELECT status_id, any(status) AS status
        FROM analytics.status_dim
        GROUP BY status_id
    ),

    completed_issue_points AS (
        SELECT
            lip.epic_key,
            sumIf(ifNull(lip.story_points_latest,0), sm.status = 'Done') AS story_points_completed
        FROM latest_issue_points AS lip
        LEFT JOIN status_map AS sm ON lip.status_id_latest = sm.status_id
        GROUP BY lip.epic_key
    ),

    epic_status AS (
        SELECT
            s.key   AS epic_key,
            sm.status AS status_name
        FROM (
            SELECT key, argMax(status, updated_at) AS status_id_latest
            FROM analytics.issue_fact
            WHERE key IN (SELECT epic_key FROM epic_base)
            GROUP BY key
        ) AS s
        LEFT JOIN status_map AS sm ON s.status_id_latest = sm.status_id
    ),

    epic_assignee AS (
        SELECT
            f.key  AS epic_key,
            anyLast(ud.name) AS assignee_name
        FROM analytics.issue_fact AS f
        LEFT JOIN analytics.user_dim AS ud ON ud.user_id = f.assignee
        WHERE f.key IN (SELECT epic_key FROM epic_base)
        GROUP BY f.key
    ),

    epic_dates AS (
        SELECT
            f.key AS epic_key,
            argMin(f.created_at, f.updated_at) AS created_id,
            argMax(f.due_date,   f.updated_at) AS due_id
        FROM analytics.issue_fact AS f
        WHERE f.key IN (SELECT epic_key FROM epic_base)
        GROUP BY f.key
    ),
    days_calc AS (
        SELECT
            ed.epic_key,
            ifNull(dateDiff('day', toDate(toString(ed.created_id)), today()),0) AS days_elapsed,
            ifNull(dateDiff('day', toDate(toString(ed.created_id)), toDate(toString(ed.due_id))),0) AS planned_days
        FROM epic_dates AS ed
    )

SELECT
    eb.summary                                   AS epic,
    ea.assignee_name                             AS owner,
    ifNull(cc.story_points_planned,   0)         AS story_points,
    ifNull(cp.story_points_completed, 0)         AS story_points_completed,
    round(ifNull(cp.story_points_completed,0) / nullIf(ifNull(cc.story_points_planned,0),0) * 100,2) AS pct_story_points_completed,
    IF(es.status_name = 'Done', 100,
       least(100, round(days.days_elapsed / nullIf(days.planned_days,0) * 100,2))) AS pct_days_elapsed
FROM epic_base AS eb
LEFT JOIN child_created          AS cc ON eb.epic_key = cc.epic_key
LEFT JOIN completed_issue_points AS cp ON eb.epic_key = cp.epic_key
LEFT JOIN epic_assignee          AS ea ON eb.epic_key = ea.epic_key
LEFT JOIN days_calc              AS days ON eb.epic_key = days.epic_key
LEFT JOIN epic_status            AS es   ON eb.epic_key = es.epic_key
ORDER BY pct_story_points_completed DESC;
`

	rows, err := r.db.WithContext(ctx).
		Raw(epicHealthQuery,
			start.Format("2006-01-02"),
			end.Format("2006-01-02"),
			projectKey+"-%",
		).Rows()
	if err != nil {
		return nil, fmt.Errorf("executing epic health query: %w", err)
	}
	defer rows.Close()

	var out []*domain.RawEpicHealth
	for rows.Next() {
		var (
			epicN   sql.NullString
			ownerN  sql.NullString
			totalN  sql.NullInt64
			doneN   sql.NullInt64
			pctSP_N sql.NullFloat64
			pctD_N  sql.NullFloat64
		)
		if err := rows.Scan(
			&epicN,
			&ownerN,
			&totalN,
			&doneN,
			&pctSP_N,
			&pctD_N,
		); err != nil {
			// optionally log & continue
			continue
		}

		epic := epicN.String
		owner := ownerN.String
		if owner == "" {
			owner = "Unassigned"
		}
		total := int(totalN.Int64)
		done := int(doneN.Int64)
		pctSP := pctSP_N.Float64
		pctD := pctD_N.Float64

		// daysLate is no longer meaningful—always zero
		raw, err := domain.NewRawEpicHealth(epic, owner, done, total, pctSP, pctD, 0)
		if err != nil {
			continue
		}
		out = append(out, raw)
	}
	return out, nil
}

func (r *JiraRepo) GetProjectIssues(ctx context.Context, projectIds []string, startDate, endDate time.Time, issueType string) ([]*domain.Issue, error) {
	if len(projectIds) == 0 {
		return []*domain.Issue{}, nil
	}

	var sb strings.Builder
	args := []interface{}{projectIds}

	sb.WriteString(`
SELECT DISTINCT
    f.key                                   AS key,
    ifNull(f.story_points, 0)               AS story_points,
    i.type                                  AS type,
    i.summary                               AS summary,
    f.parent                                AS parent,
    f.priority                              AS priority,
    u.name                                  AS assignee,
    dd_due.date                             AS due_date,
    dd_green.date                           AS green_date,
    dd_blue.date                            AS blue_date,
    dd_red.date                             AS red_date
FROM analytics.issue_fact        AS f
INNER JOIN analytics.issue_dim   AS i   ON f.key     = i.key
INNER JOIN analytics.project_dim AS p  ON f.project = p.project_key
LEFT  JOIN analytics.user_dim    AS u   ON f.assignee = u.user_id
LEFT JOIN analytics.date_dim AS dd_due
       ON CAST(f.due_date   AS Nullable(UInt32)) = dd_due.id
LEFT JOIN analytics.date_dim AS dd_green
       ON CAST(f.green_date AS Nullable(UInt32)) = dd_green.id
LEFT JOIN analytics.date_dim AS dd_blue
       ON CAST(f.blue_date  AS Nullable(UInt32)) = dd_blue.id
LEFT JOIN analytics.date_dim AS dd_red
       ON CAST(f.red_date   AS Nullable(UInt32)) = dd_red.id
WHERE project_dim.project_key IN (?)`)

	// Dynamic date filters (if provided)
	// if !startDate.IsZero() {
	// 	sb.WriteString(" AND f.updated_at >= ?")
	// 	args = append(args, startDate)
	// }
	// if !endDate.IsZero() {
	// 	sb.WriteString(" AND f.updated_at < ?")
	// 	args = append(args, endDate)
	// }

	// Dynamic issue type filter
	if strings.TrimSpace(issueType) != "" {
		sb.WriteString(" AND i.type = ?")
		args = append(args, strings.TrimSpace(issueType))
	}

	sb.WriteString(" ORDER BY f.key LIMIT 100")

	var rows []IssueData
	if err := r.db.WithContext(ctx).Raw(sb.String(), args...).Scan(&rows).Error; err != nil {
		return nil, fmt.Errorf("query project issues: %w", err)
	}

	issues := make([]*domain.Issue, 0, len(rows))
	for _, rec := range rows {
		issues = append(issues, r.toDomainIssue(rec))
	}

	return issues, nil
}

// Convert simplified data to domain
func (r *JiraRepo) toDomainIssue(data IssueData) *domain.Issue {
	return domain.HydrateIssue(
		data.Key,
		data.StoryPoints,
		data.Type,
		data.Summary,
		data.Parent,
		data.Priority,
		data.Assignee,
		data.DueDate,
		data.GreenDate,
		data.BlueDate,
		data.RedDate,
	)
}

func toDomainSprint(sprint Sprint) *domain.Sprint {
	return domain.HydrateSprint(sprint.SprintName, sprint.StartDate, sprint.EndDate)
}

func toDomainRelease(release Release) *domain.Release {
	return domain.HydrateRelease(release.ReleaseName, release.StartDate, release.EndDate)
}

func toDomainRawEpicHealth(
	epicName string,
	owner string,
	done64, total64 int64,
	pctComplete, pctElapsed float64,
	daysLate64 int64,
) *domain.RawEpicHealth {
	return domain.HydrateRawEpicHealth(
		epicName,
		owner,
		int(done64),
		int(total64),
		pctComplete,
		pctElapsed,
		int(daysLate64),
	)
}

// =============================================================
//  TABLE MANAGEMENT OPERATIONS
// =============================================================

// TableManager provides operations for managing ClickHouse tables
type TableManager struct {
	db *gorm.DB
}

// NewTableManager creates a new table manager
func NewTableManager(db *gorm.DB) *TableManager {
	return &TableManager{db: db}
}

// DropTables drops specified tables from the analytics database
// If tableNames is empty or nil, drops all tables in the analytics database
func (tm *TableManager) DropTables(ctx context.Context, tableNames []string) error {
	var tablesToDrop []string

	if len(tableNames) == 0 {
		// Get all table names in the analytics database
		var allTables []string
		query := "SELECT name FROM system.tables WHERE database = 'analytics'"

		if err := tm.db.WithContext(ctx).Raw(query).Scan(&allTables).Error; err != nil {
			return fmt.Errorf("failed to get table names: %w", err)
		}

		if len(allTables) == 0 {
			return nil // No tables to drop
		}

		tablesToDrop = allTables
	} else {
		// Use provided table names
		tablesToDrop = tableNames
	}

	// Validate all table names to prevent SQL injection
	for _, tableName := range tablesToDrop {
		if !isValidTableName(tableName) {
			return fmt.Errorf("invalid table name: %s", tableName)
		}
	}

	// Drop each table
	for _, tableName := range tablesToDrop {
		// Double-check we're only operating on analytics database
		query := "DROP TABLE IF EXISTS analytics.`" + tableName + "`"

		if err := tm.db.WithContext(ctx).Exec(query).Error; err != nil {
			return fmt.Errorf("failed to drop table %s: %w", tableName, err)
		}
	}

	return nil
}

// ListTables returns all tables in the analytics database
func (tm *TableManager) ListTables(ctx context.Context) ([]string, error) {
	var tables []string
	query := "SELECT name FROM system.tables WHERE database = 'analytics' ORDER BY name"

	if err := tm.db.WithContext(ctx).Raw(query).Scan(&tables).Error; err != nil {
		return nil, fmt.Errorf("failed to list tables: %w", err)
	}

	return tables, nil
}

// TableExists checks if a table exists in the analytics database
func (tm *TableManager) TableExists(ctx context.Context, tableName string) (bool, error) {
	if !isValidTableName(tableName) {
		return false, fmt.Errorf("invalid table name: %s", tableName)
	}

	var count int64
	query := "SELECT count() FROM system.tables WHERE database = 'analytics' AND name = ?"

	if err := tm.db.WithContext(ctx).Raw(query, tableName).Scan(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check table existence: %w", err)
	}

	return count > 0, nil
}

// GetTableInfo returns information about a specific table
func (tm *TableManager) GetTableInfo(ctx context.Context, tableName string) (map[string]interface{}, error) {
	if !isValidTableName(tableName) {
		return nil, fmt.Errorf("invalid table name: %s", tableName)
	}

	var result map[string]interface{}
	query := `
		SELECT 
			name,
			engine,
			total_rows,
			total_bytes,
			metadata_modification_time
		FROM system.tables 
		WHERE database = 'analytics' AND name = ?
	`

	if err := tm.db.WithContext(ctx).Raw(query, tableName).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("failed to get table info: %w", err)
	}

	return result, nil
}

// isValidTableName validates table names to prevent SQL injection
func isValidTableName(name string) bool {
	// Only allow alphanumeric characters, underscores, and hyphens
	// This prevents SQL injection while allowing valid ClickHouse table names
	for _, char := range name {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return false
		}
	}
	return len(name) > 0 && len(name) <= 255
}
