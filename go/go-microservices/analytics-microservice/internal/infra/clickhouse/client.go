package clickhouse

import (
	"database/sql"
	"fmt"

	_ "github.com/ClickHouse/clickhouse-go/v2"
	"gorm.io/driver/clickhouse"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// Client represents a ClickHouse client
type Client struct {
	db *gorm.DB
}

// NewClient creates a new ClickHouse client
func NewClient(dsn string) (*Client, error) {
	gormDB, err := gorm.Open(clickhouse.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse via GORM: %w", err)
	}

	var one int
	if err := gormDB.Raw("SELECT 1").Scan(&one).Error; err != nil {
		return nil, fmt.Errorf("failed to ping ClickHouse: %w", err)
	}

	return &Client{db: gormDB}, nil
}

// AutoMigrate runs GORM migrations first to create base tables, then runs SQL migrations
func (c *Client) AutoMigrate(models ...interface{}) error {

	// SQL migrations
	if err := c.RunMigrations(); err != nil {
		return fmt.Errorf("failed to run SQL migrations: %w", err)
	}

	return nil
}

// Close closes the ClickHouse connection
func (c *Client) Close() error {
	sqlDB, err := c.db.DB()
	if err != nil {
		return fmt.Errorf("failed to obtain underlying sql.DB: %w", err)
	}
	return sqlDB.Close()
}

// DB returns the underlying *sql.DB for compatibility with code that expects the low-level database/sql driver.
func (c *Client) DB() *sql.DB {
	sqlDB, _ := c.db.DB()
	return sqlDB
}

// Gorm returns the underlying *gorm.DB instance.
func (c *Client) Gorm() *gorm.DB {
	return c.db
}

// PopulateDateDimension populates the date_dim table with dates for 2025
func (c *Client) PopulateDateDimension() error {
	fmt.Println("ClickHouse Client: Populating date dimension table...")

	// SQL query to populate date_dim table
	query := `
		INSERT INTO analytics.date_dim (id, date)
		SELECT
			toYYYYMMDD(toDate('2025-01-01') + number) AS id,
			toDate(toDate('2025-01-01') + number) AS date
		FROM system.numbers
		LIMIT 364;
	`

	// Execute the query
	result := c.db.Exec(query)
	if result.Error != nil {
		return fmt.Errorf("failed to populate date dimension: %w", result.Error)
	}

	fmt.Printf("ClickHouse Client: Successfully populated date dimension table with %d rows\n", result.RowsAffected)
	return nil
}

// GetTableNames retrieves all table names from the analytics database
func (c *Client) GetTableNames() ([]string, error) {
	var tables []string
	query := "SELECT name FROM system.tables WHERE database = 'analytics' ORDER BY name"

	rows, err := c.db.Raw(query).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to query table names: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return nil, fmt.Errorf("failed to scan table name: %w", err)
		}
		tables = append(tables, tableName)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating table names: %w", err)
	}

	return tables, nil
}

// DropAllTables drops all tables in the analytics database
func (c *Client) DropAllTables() error {
	fmt.Println("ClickHouse Client: Dropping all tables in analytics database...")

	// Get all table names first
	tables, err := c.GetTableNames()
	if err != nil {
		return fmt.Errorf("failed to get table names: %w", err)
	}

	if len(tables) == 0 {
		fmt.Println("ClickHouse Client: No tables found to drop")
		return nil
	}

	// Drop each table
	for _, table := range tables {
		fmt.Printf("ClickHouse Client: Dropping table: %s\n", table)
		dropQuery := fmt.Sprintf("DROP TABLE IF EXISTS analytics.%s", table)
		if err := c.db.Exec(dropQuery).Error; err != nil {
			return fmt.Errorf("failed to drop table %s: %w", table, err)
		}
	}

	fmt.Printf("ClickHouse Client: Successfully dropped %d tables\n", len(tables))
	return nil
}
