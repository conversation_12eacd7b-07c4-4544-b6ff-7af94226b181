package clickhouse

import (
	"analytics-microservice/internal/domain"
	"context"
	"fmt"
	"time"

	"log/slog"

	"gorm.io/gorm"
)

// SeedJobRecord represents the database record for seed jobs
type SeedJobRecord struct {
	JobID         string     `gorm:"column:job_id;primaryKey"`
	Platform      string     `gorm:"column:platform"`
	Category      string     `gorm:"column:category"`
	Endpoint      string     `gorm:"column:endpoint"`
	Project       string     `gorm:"column:project"`
	Status        string     `gorm:"column:status"`
	JiraBaseURL   *string    `gorm:"column:jira_base_url"`
	JiraEmail     *string    `gorm:"column:jira_email"`
	JiraToken     *string    `gorm:"column:jira_token"`
	GitHubBaseURL *string    `gorm:"column:github_base_url"`
	GitHubToken   *string    `gorm:"column:github_token"`
	StartedAt     time.Time  `gorm:"column:started_at"`
	UpdatedAt     time.Time  `gorm:"column:updated_at"`
	ErrorMessage  *string    `gorm:"column:error_message"`
	EarliestDate  *time.Time `gorm:"column:earliest_date"`
}

func (SeedJobRecord) TableName() string {
	return "analytics.seed_jobs"
}

// SeedJobRepository implements the SeedJobRepository interface using ClickHouse
type SeedJobRepository struct {
	db     *gorm.DB
	logger *slog.Logger
}

// NewSeedJobRepository creates a new seed job repository
func NewSeedJobRepository(db *gorm.DB, logger *slog.Logger) *SeedJobRepository {
	return &SeedJobRepository{
		db:     db,
		logger: logger.WithGroup("SeedJobRepository"),
	}
}

// Store saves a seed job to the database
func (r *SeedJobRepository) Store(ctx context.Context, job *domain.SeedJob) error {
	r.logger.Info("Storing seed job", "job_id", job.ID())

	record := r.domainToRecord(job)

	// Use INSERT with ON CLUSTER for shard support
	result := r.db.WithContext(ctx).Create(&record)
	if result.Error != nil {
		r.logger.Error("Failed to store seed job", "job_id", job.ID(), "error", result.Error)
		return fmt.Errorf("failed to store seed job: %w", result.Error)
	}

	r.logger.Info("Successfully stored seed job", "job_id", job.ID())
	return nil
}

// GetByID retrieves a seed job by its ID
func (r *SeedJobRepository) GetByID(ctx context.Context, jobID string) (*domain.SeedJob, error) {
	r.logger.Info("Getting seed job by ID", "job_id", jobID)

	var record SeedJobRecord
	result := r.db.WithContext(ctx).Where("job_id = ?", jobID).First(&record)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			r.logger.Warn("Seed job not found", "job_id", jobID)
			return nil, fmt.Errorf("seed job not found: %s", jobID)
		}
		r.logger.Error("Failed to get seed job", "job_id", jobID, "error", result.Error)
		return nil, fmt.Errorf("failed to get seed job: %w", result.Error)
	}

	job := r.recordToDomain(&record)
	r.logger.Info("Successfully retrieved seed job", "job_id", jobID)
	return job, nil
}

// Update updates an existing seed job
func (r *SeedJobRepository) Update(ctx context.Context, job *domain.SeedJob) error {
	r.logger.Info("Updating seed job", "job_id", job.ID())

	record := r.domainToRecord(job)

	// Use UPDATE with WHERE clause for shard support
	result := r.db.WithContext(ctx).Where("job_id = ?", job.ID()).Updates(&record)
	if result.Error != nil {
		r.logger.Error("Failed to update seed job", "job_id", job.ID(), "error", result.Error)
		return fmt.Errorf("failed to update seed job: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		r.logger.Warn("No rows affected when updating seed job", "job_id", job.ID())
		return fmt.Errorf("seed job not found for update: %s", job.ID())
	}

	r.logger.Info("Successfully updated seed job", "job_id", job.ID())
	return nil
}

// ListActive retrieves all active (pending or running) seed jobs
func (r *SeedJobRepository) ListActive(ctx context.Context) ([]*domain.SeedJob, error) {
	r.logger.Info("Getting active seed jobs")

	var records []SeedJobRecord
	result := r.db.WithContext(ctx).
		Where("status IN (?, ?)", string(domain.SeedJobStatusPending), string(domain.SeedJobStatusRunning)).
		Order("started_at DESC").
		Find(&records)

	if result.Error != nil {
		r.logger.Error("Failed to get active seed jobs", "error", result.Error)
		return nil, fmt.Errorf("failed to get active seed jobs: %w", result.Error)
	}

	jobs := make([]*domain.SeedJob, len(records))
	for i, record := range records {
		jobs[i] = r.recordToDomain(&record)
	}

	r.logger.Info("Successfully retrieved active seed jobs", "count", len(jobs))
	return jobs, nil
}

// GetJobs retrieves all seed jobs
func (r *SeedJobRepository) GetJobs(ctx context.Context) ([]*domain.SeedJob, error) {
	r.logger.Info("Getting all seed jobs")

	var records []SeedJobRecord
	result := r.db.WithContext(ctx).
		Order("started_at DESC").
		Find(&records)

	if result.Error != nil {
		r.logger.Error("Failed to get all seed jobs", "error", result.Error)
		return nil, fmt.Errorf("failed to get all seed jobs: %w", result.Error)
	}

	jobs := make([]*domain.SeedJob, len(records))
	for i, record := range records {
		jobs[i] = r.recordToDomain(&record)
	}

	r.logger.Info("Successfully retrieved all seed jobs", "count", len(jobs))
	return jobs, nil
}

// Helper methods for domain/record conversion
func (r *SeedJobRepository) domainToRecord(job *domain.SeedJob) SeedJobRecord {
	auth := job.Auth()

	record := SeedJobRecord{
		JobID:     job.ID(),
		Platform:  string(job.Platform()),
		Category:  job.Category(),
		Endpoint:  job.Endpoint(),
		Project:   job.Project(),
		Status:    string(job.Status()),
		StartedAt: job.StartedAt(),
		UpdatedAt: job.UpdatedAt(),
	}

	// Handle authentication fields
	if auth.JiraBaseURL != "" {
		record.JiraBaseURL = &auth.JiraBaseURL
	}
	if auth.JiraEmail != "" {
		record.JiraEmail = &auth.JiraEmail
	}
	if auth.JiraToken != "" {
		record.JiraToken = &auth.JiraToken
	}
	if auth.GitHubBaseURL != "" {
		record.GitHubBaseURL = &auth.GitHubBaseURL
	}
	if auth.GitHubToken != "" {
		record.GitHubToken = &auth.GitHubToken
	}

	// Handle error message
	if job.Error() != "" {
		errorMsg := job.Error()
		record.ErrorMessage = &errorMsg
	}

	// Handle earliest date
	if job.EarliestDate() != nil {
		record.EarliestDate = job.EarliestDate()
	}

	return record
}

func (r *SeedJobRepository) recordToDomain(record *SeedJobRecord) *domain.SeedJob {
	// Build auth object
	auth := domain.SeedJobAuth{}
	if record.JiraBaseURL != nil {
		auth.JiraBaseURL = *record.JiraBaseURL
	}
	if record.JiraEmail != nil {
		auth.JiraEmail = *record.JiraEmail
	}
	if record.JiraToken != nil {
		auth.JiraToken = *record.JiraToken
	}
	if record.GitHubBaseURL != nil {
		auth.GitHubBaseURL = *record.GitHubBaseURL
	}
	if record.GitHubToken != nil {
		auth.GitHubToken = *record.GitHubToken
	}

	// Handle error message
	errorMsg := ""
	if record.ErrorMessage != nil {
		errorMsg = *record.ErrorMessage
	}

	// Convert platform and status
	platform := domain.SeedJobPlatform(record.Platform)
	status := domain.SeedJobStatus(record.Status)

	return domain.HydrateSeedJob(
		record.JobID,
		record.Category,
		record.Endpoint,
		record.Project,
		platform,
		auth,
		status,
		record.StartedAt,
		record.UpdatedAt,
		errorMsg,
		record.EarliestDate,
	)
}
