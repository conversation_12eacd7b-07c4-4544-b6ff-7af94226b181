package seeding

import (
	"analytics-microservice/internal/app"
	"analytics-microservice/internal/domain"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"log/slog"
)

// GenericSeeder handles platform-agnostic seeding in the infrastructure layer
type GenericSeeder struct {
	jiraRepo   app.JiraRepository
	githubRepo app.GithubRepository
	logger     *slog.Logger
}

// NewGenericSeeder creates a new generic seeder following established patterns
func NewGenericSeeder(jiraRepo app.JiraRepository, githubRepo app.GithubRepository, logger *slog.Logger) *GenericSeeder {
	return &GenericSeeder{
		jiraRepo:   jiraRepo,
		githubRepo: githubRepo,
		logger:     logger.WithGroup("GenericSeeder"),
	}
}

// Seed performs generic seeding with pagination support
func (gs *GenericSeeder) Seed(ctx context.Context, job *domain.SeedJob, endpoint, project string) error {
	gs.logger.Info("Starting generic seed operation",
		"platform", job.Platform(),
		"endpoint", endpoint,
		"project", project,
		"earliest_date", job.EarliestDate())

	// Perform paginated fetching
	allData, err := gs.fetchAllPages(ctx, job, endpoint, project)
	if err != nil {
		return fmt.Errorf("failed to fetch all pages: %w", err)
	}

	gs.logger.Info("All pages fetched successfully", "total_size", len(allData))

	// Store the accumulated data using existing repository methods
	if err := gs.storeRawData(ctx, job, allData); err != nil {
		return fmt.Errorf("failed to store raw data: %w", err)
	}

	gs.logger.Info("Raw data stored successfully")
	return nil
}

// buildAPIURL constructs the full API URL based on platform and endpoint
func (gs *GenericSeeder) buildAPIURL(job *domain.SeedJob, endpoint, project string) (string, error) {
	auth := job.Auth()

	switch job.Platform() {
	case domain.SeedJobPlatformJira:
		if auth.JiraBaseURL == "" {
			return "", fmt.Errorf("Jira base URL is required")
		}

		// Handle different Jira endpoint patterns
		if strings.Contains(endpoint, "{project}") {
			// Replace {project} placeholder with actual project
			endpoint = strings.ReplaceAll(endpoint, "{project}", project)
		}

		// Ensure endpoint starts with /
		if !strings.HasPrefix(endpoint, "/") {
			endpoint = "/" + endpoint
		}

		return auth.JiraBaseURL + endpoint, nil

	case domain.SeedJobPlatformGitHub:
		baseURL := auth.GitHubBaseURL
		if baseURL == "" {
			baseURL = "https://api.github.com"
		}

		// Handle different GitHub endpoint patterns
		if strings.Contains(endpoint, "{repo}") {
			// Replace {repo} placeholder with actual repo
			endpoint = strings.ReplaceAll(endpoint, "{repo}", project)
		} else if !strings.Contains(endpoint, project) {
			// If endpoint doesn't contain repo, prepend it
			// e.g., "/issues" becomes "/repos/owner/repo/issues"
			if !strings.HasPrefix(endpoint, "/repos/") {
				endpoint = "/repos/" + project + endpoint
			}
		}

		// Ensure endpoint starts with /
		if !strings.HasPrefix(endpoint, "/") {
			endpoint = "/" + endpoint
		}

		return baseURL + endpoint, nil

	default:
		return "", fmt.Errorf("unsupported platform: %s", job.Platform())
	}
}

// makeAPIRequest performs the HTTP request with appropriate authentication
func (gs *GenericSeeder) makeAPIRequest(ctx context.Context, job *domain.SeedJob, fullURL string) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authentication headers based on platform
	auth := job.Auth()
	switch job.Platform() {
	case domain.SeedJobPlatformJira:
		req.SetBasicAuth(auth.JiraEmail, auth.JiraToken)
		req.Header.Set("Accept", "application/json")

	case domain.SeedJobPlatformGitHub:
		req.Header.Set("Authorization", "Bearer "+auth.GitHubToken)
		req.Header.Set("Accept", "application/vnd.github+json")
		req.Header.Set("X-GitHub-Api-Version", "2022-11-28")
	}

	// Make the request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Check for HTTP errors
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	// Read response body
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return responseData, nil
}

// storeRawData stores the API response using existing repository methods
func (gs *GenericSeeder) storeRawData(ctx context.Context, job *domain.SeedJob, responseData []byte) error {
	gs.logger.Info("Storing raw data", "platform", job.Platform(), "data_size", len(responseData))

	// Create webhook event domain entity
	// Generate a unique delivery ID for this seeding operation
	deliveryID := fmt.Sprintf("seed-%s-%d", job.ID(), time.Now().Unix())
	source := string(job.Platform())

	webhookEvent, err := domain.NewWebhookEvent(deliveryID, source, responseData, time.Now())
	if err != nil {
		return fmt.Errorf("failed to create webhook event: %w", err)
	}

	// Use existing repository methods based on platform
	switch job.Platform() {
	case domain.SeedJobPlatformJira:
		if err := gs.jiraRepo.StoreWebhookEvent(ctx, webhookEvent); err != nil {
			return fmt.Errorf("failed to store Jira raw data: %w", err)
		}
	case domain.SeedJobPlatformGitHub:
		if err := gs.githubRepo.StoreWebhookEvent(ctx, webhookEvent); err != nil {
			return fmt.Errorf("failed to store GitHub raw data: %w", err)
		}
	default:
		return fmt.Errorf("unsupported platform for raw data storage: %s", job.Platform())
	}

	return nil
}

// fetchAllPages handles pagination and fetches all pages until completion or earliest date
func (gs *GenericSeeder) fetchAllPages(ctx context.Context, job *domain.SeedJob, endpoint, project string) ([]byte, error) {
	var allResults []interface{}
	page := 1
	startAt := 0
	maxRetries := 3

	for {
		gs.logger.Info("Fetching page", "page", page, "start_at", startAt)

		// Build URL with pagination parameters
		pageURL, err := gs.buildPaginatedURL(job, endpoint, project, page, startAt)
		if err != nil {
			return nil, fmt.Errorf("failed to build paginated URL: %w", err)
		}

		// Make API request with retries
		var responseData []byte
		for retry := 0; retry < maxRetries; retry++ {
			responseData, err = gs.makeAPIRequest(ctx, job, pageURL)
			if err == nil {
				break
			}
			if retry < maxRetries-1 {
				gs.logger.Warn("API request failed, retrying", "retry", retry+1, "error", err)
				time.Sleep(time.Duration(retry+1) * time.Second)
			}
		}
		if err != nil {
			return nil, fmt.Errorf("API request failed after %d retries: %w", maxRetries, err)
		}

		// Parse response to extract items and pagination info
		pageResults, hasMore, reachedEarliestDate, err := gs.parsePageResponse(responseData, job)
		if err != nil {
			return nil, fmt.Errorf("failed to parse page response: %w", err)
		}

		// Add results to accumulator
		allResults = append(allResults, pageResults...)

		gs.logger.Info("Page processed",
			"page", page,
			"items_count", len(pageResults),
			"total_accumulated", len(allResults),
			"has_more", hasMore,
			"reached_earliest_date", reachedEarliestDate)

		// Check stopping conditions
		if !hasMore || reachedEarliestDate || len(pageResults) == 0 {
			break
		}

		// Prepare for next page
		page++
		startAt += len(pageResults)

		// Rate limiting - be respectful to APIs
		time.Sleep(100 * time.Millisecond)
	}

	// Wrap all results in a container object
	finalResult := map[string]interface{}{
		"items":      allResults,
		"total":      len(allResults),
		"fetched_at": time.Now().Format(time.RFC3339),
		"platform":   job.Platform(),
		"endpoint":   endpoint,
		"project":    project,
	}

	// Convert to JSON
	resultBytes, err := json.Marshal(finalResult)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal final results: %w", err)
	}

	return resultBytes, nil
}

// buildPaginatedURL builds URL with pagination parameters
func (gs *GenericSeeder) buildPaginatedURL(job *domain.SeedJob, endpoint, project string, page, startAt int) (string, error) {
	// First build the base URL
	baseURL, err := gs.buildAPIURL(job, endpoint, project)
	if err != nil {
		return "", err
	}

	// Parse URL to add pagination parameters
	parsedURL, err := url.Parse(baseURL)
	if err != nil {
		return "", fmt.Errorf("failed to parse base URL: %w", err)
	}

	// Add pagination parameters based on platform
	query := parsedURL.Query()

	switch job.Platform() {
	case domain.SeedJobPlatformJira:
		// Jira uses startAt and maxResults
		query.Set("startAt", strconv.Itoa(startAt))
		query.Set("maxResults", "100") // Reasonable page size

		// Add earliest date filter if provided
		if job.EarliestDate() != nil {
			// For Jira issues, we can use created or updated date filters
			earliestStr := job.EarliestDate().Format("2006-01-02")
			if strings.Contains(endpoint, "search") {
				// For search endpoint, add to JQL
				existingJQL := query.Get("jql")
				if existingJQL != "" {
					query.Set("jql", existingJQL+" AND created >= '"+earliestStr+"'")
				} else {
					query.Set("jql", "created >= '"+earliestStr+"'")
				}
			}
		}

	case domain.SeedJobPlatformGitHub:
		// GitHub uses page and per_page
		query.Set("page", strconv.Itoa(page))
		query.Set("per_page", "100") // Reasonable page size

		// Add earliest date filter if provided
		if job.EarliestDate() != nil {
			// For GitHub, use since parameter where supported
			query.Set("since", job.EarliestDate().Format(time.RFC3339))
		}
	}

	parsedURL.RawQuery = query.Encode()
	return parsedURL.String(), nil
}

// parsePageResponse parses API response to extract items and pagination info
func (gs *GenericSeeder) parsePageResponse(responseData []byte, job *domain.SeedJob) ([]interface{}, bool, bool, error) {
	var items []interface{}
	var hasMore bool
	var reachedEarliestDate bool

	switch job.Platform() {
	case domain.SeedJobPlatformJira:
		// Parse as object for Jira
		var response map[string]interface{}
		if err := json.Unmarshal(responseData, &response); err != nil {
			return nil, false, false, fmt.Errorf("failed to parse Jira JSON response: %w", err)
		}

		// Jira response structure: {"issues": [...], "startAt": 0, "maxResults": 50, "total": 100}
		if issuesRaw, ok := response["issues"]; ok {
			if issuesArray, ok := issuesRaw.([]interface{}); ok {
				items = issuesArray
			}
		}

		// Check pagination
		if total, ok := response["total"].(float64); ok {
			if startAt, ok := response["startAt"].(float64); ok {
				if maxResults, ok := response["maxResults"].(float64); ok {
					hasMore = (startAt + maxResults) < total
				}
			}
		}

	case domain.SeedJobPlatformGitHub:
		// Try parsing as array first (most GitHub endpoints return arrays)
		var responseArray []interface{}
		if err := json.Unmarshal(responseData, &responseArray); err == nil {
			// Direct array response (like /repos/owner/repo/issues)
			items = responseArray
			// For GitHub, if we get less than per_page items, we're done
			hasMore = len(items) >= 100 // Assuming per_page=100
		} else {
			// Try parsing as object (search API, etc.)
			var response map[string]interface{}
			if err := json.Unmarshal(responseData, &response); err != nil {
				return nil, false, false, fmt.Errorf("failed to parse GitHub JSON response: %w", err)
			}

			if itemsRaw, ok := response["items"]; ok {
				// Search API response structure
				if itemsArray, ok := itemsRaw.([]interface{}); ok {
					items = itemsArray
				}
			} else {
				// Fallback: treat entire response as single item
				items = []interface{}{response}
				hasMore = false
			}
		}
	}

	// Check if we've reached the earliest date
	if job.EarliestDate() != nil && len(items) > 0 {
		reachedEarliestDate = gs.checkEarliestDate(items, job.EarliestDate())
	}

	return items, hasMore, reachedEarliestDate, nil
}

// checkEarliestDate checks if any items are older than the earliest date
func (gs *GenericSeeder) checkEarliestDate(items []interface{}, earliestDate *time.Time) bool {
	for _, item := range items {
		if itemMap, ok := item.(map[string]interface{}); ok {
			// Look for common date fields
			dateFields := []string{"created_at", "updated_at", "created", "updated", "createdDate", "updatedDate"}

			for _, field := range dateFields {
				if dateStr, ok := itemMap[field].(string); ok {
					if itemDate, err := time.Parse(time.RFC3339, dateStr); err == nil {
						if itemDate.Before(*earliestDate) {
							return true
						}
					}
					// Try alternative date formats
					if itemDate, err := time.Parse("2006-01-02T15:04:05.000-0700", dateStr); err == nil {
						if itemDate.Before(*earliestDate) {
							return true
						}
					}
				}
			}
		}
	}
	return false
}
