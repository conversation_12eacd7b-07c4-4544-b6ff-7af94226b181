package domain

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"time"
)

// SeedJobStatus represents the current status of a seeding job
type SeedJobStatus string

const (
	SeedJobStatusPending   SeedJobStatus = "pending"
	SeedJobStatusRunning   SeedJobStatus = "running"
	SeedJobStatusCompleted SeedJobStatus = "successful"
	SeedJobStatusFailed    SeedJobStatus = "failed"
)

// SeedJobPlatform represents the source platform for seeding
type SeedJobPlatform string

const (
	SeedJobPlatformJira   SeedJobPlatform = "jira"
	SeedJobPlatformGitHub SeedJobPlatform = "github"
)

// SeedJobAuth holds authentication information for a seeding job
type SeedJobAuth struct {
	JiraBaseURL   string
	JiraEmail     string
	JiraToken     string
	GitHubBaseURL string
	GitHubToken   string
}

// SeedJob represents a seeding job in the domain
type SeedJob struct {
	id           string
	platform     SeedJobPlatform
	category     string
	endpoint     string
	project      string
	auth         SeedJobAuth
	status       SeedJobStatus
	startedAt    time.Time
	updatedAt    time.Time
	error        string
	earliestDate *time.Time // Optional earliest date for bulk fetching
}

// NewSeedJob creates a new seeding job with auto-generated ID
func NewSeedJob(
	category string,
	endpoint string,
	project string,
	platform SeedJobPlatform,
	auth SeedJobAuth,
	earliestDate *time.Time,
) (*SeedJob, error) {
	// Generate unique job ID
	id := generateJobID()
	if category == "" {
		return nil, errors.New("category cannot be empty")
	}
	if endpoint == "" {
		return nil, errors.New("endpoint cannot be empty")
	}
	if project == "" {
		return nil, errors.New("project cannot be empty")
	}
	if platform != SeedJobPlatformJira && platform != SeedJobPlatformGitHub {
		return nil, errors.New("platform must be either 'jira' or 'github'")
	}

	now := time.Now()
	return &SeedJob{
		id:           id,
		category:     category,
		endpoint:     endpoint,
		project:      project,
		platform:     platform,
		auth:         auth,
		status:       SeedJobStatusPending,
		startedAt:    now,
		updatedAt:    now,
		error:        "",
		earliestDate: earliestDate,
	}, nil
}

// HydrateSeedJob creates a seed job from existing data (for repository loading)
func HydrateSeedJob(
	id string,
	category string,
	endpoint string,
	project string,
	platform SeedJobPlatform,
	auth SeedJobAuth,
	status SeedJobStatus,
	startedAt time.Time,
	updatedAt time.Time,
	errorMsg string,
	earliestDate *time.Time,
) *SeedJob {
	return &SeedJob{
		id:           id,
		category:     category,
		endpoint:     endpoint,
		project:      project,
		platform:     platform,
		auth:         auth,
		status:       status,
		startedAt:    startedAt,
		updatedAt:    updatedAt,
		error:        errorMsg,
		earliestDate: earliestDate,
	}
}

// Getters
func (j *SeedJob) ID() string                { return j.id }
func (j *SeedJob) Category() string          { return j.category }
func (j *SeedJob) Endpoint() string          { return j.endpoint }
func (j *SeedJob) Project() string           { return j.project }
func (j *SeedJob) Platform() SeedJobPlatform { return j.platform }
func (j *SeedJob) Auth() SeedJobAuth         { return j.auth }
func (j *SeedJob) Status() SeedJobStatus     { return j.status }
func (j *SeedJob) StartedAt() time.Time      { return j.startedAt }
func (j *SeedJob) UpdatedAt() time.Time      { return j.updatedAt }
func (j *SeedJob) Error() string             { return j.error }
func (j *SeedJob) EarliestDate() *time.Time  { return j.earliestDate }

// Business methods
func (j *SeedJob) Start() error {
	if j.status != SeedJobStatusPending {
		return errors.New("job can only be started from pending status")
	}
	j.status = SeedJobStatusRunning
	j.updatedAt = time.Now()
	return nil
}

func (j *SeedJob) Complete() error {
	if j.status != SeedJobStatusRunning {
		return errors.New("job can only be completed from running status")
	}
	j.status = SeedJobStatusCompleted
	j.updatedAt = time.Now()
	j.error = ""
	return nil
}

func (j *SeedJob) Fail(errorMsg string) error {
	if j.status != SeedJobStatusRunning {
		return errors.New("job can only be failed from running status")
	}
	j.status = SeedJobStatusFailed
	j.updatedAt = time.Now()
	j.error = errorMsg
	return nil
}

func (j *SeedJob) IsActive() bool {
	return j.status == SeedJobStatusPending || j.status == SeedJobStatusRunning
}

func (j *SeedJob) IsCompleted() bool {
	return j.status == SeedJobStatusCompleted || j.status == SeedJobStatusFailed
}

// generateJobID creates a unique job identifier
func generateJobID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
