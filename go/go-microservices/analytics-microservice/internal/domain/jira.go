package domain

import (
	"errors"
	"time"
)

type Sprint struct {
	sprintName string
	startDate  time.Time
	endDate    time.Time
}

type Release struct {
	releaseName string
	startDate   time.Time
	releaseDate time.Time
}

type Issue struct {
	key         string
	storyPoints int64
	issueType   string
	summary     string
	parent      string
	priority    string
	assignee    string
	dueDate     *time.Time
	greenDate   *time.Time
	blueDate    *time.Time
	redDate     *time.Time
}

func NewSprint(
	sprintName string,
	startDate time.Time,
	endDate time.Time,
) (*Sprint, error) {
	if sprintName == "" {
		return nil, errors.New("sprintName cannot be empty")
	}
	if startDate.IsZero() {
		return nil, errors.New("startDate cannot be zero")
	}
	if endDate.IsZero() {
		return nil, errors.New("endDate cannot be zero")
	}

	return &Sprint{
		sprintName: sprintName,
		startDate:  startDate,
		endDate:    endDate,
	}, nil
}

func HydrateSprint(
	sprintName string,
	startDate time.Time,
	endDate time.Time,
) *Sprint {
	return &Sprint{
		sprintName: sprintName,
		startDate:  startDate,
		endDate:    endDate,
	}
}

func NewRelease(
	releaseName string,
	releaseDate time.Time,
	startDate time.Time,
) (*Release, error) {
	if releaseName == "" {
		return nil, errors.New("releaseName cannot be empty")
	}
	if releaseDate.IsZero() {
		return nil, errors.New("releaseDate cannot be zero")
	}
	if startDate.IsZero() {
		return nil, errors.New("startDate cannot be zero")
	}

	return &Release{
		releaseName: releaseName,
		releaseDate: releaseDate,
		startDate:   startDate,
	}, nil
}

func HydrateRelease(
	releaseName string,
	releaseDate time.Time,
	startDate time.Time,
) *Release {
	return &Release{
		releaseName: releaseName,
		releaseDate: releaseDate,
		startDate:   startDate,
	}
}

func NewIssue(
	key string,
	storyPoints int64,
	issueType string,
	summary string,
	parent string,
	priority string,
	assignee string,
	dueDate *time.Time,
	greenDate *time.Time,
	blueDate *time.Time,
	redDate *time.Time,
) *Issue {
	return &Issue{
		key:         key,
		storyPoints: storyPoints,
		issueType:   issueType,
		summary:     summary,
		parent:      parent,
		priority:    priority,
		assignee:    assignee,
		dueDate:     dueDate,
		greenDate:   greenDate,
		blueDate:    blueDate,
		redDate:     redDate,
	}
}

// Simplified constructor for the new query
func HydrateIssue(
	key string,
	storyPoints int64,
	issueType string,
	summary string,
	parent string,
	priority string,
	assignee string,
	dueDate *time.Time,
	greenDate *time.Time,
	blueDate *time.Time,
	redDate *time.Time,
) *Issue {
	return &Issue{
		key:         key,
		storyPoints: storyPoints,
		issueType:   issueType,
		summary:     summary,
		parent:      parent,
		priority:    priority,
		assignee:    assignee,
		dueDate:     dueDate,
		greenDate:   greenDate,
		blueDate:    blueDate,
		redDate:     redDate,
	}
}

// Sprint getters
func (s *Sprint) SprintName() string {
	return s.sprintName
}

func (s *Sprint) StartDate() time.Time {
	return s.startDate
}

func (s *Sprint) EndDate() time.Time {
	return s.endDate
}

// Release getters
func (r *Release) ReleaseName() string {
	return r.releaseName
}

func (r *Release) StartDate() time.Time {
	return r.startDate
}

func (r *Release) ReleaseDate() time.Time {
	return r.releaseDate
}

type JiraRawEvent struct {
	Payload   string    `gorm:"column:payload"`
	CreatedAt time.Time `gorm:"column:created_at"`
}

// Getter methods for Issue
func (s *Issue) Key() string           { return s.key }
func (s *Issue) StoryPoints() int64    { return s.storyPoints }
func (s *Issue) IssueType() string     { return s.issueType }
func (s *Issue) Summary() string       { return s.summary }
func (s *Issue) Parent() string        { return s.parent }
func (s *Issue) Priority() string      { return s.priority }
func (s *Issue) Assignee() string      { return s.assignee }
func (s *Issue) DueDate() *time.Time   { return s.dueDate }
func (s *Issue) GreenDate() *time.Time { return s.greenDate }
func (s *Issue) BlueDate() *time.Time  { return s.blueDate }
func (s *Issue) RedDate() *time.Time   { return s.redDate }
