package domain

import "time"

type GitH<PERSON><PERSON>awEvents struct {
	Payload   string    `gorm:"column:payload"`
	CreatedAt time.Time `gorm:"column:created_at"`
}

func (GitHubRawEvents) TableName() string {
	return "github_raw_events"
}

type RepoDim struct {
	RepoSK        int64     `gorm:"column:repo_sk;primaryKey" json:"repo_sk"`
	RepoID        int64     `gorm:"column:repo_id" json:"repo_id"`
	OwnerLogin    string    `gorm:"column:owner_login" json:"owner_login"`
	RepoName      string    `gorm:"column:repo_name" json:"repo_name"`
	DefaultBranch string    `gorm:"column:default_branch" json:"default_branch"`
	IsPrivate     bool      `gorm:"column:is_private" json:"is_private"`
	CreatedAt     time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at" json:"updated_at"`
}

type UserDim struct {
	UserID    string    `gorm:"column:user_id;primaryKey" json:"user_id"`
	Name      string    `gorm:"column:name" json:"name"`
	Email     string    `gorm:"column:email" json:"email"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"`
}

type BranchDim struct {
	BranchSK   int64  `gorm:"column:branch_sk;primaryKey" json:"branch_sk"`
	RepoSK     int64  `gorm:"column:repo_sk" json:"repo_sk"`
	BranchName string `gorm:"column:branch_name" json:"branch_name"`
	BranchType string `gorm:"column:branch_type" json:"branch_type"`
}

type FactCommit struct {
	CommitSK        int64     `gorm:"column:commit_sk;primaryKey" json:"commit_sk"`
	RepoSK          int64     `gorm:"column:repo_sk" json:"repo_sk"`
	BranchSK        int64     `gorm:"column:branch_sk" json:"branch_sk"`
	AuthorUserID    string    `gorm:"column:author_user_id" json:"author_user_id"`
	CommitterUserID string    `gorm:"column:committer_user_id" json:"committer_user_id"`
	CommitSHA       string    `gorm:"column:commit_sha" json:"commit_sha"`
	Message         string    `gorm:"column:message" json:"message"`
	AuthoredAt      time.Time `gorm:"column:authored_at" json:"authored_at"`
	CommittedAt     time.Time `gorm:"column:committed_at" json:"committed_at"`
	IsMergeCommit   bool      `gorm:"column:is_merge_commit" json:"is_merge_commit"`
}

type FactMerge struct {
	MergeSK        int64     `gorm:"column:merge_sk;primaryKey" json:"merge_sk"`
	RepoSK         int64     `gorm:"column:repo_sk" json:"repo_sk"`
	SourceBranchSK int64     `gorm:"column:source_branch_sk" json:"source_branch_sk"`
	TargetBranchSK int64     `gorm:"column:target_branch_sk" json:"target_branch_sk"`
	PRNumber       int64     `gorm:"column:pr_number" json:"pr_number"`
	MergedByUserID string    `gorm:"column:merged_by_user_id" json:"merged_by_user_id"`
	MergeSHA       string    `gorm:"column:merge_sha" json:"merge_sha"`
	MergedAt       time.Time `gorm:"column:merged_at" json:"merged_at"`
}

type FactWorkflowRun struct {
	WorkflowRunSK int64     `gorm:"column:workflow_run_sk;primaryKey" json:"workflow_run_sk"`
	RepoSK        int64     `gorm:"column:repo_sk" json:"repo_sk"`
	WorkflowName  string    `gorm:"column:workflow_name" json:"workflow_name"`
	Status        string    `gorm:"column:status" json:"status"`
	Conclusion    string    `gorm:"column:conclusion" json:"conclusion"`
	StartedAt     time.Time `gorm:"column:started_at" json:"started_at"`
	CompletedAt   time.Time `gorm:"column:completed_at" json:"completed_at"`
	DurationSec   int64     `gorm:"column:duration_sec" json:"duration_sec"`
}

type FactDeployment struct {
	DeploymentSK  int64     `gorm:"column:deployment_sk;primaryKey" json:"deployment_sk"`
	RepoSK        int64     `gorm:"column:repo_sk" json:"repo_sk"`
	EnvSK         int64     `gorm:"column:env_sk" json:"env_sk"`
	RunSK         int64     `gorm:"column:run_sk" json:"run_sk"`
	DeploymentID  int64     `gorm:"column:deployment_id" json:"deployment_id"`
	MainCommitSHA string    `gorm:"column:main_commit_sha" json:"main_commit_sha"`
	Status        string    `gorm:"column:status" json:"status"`
	DeployedAt    time.Time `gorm:"column:deployed_at" json:"deployed_at"`
}

type FactIncident struct {
	IncidentSK         int64     `gorm:"column:incident_sk;primaryKey" json:"incident_sk"`
	RepoSK             int64     `gorm:"column:repo_sk" json:"repo_sk"`
	JiraIssueID        string    `gorm:"column:jira_issue_id" json:"jira_issue_id"`
	IssueTypeSK        int64     `gorm:"column:issue_type_sk" json:"issue_type_sk"`
	IssueSeveritySK    int64     `gorm:"column:issue_severity_sk" json:"issue_severity_sk"`
	CreatedAt          time.Time `gorm:"column:created_at" json:"created_at"`
	ResolvedAt         time.Time `gorm:"column:resolved_at" json:"resolved_at"`
	LinkedDeploymentSK int64     `gorm:"column:linked_deployment_sk" json:"linked_deployment_sk"`
}

type BridgeCommitDeployment struct {
	CommitSK     int64 `gorm:"column:commit_sk;primaryKey" json:"commit_sk"`
	DeploymentSK int64 `gorm:"column:deployment_sk;primaryKey" json:"deployment_sk"`
}

type BridgeIncidentDeployment struct {
	IncidentSK   int64 `gorm:"column:incident_sk;primaryKey" json:"incident_sk"`
	DeploymentSK int64 `gorm:"column:deployment_sk;primaryKey" json:"deployment_sk"`
}

type BridgeUserTeam struct {
	UserID   string    `gorm:"column:user_id;primaryKey" json:"user_id"`
	TeamSK   int64     `gorm:"column:team_sk;primaryKey" json:"team_sk"`
	JoinedAt time.Time `gorm:"column:joined_at" json:"joined_at"`
}
