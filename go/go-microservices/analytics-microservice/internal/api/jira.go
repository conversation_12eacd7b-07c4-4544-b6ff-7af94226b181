package api

import (
	"context"
	"fmt"
	"strings"
	"time"

	"analytics-microservice/internal/app"
	"analytics-microservice/internal/domain"

	"log/slog"

	"github.com/gin-gonic/gin"
)

type JiraService interface {
	HandleGetProjectSprints(ctx context.Context, q app.GetProjectSprintsQuery) ([]*domain.Sprint, error)
	HandleGetProjectReleases(ctx context.Context, q app.GetProjectReleasesQuery) ([]*domain.Release, error)
	HandleGetIssueData(ctx context.Context, q app.GetProjectDataQuery) ([]*domain.Issue, error)
}

type JiraAP<PERSON> struct {
	jiraService JiraService
	logger      *slog.Logger
}

func NewJiraAPI(jiraService JiraService, logger *slog.Logger) *JiraAPI {
	return &JiraAPI{
		jiraService: jiraService,
		logger:      logger.WithGroup("JiraHandler"),
	}
}

func (h *JiraAPI) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api/v1/jira")
	{
		api.GET("/sprints", h.getProjectSprints)
		api.GET("/releases", h.getProjectReleases)
		api.GET("/issues", h.getIssueData)
	}
}

// @Summary Get project sprints
// @Description Retrieve all sprints for a given Jira project
// @Tags Jira
// @Produce json
// @Param projectKey query string true "Jira Project Key"
// @Success 200 {array} sprintResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jira/sprints [get]
func (h *JiraAPI) getProjectSprints(c *gin.Context) {
	var params projectQueryParams
	if err := params.BindAndValidate(c); err != nil {
		BadRequest(c, err.Error())
		return
	}

	q := app.GetProjectSprintsQuery{ProjectKey: params.ProjectID}
	domainSprints, err := h.jiraService.HandleGetProjectSprints(c.Request.Context(), q)
	if err != nil {
		HandleError(c, err)
		return
	}

	Success(c, h.toSprintResponses(domainSprints))
}

// @Summary Get project releases
// @Description Retrieve all releases for a given Jira project
// @Tags Jira
// @Produce json
// @Param projectKey query string true "Jira Project Key"
// @Success 200 {array} releaseResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jira/releases [get]
func (h *JiraAPI) getProjectReleases(c *gin.Context) {
	var params projectQueryParams
	if err := params.BindAndValidate(c); err != nil {
		BadRequest(c, err.Error())
		return
	}

	q := app.GetProjectReleasesQuery{ProjectKey: params.ProjectID}
	domainReleases, err := h.jiraService.HandleGetProjectReleases(c.Request.Context(), q)
	if err != nil {
		HandleError(c, err)
		return
	}

	Success(c, h.toReleaseResponses(domainReleases))
}

// @Summary Get project data
// @Description Retrieve project data with optional filters for date range and issue type
// @Tags Jira
// @Produce json
// @Param projectIds query string true "Comma-separated list of Jira Project IDs (e.g., 'DO,MP,HP')"
// @Param startDate query string false "Start date in YYYY-MM-DD format (e.g., '2024-01-01')"
// @Param endDate query string false "End date in YYYY-MM-DD format (e.g., '2024-12-31')"
// @Param issueType query string true "Issue type filter. Valid values: story, task, sub-task, bug, epic"
// @Success 200 {array} issueResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/jira/issues [get]
func (h *JiraAPI) getIssueData(c *gin.Context) {
	var params issueDataQueryParams
	if err := params.BindAndValidate(c); err != nil { //TODO: check bind and validate
		BadRequest(c, err.Error())
		return
	}

	q := app.GetProjectDataQuery{
		ProjectIds: params.ProjectIds,
		StartDate:  params.StartDate,
		EndDate:    params.EndDate,
		IssueType:  params.IssueType,
	}

	issues, err := h.jiraService.HandleGetIssueData(c.Request.Context(), q)
	if err != nil {
		HandleError(c, err)
		return
	}

	Success(c, h.toIssueDataResponses(issues))
}

// --- API Jira Helpers ---

type projectQueryParams struct {
	ProjectID string `json:"projectId"`
}

func (r *projectQueryParams) BindAndValidate(c *gin.Context) error {
	r.ProjectID = c.Query("projectId")
	if r.ProjectID == "" {
		return fmt.Errorf("projectId is required")
	}
	return nil
}

type issueDataQueryParams struct {
	ProjectIds []string  `json:"projectIds"`
	StartDate  time.Time `json:"startDate"`
	EndDate    time.Time `json:"endDate"`
	IssueType  string    `json:"issueType"`
}

func (r *issueDataQueryParams) BindAndValidate(c *gin.Context) error {
	// Parse project IDs (required)
	projectIdsStr := c.Query("projectIds")
	if projectIdsStr == "" {
		return fmt.Errorf("projectIds is required")
	}
	
	// Split by comma and trim whitespace
	rawIds := strings.Split(projectIdsStr, ",")
	r.ProjectIds = make([]string, 0, len(rawIds))
	
	for _, id := range rawIds {
		trimmedId := strings.TrimSpace(id)
		if trimmedId == "" {
			continue // Skip empty entries
		}
		
		// Validate each project ID using the shared validProjectID regex from kpi-data.go
		if !validProjectID.MatchString(trimmedId) {
			return fmt.Errorf("projectId '%s' contains invalid characters. Valid IDs: DO, MP, HP, HAR, PF, AAM, SHT", trimmedId)
		}
		
		r.ProjectIds = append(r.ProjectIds, trimmedId)
	}
	
	if len(r.ProjectIds) == 0 {
		return fmt.Errorf("at least one valid projectId is required")
	}

	// Parse issue type (required)
	r.IssueType = c.Query("issueType")
	if r.IssueType == "" {
		return fmt.Errorf("issueType is required")
	}
	
	// Validate issue type
	validTypes := map[string]bool{
		"Story":    true,
		"Task":     true,
		"Sub-Task": true,
		"Bug":      true,
		"Epic":     true,
	}
	if !validTypes[r.IssueType] {
		return fmt.Errorf("invalid issueType: '%s'. Valid types are: Story, Task, Sub-Task, Bug, Epic", r.IssueType)
	}

	// Parse and validate date range using the shared function from kpi-data.go
	start, end, err := parseAndValidateDateRange(c.Query("startDate"), c.Query("endDate"))
	if err != nil {
		return fmt.Errorf("date parsing error: %w", err)
	}
	r.StartDate, r.EndDate = start, end

	return nil
}

type sprintResponse struct {
	Name      string `json:"name"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
}

type releaseResponse struct {
	Name      string `json:"name"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
}

func (h *JiraAPI) toSprintResponses(domainSprints []*domain.Sprint) []sprintResponse {
	resp := make([]sprintResponse, len(domainSprints))
	for i, s := range domainSprints {
		resp[i] = sprintResponse{
			Name:      s.SprintName(),
			StartDate: s.StartDate().Format("2006-01-02"),
			EndDate:   s.EndDate().Format("2006-01-02"),
		}
	}
	return resp
}
func (h *JiraAPI) toReleaseResponses(domainReleases []*domain.Release) []releaseResponse {
	resp := make([]releaseResponse, len(domainReleases))
	for i, r := range domainReleases {
		resp[i] = releaseResponse{
			Name:      r.ReleaseName(),
			StartDate: r.StartDate().Format("2006-01-02"),
			EndDate:   r.ReleaseDate().Format("2006-01-02"),
		}
	}
	return resp
}

type issueDataResponse struct {
	Key         string     `json:"key"`
	StoryPoints int64      `json:"storyPoints"`
	IssueType   string     `json:"issueType"`
	Summary     string     `json:"summary"`
	Parent      string     `json:"parent,omitempty"`
	Priority    string     `json:"priority,omitempty"`
	Assignee    string     `json:"assignee,omitempty"`
	DueDate     *string    `json:"dueDate,omitempty"`
	GreenDate   *string    `json:"greenDate,omitempty"`
	BlueDate    *string    `json:"blueDate,omitempty"`
	RedDate     *string    `json:"redDate,omitempty"`
}

func (h *JiraAPI) toIssueDataResponses(domainIssues []*domain.Issue) []issueDataResponse {
	resp := make([]issueDataResponse, len(domainIssues))
	for i, issue := range domainIssues {
		var dueDate, greenDate, blueDate, redDate *string
		
		if issue.DueDate() != nil {
			due := issue.DueDate().Format("2006-01-02")
			dueDate = &due
		}
		if issue.GreenDate() != nil {
			green := issue.GreenDate().Format("2006-01-02")
			greenDate = &green
		}
		if issue.BlueDate() != nil {
			blue := issue.BlueDate().Format("2006-01-02")
			blueDate = &blue
		}
		if issue.RedDate() != nil {
			red := issue.RedDate().Format("2006-01-02")
			redDate = &red
		}

		resp[i] = issueDataResponse{
			Key:         issue.Key(),
			StoryPoints: issue.StoryPoints(),
			IssueType:   issue.IssueType(),
			Summary:     issue.Summary(),
			Parent:      issue.Parent(),
			Priority:    issue.Priority(),
			Assignee:    issue.Assignee(),
			DueDate:     dueDate,
			GreenDate:   greenDate,
			BlueDate:    blueDate,
			RedDate:     redDate,
		}
	}
	return resp
}
