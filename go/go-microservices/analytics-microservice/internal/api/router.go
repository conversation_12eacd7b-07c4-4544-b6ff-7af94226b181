package api

import (
	"net/http"
	"time"

	"analytics-microservice/internal/container"
	"analytics-microservice/internal/logging"
	"analytics-microservice/internal/observability"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RegisterRoutes registers all API routes
func RegisterRoutes(router *gin.Engine, container *container.Container) {
	// Add global middleware
	router.Use(
		TimeoutMiddleware(30*time.Second),
		CORSMiddleware(),
		RecoveryMiddleware(),
		RateLimitMiddleware(100, 200), // 100 requests per second, burst of 200
		observability.PrometheusMiddleware(),
	)

	// Add request logging middleware
	router.Use(func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// Process request
		c.Next()

		// Log request details
		latency := time.Since(start)
		status := c.Writer.Status()
		clientIP := c.ClientIP()

		logger := logging.GetLogger()
		logger.Info("Request processed",
			zap.String("method", method),
			zap.String("path", path),
			zap.Int("status", status),
			zap.Duration("latency", latency),
			zap.String("client_ip", clientIP),
		)
	})

	// Register health endpoints with container health check
	router.GET("/healthz", func(c *gin.Context) {
		Success(c, gin.H{"status": "ok"})
	})

	router.GET("/readyz", func(c *gin.Context) {
		if err := container.HealthCheck(c.Request.Context()); err != nil {
			JSON(c, http.StatusServiceUnavailable, NewErrorResponse("SERVICE_UNAVAILABLE", "Service not ready"))
			return
		}
		Success(c, gin.H{"status": "ready"})
	})

	// Register KPI data routes
	//TODO: Refactor the way routes are registered in future - Medina, Erzen
	if kpiDataService := container.KPIDataService(); kpiDataService != nil {
		kpiDataAPI := NewKPIDataAPI(kpiDataService, container.Logger())
		kpiDataAPI.RegisterRoutes(router)
	} else {
		container.Logger().Warn("KPI data service not available, skipping KPI data routes")
	}
	if jiraSvc := container.JiraService(); jiraSvc != nil {
		jiraAPI := NewJiraAPI(jiraSvc, container.Logger())
		jiraAPI.RegisterRoutes(router)
	} else {
		container.Logger().Warn("Jira service not available, skipping Jira routes")
	}
	if epicSvc := container.EpicService(); epicSvc != nil {
		epicAPI := NewEpicHealthAPI(epicSvc, container.Logger())
		epicAPI.RegisterRoutes(router)
	} else {
		container.Logger().Warn("Epic service not available, skipping Epic routes")
	}

	// Register GitHub DORA metrics routes
	if githubRepo := container.GitHubRepo(); githubRepo != nil {
		githubHandler := NewGitHubHandler(githubRepo)

		// GitHub webhook endpoint
		router.POST("/webhooks/github", githubHandler.HandleGitHubWebhook)

		// DORA metrics endpoints
		doraRoutes := router.Group("/api/v1/dora")
		{
			doraRoutes.GET("/metrics", githubHandler.GetDORAMetrics)
			doraRoutes.GET("/metrics/repository/:repo_id", githubHandler.GetRepositoryDORAMetrics)
			doraRoutes.GET("/events", githubHandler.GetGitHubEvents)
			doraRoutes.GET("/health", githubHandler.HealthCheck)
		}

		container.Logger().Info("GitHub DORA metrics routes registered successfully")
	} else {
		container.Logger().Warn("GitHub repository not available, skipping GitHub routes")
	}

	// Register Dapr subscription endpoints
	if pubsubClient := container.PubSubClient(); pubsubClient != nil {
		// Dapr subscription discovery endpoint
		pubsubClient.RegisterSubscriptionsEndpoint(router)
		container.Logger().Info("Dapr subscription discovery endpoint registered")
	} else {
		container.Logger().Info("PubSubClient not available, skipping Dapr subscription endpoints")
	}

	// Register webhook event handler
	if webhookEventHandler := container.WebhookEventHandler(); webhookEventHandler != nil {
		// Dapr webhook event delivery endpoint
		router.POST("/webhook-events", webhookEventHandler.HandleWebhookEvent)
		container.Logger().Info("Dapr webhook event handler registered successfully")
	} else {
		container.Logger().Info("WebhookEventHandler not available, skipping webhook endpoints")
	}

	if seedService := container.SeedService(); seedService != nil {
		seedAPI := NewSeedAPI(seedService, container.Logger())
		seedAPI.RegisterRoutes(router)
	} else {
		container.Logger().Info("SeedService not available, skipping seed endpoints")
	}
}
