package api

import (
	"context"
	"log/slog"

	"analytics-microservice/internal/app"
	"analytics-microservice/internal/domain"

	"github.com/gin-gonic/gin"
)

type SeedService interface {
	SeedJob(ctx context.Context, request app.SeedRequest) (*app.SeedResponse, error)
	GetSeedJobStatus(ctx context.Context, jobID string) (*domain.SeedJob, error)
	GetAllSeedJobs(ctx context.Context) ([]*domain.SeedJob, error)
	GetTableNames() ([]string, error)
	RunMigrations() error
	DropAllTables() error
	PopulateDateDimension() error
}

type SeedAPI struct {
	seedService SeedService
	logger      *slog.Logger
}

func NewSeedAPI(seedService SeedService, logger *slog.Logger) *SeedAPI {
	return &SeedAPI{
		seedService: seedService,
		logger:      logger.WithGroup("SeedHandler"),
	}
}

func (h *SeedAPI) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api/v1")
	{
		api.POST("/seed", h.seedJob)
		api.GET("/seed", h.getAllSeedJobs)
		api.GET("/seed/:job_id/status", h.getSeedJobStatus)
		api.GET("/tables", h.getTableNames)
		api.POST("/migrations", h.runMigrations)
		api.DELETE("/tables", h.dropAllTables)
		api.POST("/date-dimension", h.populateDateDimension)
	}
}

func (h *SeedAPI) seedJob(c *gin.Context) {
	var request app.SeedRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	h.logger.Info("Creating seed job",
		"source", request.Source,
		"category", request.Category,
		"project", request.Project)

	response, err := h.seedService.SeedJob(c.Request.Context(), request)
	if err != nil {
		h.logger.Error("Failed to create seed job", "error", err)
		InternalError(c, "Failed to create seed job: "+err.Error())
		return
	}

	Success(c, response)
}

func (h *SeedAPI) getSeedJobStatus(c *gin.Context) {
	jobID := c.Param("job_id")
	if jobID == "" {
		BadRequest(c, "job_id parameter is required")
		return
	}

	h.logger.Info("Getting seed job status", "job_id", jobID)

	job, err := h.seedService.GetSeedJobStatus(c.Request.Context(), jobID)
	if err != nil {
		h.logger.Error("Failed to get seed job status", "job_id", jobID, "error", err)
		InternalError(c, "Failed to get seed job status: "+err.Error())
		return
	}

	response := map[string]interface{}{
		"job_id":     job.ID(),
		"status":     string(job.Status()),
		"category":   job.Category(),
		"endpoint":   job.Endpoint(),
		"project":    job.Project(),
		"platform":   string(job.Platform()),
		"started_at": job.StartedAt(),
		"updated_at": job.UpdatedAt(),
	}

	if job.Error() != "" {
		response["error"] = job.Error()
	}

	Success(c, response)
}

func (h *SeedAPI) getAllSeedJobs(c *gin.Context) {
	h.logger.Info("Getting all seed jobs")

	jobs, err := h.seedService.GetAllSeedJobs(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get all seed jobs", "error", err)
		InternalError(c, "Failed to get seed jobs: "+err.Error())
		return
	}

	// Convert jobs to response format
	jobResponses := make([]map[string]interface{}, len(jobs))
	for i, job := range jobs {
		jobResponse := map[string]interface{}{
			"job_id":     job.ID(),
			"status":     string(job.Status()),
			"category":   job.Category(),
			"endpoint":   job.Endpoint(),
			"project":    job.Project(),
			"platform":   string(job.Platform()),
			"started_at": job.StartedAt(),
			"updated_at": job.UpdatedAt(),
		}

		if job.Error() != "" {
			jobResponse["error"] = job.Error()
		}

		jobResponses[i] = jobResponse
	}

	response := map[string]interface{}{
		"jobs":  jobResponses,
		"count": len(jobs),
	}

	Success(c, response)
}

// getTableNames retrieves all table names from the analytics database
func (h *SeedAPI) getTableNames(c *gin.Context) {
	h.logger.Info("Getting table names")

	tables, err := h.seedService.GetTableNames()
	if err != nil {
		h.logger.Error("Failed to get table names", "error", err)
		InternalError(c, err.Error())
		return
	}

	Success(c, gin.H{
		"tables": tables,
		"count":  len(tables),
	})
}

// runMigrations runs all database migrations
func (h *SeedAPI) runMigrations(c *gin.Context) {
	h.logger.Info("Running migrations")

	// run in background
	go func() {
		if err := h.seedService.RunMigrations(); err != nil {
			h.logger.Error("Migrations failed", "error", err)
		} else {
			h.logger.Info("Migrations completed successfully")
		}
	}()

	// immediately respond
	Success(c, "Migrations started")
}

// dropAllTables drops all tables in the analytics database
func (h *SeedAPI) dropAllTables(c *gin.Context) {
	h.logger.Info("Dropping all tables")

	// run in background
	go func() {
		if err := h.seedService.DropAllTables(); err != nil {
			h.logger.Error("Failed to drop tables", "error", err)
		} else {
			h.logger.Info("All tables dropped successfully")
		}
	}()

	// immediately respond
	Success(c, "Table drop operation started")
}

// populateDateDimension populates the date_dim table with dates for 2025
func (h *SeedAPI) populateDateDimension(c *gin.Context) {
	h.logger.Info("Populating date dimension table")

	// run in background
	go func() {
		if err := h.seedService.PopulateDateDimension(); err != nil {
			h.logger.Error("Failed to populate date dimension", "error", err)
		} else {
			h.logger.Info("Date dimension populated successfully")
		}
	}()

	// immediately respond
	Success(c, "Date dimension population started")
}
