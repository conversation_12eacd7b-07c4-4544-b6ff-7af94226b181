package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"analytics-microservice/internal/domain"
	"analytics-microservice/internal/infra/clickhouse"

	"github.com/gin-gonic/gin"
)

// GitHubHandler handles GitHub webhook events and DORA metrics endpoints
type GitHubHandler struct {
	repo *clickhouse.GitHubRepo
}

// NewGitHubHandler creates a new GitHub handler
func NewGitHubHandler(repo *clickhouse.GitHubRepo) *GitHubHandler {
	return &GitHubHandler{repo: repo}
}

// GitHubWebhookRequest represents a GitHub webhook payload
type GitHubWebhookRequest struct {
	DeliveryGUID string                 `json:"-"` // From X-GitHub-Delivery header
	EventType    string                 `json:"-"` // From X-GitHub-Event header
	Repository   map[string]interface{} `json:"repository"`
	Payload      map[string]interface{} `json:"-"` // Full payload
}

// HandleGitHubWebhook processes incoming GitHub webhook events
func (h *Git<PERSON>ub<PERSON>andler) HandleGitHubWebhook(c *gin.Context) {
	// Extract headers
	deliveryGUID := c.GetHeader("X-GitHub-Delivery")
	eventType := c.GetHeader("X-GitHub-Event")

	if deliveryGUID == "" || eventType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing required GitHub headers",
		})
		return
	}

	// Parse the JSON payload
	var payload map[string]interface{}
	if err := c.ShouldBindJSON(&payload); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid JSON payload",
			"details": err.Error(),
		})
		return
	}

	// Convert payload to JSON string
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to serialize payload",
			"details": err.Error(),
		})
		return
	}

	// Store the raw event using the unified webhook event pipeline
	webhookEvent, err := domain.NewWebhookEvent(deliveryGUID, "github", payloadBytes, time.Now())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create webhook event",
			"details": err.Error(),
		})
		return
	}

	if err := h.repo.StoreWebhookEvent(c.Request.Context(), webhookEvent); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to store GitHub event",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "GitHub event processed successfully",
		"event_type":    eventType,
		"delivery_guid": deliveryGUID,
	})
}

// DORAMetricsRequest represents a request for DORA metrics
type DORAMetricsRequest struct {
	RepoID   uint64 `form:"repo_id"`
	FromDate string `form:"from_date"`
	ToDate   string `form:"to_date"`
	Metric   string `form:"metric"` // specific metric: deployment_frequency, lead_time, mttr, change_failure_rate
}

// DORAMetricsResponse represents the response for DORA metrics
type DORAMetricsResponse struct {
	Metrics []clickhouse.DORAMetrics `json:"metrics"`
	Summary struct {
		TotalRepositories     int     `json:"total_repositories"`
		AverageDeploymentFreq float64 `json:"average_deployment_frequency"`
		AverageLeadTime       float64 `json:"average_lead_time_hours"`
		AverageMTTR           float64 `json:"average_mttr_minutes"`
		AverageFailureRate    float64 `json:"average_failure_rate"`
	} `json:"summary"`
}

// GetDORAMetrics retrieves DORA metrics for repositories
func (h *GitHubHandler) GetDORAMetrics(c *gin.Context) {
	var req DORAMetricsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid query parameters",
			"details": err.Error(),
		})
		return
	}

	// Parse dates
	fromDate, err := time.Parse("2006-01-02", req.FromDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid from_date format, expected YYYY-MM-DD",
		})
		return
	}

	toDate, err := time.Parse("2006-01-02", req.ToDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid to_date format, expected YYYY-MM-DD",
		})
		return
	}

	var metrics []clickhouse.DORAMetrics
	var response DORAMetricsResponse

	ctx := c.Request.Context()

	// Get specific metric or all metrics
	switch req.Metric {
	case "deployment_frequency":
		metrics, err = h.repo.GetDeploymentFrequency(ctx, req.RepoID, fromDate, toDate)
	case "lead_time":
		metrics, err = h.repo.GetLeadTimeForChanges(ctx, req.RepoID, fromDate, toDate)
	case "mttr":
		metrics, err = h.repo.GetMeanTimeToRestore(ctx, req.RepoID, fromDate, toDate)
	case "change_failure_rate":
		metrics, err = h.repo.GetChangeFailureRate(ctx, req.RepoID, fromDate, toDate)
	default:
		// Get all metrics (deployment frequency as default)
		metrics, err = h.repo.GetDeploymentFrequency(ctx, req.RepoID, fromDate, toDate)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve DORA metrics",
			"details": err.Error(),
		})
		return
	}

	response.Metrics = metrics

	// Calculate summary statistics
	if len(metrics) > 0 {
		response.Summary.TotalRepositories = len(metrics)

		var totalDeployFreq, totalLeadTime, totalMTTR, totalFailureRate float64
		for _, metric := range metrics {
			totalDeployFreq += metric.DeploymentFrequency
			totalLeadTime += metric.LeadTimeHours
			totalMTTR += metric.MTTRMinutes
			totalFailureRate += metric.ChangeFailureRate
		}

		count := float64(len(metrics))
		response.Summary.AverageDeploymentFreq = totalDeployFreq / count
		response.Summary.AverageLeadTime = totalLeadTime / count
		response.Summary.AverageMTTR = totalMTTR / count
		response.Summary.AverageFailureRate = totalFailureRate / count
	}

	c.JSON(http.StatusOK, response)
}

// GetRepositoryDORAMetrics gets comprehensive DORA metrics for a specific repository
func (h *GitHubHandler) GetRepositoryDORAMetrics(c *gin.Context) {
	repoIDStr := c.Param("repo_id")
	repoID, err := strconv.ParseUint(repoIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid repository ID",
		})
		return
	}

	fromDateStr := c.Query("from_date")
	toDateStr := c.Query("to_date")

	if fromDateStr == "" || toDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "from_date and to_date are required",
		})
		return
	}

	fromDate, err := time.Parse("2006-01-02", fromDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid from_date format, expected YYYY-MM-DD",
		})
		return
	}

	toDate, err := time.Parse("2006-01-02", toDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid to_date format, expected YYYY-MM-DD",
		})
		return
	}

	// Get all DORA metrics for the repository
	metrics, err := h.repo.GetAllDORAMetrics(c.Request.Context(), repoID, fromDate, toDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve repository DORA metrics",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"repository_metrics": metrics,
		"calculated_at":      time.Now(),
	})
}

// GetGitHubEvents retrieves stored GitHub events
func (h *GitHubHandler) GetGitHubEvents(c *gin.Context) {
	eventType := c.Query("event_type")
	limitStr := c.DefaultQuery("limit", "100")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000 // Cap the limit
	}

	var events []domain.GitHubRawEvents
	if eventType != "" {
		events, err = h.repo.GetGitHubEventsByType(c.Request.Context(), eventType, limit)
	} else {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "event_type parameter is required",
		})
		return
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve GitHub events",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"events": events,
		"count":  len(events),
	})
}

// HealthCheck provides a health check endpoint for the GitHub service
func (h *GitHubHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"service":   "github-dora-metrics",
		"timestamp": time.Now(),
	})
}
