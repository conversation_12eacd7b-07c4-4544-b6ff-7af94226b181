# Analytics Microservice - Generic Seeding API

## Overview

The Analytics Microservice provides a powerful generic seeding system that can fetch data from any Jira or GitHub API endpoint with automatic pagination, date filtering, and bulk data storage. This system follows Domain-Driven Design (DDD) principles and Clean Architecture patterns.

## Key Features

- **🌐 Universal API Support**: Works with any Jira or GitHub API endpoint
- **📄 Automatic Pagination**: Fetches all pages automatically with platform-specific pagination
- **📅 Date Filtering**: Optional earliest date cutoff to prevent over-fetching historical data
- **🔄 Retry Logic**: Exponential backoff retry mechanism for failed requests
- **📊 Job Tracking**: Full job status tracking with detailed logging
- **🏗️ Clean Architecture**: Proper DDD layer separation with infrastructure concerns isolated

## Architecture Overview

### Layer Separation

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Layer     │───▶│ Application     │───▶│ Infrastructure  │
│                 │    │ Layer           │    │ Layer           │
│ • REST Endpoints│    │ • Business Logic│    │ • External APIs │
│ • Request/Response│   │ • Job Orchestration│ │ • Pagination    │
│ • Validation    │    │ • Retry Logic   │    │ • Data Storage  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │ Domain Layer    │
                       │                 │
                       │ • SeedJob Entity│
                       │ • Business Rules│
                       │ • Domain Logic  │
                       └─────────────────┘
```

### Key Components

1. **SeedService** (Application Layer): Orchestrates seeding workflow and job management
2. **GenericSeeder** (Infrastructure Layer): Handles external API calls and pagination
3. **SeedJob** (Domain Layer): Core business entity representing a seeding operation
4. **SeedJobRepository** (Infrastructure Layer): Persists job state and history

## API Endpoints

### 1. Create Seed Job
**POST** `/api/v1/seed`

Creates a new seeding job and processes it asynchronously.

### 2. Get Job Status
**GET** `/api/v1/seed/status/{job_id}`

Retrieves the current status of a specific seeding job.

### 3. List All Jobs
**GET** `/api/v1/seed/jobs`

Lists all seeding jobs with their current status.

### 4. Database Operations
- **GET** `/api/v1/seed/tables` - List all database tables
- **POST** `/api/v1/seed/migrate` - Run database migrations
- **DELETE** `/api/v1/seed/tables` - Drop all tables (⚠️ Destructive)
- **POST** `/api/v1/seed/populate-date-dimension` - Populate date dimension table

## Request Format

### SeedRequest Schema

```json
{
  "source": "jira|github",
  "category": "string",
  "endpoint": "string", 
  "project": "string",
  "earliest_date": "2024-01-01T00:00:00Z",
  
  // Jira Authentication (required when source="jira")
  "jira_base_url": "https://company.atlassian.net",
  "jira_email": "<EMAIL>",
  "jira_token": "api_token",
  
  // GitHub Authentication (required when source="github")  
  "github_base_url": "https://api.github.com",
  "github_token": "ghp_token"
}
```

### Field Descriptions

- **source**: Platform identifier (`jira` or `github`)
- **category**: Descriptive category for the data type (e.g., `issues`, `sprints`, `pulls`)
- **endpoint**: API endpoint path (supports placeholders like `{project}`, `{repo}`)
- **project**: Project key (Jira) or repository name (GitHub) in format `owner/repo`
- **earliest_date**: Optional RFC3339 timestamp - won't fetch data older than this date
- **Authentication fields**: Platform-specific credentials

## Pagination & Date Filtering

### Automatic Pagination

The system automatically handles pagination for both platforms:

- **Jira**: Uses `startAt` and `maxResults` parameters (100 items per page)
- **GitHub**: Uses `page` and `per_page` parameters (100 items per page)

### Smart Date Filtering

Date filtering works at two levels:

1. **URL-level**: Adds date parameters to API requests where supported
   - Jira: Adds `created >= 'YYYY-MM-DD'` to JQL queries
   - GitHub: Adds `since=RFC3339` parameter

2. **Response-level**: Parses JSON responses to check item dates
   - Looks for common date fields: `created_at`, `updated_at`, `created`, `updated`
   - Stops pagination when items older than `earliest_date` are found

## cURL Examples

### Basic API Operations

#### Create a Seed Job
```bash
curl -X POST http://localhost:/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "jira",
    "category": "issues",
    "endpoint": "/rest/api/3/search?jql=project={project}",
    "project": "TMP",
    "jira_base_url": "https://company.atlassian.net",
    "jira_email": "<EMAIL>",
    "jira_token": "your_api_token"
  }'
```

#### Check Job Status
```bash
curl -X GET http://localhost:2020/api/v1/seed/status/job_12345
```

#### List All Jobs
```bash
curl -X GET http://localhost:2020/api/v1/seed
```

#### Get Database Tables
```bash
curl -X GET http://localhost:2020/api/v1/tables
```

#### Run Migrations
```bash
curl -X POST http://localhost:2020/api/v1/migrations
```

#### Drop all analytics database tables
```bash
curl -X DELETE http://localhost:2020/api/v1/tables
```

#### Populate date dimension table
```bash
curl -X DELETE http://localhost:2020/api/v1/tables
```

### Jira Seeding Examples

#### Fetch All Jira Issues
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "jira",
    "category": "issues",
    "endpoint": "/rest/api/3/search?jql=project={project}",
    "project": "TMP",
    "earliest_date": "2024-01-01T00:00:00Z",
    "jira_base_url": "https://company.atlassian.net",
    "jira_email": "<EMAIL>",
    "jira_token": "your_api_token"
  }'
```

#### Fetch All Jira Sprints
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "jira",
    "category": "sprints",
    "endpoint": "/rest/agile/1.0/board/{board_id}/sprint",
    "project": "123",
    "earliest_date": "2024-01-01T00:00:00Z",
    "jira_base_url": "https://company.atlassian.net",
    "jira_email": "<EMAIL>",
    "jira_token": "your_api_token"
  }'
```

#### Fetch All Jira Releases/Versions
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "jira",
    "category": "releases",
    "endpoint": "/rest/api/3/project/{project}/version",
    "project": "TMP",
    "earliest_date": "2024-01-01T00:00:00Z",
    "jira_base_url": "https://company.atlassian.net",
    "jira_email": "<EMAIL>",
    "jira_token": "your_api_token"
  }'
```

#### Fetch Jira Issues with Custom JQL
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "jira",
    "category": "bugs",
    "endpoint": "/rest/api/3/search?jql=project={project} AND issuetype=Bug AND status!=Closed",
    "project": "TMP",
    "earliest_date": "2024-06-01T00:00:00Z",
    "jira_base_url": "https://company.atlassian.net",
    "jira_email": "<EMAIL>",
    "jira_token": "your_api_token"
  }'
```

### GitHub Seeding Examples

#### Fetch All GitHub Pull Requests
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "pull_requests",
    "endpoint": "/pulls?state=all",
    "project": "owner/repo",
    "earliest_date": "2024-01-01T00:00:00Z",
    "github_token": "ghp_your_token_here"
  }'
```

#### Fetch GitHub Issues
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "issues",
    "endpoint": "/issues?state=all",
    "project": "owner/repo",
    "earliest_date": "2024-01-01T00:00:00Z",
    "github_token": "ghp_your_token_here"
  }'
```

#### Fetch GitHub Commits
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "commits",
    "endpoint": "/commits",
    "project": "owner/repo",
    "earliest_date": "2024-01-01T00:00:00Z",
    "github_token": "ghp_your_token_here"
  }'
```

#### Fetch GitHub Releases
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "releases",
    "endpoint": "/releases",
    "project": "owner/repo",
    "earliest_date": "2024-01-01T00:00:00Z",
    "github_token": "ghp_your_token_here"
  }'
```

#### Fetch GitHub Workflow Runs
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "workflow_runs",
    "endpoint": "/actions/runs",
    "project": "owner/repo",
    "earliest_date": "2024-01-01T00:00:00Z",
    "github_token": "ghp_your_token_here"
  }'
```

#### Fetch GitHub Deployments
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "deployments",
    "endpoint": "/deployments",
    "project": "owner/repo",
    "earliest_date": "2024-01-01T00:00:00Z",
    "github_token": "ghp_your_token_here"
  }'
```

#### Fetch GitHub Repository Events
```bash
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "events",
    "endpoint": "/events",
    "project": "owner/repo",
    "earliest_date": "2024-11-01T00:00:00Z",
    "github_token": "ghp_your_token_here"
  }'
```

## Advanced Usage Examples

### Bulk Historical Data Fetch
```bash
# Fetch all Jira issues from the last 2 years
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "jira",
    "category": "historical_issues",
    "endpoint": "/rest/api/3/search?jql=project={project}",
    "project": "TMP",
    "earliest_date": "2022-01-01T00:00:00Z",
    "jira_base_url": "https://company.atlassian.net",
    "jira_email": "<EMAIL>",
    "jira_token": "your_api_token"
  }'
```

### No Date Limit (Fetch Everything)
```bash
# Fetch all GitHub PRs with no date restriction
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "github",
    "category": "all_pulls",
    "endpoint": "/pulls?state=all",
    "project": "owner/repo",
    "github_token": "ghp_your_token_here"
  }'
```

### Custom API Endpoints
```bash
# Fetch custom Jira field data
curl -X POST http://localhost:2020/api/v1/seed \
  -H "Content-Type: application/json" \
  -d '{
    "source": "jira",
    "category": "custom_fields",
    "endpoint": "/rest/api/3/field",
    "project": "ANY",
    "jira_base_url": "https://company.atlassian.net",
    "jira_email": "<EMAIL>",
    "jira_token": "your_api_token"
  }'
```

## Response Format

### Successful Job Creation
```json
{
  "job_id": "job_abc123def456",
  "status": "pending"
}
```

### Job Status Response
```json
{
  "id": "job_abc123def456",
  "platform": "jira",
  "category": "issues",
  "endpoint": "/rest/api/3/search?jql=project=TMP",
  "project": "TMP",
  "status": "successful",
  "started_at": "2024-12-01T10:00:00Z",
  "updated_at": "2024-12-01T10:05:30Z",
  "error": null,
  "earliest_date": "2024-01-01T00:00:00Z"
}
```

### Job Status Values
- **pending**: Job created, waiting to be processed
- **running**: Job is currently being processed
- **successful**: Job completed successfully
- **failed**: Job failed with error (check `error` field)

## Error Handling

### Common Error Responses

#### Invalid Request Format
```json
{
  "error": "invalid seed request: missing required field 'source'"
}
```

#### Authentication Error
```json
{
  "error": "API request failed: HTTP 401: Unauthorized"
}
```

#### Invalid Date Format
```json
{
  "error": "invalid earliest_date format, expected RFC3339: parsing time \"2024-01-01\" as \"2006-01-02T15:04:05Z07:00\": cannot parse \"\" as \"T\""
}
```

## Best Practices

### 1. Use Earliest Date for Large Datasets
Always specify an `earliest_date` when fetching large historical datasets to prevent excessive API calls and storage usage.

### 2. Monitor Job Status
For long-running jobs, poll the status endpoint to monitor progress:
```bash
# Check job status every 30 seconds
watch -n 30 'curl -s http://localhost:2020/api/v1/seed/status/job_abc123def456 | jq'
```

### 3. Rate Limiting Awareness
The system includes built-in rate limiting (100ms between requests), but be mindful of API rate limits:
- **Jira Cloud**: 10 requests per second per app
- **GitHub**: 5,000 requests per hour for authenticated requests

### 4. Credential Security
- Use environment variables for tokens in production
- Rotate API tokens regularly
- Use fine-grained personal access tokens for GitHub when possible

### 5. Endpoint Optimization
- Use specific JQL queries for Jira to reduce data volume
- Leverage GitHub's filtering parameters (state, labels, etc.)
- Consider using search APIs for complex queries

## Troubleshooting

### Common Issues

1. **Job Stuck in "running" Status**
   - Check microservice logs for detailed error information
   - Verify API credentials are still valid
   - Check network connectivity to external APIs

2. **Empty Results**
   - Verify the endpoint URL is correct
   - Check if `earliest_date` is too restrictive
   - Ensure proper authentication and permissions

3. **Rate Limit Errors**
   - The system will retry automatically with exponential backoff
   - Consider spreading large jobs across multiple smaller requests
   - Check your API rate limit quotas

### Debugging Tips

1. **Enable Debug Logging**: Set log level to DEBUG for detailed request/response information
2. **Test Endpoints Manually**: Use tools like Postman to verify API endpoints work correctly
3. **Check Job History**: Use the `/api/v1/seed/jobs` endpoint to see all job attempts

## Data Storage

All fetched data is stored in ClickHouse raw events tables:
- **Jira data**: `analytics.jira_raw_events` table
- **GitHub data**: `analytics.github_raw_events` table

Each record contains:
- **payload**: Complete JSON response from the API
- **created_at**: Timestamp when the data was stored

The data can then be processed by other microservices or ETL pipelines for analytics and reporting purposes.

---

## Summary

The Generic Seeding API provides a powerful, flexible way to bulk-fetch data from Jira and GitHub APIs with automatic pagination, intelligent date filtering, and robust error handling. Its generic design allows it to work with any API endpoint while maintaining proper architectural separation and observability.

For questions or issues, check the microservice logs or contact the development team.
