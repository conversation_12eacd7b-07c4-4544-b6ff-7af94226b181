#!/bin/bash
echo "📊 Kafka Queue Message Counts"
echo "=============================="

get_message_count() {
    local topic=$1
    local count=$(docker exec dev-kafka-1 kafka-run-class.sh kafka.tools.GetOffsetShell \
        --broker-list localhost:9092 \
        --topic "$topic" 2>/dev/null | \
        awk -F: '{sum += $3} END {print sum+0}')
    echo "$topic: $count messages"
}

get_message_count "annotationtool-kafka-bus"
get_message_count "annotationtool-retry" 
get_message_count "annotationtool-dlq"

echo "=============================="