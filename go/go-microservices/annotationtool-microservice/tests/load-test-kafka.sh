#!/bin/bash
# load-test.sh

BASE_URL="http://localhost:2021"
OPERATIONS_ENDPOINT="$BASE_URL/api/v1/operation"
DATASET_ENDPOINT="$BASE_URL/api/v1/datasets/projects/test-project/environments/dev/datasets"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Load Testing${NC}"
echo "======================================"
echo -e "${YELLOW}Operations Endpoint:${NC} $OPERATIONS_ENDPOINT"
echo -e "${YELLOW}Dataset Endpoint:${NC} $DATASET_ENDPOINT"
echo "======================================"

# Create results directory
mkdir -p results
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Function to send operations request
send_operation_request() {
    local id=$1
    local start_time=$(date +%s.%3N)
    
    # Generate random operation type from valid values
    local op_types="addition removal approval disapproval"
    local random_op=$(echo $op_types | cut -d' ' -f$((RANDOM % 4 + 1)))
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null \
        -X POST "$OPERATIONS_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d '{
            "datasetID": "dataset-'$id'",
            "episodeID": "episode-'$id'", 
            "stripID": "strip-'$id'",
            "label": "test-label-'$id'",
            "labelTimestamp": '$(($(date +%s)))',
            "operationType": "addition",
            "submittedBy": "load-test-user"
        }')
    
    local end_time=$(date +%s.%3N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    echo "op,$id,$response,$duration" >> "results/operations_$TIMESTAMP.csv"
}

# Function to send dataset request  
send_dataset_request() {
    local id=$1
    local start_time=$(date +%s.%3N)
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null \
        -X POST "$DATASET_ENDPOINT" \
        -H "Content-Type: application/json" \
        -d '{
            "dataset_name": "test-dataset-'$id'",
            "version": "v1.0.'$id'",
            "columns": [
                {
                    "name": "id",
                    "type": "string",
                    "nullable": false
                },
                {
                    "name": "value", 
                    "type": "float",
                    "nullable": true
                }
            ],
            "requested_by": "load-test-user"
        }')
    
    local end_time=$(date +%s.%3N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    echo "dataset,$id,$response,$duration" >> "results/datasets_$TIMESTAMP.csv"
}

# Export functions for parallel execution
export -f send_operation_request send_dataset_request
export OPERATIONS_ENDPOINT DATASET_ENDPOINT TIMESTAMP

# Create CSV headers
echo "type,id,status_code,duration" > "results/operations_$TIMESTAMP.csv"
echo "type,id,status_code,duration" > "results/datasets_$TIMESTAMP.csv"

echo -e "${GREEN}📊 Starting Operations Load Test (10,000 requests)${NC}"
start_time=$(date +%s)

# Send 10,000 operations requests in parallel (batches of 100 to avoid overwhelming)
seq 1 10000 | xargs -n 1 -P 100 -I {} bash -c 'send_operation_request {}'

operations_end_time=$(date +%s)
operations_duration=$((operations_end_time - start_time))

echo -e "${GREEN}✅ Operations completed in ${operations_duration}s${NC}"

echo -e "${GREEN}📊 Starting Dataset Load Test (100 requests)${NC}"
dataset_start_time=$(date +%s)

# Send 100 dataset requests in parallel
seq 1 100 | xargs -n 1 -P 20 -I {} bash -c 'send_dataset_request {}'

dataset_end_time=$(date +%s)
dataset_duration=$((dataset_end_time - dataset_start_time))

echo -e "${GREEN}✅ Dataset requests completed in ${dataset_duration}s${NC}"

# Generate summary report
echo -e "${BLUE}📈 Test Results Summary${NC}"
echo "======================================"

# Operations summary
ops_total=$(wc -l < "results/operations_$TIMESTAMP.csv")
ops_success=$(awk -F, '$3 >= 200 && $3 < 300 {count++} END {print count+0}' "results/operations_$TIMESTAMP.csv")
ops_errors=$(awk -F, '$3 >= 400 {count++} END {print count+0}' "results/operations_$TIMESTAMP.csv")
ops_avg_time=$(awk -F, 'NR>1 {sum+=$4; count++} END {if(count>0) print sum/count; else print 0}' "results/operations_$TIMESTAMP.csv")

echo -e "${YELLOW}Operations Results:${NC}"
echo "  Total requests: $((ops_total-1))"
echo "  Successful (2xx): $ops_success"
echo "  Errors (4xx+): $ops_errors"
echo "  Average response time: ${ops_avg_time}s"
echo "  Total duration: ${operations_duration}s"

# Dataset summary  
dataset_total=$(wc -l < "results/datasets_$TIMESTAMP.csv")
dataset_success=$(awk -F, '$3 >= 200 && $3 < 300 {count++} END {print count+0}' "results/datasets_$TIMESTAMP.csv")
dataset_errors=$(awk -F, '$3 >= 400 {count++} END {print count+0}' "results/datasets_$TIMESTAMP.csv")
dataset_avg_time=$(awk -F, 'NR>1 {sum+=$4; count++} END {if(count>0) print sum/count; else print 0}' "results/datasets_$TIMESTAMP.csv")

echo -e "${YELLOW}Dataset Results:${NC}"
echo "  Total requests: $((dataset_total-1))"
echo "  Successful (2xx): $dataset_success"
echo "  Errors (4xx+): $dataset_errors"
echo "  Average response time: ${dataset_avg_time}s"
echo "  Total duration: ${dataset_duration}s"

echo "======================================"
echo -e "${BLUE}📋 Detailed results saved to:${NC}"
echo "  - results/operations_$TIMESTAMP.csv"
echo "  - results/datasets_$TIMESTAMP.csv"

# Show top 10 slowest requests
echo -e "${YELLOW}🐌 Top 10 Slowest Operations:${NC}"
tail -n +2 "results/operations_$TIMESTAMP.csv" | sort -t, -k4 -nr | head -10 | awk -F, '{printf "  Request %s: %.3fs (Status: %s)\n", $2, $4, $3}'

echo -e "${YELLOW}🐌 Top 10 Slowest Dataset Requests:${NC}"
tail -n +2 "results/datasets_$TIMESTAMP.csv" | sort -t, -k4 -nr | head -10 | awk -F, '{printf "  Request %s: %.3fs (Status: %s)\n", $2, $4, $3}'

echo -e "${GREEN}🎉 Load testing completed!${NC}"