#!/bin/bash
echo "🎧 Monitoring Kafka topics for annotation tool..."
echo "Press Ctrl+C to stop"

# Run multiple consumers in parallel
docker exec dev-kafka-1 kafka-console-consumer.sh \
  --bootstrap-server localhost:9092 \
  --topic annotationtool-kafka-bus \
  --property print.timestamp=true \
  --property print.key=true \
  --formatter kafka.tools.DefaultMessageFormatter \
  --property print.value=true &

docker exec dev-kafka-1 kafka-console-consumer.sh \
  --bootstrap-server localhost:9092 \
  --topic annotationtool-retry \
  --property print.timestamp=true &

# Add DLQ monitoring
docker exec dev-kafka-1 kafka-console-consumer.sh \
  --bootstrap-server localhost:9092 \
  --topic annotationtool-dlq \
  --property print.timestamp=true \
  --property print.key=true \
  --property print.headers=true \
  --from-beginning &

wait