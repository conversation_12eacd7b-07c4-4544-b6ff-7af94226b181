#!/bin/bash
set -e

# File watching can be controlled with the USE_FILE_WATCHING environment variable:
# - USE_FILE_WATCHING=true (default): Uses Air for live reload
# - USE_FILE_WATCHING=false: No file watching, standard go run
# 
# Usage examples:
#   ./run-local.sh                    # with file watching
#   USE_FILE_WATCHING=false ./run-local.sh   # without file watching

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Change to script directory (where docker-compose.yml is located)
cd "$SCRIPT_DIR"

# Cleanup any existing containers first
echo "Cleaning up existing containers..."
docker-compose down --remove-orphans

# Clean up volumes to ensure fresh start
echo "Cleaning up volumes..."
docker volume rm annotationtool_clickhouse_data annotationtool_mongodb_data annotationtool_redis_data 2>/dev/null || true

# Install all dependencies
echo "Installing all dependencies..."
go mod tidy

# Function to check and free port 2021
check_and_free_port() {
    local port=2021
    local pid=$(lsof -ti:$port 2>/dev/null)
    
    if [ ! -z "$pid" ]; then
        echo "Port $port is in use by process $pid. Killing it..."
        kill $pid 2>/dev/null || kill -9 $pid 2>/dev/null
        sleep 2
        
        # Verify port is now free
        local new_pid=$(lsof -ti:$port 2>/dev/null)
        if [ ! -z "$new_pid" ]; then
            echo "Warning: Could not free port $port (still used by process $new_pid)"
        else
            echo "Port $port is now free"
        fi
    else
        echo "Port $port is already free"
    fi
}

# Check and free port 2021 before starting the application
check_and_free_port

# Function to wait for container with timeout
wait_for_container() {
    local container=$1
    local check_command=$2
    local timeout=${3:-30}  # Default 30 seconds, but can be overridden
    local counter=0
    
    echo "Waiting for $container (timeout: ${timeout}s)..."
    while ! docker exec $container $check_command >/dev/null 2>&1; do
        counter=$((counter + 1))
        if [ $counter -ge $timeout ]; then
            echo "Error: Timeout waiting for $container to be ready after ${timeout} seconds"
            echo "Container logs:"
            docker logs $container
            echo "Cleaning up..."
            docker-compose down
            exit 1
        fi
        if [ "$container" = "annotationtool-clickhouse" ]; then
            # For ClickHouse, try a more basic health check first
            if docker exec $container clickhouse-client --query "SELECT 1" >/dev/null 2>&1; then
                echo "ClickHouse server is responding but database might not be ready..."
                break
            fi
            # Show initialization progress
            echo -n "ClickHouse init status: "
            docker logs --tail 1 $container 2>&1
        fi
        echo "$container is not ready yet... (${counter}/${timeout}s)"
        sleep 1
    done
    echo "$container is ready!"
}

# Start services
echo "Starting services..."
docker-compose up -d

# Wait for services with timeout (giving ClickHouse more time)
wait_for_container "annotationtool-clickhouse" "clickhouse-client --query \"SHOW DATABASES\"" 90
wait_for_container "annotationtool-mongodb" "mongosh --eval \"db.adminCommand('ping')\"" 30
wait_for_container "annotationtool-redis" "redis-cli ping" 30

# Ensure ClickHouse is really ready for migrations
echo "Ensuring ClickHouse is ready for migrations..."
for i in {1..5}; do
    if docker exec annotationtool-clickhouse clickhouse-client --query "SHOW DATABASES" | grep -q "annotationtool"; then
        break
    fi
    echo "Waiting for ClickHouse database 'annotationtool' to be available... (attempt $i/5)"
    sleep 2
done

echo "Services are up. Running migrations..."

# Run ClickHouse migrations
echo "Running ClickHouse migrations..."

docker exec -i annotationtool-clickhouse clickhouse-client --multiquery --echo --time --progress --log-level=information < migrations/0001_create_tables.sql

echo "Migration complete. Starting Dapr sidecar..."

# Function to check and install Dapr if needed
install_dapr_if_needed() {
    if ! command -v dapr &> /dev/null; then
        echo "Dapr CLI not found. Installing..."
        
        # Detect OS
        OS_NAME=$(uname -s)
        if [ "$OS_NAME" = "Linux" ]; then
            wget -q https://raw.githubusercontent.com/dapr/cli/master/install/install.sh -O - | /bin/bash
        elif [ "$OS_NAME" = "Darwin" ]; then
            brew install dapr/tap/dapr-cli
        else
            echo "Unsupported OS. Please install Dapr manually from https://docs.dapr.io/getting-started/install-dapr-cli/"
            exit 1
        fi
        
        # Initialize Dapr
        echo "Initializing Dapr runtime..."
        dapr init
        echo "Dapr installed and initialized successfully."
    fi
}

# Function to check if Dapr containers are running
check_dapr_containers() {
    echo "Checking Dapr infrastructure..."
    if ! docker ps | grep -q "dapr_placement" || ! docker ps | grep -q "dapr_zipkin"; then
        echo "Dapr containers are not running. Starting Dapr..."
        dapr init
    else
        echo "Dapr containers are running."
    fi
}

# Function to install Air if needed
install_air_if_needed() {
    # Get Go bin path
    GOBIN=$(go env GOPATH)/bin
    AIR_PATH="$GOBIN/air"
    
    # Add Go bin to PATH if not already there
    if [[ ":$PATH:" != *":$GOBIN:"* ]]; then
        export PATH="$GOBIN:$PATH"
        echo "Added $GOBIN to PATH"
    fi
    
    if ! command -v air &> /dev/null; then
        echo "Air (Go live reload) not found. Installing..."
        go install github.com/air-verse/air@latest
        
        # Verify installation
        if [ -f "$AIR_PATH" ]; then
            echo "Air installed successfully at $AIR_PATH"
        else
            echo "Error: Air installation failed"
            exit 1
        fi
    else
        echo "Air is already available: $(which air)"
    fi
}

# Call these functions before starting Dapr
install_dapr_if_needed
check_dapr_containers
install_air_if_needed

# Function to cleanup on exit
cleanup() {
    echo "Shutting down..."
    if [ ! -z "$DAPR_PID" ]; then
        echo "Stopping Dapr sidecar..."
        kill $DAPR_PID 2>/dev/null || true
        wait $DAPR_PID 2>/dev/null || true
    fi
    echo "Stopping Docker services..."
    docker-compose down
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Check and free port 2021 before starting the application
check_and_free_port

# Set annotation tool configuration
echo "🔧 Setting annotation tool configuration..."

# Trino configuration
export TRINO_HOST="trino.heartplus.ai"
export TRINO_PORT="443"
export TRINO_CATALOG="hive"
export TRINO_SCHEMA="default"
export TRINO_USER="admin"
export TRINO_PASSWORD="Ze6VDAnARtCCP"
export TRINO_TIMEOUT="30"

# MinIO configuration
export MINIO_ENDPOINT="minio.heartplus.ai:443"
export MINIO_ACCESS_KEY="devopsat"
export MINIO_SECRET_KEY="a9Y-S>HZn@O<"
export MINIO_BUCKET="gold"
export MINIO_USE_SSL="true"
export MINIO_ROOT_PREFIX="rnd"
export MINIO_DEFAULT_PAGE_SIZE="50"

# Debug configuration
export LOG_LEVEL="debug"
export ENVIRONMENT="development"

# Check if user wants file watching
USE_FILE_WATCHING=${USE_FILE_WATCHING:-true}

if [ "$USE_FILE_WATCHING" = "true" ]; then
    echo "Starting Dapr sidecar with Air (live reload)..."
    dapr run \
      --app-id annotationtool-microservice \
      --app-port 2021 \
      --dapr-http-port 3500 \
      --dapr-grpc-port 50001 \
      --resources-path ./components \
      --log-level info \
      --app-protocol http \
      -- air &
else
    echo "Starting Dapr sidecar (no file watching)..."
    dapr run \
      --app-id annotationtool-microservice \
      --app-port 2021 \
      --dapr-http-port 3500 \
      --dapr-grpc-port 50001 \
      --resources-path ./components \
      --log-level info \
      --app-protocol http \
      -- go run ./cmd/server/main.go &
fi

# Store the PID for cleanup
DAPR_PID=$!

echo "Dapr sidecar started with PID: $DAPR_PID"
if [ "$USE_FILE_WATCHING" = "true" ]; then
    echo "Application is running with file watching (Air)..."
    echo "Go files will be automatically recompiled when changed!"
else
    echo "Application is running..."
fi
echo "- Application: http://localhost:2021"
echo "- Dapr HTTP: http://localhost:3500"
echo "- ClickHouse HTTP: http://localhost:8124"
echo "- ClickHouse Native: localhost:9002"
echo "- MongoDB: localhost:27019"
echo "- Redis: localhost:6382"
echo "- Health check: http://localhost:2021/health"
echo "- Metrics: http://localhost:2021/metrics"
echo ""
echo "ClickHouse connection for DBeaver:"
echo "- Host: localhost"
echo "- Port: 9002 (native) or 8124 (HTTP)"
echo "- Database: annotationtool"
echo "- User: default"
echo "- Password: (empty)"
echo ""
echo "Press Ctrl+C to stop..."

# Wait for the process
wait $DAPR_PID 