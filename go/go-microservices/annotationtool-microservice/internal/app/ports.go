package app

import (
	"annotationtool-microservice/internal/domain"
	"context"
)

// EpisodeRepository defines the interface for episode persistence
type EpisodeRepository interface {
	GetEpisodes(ctx context.Context, query string) (*domain.EpisodeQueryResult, error)
	GetDashboardData(ctx context.Context, query string) (*domain.DashboardDataQueryResult, error)
}

// AnnotationRepository defines the interface for annotation persistence
type AnnotationRepository interface {
	CreateAnnotations(ctx context.Context, annotations []*domain.Annotation) error
	GetLatestAnnotationsByEpisode(ctx context.Context, episodeID string) ([]*domain.Annotation, error)
	GetLatestAnnotationsByStrip(ctx context.Context, stripID string) ([]*domain.Annotation, error)
}

// ReferenceDataRepository defines the interface for reference data access
type ReferenceDataRepository interface {
	GetClinics(ctx context.Context) ([]domain.Clinic, error)
	GetPatientsByClinic(ctx context.Context, clinicID string) ([]domain.Patient, error)
	GetLabellers(ctx context.Context) ([]domain.Labeller, error)
}

// EventPublisher defines the interface for publishing domain events
type EventPublisher interface {
	PublishEpisodeUpdated(ctx context.Context, episode *domain.Episode) error
	PublishAnnotationCreated(ctx context.Context, annotation *domain.Annotation) error
}

// DatasetRepository defines the interface for dataset storage access
type DatasetRepository interface {
	// MinIO-based methods (for file-based datasets)
	GetProjects(ctx context.Context, page, pageSize int) (*domain.ProjectSearchResult, error)
	GetProjectEnvironments(ctx context.Context, projectName string) ([]domain.DatasetEnvironment, error)
	GetDatasets(ctx context.Context, projectName string, env domain.DatasetEnvironment, page, pageSize int) (*domain.DatasetSearchResult, error)
	SearchDatasets(ctx context.Context, params domain.DatasetSearchParams) (*domain.DatasetSearchResult, error)
	GetDatasetVersions(ctx context.Context, projectName string, env domain.DatasetEnvironment, datasetName string) ([]domain.DatasetVersion, error)

	// Trino-based methods (for query-based datasets)
	ExecuteQuery(ctx context.Context, query *domain.DatasetQuery) (*domain.QueryResult, error)
	ExecuteRawSQL(ctx context.Context, sql string, parameters map[string]interface{}) (*domain.QueryResult, error)
	ListDatasets(ctx context.Context, catalog, schema string) ([]*domain.Dataset, error)
	GetDatasetMetadata(ctx context.Context, catalog, schema, tableName string) (*domain.DatasetMetadata, error)
	TestConnection(ctx context.Context) error

	Close() error
}

// NotebookRepository defines the interface for notebook operations
type NotebookRepository interface {
	GetNotebooks(ctx context.Context, nameSpace string) (*domain.NotebookSearchResult, error)
	CreateNotebook(ctx context.Context, nameSpace string, cmd CreateNotebookCommand) (*domain.Notebook, error)
	UpdateNotebook(ctx context.Context, nameSpace string, cmd UpdateNotebookCommand) (*domain.Notebook, error)
	DeleteNotebook(ctx context.Context, nameSpace, name string) error
	StartNotebook(ctx context.Context, nameSpace, name string) error
	StopNotebook(ctx context.Context, nameSpace, name string) error
	RestartNotebook(ctx context.Context, nameSpace, name string) error
	Close() error
}

// ECGServiceInterface defines the interface for ECG data operations
type ECGServiceInterface interface {
	QueryECGData(ctx context.Context, req domain.ECGQueryRequest) (*domain.ECGQueryResponse, error)
	GetConflictingEpisodes(ctx context.Context, req domain.ECGQueryRequest) (*domain.ECGQueryResponse, error)
	GetNumConflictingEpisodes(ctx context.Context, req domain.ECGNumConflictingEpisodesRequest) (*domain.ECGNumConflictingEpisodesResponse, error)
	GetECGAnalytics(ctx context.Context, req domain.ECGAnalyticsRequest) (*domain.ECGAnalyticsResponse, error)
	GetEpisodeDetails(ctx context.Context, episodeID string, catalog string) (*domain.ECGEpisodeSummary, error)
	GetClinics(ctx context.Context, req domain.ECGClinicRequest) ([]domain.Clinic, error)
	GetPatients(ctx context.Context, req domain.ECGPatientRequest) ([]domain.Patient, error)
	GetManufacturers(ctx context.Context, req domain.ECGManufacturerRequest) ([]string, error)
	GetDeviceModels(ctx context.Context, req domain.ECGDeviceModelRequest) ([]string, error)
	GetECGDashboard(ctx context.Context, req domain.ECGDashboardRequest, rawQueryParams string) (*domain.ECGDashboardResponse, error)
}

// ClickHouseOperationRepository defines the interface for ClickHouse operation storage
type ClickHouseOperationRepository interface {
	CreateOperation(ctx context.Context, operation *domain.Operation) error
	CreateDataset(ctx context.Context, datasetPath string, tableName string) error
	// Stats
	GetDatasetStats(ctx context.Context, datasetID string) (*domain.DatasetStats, error)
	Close() error
}

// EpisodeFilters represents filters for episode queries
type EpisodeFilters struct {
	ProjectID       string
	ClinicID        string
	PatientID       string
	Manufacturer    string
	LabelledBy      string
	NotLabelledBy   string
	LabellingStatus string
	DateFrom        string
	DateTo          string
	InterestingOnly bool
	SortBy          string
	SortOrder       string
	Page            int
	Limit           int
}

// PublishingService defines the interface for publishing messages
// Currently used for sending CQRS commands to kafka to be executed asynchronously.
type Publisher interface {
	PublishCommand(ctx context.Context, cmd any) error
}
