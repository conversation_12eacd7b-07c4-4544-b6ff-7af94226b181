package app

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"annotationtool-microservice/internal/domain"
)

// DatasetService implements the business logic for dataset operations
type DatasetService struct {
	datasetRepo    DatasetRepository
	clickhouseRepo ClickHouseOperationRepository
	logger         *slog.Logger
}

// NewDatasetService creates a new DatasetService
func NewDatasetService(datasetRepo DatasetRepository, clickhouseRepo ClickHouseOperationRepository, logger *slog.Logger) *DatasetService {
	return &DatasetService{
		datasetRepo:    datasetRepo,
		clickhouseRepo: clickhouseRepo,
		logger:         logger,
	}
}

// ExecuteQuery executes a dataset query with validation and error handling
func (s *DatasetService) ExecuteQuery(ctx context.Context, cmd ExecuteDatasetQueryCommand) (*domain.DatasetQueryResponse, error) {
	s.logger.Info("Executing dataset query",
		"dataset_name", cmd.DatasetName,
		"requested_by", cmd.RequestedBy,
		"has_sql", cmd.SQL != "",
	)

	// Validate the command
	if err := cmd.Validate(); err != nil {
		s.logger.Warn("Invalid dataset query command", "error", err)
		return nil, NewValidationError("Invalid query parameters", err)
	}

	// Convert command to domain query
	query := s.buildDatasetQuery(cmd)

	// Execute the query
	startTime := time.Now()
	result, err := s.datasetRepo.ExecuteQuery(ctx, query)
	if err != nil {
		s.logger.Error("Failed to execute dataset query",
			"dataset_name", cmd.DatasetName,
			"error", err,
		)
		return nil, NewInternalError("Failed to execute query", err)
	}

	executionTime := time.Since(startTime)
	s.logger.Info("Dataset query executed successfully",
		"dataset_name", cmd.DatasetName,
		"rows_returned", len(result.Rows),
		"execution_time_ms", executionTime.Milliseconds(),
	)

	// Convert result to response
	response := s.buildQueryResponse(cmd, result)
	return response, nil
}

// ExecuteRawSQL executes raw SQL with validation and error handling
func (s *DatasetService) ExecuteRawSQL(ctx context.Context, cmd ExecuteRawSQLCommand) (*domain.DatasetQueryResponse, error) {
	s.logger.Info("Executing raw SQL",
		"requested_by", cmd.RequestedBy,
		"sql_length", len(cmd.SQL),
	)

	// Validate the command
	if err := cmd.Validate(); err != nil {
		s.logger.Warn("Invalid raw SQL command", "error", err)
		return nil, NewValidationError("Invalid SQL parameters", err)
	}

	// Execute the raw SQL
	startTime := time.Now()
	result, err := s.datasetRepo.ExecuteRawSQL(ctx, cmd.SQL, cmd.Parameters)
	if err != nil {
		s.logger.Error("Failed to execute raw SQL",
			"sql", cmd.SQL,
			"error", err,
		)
		return nil, NewInternalError("Failed to execute SQL", err)
	}

	executionTime := time.Since(startTime)
	s.logger.Info("Raw SQL executed successfully",
		"rows_returned", len(result.Rows),
		"execution_time_ms", executionTime.Milliseconds(),
	)

	// Convert result to response
	response := &domain.DatasetQueryResponse{
		QueryID:       result.QueryID,
		DatasetName:   "raw_sql",
		Columns:       result.Columns,
		Rows:          result.Rows,
		TotalRows:     result.TotalRows,
		ReturnedRows:  len(result.Rows),
		ExecutionTime: result.ExecutionTime.Milliseconds(),
		Pagination: domain.PaginationInfo{
			Page:       1,
			Limit:      len(result.Rows),
			Offset:     0,
			TotalRows:  result.TotalRows,
			TotalPages: 1,
			HasNext:    false,
			HasPrev:    false,
		},
		Timestamp: time.Now(),
	}

	return response, nil
}

// ListDatasets lists available datasets with filtering and pagination
func (s *DatasetService) ListDatasets(ctx context.Context, query ListDatasetsQuery) (*domain.DatasetListResponse, error) {
	s.logger.Info("Listing datasets",
		"catalog", query.Catalog,
		"schema", query.Schema,
		"layer", query.Layer,
	)

	// Set defaults
	if query.Catalog == "" {
		query.Catalog = "minio" // Default catalog
	}
	if query.Schema == "" {
		query.Schema = "default" // Default schema
	}
	if query.Limit <= 0 {
		query.Limit = 50
	}
	if query.Page <= 0 {
		query.Page = 1
	}

	// Get datasets from repository
	datasets, err := s.datasetRepo.ListDatasets(ctx, query.Catalog, query.Schema)
	if err != nil {
		s.logger.Error("Failed to list datasets",
			"catalog", query.Catalog,
			"schema", query.Schema,
			"error", err,
		)
		return nil, NewInternalError("Failed to list datasets", err)
	}

	// Apply filtering
	filteredDatasets := s.filterDatasets(datasets, query)

	// Apply pagination
	totalRows := int64(len(filteredDatasets))
	offset := (query.Page - 1) * query.Limit
	end := offset + query.Limit
	if end > len(filteredDatasets) {
		end = len(filteredDatasets)
	}
	if offset > len(filteredDatasets) {
		offset = len(filteredDatasets)
	}

	paginatedDatasets := filteredDatasets[offset:end]

	// Convert to summary format
	summaries := make([]domain.DatasetSummary, len(paginatedDatasets))
	for i, dataset := range paginatedDatasets {
		summaries[i] = domain.DatasetSummary{
			ID:          dataset.ID,
			Name:        dataset.Name,
			Schema:      dataset.Schema,
			Catalog:     dataset.Catalog,
			TableName:   dataset.TableName,
			Layer:       dataset.Layer,
			Description: dataset.Description,
			CreatedAt:   dataset.CreatedAt,
			UpdatedAt:   dataset.UpdatedAt,
		}
	}

	// Build pagination info
	pagination := domain.NewPaginationInfo(query.Page, query.Limit, offset, totalRows)

	response := &domain.DatasetListResponse{
		Datasets:   summaries,
		Pagination: pagination,
		Timestamp:  time.Now(),
	}

	s.logger.Info("Listed datasets successfully",
		"total_datasets", totalRows,
		"returned_datasets", len(summaries),
	)

	return response, nil
}

// GetDatasetMetadata retrieves metadata for a specific dataset
func (s *DatasetService) GetDatasetMetadata(ctx context.Context, query GetDatasetMetadataQuery) (*domain.DatasetMetadataResponse, error) {
	s.logger.Info("Getting dataset metadata",
		"catalog", query.Catalog,
		"schema", query.Schema,
		"table", query.TableName,
	)

	// Get metadata from repository
	metadata, err := s.datasetRepo.GetDatasetMetadata(ctx, query.Catalog, query.Schema, query.TableName)
	if err != nil {
		s.logger.Error("Failed to get dataset metadata",
			"catalog", query.Catalog,
			"schema", query.Schema,
			"table", query.TableName,
			"error", err,
		)
		return nil, NewInternalError("Failed to get dataset metadata", err)
	}

	// Create dataset summary
	summary := domain.DatasetSummary{
		ID:          fmt.Sprintf("%s.%s.%s", query.Catalog, query.Schema, query.TableName),
		Name:        query.TableName,
		Schema:      query.Schema,
		Catalog:     query.Catalog,
		TableName:   query.TableName,
		Layer:       s.determineLayer(query.Schema, query.TableName),
		Description: fmt.Sprintf("Table %s in %s.%s", query.TableName, query.Catalog, query.Schema),
		RowCount:    metadata.RowCount,
		SizeBytes:   metadata.SizeBytes,
		CreatedAt:   metadata.LastModified,
		UpdatedAt:   metadata.LastModified,
	}

	response := &domain.DatasetMetadataResponse{
		Dataset:   summary,
		Metadata:  *metadata,
		Timestamp: time.Now(),
	}

	s.logger.Info("Retrieved dataset metadata successfully",
		"table", query.TableName,
		"columns", len(metadata.Columns),
	)

	return response, nil
}

// TestConnection tests the connection to the Trino cluster
func (s *DatasetService) TestConnection(ctx context.Context) error {
	s.logger.Info("Testing Trino connection")

	err := s.datasetRepo.TestConnection(ctx)
	if err != nil {
		s.logger.Error("Trino connection test failed", "error", err)
		return NewInternalError("Connection test failed", err)
	}

	s.logger.Info("Trino connection test successful")
	return nil
}

// Helper methods

// buildDatasetQuery builds a domain DatasetQuery from the command
func (s *DatasetService) buildDatasetQuery(cmd ExecuteDatasetQueryCommand) *domain.DatasetQuery {
	query := domain.NewDatasetQuery("", cmd.SQL, cmd.RequestedBy)
	query.Parameters = cmd.Parameters
	query.Limit = cmd.Limit
	query.Offset = cmd.Offset
	query.Columns = cmd.Columns
	query.OrderBy = cmd.OrderBy

	// Convert filters
	query.Filters = make(map[string]string)
	for _, filter := range cmd.Filters {
		query.Filters[filter.Column] = fmt.Sprintf("%s:%v", filter.Operator, filter.Value)
	}

	// If no SQL provided, build a basic SELECT query
	if cmd.SQL == "" {
		query.SQL = s.buildSelectQuery(cmd)
	}

	return query
}

// buildSelectQuery builds a basic SELECT query from command parameters
func (s *DatasetService) buildSelectQuery(cmd ExecuteDatasetQueryCommand) string {
	// Start with basic SELECT
	columns := "*"
	if len(cmd.Columns) > 0 {
		columns = strings.Join(cmd.Columns, ", ")
	}

	sql := fmt.Sprintf("SELECT %s FROM %s", columns, cmd.DatasetName)

	// Add WHERE conditions from filters
	if len(cmd.Filters) > 0 {
		var conditions []string
		for _, filter := range cmd.Filters {
			condition := s.buildFilterCondition(filter)
			if condition != "" {
				conditions = append(conditions, condition)
			}
		}
		if len(conditions) > 0 {
			sql += " WHERE " + strings.Join(conditions, " AND ")
		}
	}

	// Add ORDER BY
	if cmd.OrderBy != "" {
		sql += " ORDER BY " + cmd.OrderBy
	}

	// Add LIMIT and OFFSET
	if cmd.Limit > 0 {
		sql += fmt.Sprintf(" LIMIT %d", cmd.Limit)
	}
	if cmd.Offset > 0 {
		sql += fmt.Sprintf(" OFFSET %d", cmd.Offset)
	}

	return sql
}

// buildFilterCondition builds a SQL condition from a query filter
func (s *DatasetService) buildFilterCondition(filter domain.QueryFilter) string {
	// Basic SQL injection protection - sanitize column name
	column := strings.ReplaceAll(filter.Column, "'", "")
	column = strings.ReplaceAll(column, "\"", "")
	column = strings.ReplaceAll(column, ";", "")

	switch filter.Operator {
	case "=", "!=", ">", "<", ">=", "<=":
		return fmt.Sprintf("%s %s '%v'", column, filter.Operator, filter.Value)
	case "LIKE":
		return fmt.Sprintf("%s LIKE '%v'", column, filter.Value)
	case "IN":
		// Handle IN operator - value should be a slice
		if values, ok := filter.Value.([]interface{}); ok {
			var valueStrings []string
			for _, v := range values {
				valueStrings = append(valueStrings, fmt.Sprintf("'%v'", v))
			}
			return fmt.Sprintf("%s IN (%s)", column, strings.Join(valueStrings, ", "))
		}
		return fmt.Sprintf("%s IN ('%v')", column, filter.Value)
	case "NOT IN":
		// Handle NOT IN operator
		if values, ok := filter.Value.([]interface{}); ok {
			var valueStrings []string
			for _, v := range values {
				valueStrings = append(valueStrings, fmt.Sprintf("'%v'", v))
			}
			return fmt.Sprintf("%s NOT IN (%s)", column, strings.Join(valueStrings, ", "))
		}
		return fmt.Sprintf("%s NOT IN ('%v')", column, filter.Value)
	default:
		s.logger.Warn("Unsupported filter operator", "operator", filter.Operator)
		return ""
	}
}

// buildQueryResponse builds a DatasetQueryResponse from command and result
func (s *DatasetService) buildQueryResponse(cmd ExecuteDatasetQueryCommand, result *domain.QueryResult) *domain.DatasetQueryResponse {
	// Calculate pagination
	page := cmd.Page
	if page <= 0 {
		page = 1
	}
	limit := cmd.Limit
	if limit <= 0 {
		limit = 100
	}
	offset := cmd.Offset
	if offset < 0 {
		offset = 0
	}

	pagination := domain.NewPaginationInfo(page, limit, offset, result.TotalRows)

	return &domain.DatasetQueryResponse{
		QueryID:       result.QueryID,
		DatasetName:   cmd.DatasetName,
		Columns:       result.Columns,
		Rows:          result.Rows,
		TotalRows:     result.TotalRows,
		ReturnedRows:  len(result.Rows),
		ExecutionTime: result.ExecutionTime.Milliseconds(),
		Pagination:    pagination,
		Timestamp:     time.Now(),
	}
}

// filterDatasets applies filtering to the dataset list
func (s *DatasetService) filterDatasets(datasets []*domain.Dataset, query ListDatasetsQuery) []*domain.Dataset {
	var filtered []*domain.Dataset

	for _, dataset := range datasets {
		// Apply layer filter
		if query.Layer != "" && dataset.Layer != query.Layer {
			continue
		}

		// Apply search filter
		if query.Search != "" {
			searchLower := strings.ToLower(query.Search)
			if !strings.Contains(strings.ToLower(dataset.Name), searchLower) &&
				!strings.Contains(strings.ToLower(dataset.Description), searchLower) {
				continue
			}
		}

		filtered = append(filtered, dataset)
	}

	// Apply sorting
	// For now, we'll keep the default order from the repository
	// In a more complete implementation, we would sort based on query.OrderBy and query.OrderDir

	return filtered
}

// determineLayer determines the data layer based on schema or table name patterns
func (s *DatasetService) determineLayer(schema, tableName string) string {
	schemaLower := strings.ToLower(schema)
	tableNameLower := strings.ToLower(tableName)

	// Check schema patterns
	if strings.Contains(schemaLower, "bronze") {
		return "bronze"
	}
	if strings.Contains(schemaLower, "silver") {
		return "silver"
	}
	if strings.Contains(schemaLower, "gold") {
		return "gold"
	}

	// Check table name patterns
	if strings.Contains(tableNameLower, "bronze") || strings.Contains(tableNameLower, "raw") {
		return "bronze"
	}
	if strings.Contains(tableNameLower, "silver") || strings.Contains(tableNameLower, "processed") {
		return "silver"
	}
	if strings.Contains(tableNameLower, "gold") || strings.Contains(tableNameLower, "curated") {
		return "gold"
	}

	// Default to bronze if no pattern matches
	return "bronze"
}

// Validate validates the execute dataset query command
func (cmd *ExecuteDatasetQueryCommand) Validate() error {
	if cmd.DatasetName == "" {
		return NewValidationError("dataset_name is required", nil)
	}

	if cmd.RequestedBy == "" {
		return NewValidationError("requested_by is required", nil)
	}

	// Set defaults
	if cmd.Limit <= 0 {
		cmd.Limit = 100
	}
	if cmd.Limit > 1000 {
		return NewValidationError("limit cannot exceed 1000", nil)
	}

	if cmd.Offset < 0 {
		cmd.Offset = 0
	}

	if cmd.Page <= 0 {
		cmd.Page = 1
	}

	// Validate filters
	for _, filter := range cmd.Filters {
		if !filter.IsValidOperator() {
			return NewValidationError("invalid filter operator: "+filter.Operator, nil)
		}
		if filter.Column == "" {
			return NewValidationError("filter column cannot be empty", nil)
		}
	}

	return nil
}

// Validate validates the execute raw SQL command
func (cmd *ExecuteRawSQLCommand) Validate() error {
	if cmd.SQL == "" {
		return NewValidationError("sql is required", nil)
	}

	if cmd.RequestedBy == "" {
		return NewValidationError("requested_by is required", nil)
	}

	// Basic SQL validation - check for dangerous operations
	if containsDangerousSQL(cmd.SQL) {
		return NewValidationError("SQL contains potentially dangerous operations", nil)
	}

	return nil
}

// Helper function to check for dangerous SQL operations
func containsDangerousSQL(sql string) bool {
	dangerousKeywords := []string{
		"DROP", "DELETE", "INSERT", "UPDATE", "CREATE", "ALTER",
		"TRUNCATE", "GRANT", "REVOKE", "EXEC", "EXECUTE",
	}

	sqlUpper := strings.ToUpper(sql)
	for _, keyword := range dangerousKeywords {
		if strings.Contains(sqlUpper, keyword) {
			return true
		}
	}
	return false
}

// CreateDataset creates a new dataset (including its initial version) by
// 1. Ensuring the project schema exists in Trino
// 2. Creating the table for the provided version pointing to the correct object-storage location
// 3. (optional) Creating the folder in MinIO so that the location exists
//
// NOTE: This is a simplified implementation which focuses on the Trino side only. Folder creation
// is delegated to Trino's LOCATION clause. For production use you might want to explicitly create
// the folder or upload a _SUCCESS marker file in MinIO.
func (s *DatasetService) CreateDataset(ctx context.Context, cmd CreateDatasetCommand) error {
	s.logger.Info("Creating new dataset", "project", cmd.ProjectName, "env", cmd.Environment, "dataset", cmd.DatasetName, "version", cmd.Version, "requested_by", cmd.RequestedBy)

	// Basic validation
	if cmd.DatasetName == "" || cmd.Version == "" || cmd.RequestedBy == "" {
		return NewValidationError("dataset_name, version and requested_by are required", nil)
	}

	// Build schema and table names – replace hyphens with underscores for Trino compatibility
	projectSanitized := strings.ReplaceAll(cmd.ProjectName, "-", "_")
	envSanitized := strings.ReplaceAll(string(cmd.Environment), "-", "_")
	schemaName := fmt.Sprintf("rnd_%s_%s", projectSanitized, envSanitized)

	datasetSanitized := strings.ReplaceAll(cmd.DatasetName, "-", "_")
	tableName := fmt.Sprintf("%s_%s", datasetSanitized, cmd.Version)

	// Step 1: create schema if it does not exist
	schemaSQL := fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schemaName)
	if _, err := s.datasetRepo.ExecuteRawSQL(ctx, schemaSQL, nil); err != nil {
		s.logger.Error("Failed to create schema", "schema", schemaName, "error", err)
		return err
	}

	// Build column definition list
	columnsDef := "id INT, data STRING" // default
	if len(cmd.Columns) > 0 {
		var parts []string
		for _, col := range cmd.Columns {
			parts = append(parts, fmt.Sprintf("%s %s", col.Name, col.Type))
		}
		columnsDef = strings.Join(parts, ", ")
	}

	// Build LOCATION path – this is a best-guess based on MinIO conventions.
	// In practice you might want to generate this differently or read from config.
	location := fmt.Sprintf("s3://rnd-%s-data/%s/%s/%s/", strings.ToLower(string(cmd.Environment)), cmd.ProjectName, cmd.DatasetName, cmd.Version)

	// Step 2: create table for initial version
	// NOTE: We partition by year and month to follow the conventions described in the requirements.
	tableSQL := fmt.Sprintf(`CREATE TABLE IF NOT EXISTS %s.%s (
		%s
	)
	PARTITIONED BY (year STRING, month STRING)
	STORED AS PARQUET
	LOCATION '%s'`, schemaName, tableName, columnsDef, location)

	if _, err := s.datasetRepo.ExecuteRawSQL(ctx, tableSQL, nil); err != nil {
		s.logger.Error("Failed to create table", "table", tableName, "error", err)
		return err
	}

	s.logger.Info("Dataset created successfully", "schema", schemaName, "table", tableName)
	return nil
}

// CreateDatasetVersion creates a new version (table) for an existing dataset.
func (s *DatasetService) CreateDatasetVersion(ctx context.Context, cmd CreateDatasetVersionCommand) error {
	s.logger.Info("Creating new dataset version", "project", cmd.ProjectName, "env", cmd.Environment, "dataset", cmd.DatasetName, "version", cmd.Version, "requested_by", cmd.RequestedBy)

	// Validation
	if cmd.Version == "" || cmd.RequestedBy == "" {
		return NewValidationError("version and requested_by are required", nil)
	}

	projectSanitized := strings.ReplaceAll(cmd.ProjectName, "-", "_")
	envSanitized := strings.ReplaceAll(string(cmd.Environment), "-", "_")
	schemaName := fmt.Sprintf("rnd_%s_%s", projectSanitized, envSanitized)

	datasetSanitized := strings.ReplaceAll(cmd.DatasetName, "-", "_")
	tableName := fmt.Sprintf("%s_%s", datasetSanitized, cmd.Version)

	// Build column definition – re-use cmd.Columns if provided, else fallback to default
	columnsDef := "id INT, data STRING"
	if len(cmd.Columns) > 0 {
		var parts []string
		for _, col := range cmd.Columns {
			parts = append(parts, fmt.Sprintf("%s %s", col.Name, col.Type))
		}
		columnsDef = strings.Join(parts, ", ")
	}

	location := fmt.Sprintf("s3://rnd-%s-data/%s/%s/%s/", strings.ToLower(string(cmd.Environment)), cmd.ProjectName, cmd.DatasetName, cmd.Version)

	createTableSQL := fmt.Sprintf(`CREATE TABLE IF NOT EXISTS %s.%s (
		%s
	)
	PARTITIONED BY (year STRING, month STRING)
	STORED AS PARQUET
	LOCATION '%s'`, schemaName, tableName, columnsDef, location)

	if _, err := s.datasetRepo.ExecuteRawSQL(ctx, createTableSQL, nil); err != nil {
		s.logger.Error("Failed to create dataset version table", "table", tableName, "error", err)
		return err
	}

	s.logger.Info("Dataset version created successfully", "schema", schemaName, "table", tableName)
	return nil
}

// MinIO-related methods (delegated to repository)

// GetProjects retrieves all projects with pagination
func (s *DatasetService) GetProjects(ctx context.Context, page, pageSize int) (*domain.ProjectSearchResult, error) {
	s.logger.Info("Getting projects", "page", page, "page_size", pageSize)

	return s.datasetRepo.GetProjects(ctx, page, pageSize)
}

// GetProjectEnvironments retrieves all environments for a specific project
func (s *DatasetService) GetProjectEnvironments(ctx context.Context, projectName string) ([]domain.DatasetEnvironment, error) {
	s.logger.Info("Getting project environments", "project", projectName)

	return s.datasetRepo.GetProjectEnvironments(ctx, projectName)
}

// GetDatasets retrieves datasets for a specific project and environment
func (s *DatasetService) GetDatasets(ctx context.Context, projectName string, env domain.DatasetEnvironment, page, pageSize int) (*domain.DatasetSearchResult, error) {
	s.logger.Info("Getting datasets", "project", projectName, "environment", env, "page", page, "page_size", pageSize)

	return s.datasetRepo.GetDatasets(ctx, projectName, env, page, pageSize)
}

// SearchDatasets searches for datasets based on parameters
func (s *DatasetService) SearchDatasets(ctx context.Context, params domain.DatasetSearchParams) (*domain.DatasetSearchResult, error) {
	s.logger.Info("Searching datasets", "params", params)

	return s.datasetRepo.SearchDatasets(ctx, params)
}

// GetDatasetVersions retrieves all versions for a specific dataset
func (s *DatasetService) GetDatasetVersions(ctx context.Context, projectName string, env domain.DatasetEnvironment, datasetName string) ([]domain.DatasetVersion, error) {
	s.logger.Info("Getting dataset versions", "project", projectName, "environment", env, "dataset", datasetName)

	return s.datasetRepo.GetDatasetVersions(ctx, projectName, env, datasetName)
}

// GetDatasetByPath retrieves a dataset by project, environment, and dataset name
func (s *DatasetService) GetDatasetByPath(ctx context.Context, projectName string, env domain.DatasetEnvironment, datasetName string) (*domain.Dataset, error) {
	s.logger.Info("Getting dataset by path", "project", projectName, "environment", env, "dataset", datasetName)

	// Get the dataset versions first
	versions, err := s.datasetRepo.GetDatasetVersions(ctx, projectName, env, datasetName)
	if err != nil {
		return nil, err
	}

	if len(versions) == 0 {
		return nil, NewNotFoundError("Dataset not found", nil)
	}

	// Create a dataset object with the latest version
	latestVersion := &versions[0]
	for i := range versions {
		if versions[i].LastModified.After(latestVersion.LastModified) {
			latestVersion = &versions[i]
		}
	}

	dataset := &domain.Dataset{
		ID:            fmt.Sprintf("%s|%s|%s", projectName, env, datasetName),
		Name:          datasetName,
		Project:       projectName,
		Environment:   env,
		Versions:      versions,
		Path:          fmt.Sprintf("%s/%s/%s", projectName, env, datasetName),
		LatestVersion: latestVersion,
		Description:   fmt.Sprintf("Dataset %s in project %s (%s)", datasetName, projectName, env),
		CreatedAt:     latestVersion.LastModified,
		UpdatedAt:     latestVersion.LastModified,
	}

	return dataset, nil
}

func (s *DatasetService) ImportDatasetToClickhouse(ctx context.Context, datasetPath string, tableName string) error {
	s.logger.Info("Starting async dataset import to clickhouse", "dataset", datasetPath, "table", tableName)

	// Start the import operation asynchronously to avoid HTTP timeout
	go func() {
		// Create a new context with extended timeout for large dataset imports
		// Use 30 minutes timeout for dataset import operations
		importCtx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
		defer cancel()

		s.logger.Info("Beginning dataset import process", "dataset", datasetPath, "table", tableName)

		err := s.clickhouseRepo.CreateDataset(importCtx, datasetPath, tableName)
		if err != nil {
			s.logger.Error("Failed to import dataset to clickhouse", "error", err, "dataset", datasetPath, "table", tableName)
		} else {
			s.logger.Info("Successfully completed dataset import to clickhouse", "dataset", datasetPath, "table", tableName)
		}
	}()

	// Return immediately - the import is running in the background
	s.logger.Info("Dataset import initiated successfully - running in background", "dataset", datasetPath, "table", tableName)
	return nil
}

// GetDatasetStats retrieves aggregated stats for a dataset (catalog) from ClickHouse
func (s *DatasetService) GetDatasetStats(ctx context.Context, catalog string) (*domain.DatasetStats, error) {
	if strings.TrimSpace(catalog) == "" {
		return nil, NewValidationError("Invalid catalog", fmt.Errorf("catalog is required"))
	}
	stats, err := s.clickhouseRepo.GetDatasetStats(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get dataset stats", "catalog", catalog, "error", err)
		return nil, NewInternalError("Failed to get dataset stats", err)
	}
	return stats, nil
}
