package app

// GetEpisodesQuery represents a query for getting episodes with filters
type GetEpisodesQuery struct {
	ProjectID       string `form:"project_id"`
	ClinicID        string `form:"clinic_id"`
	PatientID       string `form:"patient_id"`
	Manufacturer    string `form:"manufacturer"`
	LabelledBy      string `form:"labelled_by"`
	NotLabelledBy   string `form:"not_labelled_by"`
	LabellingStatus string `form:"labelling_status"`
	DateFrom        string `form:"date_from"`
	DateTo          string `form:"date_to"`
	InterestingOnly bool   `form:"interesting_only"`
	SortBy          string `form:"sort_by"`
	SortOrder       string `form:"sort_order"`
	Page            int    `form:"page"`
	Limit           int    `form:"limit"`
}

// GetEpisodeByIDQuery represents a query for getting a specific episode
type GetEpisodeByIDQuery struct {
	EpisodeID string `uri:"episode_id" binding:"required"`
}

// GetClinicsQuery represents a query for getting all clinics
type GetClinicsQuery struct{}

// GetPatientsByClinicQuery represents a query for getting patients by clinic
type GetPatientsByClinicQuery struct {
	ClinicID string `uri:"clinic_id" binding:"required"`
}

// GetLabellersQuery represents a query for getting all labellers
type GetLabellersQuery struct{}

// ToFilters converts GetEpisodesQuery to EpisodeFilters
func (q GetEpisodesQuery) ToFilters() EpisodeFilters {
	// Set defaults
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.Limit <= 0 {
		q.Limit = 10
	}
	if q.SortBy == "" {
		q.SortBy = "report_time"
	}
	if q.SortOrder == "" {
		q.SortOrder = "desc"
	}

	return EpisodeFilters{
		ProjectID:       q.ProjectID,
		ClinicID:        q.ClinicID,
		PatientID:       q.PatientID,
		Manufacturer:    q.Manufacturer,
		LabelledBy:      q.LabelledBy,
		NotLabelledBy:   q.NotLabelledBy,
		LabellingStatus: q.LabellingStatus,
		DateFrom:        q.DateFrom,
		DateTo:          q.DateTo,
		InterestingOnly: q.InterestingOnly,
		SortBy:          q.SortBy,
		SortOrder:       q.SortOrder,
		Page:            q.Page,
		Limit:           q.Limit,
	}
}

// Dataset-related queries

// ListDatasetsQuery represents a query for listing datasets
type ListDatasetsQuery struct {
	Layer    string `form:"layer,omitempty"`   // bronze, silver, gold
	Schema   string `form:"schema,omitempty"`  // schema filter
	Catalog  string `form:"catalog,omitempty"` // catalog filter
	Search   string `form:"search,omitempty"`  // search in name/description
	Limit    int    `form:"limit,omitempty"`
	Offset   int    `form:"offset,omitempty"`
	Page     int    `form:"page,omitempty"`
	OrderBy  string `form:"order_by,omitempty"`  // name, created_at, updated_at
	OrderDir string `form:"order_dir,omitempty"` // asc, desc
}

// GetDatasetMetadataQuery represents a query for getting dataset metadata
type GetDatasetMetadataQuery struct {
	Catalog   string `uri:"catalog" binding:"required"`
	Schema    string `uri:"schema" binding:"required"`
	TableName string `uri:"table_name" binding:"required"`
}

// GetNotebooksQuery represents a query for getting notebooks
type GetNotebooksQuery struct {
	NameSpace string `uri:"nameSpace" binding:"required"`
}