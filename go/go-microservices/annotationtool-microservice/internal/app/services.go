package app

import (
	"context"
	"log/slog"

	"annotationtool-microservice/internal/domain"
)

// AnnotationService implements the business logic for the annotation tool
type AnnotationService struct {
	episodeRepo    EpisodeRepository
	annotationRepo AnnotationRepository
	refDataRepo    ReferenceDataRepository
	eventPublisher EventPublisher
	logger         *slog.Logger
}

// NewAnnotationService creates a new AnnotationService
func NewAnnotationService(
	episodeRepo EpisodeRepository,
	annotationRepo AnnotationRepository,
	refDataRepo ReferenceDataRepository,
	eventPublisher EventPublisher,
	logger *slog.Logger,
) *AnnotationService {
	return &AnnotationService{
		episodeRepo:    episodeRepo,
		annotationRepo: annotationRepo,
		refDataRepo:    refDataRepo,
		eventPublisher: eventPublisher,
		logger:         logger,
	}
}

// GetEpisodes retrieves episodes with filtering and pagination
func (s *AnnotationService) GetEpisodes(ctx context.Context, query string) (*domain.EpisodeQueryResult, error) {
	result, err := s.episodeRepo.GetEpisodes(ctx, query)
	if err != nil {
		s.logger.Error("Failed to get episodes", "error", err)
		return nil, NewInternalError("Failed to retrieve episodes", err)
	}

	return result, nil
}
