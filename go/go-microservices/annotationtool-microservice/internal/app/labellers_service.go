package app

import (
	"context"
	"log/slog"
	"sort"

	"annotationtool-microservice/internal/domain"
)

// LabellersService implements the business logic for labeller handling
type LabellersService struct {
	labellersRepo  LabellersRepository
	eventPublisher EventPublisher
	logger         *slog.Logger
}

// NewLabellersService creates a new LabellersService
func NewLabellersService(
	labellersRepo LabellersRepository,
	eventPublisher EventPublisher,
	logger *slog.Logger,
) *LabellersService {
	return &LabellersService{
		labellersRepo:  labellersRepo,
		eventPublisher: eventPublisher,
		logger:         logger,
	}
}

// GetLabellers gets all labellers
func (s *LabellersService) GetLabellers(ctx context.Context, catalog string) ([]domain.Labeller, error) {
	labellers, err := s.labellersRepo.GetLabellers(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get labellers", "error", err)
		return nil, NewInternalError("Failed to get labellers", err)
	}

	return labellers, nil
}

// GetLabelledEpisodeIds gets all distinct episode ids for a labeller
func (s *LabellersService) GetLabelledEpisodeIds(ctx context.Context, catalog string, labels, userIds []string, maxLabellingTime *string) ([]string, error) {
	episodeIds, err := s.labellersRepo.GetLabelledEpisodeIds(ctx, catalog, labels, userIds, maxLabellingTime)
	if err != nil {
		s.logger.Error("Failed to get labelled episode ids", "error", err)
		return nil, NewInternalError("Failed to get labelled episode ids", err)
	}

	return episodeIds, nil
}

// GetLabelledStripIds gets all distinct strip ids for a labeller
func (s *LabellersService) GetLabelledStripIds(ctx context.Context, catalog string) ([]string, error) {
	stripIds, err := s.labellersRepo.GetLabelledStripIds(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get labelled strip ids", "error", err)
		return nil, NewInternalError("Failed to get labelled strip ids", err)
	}

	return stripIds, nil
}

// GetAnnotationsByEpisodeIDs gets all annotation entries that match any of the provided episode IDs
func (s *LabellersService) GetAnnotationsByEpisodeIDs(ctx context.Context, episodeIDs []string, catalog string) ([]domain.AnnotationEntry, error) {
	annotations, err := s.labellersRepo.GetAnnotationsByEpisodeIDs(ctx, episodeIDs, catalog)
	if err != nil {
		s.logger.Error("Failed to get annotations by episode IDs", "error", err)
		return nil, NewInternalError("Failed to get annotations by episode IDs", err)
	}

	return annotations, nil
}

// GetAnnotationsByUserIDs gets all annotation entries that match any of the provided user IDs
func (s *LabellersService) GetEpisodeIdsAnnotatedByUserIDs(ctx context.Context, userIDs []string, catalog string) ([]string, error) {
	episodeIds, err := s.labellersRepo.GetEpisodeIdsAnnotatedByUserIDs(ctx, userIDs, catalog)
	if err != nil {
		s.logger.Error("Failed to get annotations by user IDs", "error", err)
		return nil, NewInternalError("Failed to get annotations by user IDs", err)
	}
	return episodeIds, nil
}

// GetAllStripAnnotators gets all strip annotators
func (s *LabellersService) GetAllStripAnnotators(ctx context.Context, catalog string) ([]domain.StripAnnotatorEntry, error) {
	annotations, err := s.labellersRepo.GetAllStripAnnotators(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get all annotations", "error", err)
		return nil, NewInternalError("Failed to get all annotations", err)
	}
	return annotations, nil
}

// GetUnsureEpisodeIds gets all episode ids that have at least one unsure label
func (s *LabellersService) GetUnsureEpisodeIds(ctx context.Context, catalog string) ([]string, error) {
	episodeIds, err := s.labellersRepo.GetUnsureEpisodeIds(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get unsure episode ids", "error", err)
		return nil, NewInternalError("Failed to get unsure episode ids", err)
	}
	return episodeIds, nil
}

// GetConflictingEpisodeIds gets all episode ids that have conflicting labels
func (s *LabellersService) GetConflictingEpisodeIds(ctx context.Context, catalog string) ([]string, error) {
	episodeIds, err := s.labellersRepo.GetConflictingEpisodeIds(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get conflicting episode ids", "error", err)
		return nil, NewInternalError("Failed to get conflicting episode ids", err)
	}
	return episodeIds, nil
}

// GetConflictResolvedEpisodeIds gets all episode ids that have the isConflictResolvedFlag label
func (s *LabellersService) GetConflictResolvedEpisodeIds(ctx context.Context, catalog string) ([]string, error) {
	episodeIds, err := s.labellersRepo.GetConflictResolvedEpisodeIds(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get conflict resolved episode ids", "error", err)
		return nil, NewInternalError("Failed to get conflict resolved episode ids", err)
	}
	return episodeIds, nil
}

// GetEpisodeLabellingHistory gets the labelling history for an episode
func (s *LabellersService) GetEpisodeLabellingHistory(ctx context.Context, episode_id string, catalog string) ([]domain.Group, error) {
	labelling_history, err := s.labellersRepo.GetEpisodeLabellingHistory(ctx, episode_id, catalog)
	if err != nil {
		s.logger.Error("Failed to get episode labelling history", "error", err)
		return nil, NewInternalError("Failed to get episode labelling history", err)
	}

	groups := groupLabellingHistory(labelling_history)

	return groups, nil
}

// GetStripLabellingHistory gets the labelling history for a strip
func (s *LabellersService) GetStripLabellingHistory(ctx context.Context, strip_id string, catalog string) ([]domain.Group, error) {
	labelling_history, err := s.labellersRepo.GetStripLabellingHistory(ctx, strip_id, catalog)
	if err != nil {
		s.logger.Error("Failed to get strip labelling history", "error", err)
		return nil, NewInternalError("Failed to get strip labelling history", err)
	}

	groups := groupLabellingHistory(labelling_history)

	return groups, nil
}

func groupLabellingHistory(labelling_history []domain.LabellingHistory) []domain.Group {
	// Group labelling_history by LabellerID and 10 minute sliding window (in millis)
	const tenMinutesMillis = 10 * 60 * 1000

	// First, sort labelling_history by LabellerID, then by LabelledAt
	sort.SliceStable(labelling_history, func(i, j int) bool {
		if labelling_history[i].LabellerID == labelling_history[j].LabellerID {
			return labelling_history[i].LabelledAt < labelling_history[j].LabelledAt
		}
		return labelling_history[i].LabellerID < labelling_history[j].LabellerID
	})

	grouped := []domain.Group{}
	i := 0
	for i < len(labelling_history) {
		entry := labelling_history[i]
		currentLabeller := entry.LabellerID
		currentGroup := domain.Group{
			LabellerID: currentLabeller,
			Entries:    []domain.LabellingHistory{entry},
			StartAt:    entry.LabelledAt,
			EndAt:      entry.LabelledAt,
		}
		i++
		for i < len(labelling_history) && labelling_history[i].LabellerID == currentLabeller {
			nextEntry := labelling_history[i]
			// If within 10 minutes of the last entry in the group, add to group
			if (nextEntry.LabelledAt - currentGroup.EndAt) <= float64(tenMinutesMillis) {
				currentGroup.Entries = append(currentGroup.Entries, nextEntry)
				currentGroup.EndAt = nextEntry.LabelledAt
				i++
			} else {
				break
			}
		}
		grouped = append(grouped, currentGroup)
	}

	// Now, sort groups by the StartAt (ascending order of the first entry of the group)
	sort.SliceStable(grouped, func(i, j int) bool {
		return grouped[i].StartAt < grouped[j].StartAt
	})

	return grouped
}

func groupLabellingStats(stats []domain.LabellingStats) []map[string]interface{} {
	// Return stats as a slice of maps: each map is {labeller_id, day-x: total, day-y: total, ...}
	groupMap := make(map[string]map[string]int) // labeller_id -> day -> strip_count

	for _, entry := range stats {
		if _, ok := groupMap[entry.LabellerID]; !ok {
			groupMap[entry.LabellerID] = make(map[string]int)
		}
		groupMap[entry.LabellerID][entry.Day] += entry.StripCount
	}

	result := make([]map[string]interface{}, 0, len(groupMap))
	for labellerID, dayMap := range groupMap {
		entry := make(map[string]interface{})
		entry["labeller_id"] = labellerID
		for day, total := range dayMap {
			entry[day] = total
		}
		result = append(result, entry)
	}

	// Optionally, sort by labeller_id ascending
	sort.SliceStable(result, func(i, j int) bool {
		return result[i]["labeller_id"].(string) < result[j]["labeller_id"].(string)
	})

	// This function signature expects []domain.LabellingStats, but we want []map[string]interface{}
	// So, you may need to change the return type of the handler/controller to []map[string]interface{}
	// For now, we use type assertion to allow this structure to be returned as interface{}
	return result
}

func (s *LabellersService) GetLabellingStats(ctx context.Context, catalog string) ([]map[string]interface{}, error) {
	stats, err := s.labellersRepo.GetLabellingStats(ctx, catalog)
	if err != nil {
		s.logger.Error("Failed to get labelling stats", "error", err)
		return nil, NewInternalError("Failed to get labelling stats", err)
	}

	groupedStats := groupLabellingStats(stats)

	return groupedStats, nil
}

func (s *LabellersService) GetLabellersDump(ctx context.Context) ([]domain.Dump, error) {
	labellers, err := s.labellersRepo.GetLabellersDump(ctx)
	if err != nil {
		s.logger.Error("Failed to get labellers", "error", err)
		return nil, NewInternalError("Failed to get labellers", err)
	}
	return labellers, nil
}
