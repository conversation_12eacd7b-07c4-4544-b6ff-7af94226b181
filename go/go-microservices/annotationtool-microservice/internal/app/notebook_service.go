package app

import (
	"annotationtool-microservice/internal/domain"
	"context"
	"log/slog"
)

type NotebookService struct {
	notebookRepo NotebookRepository
	logger       *slog.Logger
}

func NewNotebookService(notebookRepo NotebookRepository, logger *slog.Logger) *NotebookService {
	return &NotebookService{
		notebookRepo: notebookRepo,
		logger:       logger,
	}
}

// GetNotebooks retrieves all notebooks for a given nameSpace
func (s *NotebookService) GetNotebooks(ctx context.Context, query GetNotebooksQuery) (*domain.NotebookSearchResult, error) {
	s.logger.Info("Getting notebooks", "nameSpace", query.NameSpace)

	return s.notebookRepo.GetNotebooks(ctx, query.NameSpace)
}

// CreateNotebook creates a new notebook in the provided namespace
func (s *NotebookService) CreateNotebook(ctx context.Context, nameSpace string, cmd CreateNotebookCommand) (*domain.Notebook, error) {
	s.logger.Info("Creating notebook", "nameSpace", nameSpace, "name", cmd.Name, "creator", cmd.CreatorEmail)

	// Normalize/validate server type
	if cmd.ServerType == "" {
		cmd.ServerType = "jupyter"
	}
	if cmd.ServerType != "jupyter" && cmd.ServerType != "vscode" {
		return nil, NewValidationError("server_type must be either 'jupyter' or 'vscode'", nil)
	}

	// Default image pull policy if not provided
	if cmd.ImagePullPolicy == "" {
		cmd.ImagePullPolicy = "IfNotPresent"
	}
	// Basic validation
	switch cmd.ImagePullPolicy {
	case "IfNotPresent", "Always", "Never":
	default:
		return nil, NewValidationError("image_pull_policy must be one of: IfNotPresent, Always, Never", nil)
	}

	return s.notebookRepo.CreateNotebook(ctx, nameSpace, cmd)
}

// UpdateNotebook updates an existing notebook (e.g., resources, image, server type)
func (s *NotebookService) UpdateNotebook(ctx context.Context, nameSpace string, cmd UpdateNotebookCommand) (*domain.Notebook, error) {
	s.logger.Info("Updating notebook", "nameSpace", nameSpace, "name", cmd.Name)
	if cmd.ServerType != "" && cmd.ServerType != "jupyter" && cmd.ServerType != "vscode" {
		return nil, NewValidationError("server_type must be either 'jupyter' or 'vscode'", nil)
	}
	if cmd.ImagePullPolicy != "" {
		switch cmd.ImagePullPolicy {
		case "IfNotPresent", "Always", "Never":
		default:
			return nil, NewValidationError("image_pull_policy must be one of: IfNotPresent, Always, Never", nil)
		}
	}
	return s.notebookRepo.UpdateNotebook(ctx, nameSpace, cmd)
}

// DeleteNotebook deletes a notebook
func (s *NotebookService) DeleteNotebook(ctx context.Context, nameSpace, name string) error {
	s.logger.Info("Deleting notebook", "nameSpace", nameSpace, "name", name)
	return s.notebookRepo.DeleteNotebook(ctx, nameSpace, name)
}

// StartNotebook starts a stopped notebook
func (s *NotebookService) StartNotebook(ctx context.Context, nameSpace, name string) error {
	s.logger.Info("Starting notebook", "nameSpace", nameSpace, "name", name)
	return s.notebookRepo.StartNotebook(ctx, nameSpace, name)
}

// StopNotebook stops a running notebook
func (s *NotebookService) StopNotebook(ctx context.Context, nameSpace, name string) error {
	s.logger.Info("Stopping notebook", "nameSpace", nameSpace, "name", name)
	return s.notebookRepo.StopNotebook(ctx, nameSpace, name)
}

// RestartNotebook restarts a notebook
func (s *NotebookService) RestartNotebook(ctx context.Context, nameSpace, name string) error {
	s.logger.Info("Restarting notebook", "nameSpace", nameSpace, "name", name)
	return s.notebookRepo.RestartNotebook(ctx, nameSpace, name)
}
