package app

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"sort"
	"strconv"
	"strings"
	"time"

	"annotationtool-microservice/internal/domain"
)

// ProcessedAnnotations represents the grouped and processed annotations
type ProcessedAnnotations map[string]map[string]map[string]struct {
	Labels            []domain.ProcessedLabel `json:"labels"`
	ApprovedLabels    []domain.ProcessedLabel `json:"approved_labels"`
	DisapprovedLabels []domain.ProcessedLabel `json:"disapproved_labels"`
}

// ECGService implements business logic for ECG data operations
type ECGService struct {
	datasetRepo      DatasetRepository
	episodeRepo      EpisodeRepository
	labellersService *LabellersService
	logger           *slog.Logger
	catalog          string
	schema           string
}

// NewECGService creates a new ECGService
func NewECGService(datasetRepo DatasetRepository, episodeRepo EpisodeRepository, labellersService *LabellersService, logger *slog.Logger, catalog, schema string) *ECGService {
	return &ECGService{
		datasetRepo:      datasetRepo,
		episodeRepo:      episodeRepo,
		labellersService: labellersService,
		logger:           logger,
		catalog:          catalog,
		schema:           schema,
	}
}

// QueryECGData queries ECG episodes and strips from Trino
func (s *ECGService) QueryECGData(ctx context.Context, req domain.ECGQueryRequest) (*domain.ECGQueryResponse, error) {
	s.logger.Info("Querying ECG data",
		"annotator_ids", len(req.AnnotatorIDs),
		"episode_ids", len(req.EpisodeIDs),
		"patient_ids", len(req.PatientIDs),
		"clinic_ids", len(req.ClinicIDs),
		"catalog", req.Catalog,
		"limit", req.Limit,
	)

	// Validate request
	if err := req.Validate(); err != nil {
		s.logger.Warn("Invalid ECG query request", "error", err)
		return nil, NewValidationError("Invalid query parameters", err)
	}

	// Build Trino query
	sql := req.BuildTrinoQueryWithLabellers(s.labellersService)
	s.logger.Debug("Generated Trino query", "sql", sql)

	// Execute query
	startTime := time.Now()
	result, err := s.episodeRepo.GetEpisodes(ctx, sql)
	if err != nil {
		s.logger.Error("Failed to execute ECG query", "error", err)
		return nil, NewInternalError("Failed to query ECG data", err)
	}

	queryTime := time.Since(startTime)
	s.logger.Info("ECG query executed successfully",
		"rows_returned", len(result.Episodes),
		"total_rows", result.TotalRows,
		"query_time_ms", queryTime.Milliseconds(),
	)

	// Convert results to ECG episodes
	rows := s.convertClickHouseToMapRows(result.Episodes)
	episodes, err := s.convertToECGEpisodes(rows, req.Catalog, false)
	if err != nil {
		s.logger.Error("Failed to convert query results", "error", err)
		return nil, NewInternalError("Failed to process ECG data", err)
	}

	if len(req.EpisodeIDs) > 0 {
		// Find the episode with ID matching the first element in req.EpisodeIDs
		var firstEpisodeIndex int = -1
		if len(req.EpisodeIDs) > 0 {
			targetID := req.EpisodeIDs[0]
			for i := range episodes {
				if episodes[i].EpisodeID == targetID {
					firstEpisodeIndex = i
					break
				}
			}
		}

		if firstEpisodeIndex != -1 {
			var firstEpisode domain.ECGEpisodeSummary = episodes[firstEpisodeIndex]
			episodes = append([]domain.ECGEpisodeSummary{firstEpisode}, episodes...)
			episodes = episodes[:firstEpisodeIndex+1]
		}
	}

	// Build pagination info
	pagination := domain.NewPaginationInfo(
		(req.Offset/req.Limit)+1, // page
		req.Limit,
		req.Offset,
		result.TotalRows,
	)

	// Determine the version for response
	version := req.Catalog
	if version == "" {
		version = "hive.default.episodes_v1_0_3" // default version
	}

	response := &domain.ECGQueryResponse{
		Episodes:   episodes,
		TotalCount: result.TotalRows,
		Pagination: pagination,
		QueryTime:  queryTime,
		Version:    version,
		Timestamp:  time.Now(),
	}

	return response, nil
}

// GetConflictingEpisodes queries ECG episodes and strips from Trino
func (s *ECGService) GetConflictingEpisodes(ctx context.Context, req domain.ECGQueryRequest) (*domain.ECGQueryResponse, error) {
	s.logger.Info("Getting conflicting episodes",
		"episode_ids", len(req.EpisodeIDs),
		"patient_ids", len(req.PatientIDs),
		"clinic_ids", len(req.ClinicIDs),
		"catalog", req.Catalog,
		"limit", req.Limit,
	)

	// Validate request
	if err := req.Validate(); err != nil {
		s.logger.Warn("Invalid conflicting episodes request", "error", err)
		return nil, NewValidationError("Invalid query parameters", err)
	}

	// Build Trino query
	sql := req.BuildTrinoQueryForConflictingEpisodes(s.labellersService)
	s.logger.Debug("Generated Trino query", "sql", sql)

	if sql == "" {
		return nil, NewNotFoundError("No conflicting episodes found", nil)
	}

	// Execute query
	startTime := time.Now()
	result, err := s.episodeRepo.GetEpisodes(ctx, sql)
	if err != nil {
		s.logger.Error("Failed to execute conflicting episodes query", "error", err)
		return nil, NewInternalError("Failed to get conflicting episodes", err)
	}

	queryTime := time.Since(startTime)
	s.logger.Info("Conflicting episodes query executed successfully",
		"rows_returned", len(result.Episodes),
		"total_rows", result.TotalRows,
		"query_time_ms", queryTime.Milliseconds(),
	)

	// Convert results to ECG episodes
	rows := s.convertClickHouseToMapRows(result.Episodes)
	episodes, err := s.convertToECGEpisodes(rows, req.Catalog, true)
	if err != nil {
		s.logger.Error("Failed to convert query results", "error", err)
		return nil, NewInternalError("Failed to process conflicting episodes", err)
	}

	if len(req.EpisodeIDs) > 0 {
		// Find the episode with ID matching the first element in req.EpisodeIDs
		var firstEpisodeIndex int = -1
		if len(req.EpisodeIDs) > 0 {
			targetID := req.EpisodeIDs[0]
			for i := range episodes {
				if episodes[i].EpisodeID == targetID {
					firstEpisodeIndex = i
					break
				}
			}
		}

		if firstEpisodeIndex != -1 {
			var firstEpisode domain.ECGEpisodeSummary = episodes[firstEpisodeIndex]
			episodes = append([]domain.ECGEpisodeSummary{firstEpisode}, episodes...)
			episodes = episodes[:firstEpisodeIndex+1]
		}
	}

	// Build pagination info
	pagination := domain.NewPaginationInfo(
		(req.Offset/req.Limit)+1, // page
		req.Limit,
		req.Offset,
		result.TotalRows,
	)

	// Determine the version for response
	version := req.Catalog
	if version == "" {
		version = "hive.default.episodes_v1_0_3" // default version
	}

	response := &domain.ECGQueryResponse{
		Episodes:   episodes,
		TotalCount: result.TotalRows,
		Pagination: pagination,
		QueryTime:  queryTime,
		Version:    version,
		Timestamp:  time.Now(),
	}

	return response, nil
}

// GetConflictingEpisodes queries ECG episodes and strips from Trino
func (s *ECGService) GetNumConflictingEpisodes(ctx context.Context, req domain.ECGNumConflictingEpisodesRequest) (*domain.ECGNumConflictingEpisodesResponse, error) {
	s.logger.Info("Getting conflicting episodes",
		"catalog", req.Catalog,
	)

	catalog := req.Catalog

	startTime := time.Now()
	// Extract conflicting episodes from ClickHouse
	conflictingEpisodeIds, err := s.labellersService.GetConflictingEpisodeIds(context.Background(), catalog)
	if err != nil {
		return nil, NewInternalError("Failed to get conflicting episode ids", err)
	}

	conflictResolvedEpisodeIds, err := s.labellersService.GetConflictResolvedEpisodeIds(context.Background(), catalog)
	if err != nil {
		return nil, NewInternalError("Failed to get conflict resolved episode ids", err)
	}

	numConflictingEpisodes := len(conflictingEpisodeIds) - len(conflictResolvedEpisodeIds)

	queryTime := time.Since(startTime)
	s.logger.Info("Number of conflicting episodes query executed successfully",
		"num_conflicting_episodes", numConflictingEpisodes,
		"query_time_ms", queryTime.Milliseconds(),
	)

	response := &domain.ECGNumConflictingEpisodesResponse{
		NumConflictingEpisodes: int64(numConflictingEpisodes),
		QueryTime:              queryTime,
	}

	return response, nil
}

// GetECGAnalytics generates analytics for ECG data
func (s *ECGService) GetECGAnalytics(ctx context.Context, req domain.ECGAnalyticsRequest) (*domain.ECGAnalyticsResponse, error) {
	s.logger.Info("Generating ECG analytics",
		"date_from", req.DateFrom,
		"date_to", req.DateTo,
		"group_by", req.GroupBy,
	)

	// Build analytics queries
	queries := s.buildAnalyticsQueries(req)

	response := &domain.ECGAnalyticsResponse{
		Timestamp: time.Now(),
	}

	// Execute total counts query
	if totalQuery, exists := queries["total"]; exists {
		result, err := s.datasetRepo.ExecuteRawSQL(ctx, totalQuery, nil)
		if err != nil {
			s.logger.Error("Failed to execute total counts query", "error", err)
			return nil, NewInternalError("Failed to generate analytics", err)
		}

		if len(result.Rows) > 0 {
			if episodes, ok := result.Rows[0]["total_episodes"].(int64); ok {
				response.TotalEpisodes = episodes
			}
			if strips, ok := result.Rows[0]["total_strips"].(int64); ok {
				response.TotalStrips = strips
			}
		}
	}

	// Execute clinic stats query if requested
	if contains(req.GroupBy, "clinic") {
		if clinicQuery, exists := queries["clinic"]; exists {
			result, err := s.datasetRepo.ExecuteRawSQL(ctx, clinicQuery, nil)
			if err != nil {
				s.logger.Warn("Failed to execute clinic stats query", "error", err)
			} else {
				response.ByClinic = s.convertToClinicStats(result.Rows)
			}
		}
	}

	// Execute manufacturer stats query if requested
	if contains(req.GroupBy, "manufacturer") {
		if mfgQuery, exists := queries["manufacturer"]; exists {
			result, err := s.datasetRepo.ExecuteRawSQL(ctx, mfgQuery, nil)
			if err != nil {
				s.logger.Warn("Failed to execute manufacturer stats query", "error", err)
			} else {
				response.ByManufacturer = s.convertToManufacturerStats(result.Rows)
			}
		}
	}

	// Execute labelling status stats query if requested
	if contains(req.GroupBy, "labelling_status") {
		if labelQuery, exists := queries["labelling_status"]; exists {
			result, err := s.datasetRepo.ExecuteRawSQL(ctx, labelQuery, nil)
			if err != nil {
				s.logger.Warn("Failed to execute labelling stats query", "error", err)
			} else {
				response.ByLabellingStatus = s.convertToLabellingStats(result.Rows)
			}
		}
	}

	// Execute time range stats query
	if timeQuery, exists := queries["time_range"]; exists {
		result, err := s.datasetRepo.ExecuteRawSQL(ctx, timeQuery, nil)
		if err != nil {
			s.logger.Warn("Failed to execute time range stats query", "error", err)
		} else {
			response.TimeRange = s.convertToTimeRangeStats(result.Rows)
		}
	}

	s.logger.Info("ECG analytics generated successfully",
		"total_episodes", response.TotalEpisodes,
		"total_strips", response.TotalStrips,
	)

	return response, nil
}

// buildAnalyticsQueries builds the SQL queries needed for ECG analytics
func (s *ECGService) buildAnalyticsQueries(req domain.ECGAnalyticsRequest) map[string]string {
	queries := make(map[string]string)
	tableName := fmt.Sprintf("%s.%s.episodes_v1_0_3", s.catalog, s.schema)

	// Build WHERE conditions
	var conditions []string
	if len(req.ClinicIDs) > 0 {
		conditions = append(conditions, fmt.Sprintf("clinicid IN (%s)", joinStringValues(req.ClinicIDs)))
	}
	if len(req.Manufacturers) > 0 {
		conditions = append(conditions, fmt.Sprintf("manufacturer IN (%s)", joinStringValues(req.Manufacturers)))
	}
	if req.DateFrom != nil {
		conditions = append(conditions, fmt.Sprintf("reporttime >= TIMESTAMP '%s'", req.DateFrom.Format("2006-01-02 15:04:05")))
	}
	if req.DateTo != nil {
		conditions = append(conditions, fmt.Sprintf("reporttime <= TIMESTAMP '%s'", req.DateTo.Format("2006-01-02 15:04:05")))
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + joinConditions(conditions)
	}

	// Total counts query
	queries["total"] = fmt.Sprintf(`
		SELECT 
			COUNT(DISTINCT episodeid) as total_episodes,
			COUNT(*) as total_strips
		FROM %s%s`, tableName, whereClause)

	// Clinic stats query
	if contains(req.GroupBy, "clinic") {
		queries["clinic"] = fmt.Sprintf(`
			SELECT 
				clinicid as clinic_id,
				clinicname as clinic_name,
				COUNT(DISTINCT episodeid) as episode_count,
				COUNT(*) as strip_count,
				0 as labelled_count,
				0 as unlabelled_count
			FROM %s%s
			GROUP BY clinicID, clinicName
			ORDER BY clinic_name`, tableName, whereClause)
	}

	// Manufacturer stats query
	if contains(req.GroupBy, "manufacturer") {
		queries["manufacturer"] = fmt.Sprintf(`
			SELECT 
				manufacturer,
				COUNT(DISTINCT episodeid) as episode_count,
				COUNT(*) as strip_count,
				ARRAY_AGG(DISTINCT deviceModel) as device_models
			FROM %s%s
			GROUP BY manufacturer
			ORDER BY manufacturer`, tableName, whereClause)
	}

	// Labelling status stats query
	if contains(req.GroupBy, "labelling_status") {
		queries["labelling_status"] = fmt.Sprintf(`
			SELECT 
				'total' as status,
				COUNT(DISTINCT episodeid) as episode_count,
				COUNT(*) as strip_count,
				100.0 as percentage
			FROM %s%s`, tableName, whereClause)
	}

	// Time range stats query
	queries["time_range"] = fmt.Sprintf(`
		SELECT 
			MIN(reporttime) as earliest_episode,
			MAX(reporttime) as latest_episode,
			DATE_DIFF('day', MIN(reporttime), MAX(reporttime)) as days_covered,
			CAST(COUNT(DISTINCT episodeid) AS DOUBLE) / GREATEST(DATE_DIFF('day', MIN(reporttime), MAX(reporttime)), 1) as avg_per_day
		FROM %s%s`, tableName, whereClause)

	return queries
}

// GetEpisodeDetails gets detailed information for a specific episode
func (s *ECGService) GetEpisodeDetails(ctx context.Context, episodeID string, catalog string) (*domain.ECGEpisodeSummary, error) {
	s.logger.Info("Getting episode details", "episode_id", episodeID)

	sql := fmt.Sprintf(`
		SELECT episodeid, id as strip_id, patientid, patientname, clinicid, clinicname, manufacturer, devicelabel, devicemodel, reporttime, episodelength, ecgstrip_timestamps, ecgstrip_voltages
		FROM %s
		WHERE episodeid = '%s'
	`, catalog, episodeID)

	// Execute query
	startTime := time.Now()
	result, err := s.episodeRepo.GetEpisodes(ctx, sql)
	if err != nil {
		s.logger.Error("Failed to execute ECG query", "error", err)
		return nil, NewInternalError("Failed to query ECG data", err)
	}

	queryTime := time.Since(startTime)
	s.logger.Info("ECG query executed successfully",
		"rows_returned", len(result.Episodes),
		"total_rows", result.TotalRows,
		"query_time_ms", queryTime.Milliseconds(),
	)

	// Convert results to ECG episodes
	rows := s.convertClickHouseToMapRows(result.Episodes)
	episodes, err := s.convertToECGEpisodes(rows, catalog, false)
	if err != nil {
		s.logger.Error("Failed to convert query results", "error", err)
		return nil, NewInternalError("Failed to process ECG data", err)
	}

	return &episodes[0], nil
}

// GetClinics retrieves distinct clinics from ECG data
func (s *ECGService) GetClinics(ctx context.Context, req domain.ECGClinicRequest) ([]domain.Clinic, error) {
	s.logger.Info("Getting distinct clinics from Trino")

	query := fmt.Sprintf(
		"SELECT DISTINCT clinicid, clinicname FROM %s WHERE TRIM(clinicid) <> '' AND TRIM(clinicname) <> '' ORDER BY clinicname ASC",
		req.Catalog,
	)

	result, err := s.episodeRepo.GetEpisodes(ctx, query)
	if err != nil {
		s.logger.Error("Failed to get clinics from Trino", "error", err)
		return nil, NewInternalError("Failed to retrieve clinics", err)
	}

	clinics := make([]domain.Clinic, 0, len(result.Episodes))
	for _, episode := range result.Episodes {
		clinic := domain.Clinic{
			ClinicID:   episode.ClinicID,
			ClinicName: episode.ClinicName,
		}
		clinics = append(clinics, clinic)
	}

	return clinics, nil
}

// GetPatients retrieves distinct patients, optionally filtered by clinic
func (s *ECGService) GetPatients(ctx context.Context, req domain.ECGPatientRequest) ([]domain.Patient, error) {
	s.logger.Info("Getting distinct patients from Trino", "clinic_id", req.ClinicID)

	query := fmt.Sprintf(
		"SELECT DISTINCT patientid, patientname FROM %s",
		req.Catalog,
	)
	if req.ClinicID != "" {
		query += fmt.Sprintf(" WHERE clinicid = '%s' AND TRIM(patientid) <> ''", req.ClinicID)
	}
	query += " ORDER BY patientname ASC"

	result, err := s.episodeRepo.GetEpisodes(ctx, query)
	if err != nil {
		s.logger.Error("Failed to get patients from Trino", "error", err)
		return nil, NewInternalError("Failed to retrieve patients", err)
	}

	patients := make([]domain.Patient, 0, len(result.Episodes))
	for _, episode := range result.Episodes {
		patient := domain.Patient{
			PatientID:   episode.PatientID,
			PatientName: episode.PatientID,
		}
		patients = append(patients, patient)
	}

	return patients, nil
}

// GetManufacturers retrieves distinct manufacturers from ECG data
func (s *ECGService) GetManufacturers(ctx context.Context, req domain.ECGManufacturerRequest) ([]string, error) {
	s.logger.Info("Getting distinct manufacturers from Trino")

	query := fmt.Sprintf(
		"SELECT DISTINCT manufacturer FROM %s ORDER BY manufacturer ASC",
		req.Catalog,
	)

	result, err := s.episodeRepo.GetEpisodes(ctx, query)
	if err != nil {
		s.logger.Error("Failed to get manufacturers from Trino", "error", err)
		return nil, NewInternalError("Failed to retrieve manufacturers", err)
	}

	manufacturers := make([]string, 0, len(result.Episodes))
	for _, episode := range result.Episodes {
		manufacturers = append(manufacturers, episode.Manufacturer)
	}

	return manufacturers, nil
}

// GetDeviceModels retrieves distinct device models, optionally filtered by manufacturer
func (s *ECGService) GetDeviceModels(ctx context.Context, req domain.ECGDeviceModelRequest) ([]string, error) {
	s.logger.Info("Getting distinct device models from Trino", "manufacturer", req.Manufacturer)

	query := fmt.Sprintf(
		"SELECT DISTINCT devicemodel FROM %s",
		req.Catalog,
	)
	if req.Manufacturer != "" {
		query += fmt.Sprintf(" WHERE manufacturer = '%s'", req.Manufacturer)
	}
	query += " ORDER BY devicemodel ASC"

	result, err := s.episodeRepo.GetEpisodes(ctx, query)
	if err != nil {
		s.logger.Error("Failed to get device models from Trino", "error", err)
		return nil, NewInternalError("Failed to retrieve device models", err)
	}

	models := make([]string, 0, len(result.Episodes))
	for _, episode := range result.Episodes {
		models = append(models, episode.DeviceModel)
	}

	return models, nil
}

func (s *ECGService) GetECGDashboard(ctx context.Context, req domain.ECGDashboardRequest, rawQueryParams string) (*domain.ECGDashboardResponse, error) {
	prettyParams, _ := json.MarshalIndent(rawQueryParams, "", "  ")
	s.logger.Info("Querying Dashboard data", "query_params", string(prettyParams))

	// Validate request
	if err := req.Validate(); err != nil {
		s.logger.Warn("Invalid ECG query request", "error", err)
		return nil, NewValidationError("Invalid query parameters", err)
	}

	queryParams := make([][2]string, 0)
	if rawQueryParams != "" {
		// Split by '&'
		pairs := make([]string, 0)
		for _, pair := range strings.Split(rawQueryParams, "&") {
			if pair == "" {
				continue
			}
			pairs = append(pairs, pair)
		}
		for _, pair := range pairs {
			kv := strings.Split(pair, "=")
			key, val := "", ""
			if len(kv) > 0 {
				key = kv[0]
			}
			if len(kv) > 1 {
				val = kv[1]
			}
			queryParams = append(queryParams, [2]string{key, val})
		}
	}

	query, _, columnOrder := req.BuildTrinoQueryForDashboard(queryParams, s.labellersService)
	s.logger.Debug("Generated Clickhouse query", "sql", query)

	// Execute query
	startTime := time.Now()
	result, err := s.episodeRepo.GetDashboardData(ctx, query)
	if err != nil {
		s.logger.Error("Failed to execute dashboard query", "error", err)
		return nil, NewInternalError("Failed to execute dashboard query", err)
	}

	queryTime := time.Since(startTime)
	s.logger.Info("ECG query executed successfully",
		"rows_returned", len(result.Data),
		"query_time_ms", queryTime.Milliseconds(),
	)

	rows := s.convertClickHouseDashboardToMapRows(result.Data)

	for i, col := range columnOrder {
		columnOrder[i] = strings.ReplaceAll(col, "\"", "")
	}

	dashboardData, err := s.convertToDashboardData(rows, columnOrder)
	if err != nil {
		s.logger.Error("Failed to convert query results", "error", err)
		return nil, NewInternalError("Failed to process ECG data", err)
	}

	return &domain.ECGDashboardResponse{
		Data:        dashboardData,
		ColumnOrder: columnOrder,
		QueryTime:   queryTime,
		Version:     req.Catalog,
		Timestamp:   time.Now(),
	}, nil
}

// Helper methods

// convertClickHouseToMapRows converts ClickHouse structured data to map format for compatibility with existing conversion logic
// You can have two types by overloading the function name or using different function names.
// Here are two versions for two different input types:

// For ClickhouseDashboardStripRow
func (s *ECGService) convertClickHouseDashboardToMapRows(episodes []domain.ClickhouseDashboardStripRow) []map[string]interface{} {
	rows := make([]map[string]interface{}, len(episodes))

	for i, episode := range episodes {
		row := map[string]interface{}{
			"episodeId":                           episode.EpisodeID,
			"strip_id":                            episode.StripID,
			"patientid":                           episode.PatientID,
			"patientname":                         episode.PatientName,
			"clinicid":                            episode.ClinicID,
			"clinicname":                          episode.ClinicName,
			"manufacturer":                        episode.Manufacturer,
			"devicelabel":                         episode.DeviceLabel,
			"devicemodel":                         episode.DeviceModel,
			"episodelength":                       episode.EpisodeLength,
			"reporttime":                          episode.ReportTime.String(),
			"ecgstrip_timestamps":                 episode.Timestamps,
			"ecgstrip_voltages":                   episode.Voltages,
			"patientcount":                        episode.PatientCount,
			"episodecount":                        episode.EpisodeCount,
			"ecgstripscount":                      episode.ECGStripsCount,
			"episodelengthminutes":                episode.EpisodeLengthMinutes,
			"is_labelled":                         episode.IsLabelled,
			"hierarichical-doctorLabel":           episode.HierarichicalDoctorLabel,
			"leftColumnDistributor-episodeLength": episode.LeftColumnDistributorEpisodeLength,
			"leftColumnDistributor-deviceLabel":   episode.LeftColumnDistributorDeviceLabel,
			"leftColumnDistributor-doctorLabel":   episode.LeftColumnDistributorDoctorLabel,
		}
		rows[i] = row
	}

	return rows
}

// For ClickhouseStripRow (if you want a second type)
func (s *ECGService) convertClickHouseToMapRows(episodes []domain.ClickhouseStripRow) []map[string]interface{} {
	rows := make([]map[string]interface{}, len(episodes))

	for i, episode := range episodes {
		row := map[string]interface{}{
			"episodeId":           episode.EpisodeID,
			"strip_id":            episode.StripID,
			"patientid":           episode.PatientID,
			"patientname":         episode.PatientName,
			"clinicid":            episode.ClinicID,
			"clinicname":          episode.ClinicName,
			"manufacturer":        episode.Manufacturer,
			"devicelabel":         episode.DeviceLabel,
			"devicemodel":         episode.DeviceModel,
			"episodelength":       episode.EpisodeLength,
			"reporttime":          episode.ReportTime.String(),
			"ecgstrip_timestamps": episode.Timestamps,
			"ecgstrip_voltages":   episode.Voltages,
		}
		rows[i] = row
	}

	return rows
}

// convertToECGEpisodes converts query results to ECG episodes
func (s *ECGService) convertToECGEpisodes(rows []map[string]interface{}, catalog string, isConflictingEpisodes bool) ([]domain.ECGEpisodeSummary, error) {
	episodeMap := make(map[string]*domain.ECGEpisodeSummary)

	episodeIDs := make([]string, 0, len(rows))
	for _, row := range rows {
		if episodeID, ok := row["episodeId"].(string); ok && episodeID != "" {
			episodeIDs = append(episodeIDs, episodeID)
		}
	}

	annotations, err := s.labellersService.GetAnnotationsByEpisodeIDs(context.Background(), episodeIDs, catalog)
	if err != nil {
		return nil, err
	}

	prettyAnnotations, _ := json.MarshalIndent(annotations, "", "  ")
	fmt.Println("Annotations", string(prettyAnnotations))

	// Process annotations to group by episode -> strip -> user
	processedAnnotations := processAnnotations(annotations)

	for _, row := range rows {
		episodeID, _ := row["episodeId"].(string)
		stripID, _ := row["strip_id"].(string)

		// Get or create episode
		episode, exists := episodeMap[episodeID]
		if !exists {
			episode = &domain.ECGEpisodeSummary{
				EpisodeID:     episodeID,
				PatientID:     getStringValue(row, "patientid"),
				PatientName:   getStringValue(row, "patientname"),
				ClinicID:      getStringValue(row, "clinicid"),
				ClinicName:    getStringValue(row, "clinicname"),
				Manufacturer:  getStringValue(row, "manufacturer"),
				DeviceLabel:   getStringValue(row, "devicelabel"),
				DeviceModel:   getStringValue(row, "devicemodel"),
				EpisodeLength: getStringValue(row, "episodelength"),
				ReportTime:    getStringValue(row, "reporttime"),
				Strips:        make([]domain.ECGStripData, 0),
			}

			episodeMap[episodeID] = episode
		}

		strip := domain.ECGStripData{
			StripID:    stripID,
			Timestamps: row["ecgstrip_timestamps"].([]float32),
			Voltages:   row["ecgstrip_voltages"].([]float32),
			Labels:     make(map[string][]domain.ProcessedLabel),
			Triage:     nil,
		}

		// Add annotation data from processed annotations
		if processedAnnotations[episodeID] != nil {
			if stripAnnotations, exists := processedAnnotations[episodeID][stripID]; exists {
				// Convert processed annotations to the format expected by frontend
				strip.Labels = make(map[string][]domain.ProcessedLabel)
				strip.ApprovedLabels = make([]domain.ProcessedLabel, 0)
				strip.DisapprovedLabels = make([]domain.ProcessedLabel, 0)

				for userID, userLabels := range stripAnnotations {
					strip.Labels[userID] = userLabels.Labels
					if isConflictingEpisodes {
						// Merge approved labels from all users
						strip.ApprovedLabels = append(strip.ApprovedLabels, userLabels.ApprovedLabels...)
						// Merge disapproved labels from all users
						strip.DisapprovedLabels = append(strip.DisapprovedLabels, userLabels.DisapprovedLabels...)
					}
				}
			}

			// Assign episode-level annotations (stripID = "null") to the episode, not to each strip
			if episodeAnnotations, exists := processedAnnotations[episodeID]["null"]; exists {
				if episode.Labels == nil {
					episode.Labels = make(map[string][]domain.ProcessedLabel)
					episode.ApprovedLabels = make([]domain.ProcessedLabel, 0)
					episode.DisapprovedLabels = make([]domain.ProcessedLabel, 0)
				}
				for userID, userLabels := range episodeAnnotations {
					if episode.Labels[userID] == nil {
						episode.Labels[userID] = userLabels.Labels
						if isConflictingEpisodes {
							// Merge approved labels from all users
							episode.ApprovedLabels = append(episode.ApprovedLabels, userLabels.ApprovedLabels...)
							// Merge disapproved labels from all users
							episode.DisapprovedLabels = append(episode.DisapprovedLabels, userLabels.DisapprovedLabels...)
						}
					}
				}
			}
		} else {
			strip.Labels = make(map[string][]domain.ProcessedLabel)
			strip.ApprovedLabels = make([]domain.ProcessedLabel, 0)
			strip.DisapprovedLabels = make([]domain.ProcessedLabel, 0)
		}

		episodeMap[episodeID].Strips = append(episodeMap[episodeID].Strips, strip)
	}

	// For each episode, sort strips by StripID
	for _, episode := range episodeMap {
		strips := episode.Strips
		// Simple sort by StripID (lexicographically)
		sort.Slice(strips, func(i, j int) bool {
			// Sort by numeric suffix if possible, otherwise lexicographically
			stripIDi := strips[i].StripID
			stripIDj := strips[j].StripID

			// Try to extract numeric suffixes
			getSuffixNum := func(s string) (string, int) {
				parts := strings.Split(s, "_")
				if len(parts) > 1 {
					num, err := strconv.Atoi(parts[len(parts)-1])
					if err == nil {
						return strings.Join(parts[:len(parts)-1], "_"), num
					}
				}
				return s, -1
			}

			basei, numi := getSuffixNum(stripIDi)
			basej, numj := getSuffixNum(stripIDj)

			if basei == basej && numi >= 0 && numj >= 0 {
				return numi < numj
			}
			return stripIDi < stripIDj
		})
		episode.Strips = strips
	}

	// Convert map to slice
	episodes := make([]domain.ECGEpisodeSummary, 0, len(episodeMap))
	for _, episode := range episodeMap {
		episodes = append(episodes, *episode)
	}

	return episodes, nil
}

// convertToDashboardData converts query results to dashboard data
func (s *ECGService) convertToDashboardData(rows []map[string]interface{}, columnOrder []string) ([][]string, error) {
	data := make([][]string, 0, len(rows))

	for _, row := range rows {
		rowData := make([]string, 0, len(columnOrder))
		for _, col := range columnOrder {
			val := row[col]
			if val == nil {
				rowData = append(rowData, "")
			} else {
				rowData = append(rowData, fmt.Sprintf("%v", val))
			}
		}

		data = append(data, rowData)
	}

	return data, nil
}

// Helper functions for data conversion

func getStringValue(row map[string]interface{}, key string) string {
	if val, ok := row[key].(string); ok {
		return val
	}
	return ""
}

func getIntValue(row map[string]interface{}, key string) int {
	if val, ok := row[key].(int); ok {
		return val
	}
	if val, ok := row[key].(int64); ok {
		return int(val)
	}
	return 0
}

func getFloat64Array(row map[string]interface{}, key string) []float64 {
	// Try to convert []interface{} to []float64
	// Debug prints removed to avoid mixed logging styles
	if arr, ok := row[key].([]interface{}); ok {
		result := make([]float64, 0, len(arr))
		for _, v := range arr {
			// Debug prints removed to avoid mixed logging styles
			switch f := v.(type) {
			case json.Number:
				if parsed, err := f.Float64(); err == nil {
					result = append(result, parsed)
				} else {
					// Keep silent; caller handles missing conversions
				}
			case float64:
				result = append(result, f)
			case float32:
				result = append(result, float64(f))
			case int:
				result = append(result, float64(f))
			case int64:
				result = append(result, float64(f))
			case string:
				if parsed, err := strconv.ParseFloat(f, 64); err == nil {
					result = append(result, parsed)
				}
			}
		}

		return result
	}

	return []float64{}
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func joinStringValues(values []string) string {
	result := ""
	for i, value := range values {
		if i > 0 {
			result += ", "
		}
		result += fmt.Sprintf("'%s'", value)
	}
	return result
}

func joinConditions(conditions []string) string {
	result := ""
	for i, condition := range conditions {
		if i > 0 {
			result += " AND "
		}
		result += condition
	}
	return result
}

func (s *ECGService) convertToClinicStats(rows []map[string]interface{}) []domain.ECGClinicStats {
	stats := make([]domain.ECGClinicStats, 0, len(rows))
	for _, row := range rows {
		stat := domain.ECGClinicStats{
			ClinicID:        getStringValue(row, "clinic_id"),
			ClinicName:      getStringValue(row, "clinic_name"),
			EpisodeCount:    getInt64Value(row, "episode_count"),
			StripCount:      getInt64Value(row, "strip_count"),
			LabelledCount:   getInt64Value(row, "labelled_count"),
			UnlabelledCount: getInt64Value(row, "unlabelled_count"),
		}
		stats = append(stats, stat)
	}
	return stats
}

func (s *ECGService) convertToManufacturerStats(rows []map[string]interface{}) []domain.ECGManufacturerStats {
	stats := make([]domain.ECGManufacturerStats, 0, len(rows))
	for _, row := range rows {
		stat := domain.ECGManufacturerStats{
			Manufacturer: getStringValue(row, "manufacturer"),
			EpisodeCount: getInt64Value(row, "episode_count"),
			StripCount:   getInt64Value(row, "strip_count"),
			DeviceModels: getStringArrayValue(row, "device_models"),
		}
		stats = append(stats, stat)
	}
	return stats
}

func (s *ECGService) convertToLabellingStats(rows []map[string]interface{}) []domain.ECGLabellingStats {
	stats := make([]domain.ECGLabellingStats, 0, len(rows))
	for _, row := range rows {
		stat := domain.ECGLabellingStats{
			Status:       getStringValue(row, "status"),
			EpisodeCount: getInt64Value(row, "episode_count"),
			StripCount:   getInt64Value(row, "strip_count"),
			Percentage:   getFloat64Value(row, "percentage"),
		}
		stats = append(stats, stat)
	}
	return stats
}

func (s *ECGService) convertToTimeRangeStats(rows []map[string]interface{}) domain.ECGTimeRangeStats {
	if len(rows) == 0 {
		return domain.ECGTimeRangeStats{}
	}

	row := rows[0]
	stats := domain.ECGTimeRangeStats{
		DaysCovered: getIntValue(row, "days_covered"),
		AvgPerDay:   getFloat64Value(row, "avg_per_day"),
	}

	if earliest, ok := row["earliest_episode"].(time.Time); ok {
		stats.EarliestEpisode = earliest
	}
	if latest, ok := row["latest_episode"].(time.Time); ok {
		stats.LatestEpisode = latest
	}

	return stats
}

func getInt64Value(row map[string]interface{}, key string) int64 {
	if val, ok := row[key].(int64); ok {
		return val
	}
	if val, ok := row[key].(int); ok {
		return int64(val)
	}
	return 0
}

func getFloat64Value(row map[string]interface{}, key string) float64 {
	if val, ok := row[key].(float64); ok {
		return val
	}
	if val, ok := row[key].(float32); ok {
		return float64(val)
	}
	return 0.0
}

func getStringArrayValue(row map[string]interface{}, key string) []string {
	if val, ok := row[key].([]string); ok {
		return val
	}
	if val, ok := row[key].([]interface{}); ok {
		result := make([]string, len(val))
		for i, v := range val {
			if s, ok := v.(string); ok {
				result[i] = s
			}
		}
		return result
	}
	return []string{}
}

// ProcessAnnotations groups annotations by episodeID -> stripID -> userID and processes them
func processAnnotations(annotations []domain.AnnotationEntry) ProcessedAnnotations {
	result := make(ProcessedAnnotations)

	prettyAnnotations, _ := json.MarshalIndent(annotations, "", "  ")
	fmt.Println("Annotations", string(prettyAnnotations))

	// Helper function to create segment key
	createSegmentKey := func(label string, start, end *float64) string {
		startStr := "0"
		endStr := "0"
		if start != nil {
			startStr = fmt.Sprintf("%.0f", *start)
		}
		if end != nil {
			endStr = fmt.Sprintf("%.0f", *end)
		}
		return fmt.Sprintf("%s|%s|%s", label, startStr, endStr)
	}

	// Group annotations by episodeID -> stripID -> userID -> segmentKey (label|start|end)
	grouped := make(map[string]map[string]map[string]map[string]struct {
		Label        string
		Start        *float64
		End          *float64
		Additions    []float64 // Just timestamps since start/end are in the key
		Removals     []float64
		Approvals    []float64
		Disapprovals []float64
	})

	// First pass: group and count operations
	for _, annotation := range annotations {
		stripKey := "null"
		if annotation.StripID != nil && *annotation.StripID != "" {
			stripKey = *annotation.StripID
		}

		segmentKey := createSegmentKey(annotation.Label, annotation.Start, annotation.End)

		// Initialize nested maps
		if grouped[annotation.EpisodeID] == nil {
			grouped[annotation.EpisodeID] = make(map[string]map[string]map[string]struct {
				Label        string
				Start        *float64
				End          *float64
				Additions    []float64
				Removals     []float64
				Approvals    []float64
				Disapprovals []float64
			})
		}
		if grouped[annotation.EpisodeID][stripKey] == nil {
			grouped[annotation.EpisodeID][stripKey] = make(map[string]map[string]struct {
				Label        string
				Start        *float64
				End          *float64
				Additions    []float64
				Removals     []float64
				Approvals    []float64
				Disapprovals []float64
			})
		}
		if grouped[annotation.EpisodeID][stripKey][annotation.SubmittedBy] == nil {
			grouped[annotation.EpisodeID][stripKey][annotation.SubmittedBy] = make(map[string]struct {
				Label        string
				Start        *float64
				End          *float64
				Additions    []float64
				Removals     []float64
				Approvals    []float64
				Disapprovals []float64
			})
		}
		if _, exists := grouped[annotation.EpisodeID][stripKey][annotation.SubmittedBy][segmentKey]; !exists {
			grouped[annotation.EpisodeID][stripKey][annotation.SubmittedBy][segmentKey] = struct {
				Label        string
				Start        *float64
				End          *float64
				Additions    []float64
				Removals     []float64
				Approvals    []float64
				Disapprovals []float64
			}{
				Label:        annotation.Label,
				Start:        annotation.Start,
				End:          annotation.End,
				Additions:    []float64{},
				Removals:     []float64{},
				Approvals:    []float64{},
				Disapprovals: []float64{},
			}
		}

		// Add operation to the appropriate slice
		operations := grouped[annotation.EpisodeID][stripKey][annotation.SubmittedBy][segmentKey]
		switch annotation.OperationType {
		case "addition":
			operations.Additions = append(operations.Additions, annotation.LabelTimestamp)
		case "removal":
			operations.Removals = append(operations.Removals, annotation.LabelTimestamp)
		case "approval":
			operations.Approvals = append(operations.Approvals, annotation.LabelTimestamp)
		case "disapproval":
			operations.Disapprovals = append(operations.Disapprovals, annotation.LabelTimestamp)
		}
		grouped[annotation.EpisodeID][stripKey][annotation.SubmittedBy][segmentKey] = operations
	}

	// Second pass: determine final labels based on addition/removal counts per segment
	for episodeID, episodes := range grouped {
		if result[episodeID] == nil {
			result[episodeID] = make(map[string]map[string]struct {
				Labels            []domain.ProcessedLabel `json:"labels"`
				ApprovedLabels    []domain.ProcessedLabel `json:"approved_labels"`
				DisapprovedLabels []domain.ProcessedLabel `json:"disapproved_labels"`
			})
		}

		for stripID, strips := range episodes {
			if result[episodeID][stripID] == nil {
				result[episodeID][stripID] = make(map[string]struct {
					Labels            []domain.ProcessedLabel `json:"labels"`
					ApprovedLabels    []domain.ProcessedLabel `json:"approved_labels"`
					DisapprovedLabels []domain.ProcessedLabel `json:"disapproved_labels"`
				})
			}

			for userID, segments := range strips {
				if result[episodeID][stripID][userID].Labels == nil {
					result[episodeID][stripID][userID] = struct {
						Labels            []domain.ProcessedLabel `json:"labels"`
						ApprovedLabels    []domain.ProcessedLabel `json:"approved_labels"`
						DisapprovedLabels []domain.ProcessedLabel `json:"disapproved_labels"`
					}{
						Labels:            []domain.ProcessedLabel{},
						ApprovedLabels:    []domain.ProcessedLabel{},
						DisapprovedLabels: []domain.ProcessedLabel{},
					}
				}

				labels := []domain.ProcessedLabel{}
				approvedLabels := []domain.ProcessedLabel{}
				disapprovedLabels := []domain.ProcessedLabel{}

				// Process each segment (label+start+end combination) separately
				for _, operations := range segments {
					additionCount := len(operations.Additions)
					removalCount := len(operations.Removals)
					approvalCount := len(operations.Approvals)
					disapprovalCount := len(operations.Disapprovals)

					// If additions > removals, include this specific segment
					if additionCount > removalCount {
						// Use the latest addition timestamp
						latestTimestamp := float64(0)
						for _, timestamp := range operations.Additions {
							if timestamp > latestTimestamp {
								latestTimestamp = timestamp
							}
						}

						labels = append(labels, domain.ProcessedLabel{
							Label:     operations.Label,
							Timestamp: latestTimestamp,
							Start:     operations.Start,
							End:       operations.End,
						})
					}

					// Process approvals/disapprovals for this specific segment
					if approvalCount > disapprovalCount {
						latestTimestamp := float64(0)
						for _, timestamp := range operations.Approvals {
							if timestamp > latestTimestamp {
								latestTimestamp = timestamp
							}
						}

						approvedLabels = append(approvedLabels, domain.ProcessedLabel{
							Label:     operations.Label,
							Timestamp: latestTimestamp,
							Start:     operations.Start,
							End:       operations.End,
						})
					} else if disapprovalCount > approvalCount {
						latestTimestamp := float64(0)
						for _, timestamp := range operations.Disapprovals {
							if timestamp > latestTimestamp {
								latestTimestamp = timestamp
							}
						}

						disapprovedLabels = append(disapprovedLabels, domain.ProcessedLabel{
							Label:     operations.Label,
							Timestamp: latestTimestamp,
							Start:     operations.Start,
							End:       operations.End,
						})
					}
				}

				result[episodeID][stripID][userID] = struct {
					Labels            []domain.ProcessedLabel `json:"labels"`
					ApprovedLabels    []domain.ProcessedLabel `json:"approved_labels"`
					DisapprovedLabels []domain.ProcessedLabel `json:"disapproved_labels"`
				}{
					Labels:            labels,
					ApprovedLabels:    approvedLabels,
					DisapprovedLabels: disapprovedLabels,
				}
			}
		}
	}

	return result
}
