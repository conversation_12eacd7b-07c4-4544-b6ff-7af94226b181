package app

import (
	"annotationtool-microservice/internal/domain"
)

// UpdateEpisodeStatusCommand represents a command to update episode status
type UpdateEpisodeStatusCommand struct {
	EpisodeID string `json:"episode_id" binding:"required"`
	Status    string `json:"status" binding:"required"`
	Notes     string `json:"notes"`
}

// CreateAnnotationsCommand represents a command to create annotations for strips
type CreateAnnotationsCommand struct {
	EpisodeID string                   `json:"episode_id" binding:"required"`
	UserID    string                   `json:"user_id" binding:"required"`
	UserName  string                   `json:"user_name" binding:"required"`
	Strips    []domain.StripAnnotation `json:"strips" binding:"required"`
}

// Dataset-related commands

// ExecuteDatasetQueryCommand represents a command to execute a dataset query
type ExecuteDatasetQueryCommand struct {
	DatasetName string               `json:"dataset_name" binding:"required"`
	SQL         string               `json:"sql,omitempty"`
	Limit       int                  `json:"limit,omitempty"`
	Offset      int                  `json:"offset,omitempty"`
	Page        int                  `json:"page,omitempty"`
	Columns     []string             `json:"columns,omitempty"`
	Filters     []domain.QueryFilter `json:"filters,omitempty"`
	OrderBy     string               `json:"order_by,omitempty"`
	Parameters  map[string]string    `json:"parameters,omitempty"`
	RequestedBy string               `json:"requested_by"`
}

// ExecuteRawSQLCommand represents a command to execute raw SQL
type ExecuteRawSQLCommand struct {
	SQL         string         `json:"sql" binding:"required"`
	Parameters  map[string]any `json:"parameters,omitempty"`
	RequestedBy string         `json:"requested_by" binding:"required"`
}

// CreateDatasetCommand represents a command to create a new dataset and its initial version
// The dataset will be persisted both in the underlying object storage (MinIO) and registered in Trino.
// Version should typically be something like "v1".
// If Columns is empty a simple (id INT, data STRING) definition will be used.
// RequestedBy is mandatory for audit logging.
// NOTE: For now partitioning strategy is kept simple (year, month). This can be extended later.
//
// Example JSON payload:
//
//	{
//	  "dataset_name": "dataset_1",
//	  "version": "v1",
//	  "columns": [{"name": "id", "type": "INT"}, {"name": "data", "type": "STRING"}],
//	  "requested_by": "john.doe"
//	}
//
// The HTTP handler injects project and environment from the URL path.
type CreateDatasetCommand struct {
	ProjectName string                    `json:"-"` // set by handler
	Environment domain.DatasetEnvironment `json:"-"` // set by handler

	DatasetName string                    `json:"dataset_name" binding:"required"`
	Version     string                    `json:"version" binding:"required"`
	Columns     []domain.ColumnDefinition `json:"columns,omitempty"`
	RequestedBy string                    `json:"requested_by" binding:"required"`
}

// CreateDatasetVersionCommand represents a command to create a new version for an existing dataset
type CreateDatasetVersionCommand struct {
	ProjectName string                    `json:"-"`
	Environment domain.DatasetEnvironment `json:"-"`
	DatasetName string                    `json:"-"`

	Version     string                    `json:"version" binding:"required"`
	Columns     []domain.ColumnDefinition `json:"columns,omitempty"`
	RequestedBy string                    `json:"requested_by" binding:"required"`
}

// END OF COMMAND DEFINITIONS

// CreateNotebookCommand represents a command to create a new Kubeflow notebook
// The namespace is provided via the API path and passed separately to the service/repository.
// Resource fields are optional; Kubeflow defaults may apply if omitted.
type CreateNotebookCommand struct {
	Name         string `json:"name" binding:"required"`
	CreatorName  string `json:"creator_name" binding:"required"`
	CreatorEmail string `json:"creator_email" binding:"required"`

	// Server type: "jupyter" (default) or "vscode"
	ServerType string `json:"server_type,omitempty"`

	// Optional resources/config. If omitted, Kubeflow defaults should be used.
	Image    string `json:"image,omitempty"`
	CPU      string `json:"cpu,omitempty"`
	Memory   string `json:"memory,omitempty"`
	GPUCount int    `json:"gpu_count,omitempty"`
	// Image pull policy: IfNotPresent (default), Always, or Never
	ImagePullPolicy string `json:"image_pull_policy,omitempty"`
}

// UpdateNotebookCommand represents updates to an existing notebook
type UpdateNotebookCommand struct {
	Name     string `json:"name" binding:"required"`
	Image    string `json:"image,omitempty"`
	CPU      string `json:"cpu,omitempty"`
	Memory   string `json:"memory,omitempty"`
	GPUCount int    `json:"gpu_count,omitempty"`
	// Optional: switch server type on edit too
	ServerType string `json:"server_type,omitempty"`
	// Optional: update labels/annotations (merging semantics decided by repo)
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
	// Optional: update image pull policy
	ImagePullPolicy string `json:"image_pull_policy,omitempty"`
}
