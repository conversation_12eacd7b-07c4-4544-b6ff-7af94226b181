package app

import (
	"annotationtool-microservice/internal/domain"
	"context"
)

// LabellerRepository defines the interface for labeller persistence
type LabellersRepository interface {
	GetLabellers(ctx context.Context, catalog string) ([]domain.Labeller, error)
	GetLabelledEpisodeIds(ctx context.Context, catalog string, labels, userIds []string, maxLabellingTime *string) ([]string, error)
	GetLabelledStripIds(ctx context.Context, catalog string) ([]string, error)
	GetAnnotationsByEpisodeIDs(ctx context.Context, episodeIDs []string, catalog string) ([]domain.AnnotationEntry, error)
	GetEpisodeIdsAnnotatedByUserIDs(ctx context.Context, userIDs []string, catalog string) ([]string, error)
	GetAllStripAnnotators(ctx context.Context, catalog string) ([]domain.StripAnnotatorEntry, error)
	GetUnsureEpisodeIds(ctx context.Context, catalog string) ([]string, error)
	GetConflictingEpisodeIds(ctx context.Context, catalog string) ([]string, error)
	GetConflictResolvedEpisodeIds(ctx context.Context, catalog string) ([]string, error)
	GetEpisodeLabellingHistory(ctx context.Context, episode_id string, catalog string) ([]domain.LabellingHistory, error)
	GetStripLabellingHistory(ctx context.Context, strip_id string, catalog string) ([]domain.LabellingHistory, error)
	GetLabellingStats(ctx context.Context, catalog string) ([]domain.LabellingStats, error)
	GetLabellersDump(ctx context.Context) ([]domain.Dump, error)
}
