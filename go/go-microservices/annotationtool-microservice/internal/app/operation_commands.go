package app

import "annotationtool-microservice/internal/domain"

// CreateOperationCommand represents a command to create a new labelling operation
type CreateOperationCommand struct {
	DatasetID      string               `json:"datasetID" binding:"required"`
	EpisodeID      string               `json:"episodeID" binding:"required"`
	StripID        string               `json:"stripID"`
	Label          string               `json:"label" binding:"required"`
	LabelTimestamp float64              `json:"labelTimestamp" binding:"required"`
	OperationType  domain.OperationType `json:"operationType" binding:"required"`
	SubmittedBy    string               `json:"submittedBy" binding:"required"`
	Start          *float64             `json:"start,omitempty"` // Start position for time-based annotations
	End            *float64             `json:"end,omitempty"`   // End position for time-based annotations
}

// ToOperation converts the command to a domain Operation
func (cmd *CreateOperationCommand) ToOperation() *domain.Operation {
	return domain.NewOperation(
		cmd.DatasetID,
		cmd.EpisodeID,
		cmd.StripID,
		cmd.Label,
		cmd.LabelTimestamp,
		cmd.OperationType,
		cmd.SubmittedBy,
		cmd.Start,
		cmd.End,
	)
}
