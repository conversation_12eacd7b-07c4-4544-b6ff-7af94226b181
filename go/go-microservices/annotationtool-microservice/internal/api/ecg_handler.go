package api

import (
	"fmt"
	"log/slog"
	"time"

	"annotationtool-microservice/internal/app"
	"annotationtool-microservice/internal/domain"
	"annotationtool-microservice/internal/infra"

	"github.com/gin-gonic/gin"
)

// ECGHandler handles HTTP requests for ECG data operations
type ECGHandler struct {
	ecgService  app.ECGServiceInterface
	logger      *slog.Logger
	metrics     *infra.DatasetMetrics
	auditLogger *infra.AuditLogger
}

// NewECGHandler creates a new ECGHandler
func NewECGHandler(
	ecgService app.ECGServiceInterface,
	logger *slog.Logger,
	metrics *infra.DatasetMetrics,
	auditLogger *infra.AuditLogger,
) *ECGHandler {
	return &ECGHandler{
		ecgService:  ecgService,
		logger:      logger,
		metrics:     metrics,
		auditLogger: auditLogger,
	}
}

// RegisterRoutes registers the ECG routes
func (h *ECGHandler) RegisterRoutes(api *gin.RouterGroup) {
	// ECG endpoints with authentication and rate limiting
	ecg := api.Group("/ecg")
	{
		ecg.POST("/query", h.QueryECGData)
		ecg.POST("/conflicting-episodes", h.GetConflictingEpisodes)
		ecg.GET("/num-conflicting-episodes", h.GetNumConflictingEpisodes)
		ecg.GET("/episodes/:catalog/:episode_id", h.GetEpisodeDetails)
		ecg.POST("/analytics", h.GetECGAnalytics)
		ecg.GET("/clinics", h.GetECGClinics)
		ecg.GET("/patients", h.GetECGPatients)
		ecg.GET("/manufacturers", h.GetECGManufacturers)
		ecg.GET("/device-models", h.GetECGDeviceModels)
		ecg.GET("/dashboard", h.GetECGDashboard)
	}
}

// QueryECGData handles POST /api/v1/ecg/query
func (h *ECGHandler) QueryECGData(c *gin.Context) {
	var req domain.ECGQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body for ECG query", "error", err)
		BadRequest(c, "Invalid request body")
		return
	}

	// Get user ID from context
	userID := "system"
	if uid, exists := c.Get("user_id"); exists {
		userID = uid.(string)
	}

	h.logger.Info("Received ECG query request",
		"user_id", userID,
		"annotator_ids", len(req.AnnotatorIDs),
		"episode_ids", len(req.EpisodeIDs),
		"patient_ids", len(req.PatientIDs),
		"clinic_ids", len(req.ClinicIDs),
		"catalog", req.Catalog,
		"labelled_by", len(req.LabelledBy),
		"not_labelled_by", len(req.NotLabelledBy),
		"limit", req.Limit,
	)

	// Create metrics collector
	queryType := "ecg_query"
	datasetName := "ecg_episodes"

	metricsCollector := infra.NewQueryMetricsCollector(h.metrics, datasetName, queryType, userID)

	// Record dataset access
	accessLayer := req.Catalog
	h.metrics.RecordDatasetAccess(datasetName, accessLayer, userID)
	h.auditLogger.LogDatasetAccess(userID, datasetName, accessLayer, "ecg_query")

	response, err := h.ecgService.QueryECGData(c.Request.Context(), req)

	if err != nil {
		// Record error metrics
		errorType := "unknown"
		switch {
		case app.IsValidationError(err):
			errorType = "validation"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			errorType = "not_found"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			NotFound(c, "ECG data not found")
		default:
			errorType = "internal"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			h.logger.Error("Failed to execute ECG query", "error", err)
			InternalError(c, "Failed to query ECG data")
		}
		return
	}

	// Record success metrics
	totalRows := len(response.Episodes)
	metricsCollector.RecordSuccess(totalRows)
	h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "",
		response.QueryTime, totalRows, true, "")

	Success(c, response)
}

func (h *ECGHandler) GetConflictingEpisodes(c *gin.Context) {
	var req domain.ECGQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body for conflicting episodes", "error", err)
		BadRequest(c, "Invalid request body")
		return
	}

	// Get user ID from context
	userID := "system"
	if uid, exists := c.Get("user_id"); exists {
		userID = uid.(string)
	}

	h.logger.Info("Received conflicting episodes request",
		"user_id", userID,
		"episode_ids", len(req.EpisodeIDs),
		"patient_ids", len(req.PatientIDs),
		"clinic_ids", len(req.ClinicIDs),
		"catalog", req.Catalog,
		"labelled_by", len(req.LabelledBy),
		"not_labelled_by", len(req.NotLabelledBy),
		"limit", req.Limit,
	)

	// Create metrics collector
	queryType := "conflicting_episodes"
	datasetName := "ecg_episodes"

	metricsCollector := infra.NewQueryMetricsCollector(h.metrics, datasetName, queryType, userID)

	// Record dataset access
	accessLayer := req.Catalog
	h.metrics.RecordDatasetAccess(datasetName, accessLayer, userID)
	h.auditLogger.LogDatasetAccess(userID, datasetName, accessLayer, queryType)

	response, err := h.ecgService.GetConflictingEpisodes(c.Request.Context(), req)
	if err != nil {
		// Record error metrics
		errorType := "unknown"
		switch {
		case app.IsValidationError(err):
			errorType = "validation"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			errorType = "not_found"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			NotFound(c, "Conflicting episodes not found")
		default:
			errorType = "internal"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			h.logger.Error("Failed to get conflicting episodes", "error", err)
			InternalError(c, "Failed to get conflicting episodes")
		}
		return
	}

	// Record success metrics
	totalRows := len(response.Episodes)
	metricsCollector.RecordSuccess(totalRows)
	h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "",
		response.QueryTime, totalRows, true, "")

	Success(c, response)
}

func (h *ECGHandler) GetNumConflictingEpisodes(c *gin.Context) {
	var req domain.ECGNumConflictingEpisodesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid request body for conflicting episodes", "error", err)
		BadRequest(c, "Invalid request body")
		return
	}

	// Get user ID from context
	userID := "system"
	if uid, exists := c.Get("user_id"); exists {
		userID = uid.(string)
	}

	h.logger.Info("Received conflicting episodes request",
		"user_id", userID,
		"catalog", req.Catalog,
	)

	// Create metrics collector
	queryType := "num_conflicting_episodes"
	datasetName := "ecg_episodes"

	metricsCollector := infra.NewQueryMetricsCollector(h.metrics, datasetName, queryType, userID)

	// Record dataset access
	accessLayer := req.Catalog
	h.metrics.RecordDatasetAccess(datasetName, accessLayer, userID)
	h.auditLogger.LogDatasetAccess(userID, datasetName, accessLayer, queryType)

	response, err := h.ecgService.GetNumConflictingEpisodes(c.Request.Context(), req)
	if err != nil {
		// Record error metrics
		errorType := "unknown"
		switch {
		case app.IsValidationError(err):
			errorType = "validation"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			errorType = "not_found"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			NotFound(c, "Conflicting episodes not found")
		default:
			errorType = "internal"
			metricsCollector.RecordError(errorType)
			h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 0, false, err.Error())
			h.logger.Error("Failed to get conflicting episodes", "error", err)
			InternalError(c, "Failed to get conflicting episodes")
		}
		return
	}

	// Record success metrics
	metricsCollector.RecordSuccess(int(response.NumConflictingEpisodes))
	h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "",
		response.QueryTime, int(response.NumConflictingEpisodes), true, "")

	Success(c, response)
}

// GetEpisodeDetails handles GET /api/v1/ecg/episodes/:episode_id
func (h *ECGHandler) GetEpisodeDetails(c *gin.Context) {
	episodeID := c.Param("episode_id")
	catalog := c.Param("catalog")
	if episodeID == "" || catalog == "" {
		BadRequest(c, "Episode ID and catalog are required")
		return
	}

	// Get user ID from context
	userID := "system"
	if uid, exists := c.Get("user_id"); exists {
		userID = uid.(string)
	}

	h.logger.Info("Getting episode details",
		"user_id", userID,
		"episode_id", episodeID,
	)

	// Create metrics collector
	queryType := "episode_details"
	datasetName := "ecg_episodes"
	metricsCollector := infra.NewQueryMetricsCollector(h.metrics, datasetName, queryType, userID)

	// Record dataset access
	h.metrics.RecordDatasetAccess(datasetName, "unknown", userID)
	h.auditLogger.LogDatasetAccess(userID, datasetName, "unknown", "episode_details")

	episode, err := h.ecgService.GetEpisodeDetails(c.Request.Context(), episodeID, catalog)
	if err != nil {
		// Record error metrics
		errorType := "unknown"
		switch {
		case app.IsValidationError(err):
			errorType = "validation"
			metricsCollector.RecordError(errorType)
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			errorType = "not_found"
			metricsCollector.RecordError(errorType)
			NotFound(c, "Episode not found")
		default:
			errorType = "internal"
			metricsCollector.RecordError(errorType)
			h.logger.Error("Failed to get episode details", "error", err)
			InternalError(c, "Failed to get episode details")
		}
		return
	}

	// Record success metrics
	metricsCollector.RecordSuccess(1)
	h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 1, true, "")

	Success(c, episode)
}

// GetECGAnalytics handles POST /api/v1/ecg/analytics
func (h *ECGHandler) GetECGAnalytics(c *gin.Context) {
	var req domain.ECGAnalyticsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body for ECG analytics", "error", err)
		BadRequest(c, "Invalid request body")
		return
	}

	// Get user ID from context
	userID := "system"
	if uid, exists := c.Get("user_id"); exists {
		userID = uid.(string)
	}

	h.logger.Info("Received ECG analytics request",
		"user_id", userID,
		"date_from", req.DateFrom,
		"date_to", req.DateTo,
		"group_by", req.GroupBy,
	)

	// Create metrics collector
	queryType := "ecg_analytics"
	datasetName := "ecg_episodes_analytics"
	metricsCollector := infra.NewQueryMetricsCollector(h.metrics, datasetName, queryType, userID)

	// Record dataset access
	h.metrics.RecordDatasetAccess(datasetName, "gold", userID)
	h.auditLogger.LogDatasetAccess(userID, datasetName, "gold", "analytics")

	response, err := h.ecgService.GetECGAnalytics(c.Request.Context(), req)
	if err != nil {
		// Record error metrics
		errorType := "unknown"
		switch {
		case app.IsValidationError(err):
			errorType = "validation"
			metricsCollector.RecordError(errorType)
			ValidationError(c, err.Error())
		default:
			errorType = "internal"
			metricsCollector.RecordError(errorType)
			h.logger.Error("Failed to generate ECG analytics", "error", err)
			InternalError(c, "Failed to generate analytics")
		}
		return
	}

	// Record success metrics
	metricsCollector.RecordSuccess(1)
	h.auditLogger.LogQueryExecution(userID, datasetName, queryType, "", 0, 1, true, "")

	Success(c, response)
}

// GetECGVersions handles GET /api/v1/ecg/versions - returns available table versions
func (h *ECGHandler) GetECGVersions(c *gin.Context) {
	versions := map[string]interface{}{
		"available_versions": []string{
			"hive.default.episodes_v1_0_3",
			"hive.default.episodes_v2_0_0",
		},
		"descriptions": map[string]string{
			"hive.default.episodes_v1_0_3": "Legacy ECG episodes table structure",
			"hive.default.episodes_v2_0_0": "Updated ECG episodes table with improved schema",
		},
		"current_default": "hive.default.episodes_v1_0_3",
		"recommended":     "hive.default.episodes_v1_0_3",
		"format":          "catalog.schema.table",
		"timestamp":       time.Now(),
	}

	Success(c, versions)
}

// GetECGSchema handles GET /api/v1/ecg/schema - returns the ECG data schema
func (h *ECGHandler) GetECGSchema(c *gin.Context) {
	layer := c.Query("layer")
	if layer == "" {
		layer = "gold"
	}

	schema := map[string]interface{}{
		"layer":      layer,
		"table_name": "ecg_episodes_" + layer,
		"fields": []map[string]string{
			{"name": "episode_id", "type": "varchar", "description": "Unique episode identifier"},
			{"name": "patient_id", "type": "varchar", "description": "Patient identifier"},
			{"name": "patient_name", "type": "varchar", "description": "Patient name"},
			{"name": "clinic_id", "type": "varchar", "description": "Clinic identifier"},
			{"name": "clinic_name", "type": "varchar", "description": "Clinic name"},
			{"name": "manufacturer", "type": "varchar", "description": "Device manufacturer"},
			{"name": "device_label", "type": "varchar", "description": "Device label"},
			{"name": "device_model", "type": "varchar", "description": "Device model"},
			{"name": "report_time", "type": "timestamp", "description": "Episode report timestamp"},
			{"name": "episode_length", "type": "varchar", "description": "Duration of the episode"},
			{"name": "assessment", "type": "varchar", "description": "Clinical assessment"},
			{"name": "labelling_status", "type": "varchar", "description": "Labelling status (labelled/unlabelled)"},
			{"name": "strip_id", "type": "varchar", "description": "ECG strip identifier"},
			{"name": "strip_number", "type": "integer", "description": "Strip sequence number"},
			{"name": "timestamps", "type": "array(double)", "description": "ECG timestamp data"},
			{"name": "voltages", "type": "array(double)", "description": "ECG voltage measurements"},
			{"name": "file_path", "type": "varchar", "description": "MinIO file path"},
			{"name": "created_at", "type": "timestamp", "description": "Record creation timestamp"},
			{"name": "updated_at", "type": "timestamp", "description": "Record update timestamp"},
		},
		"sample_query": fmt.Sprintf(`
SELECT episode_id, patient_id, clinic_name, manufacturer, report_time, labelling_status
FROM minio.%s.ecg_classification_v1_0_0
WHERE report_time >= CURRENT_DATE - INTERVAL '30' DAY
ORDER BY report_time DESC
LIMIT 100`, layer),
		"minio_path_examples": []string{
			"//minio/gold/r&d/datasets/ecg_classification/v1.0.0/data.csv",
			"//minio/silver/processed/ecg_data/2024/episodes.parquet",
			"//minio/bronze/raw/device_exports/philips/ecg_strips.json",
		},
		"timestamp": time.Now(),
	}

	Success(c, schema)
}

// GetECGClinics handles GET /api/v1/ecg/clinics
func (h *ECGHandler) GetECGClinics(c *gin.Context) {
	var req domain.ECGClinicRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid query parameters for clinics", "error", err)
		BadRequest(c, "Invalid query parameters")
		return
	}

	clinics, err := h.ecgService.GetClinics(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get clinics from Trino", "error", err)
		InternalError(c, "Failed to retrieve clinics")
		return
	}
	Success(c, clinics)
}

// GetECGPatients handles GET /api/v1/ecg/patients?clinic_id=xxx
func (h *ECGHandler) GetECGPatients(c *gin.Context) {
	var req domain.ECGPatientRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid query parameters for patients", "error", err)
		BadRequest(c, "Invalid query parameters")
		return
	}

	patients, err := h.ecgService.GetPatients(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get patients from Trino", "clinic_id", req.ClinicID, "error", err)
		InternalError(c, "Failed to retrieve patients")
		return
	}
	Success(c, patients)
}

// GetECGManufacturers handles GET /api/v1/ecg/manufacturers
func (h *ECGHandler) GetECGManufacturers(c *gin.Context) {
	var req domain.ECGManufacturerRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid query parameters for manufacturers", "error", err)
		BadRequest(c, "Invalid query parameters")
		return
	}
	manufacturers, err := h.ecgService.GetManufacturers(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get manufacturers from Trino", "error", err)
		InternalError(c, "Failed to retrieve manufacturers")
		return
	}
	Success(c, manufacturers)
}

// GetECGDeviceModels handles GET /api/v1/ecg/device-models?manufacturer=xxx
func (h *ECGHandler) GetECGDeviceModels(c *gin.Context) {
	var req domain.ECGDeviceModelRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid query parameters for device models", "error", err)
		BadRequest(c, "Invalid query parameters")
		return
	}
	models, err := h.ecgService.GetDeviceModels(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get device models from Trino", "manufacturer", req.Manufacturer, "error", err)
		InternalError(c, "Failed to retrieve device models")
		return
	}
	Success(c, models)
}

func (h *ECGHandler) GetECGDashboard(c *gin.Context) {
	var req domain.ECGDashboardRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid query parameters for dashboard", "error", err)
		BadRequest(c, "Invalid query parameters")
		return
	}

	// Debug logging
	h.logger.Info("Dashboard request parsed",
		"clinic_id", req.ClinicID,
		"manufacturer", req.Manufacturer,
		"labelled_only", req.LabelledOnly,
		"catalog", req.Catalog,
	)

	rawQueryParams := c.Request.URL.RawQuery

	dashboard, err := h.ecgService.GetECGDashboard(c.Request.Context(), req, rawQueryParams)
	if err != nil {
		h.logger.Error("Failed to get dashboard from Trino", "error", err)
		InternalError(c, "Failed to retrieve dashboard")
		return
	}
	Success(c, dashboard)
}
