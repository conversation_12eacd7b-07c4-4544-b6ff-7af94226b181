package api

import (
	"log/slog"

	"annotationtool-microservice/internal/app"

	"github.com/gin-gonic/gin"
)

// OperationHandler handles HTTP requests for operations
type OperationHandler struct {
	operationService     *app.OperationService
	logger               *slog.Logger
	publisher            app.Publisher
	shouldPublishCommand bool
}

// NewOperationHandler creates a new OperationHandler
func NewOperationHandler(
	operationService *app.OperationService,
	publisher app.Publisher,
	logger *slog.Logger,
	shouldPublishCommand bool,
) *OperationHandler {
	return &OperationHandler{
		operationService:     operationService,
		logger:               logger,
		publisher:            publisher,
		shouldPublishCommand: shouldPublishCommand,
	}
}

// RegisterRoutes registers the operation routes
func (h *OperationHandler) RegisterRoutes(api *gin.RouterGroup) {
	api.POST("/operation", h.CreateOperation)
}

// CreateOperation handles POST /api/v1/operation
func (h *OperationHandler) CreateOperation(c *gin.Context) {
	var cmd app.CreateOperationCommand

	// Bind JSON body
	if err := c.ShouldBindJSON(&cmd); err != nil {
		h.logger.Warn("Invalid request body", "error", err)
		BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Log the operation for debugging
	h.logger.Info("Received operation request",
		"dataset_id", cmd.DatasetID,
		"episode_id", cmd.EpisodeID,
		"strip_id", cmd.StripID,
		"label", cmd.Label,
		"operation_type", cmd.OperationType,
		"submitted_by", cmd.SubmittedBy,
	)

	// Check if we should publish the command to Kafka, or process it directly via app layer
	if h.shouldPublishCommand {
		// Process via Kafka
		if h.publisher == nil {
			InternalError(c, "Kafka processing enabled but publisher is not available")
			return
		}

		if asyncErr := h.publisher.PublishCommand(c.Request.Context(), cmd); asyncErr != nil {
			h.logger.Error("Failed to queue command", "Command", cmd, "error", asyncErr)
			InternalError(c, "Failed to queue operation for processing.")
			return
		}

		Success(c, gin.H{"message": "Operation creation has been queued for processing."})
		return
	}

	// Process directly via service layer
	operation, err := h.operationService.CreateOperation(c.Request.Context(), cmd)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		default:
			h.logger.Error("Failed to create operation", "error", err)
			InternalError(c, "Failed to create operation")
		}
		return
	}

	Created(c, map[string]interface{}{
		"message":      "Operation created successfully",
		"operation_id": operation.ID,
	})
}
