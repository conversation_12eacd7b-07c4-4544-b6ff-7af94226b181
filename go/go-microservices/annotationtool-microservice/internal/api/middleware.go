package api

import (
	"annotationtool-microservice/internal/infra"
	"bytes"
	"context"
	"encoding/json"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AccountInfoRequest represents the request structure for getAccountInfoByToken
type AuthTokenRequest struct {
	Method string        `json:"method"`
	Params []interface{} `json:"params"`
}

// AuthenticationMiddleware creates a middleware for JWT token validation
func AuthenticationMiddleware(accountServiceURL string, logger *slog.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract Authorization header
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			logger.Warn("Missing Authorization header", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authorization header is required",
			})
			c.Abort()
			return
		}

		// Don't log tokens; just note that the header is present
		logger.Debug("Authorization header received", "path", c.Request.URL.Path)

		// Check if it's a Bearer token
		if !strings.HasPrefix(authHeader, "Bearer ") {
			logger.Warn("Invalid Authorization header format", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Authorization header must be a Bearer token",
			})
			c.Abort()
			return
		}

		// Extract token (remove "Bearer " prefix)
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			logger.Warn("Empty token in Authorization header", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Bearer token cannot be empty",
			})
			c.Abort()
			return
		}

		// Validate token with account service
		if !validateTokenWithAccountService(token, accountServiceURL, logger) {
			logger.Warn("Token validation failed", "path", c.Request.URL.Path)
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid or expired token",
			})
			c.Abort()
			return
		}

		// Token is valid, continue with request
		c.Next()
	}
}

// validateTokenWithAccountService makes a POST call to the account service to validate the token
func validateTokenWithAccountService(token, accountServiceURL string, logger *slog.Logger) bool {
	// Prepare request payload
	requestPayload := AuthTokenRequest{
		Method: "getAccountInfoByToken",
		Params: []interface{}{},
	}

	jsonData, err := json.Marshal(requestPayload)
	if err != nil {
		logger.Error("Failed to marshal auth request", "error", err)
		return false
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", accountServiceURL, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("Failed to create auth request", "error", err)
		return false
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	req.Body = io.NopCloser(bytes.NewBuffer(jsonData))

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Make the request
	resp, err := client.Do(req)
	if err != nil {
		logger.Error("Failed to call account service", "error", err, "url", accountServiceURL)
		return false
	}
	defer resp.Body.Close()

	// Read response body for logging
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Failed to read auth response body", "error", err)
		return false
	}

	// Log high-level info only; avoid dumping full body (may contain sensitive data)
	logger.Info("Account service response received",
		"status_code", resp.StatusCode,
		"url", accountServiceURL)

	// Check if response is 200 OK
	if resp.StatusCode != http.StatusOK {
		logger.Warn("Account service returned non-200 response",
			"status_code", resp.StatusCode,
			"response_snippet", truncateString(string(body), 256),
			"url", accountServiceURL)
		return false
	}

	// Parse response body to check for error field
	var responseBody map[string]interface{}
	if err := json.Unmarshal(body, &responseBody); err != nil {
		logger.Error("Failed to parse auth response body", "error", err, "body", string(body))
		return false
	}

	// Check if response contains an error field
	if errorField, exists := responseBody["error"]; exists && errorField != nil {
		logger.Warn("Account service returned error in response",
			"error", errorField,
			"response_body", string(body))
		return false
	}

	logger.Info("Token validation successful",
		"status_code", resp.StatusCode)
	return true
}

// LoggingMiddleware creates a middleware for request logging
func LoggingMiddleware(logger *slog.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		var reqID any
		if v, ok := param.Keys["request_id"]; ok {
			reqID = v
		}
		ua := ""
		if param.Request != nil {
			ua = param.Request.UserAgent()
		}
		log := logger
		if reqID != nil {
			log = log.With("request_id", reqID)
		}
		log.Info("HTTP Request",
			"method", param.Method,
			"path", param.Path,
			"status", param.StatusCode,
			"duration", param.Latency,
			"client_ip", param.ClientIP,
			"user_agent", ua,
		)
		return ""
	})
}

// ErrorHandlingMiddleware creates a middleware for error handling
func ErrorHandlingMiddleware(logger *slog.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		reqID := c.GetString("request_id")
		log := logger
		if reqID != "" {
			log = log.With("request_id", reqID)
		}
		log.Error("Panic recovered",
			"error", recovered,
			"path", c.Request.URL.Path,
			"method", c.Request.Method,
		)

		InternalError(c, "Internal server error")
		c.Abort()
	})
}

// CORSMiddleware creates a middleware for CORS headers
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// TimeoutMiddleware creates a middleware for request timeout
func TimeoutMiddleware(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		// Replace the request context
		c.Request = c.Request.WithContext(ctx)

		// Create a channel to signal completion
		done := make(chan struct{})

		// Run the request in a goroutine
		go func() {
			defer close(done)
			c.Next()
		}()

		// Wait for completion or timeout
		select {
		case <-done:
			// Request completed normally
			return
		case <-ctx.Done():
			// Request timed out
			InternalError(c, "Request timeout")
			c.Abort()
		}
	}
}

// SecurityHeadersMiddleware adds security headers to all responses
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")

		// Remove server information
		c.Header("Server", "")

		c.Next()
	}
}

// RateLimitMiddleware implements rate limiting per user/IP
func RateLimitMiddleware(logger *slog.Logger, metrics *infra.DatasetMetrics) gin.HandlerFunc {
	// Simple in-memory rate limiter (in production, use Redis or similar)
	type rateLimiter struct {
		requests  int
		resetTime time.Time
	}

	limiters := make(map[string]*rateLimiter)
	const maxRequests = 500 // requests per minute
	const windowDuration = time.Minute

	return func(c *gin.Context) {
		// Get identifier (user_id if authenticated, otherwise IP)
		identifier := c.ClientIP()
		if userID, exists := c.Get("user_id"); exists {
			identifier = userID.(string)
		}

		now := time.Now()

		// Get or create rate limiter for this identifier
		limiter, exists := limiters[identifier]
		if !exists || now.After(limiter.resetTime) {
			limiters[identifier] = &rateLimiter{
				requests:  1,
				resetTime: now.Add(windowDuration),
			}
			c.Next()
			return
		}

		// Check if rate limit exceeded
		if limiter.requests >= maxRequests {
			// Attach request id if available
			reqID := c.GetString("request_id")
			log := logger
			if reqID != "" {
				log = log.With("request_id", reqID)
			}
			log.Warn("Rate limit exceeded", "identifier", identifier, "requests", limiter.requests)
			metrics.RecordRateLimitHit(identifier, c.Request.URL.Path)
			c.JSON(http.StatusTooManyRequests, APIResponse{
				Success: false,
				Error: &APIError{
					Code:    "RATE_LIMIT_EXCEEDED",
					Message: "Too many requests. Please try again later.",
				},
			})
			c.Abort()
			return
		}

		// Increment request count
		limiter.requests++
		c.Next()
	}
}

// RequestIDMiddleware injects a request ID into the context and response headers
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		rid := c.GetHeader("X-Request-ID")
		if rid == "" {
			rid = uuid.NewString()
		}
		c.Set("request_id", rid)
		c.Writer.Header().Set("X-Request-ID", rid)
		c.Next()
	}
}

// truncateString returns a substring of s with max length n
func truncateString(s string, n int) string {
	if len(s) <= n {
		return s
	}
	if n <= 3 {
		return s[:n]
	}
	return s[:n-3] + "..."
}
