package api

import (
	"bufio"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// LogsHandler handles log-related HTTP requests
type LogsHandler struct {
	logger *slog.Logger
}

// NewLogsHandler creates a new logs handler
func NewLogsHandler(logger *slog.Logger) *LogsHandler {
	return &LogsHandler{
		logger: logger,
	}
}

// LogEntry represents a single log entry
type LogEntry struct {
	Timestamp string `json:"timestamp"`
	Level     string `json:"level"`
	Message   string `json:"message"`
	Source    string `json:"source,omitempty"`
	Service   string `json:"service,omitempty"`
	Version   string `json:"version,omitempty"`
	Env       string `json:"environment,omitempty"`
	Error     string `json:"error,omitempty"`
	RawLine   string `json:"raw_line"`
}

// LogsResponse represents the response structure for logs API
type LogsResponse struct {
	Logs       []LogEntry `json:"logs"`
	TotalCount int        `json:"total_count"`
	Has<PERSON>ore    bool       `json:"has_more"`
	Query      LogQuery   `json:"query"`
}

// LogQuery represents query parameters for logs
type LogQuery struct {
	Level     string `json:"level,omitempty"`
	Service   string `json:"service,omitempty"`
	Since     string `json:"since,omitempty"`
	Until     string `json:"until,omitempty"`
	Limit     int    `json:"limit"`
	Search    string `json:"search,omitempty"`
	Component string `json:"component,omitempty"`
}

// GetLogs handles GET /api/v1/logs
func (h *LogsHandler) GetLogs(c *gin.Context) {
	query := LogQuery{
		Limit: 100, // default limit
	}

	// Parse query parameters
	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 1000 {
			query.Limit = l
		}
	}

	query.Level = c.Query("level")
	query.Service = c.Query("service")
	query.Since = c.Query("since")
	query.Until = c.Query("until")
	query.Search = c.Query("search")
	query.Component = c.Query("component")

	h.logger.Info("Fetching logs",
		"query_limit", query.Limit,
		"query_level", query.Level,
		"query_search", query.Search)

	logs, err := h.fetchLogs(query)
	if err != nil {
		h.logger.Error("Failed to fetch logs", "error", err)
		InternalError(c, "Failed to fetch logs")
		return
	}

	response := LogsResponse{
		Logs:       logs,
		TotalCount: len(logs),
		HasMore:    len(logs) == query.Limit,
		Query:      query,
	}

	c.JSON(http.StatusOK, response)
}

// GetRecentLogs handles GET /api/v1/logs/recent
func (h *LogsHandler) GetRecentLogs(c *gin.Context) {
	limit := 50
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 500 {
			limit = parsed
		}
	}

	level := c.Query("level")
	search := c.Query("search")

	query := LogQuery{
		Limit:  limit,
		Level:  level,
		Search: search,
		Since:  time.Now().Add(-1 * time.Hour).Format(time.RFC3339), // Last hour by default
	}

	logs, err := h.fetchLogs(query)
	if err != nil {
		h.logger.Error("Failed to fetch recent logs", "error", err)
		InternalError(c, "Failed to fetch recent logs")
		return
	}

	response := LogsResponse{
		Logs:       logs,
		TotalCount: len(logs),
		HasMore:    len(logs) == limit,
		Query:      query,
	}

	c.JSON(http.StatusOK, response)
}

// fetchLogs retrieves logs based on the query parameters
func (h *LogsHandler) fetchLogs(query LogQuery) ([]LogEntry, error) {
	// Try to read from log files first, then fall back to stdout/stderr capture
	logs, err := h.readFromLogFiles(query)
	if err != nil || len(logs) == 0 {
		h.logger.Warn("Could not read from log files, using fallback method", "error", err)
		// Fallback: read from a temporary log buffer if available
		logs = h.getRecentLogsFromBuffer(query)
	}

	return logs, nil
}

// readFromLogFiles attempts to read logs from log files
func (h *LogsHandler) readFromLogFiles(query LogQuery) ([]LogEntry, error) {
	// Common log file locations
	logPaths := []string{
		"/var/log/annotationtool-microservice.log",
		"/tmp/annotationtool-microservice.log",
		"./logs/annotationtool-microservice.log",
		"./annotationtool-microservice.log",
	}

	// Try to find an existing log file
	var logFile string
	for _, path := range logPaths {
		if _, err := os.Stat(path); err == nil {
			logFile = path
			break
		}
	}

	if logFile == "" {
		return nil, fmt.Errorf("no log file found")
	}

	file, err := os.Open(logFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file %s: %w", logFile, err)
	}
	defer file.Close()

	return h.parseLogFile(file, query)
}

// parseLogFile parses a log file and returns matching log entries
func (h *LogsHandler) parseLogFile(reader io.Reader, query LogQuery) ([]LogEntry, error) {
	var logs []LogEntry
	scanner := bufio.NewScanner(reader)

	// Parse since/until times
	var sinceTime, untilTime time.Time
	var err error

	if query.Since != "" {
		sinceTime, err = time.Parse(time.RFC3339, query.Since)
		if err != nil {
			// Try alternative formats
			if sinceTime, err = time.Parse("2006-01-02T15:04:05", query.Since); err != nil {
				h.logger.Warn("Could not parse since time", "since", query.Since, "error", err)
			}
		}
	}

	if query.Until != "" {
		untilTime, err = time.Parse(time.RFC3339, query.Until)
		if err != nil {
			if untilTime, err = time.Parse("2006-01-02T15:04:05", query.Until); err != nil {
				h.logger.Warn("Could not parse until time", "until", query.Until, "error", err)
			}
		}
	}

	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		entry := h.parseLogLine(line)
		if entry == nil {
			continue
		}

		// Apply filters
		if !h.matchesQuery(entry, query, sinceTime, untilTime) {
			continue
		}

		logs = append(logs, *entry)

		// Limit results
		if len(logs) >= query.Limit {
			break
		}
	}

	// Sort by timestamp (newest first)
	sort.Slice(logs, func(i, j int) bool {
		return logs[i].Timestamp > logs[j].Timestamp
	})

	return logs, scanner.Err()
}

// parseLogLine parses a single log line and extracts structured information
func (h *LogsHandler) parseLogLine(line string) *LogEntry {
	entry := &LogEntry{
		RawLine: line,
	}

	// Try to parse structured log format
	// Format: time=2025-08-05T14:31:31.796+02:00 level=INFO source=... msg="..." service=... version=... environment=...

	fields := make(map[string]string)
	parts := strings.Fields(line)

	for _, part := range parts {
		if strings.Contains(part, "=") {
			kv := strings.SplitN(part, "=", 2)
			if len(kv) == 2 {
				key := kv[0]
				value := kv[1]
				// Remove quotes if present
				if strings.HasPrefix(value, `"`) && strings.HasSuffix(value, `"`) {
					value = strings.Trim(value, `"`)
				}
				fields[key] = value
			}
		}
	}

	// Extract known fields
	if timestamp, ok := fields["time"]; ok {
		entry.Timestamp = timestamp
	}
	if level, ok := fields["level"]; ok {
		entry.Level = level
	}
	if msg, ok := fields["msg"]; ok {
		entry.Message = msg
	}
	if source, ok := fields["source"]; ok {
		entry.Source = source
	}
	if service, ok := fields["service"]; ok {
		entry.Service = service
	}
	if version, ok := fields["version"]; ok {
		entry.Version = version
	}
	if env, ok := fields["environment"]; ok {
		entry.Env = env
	}
	if errorMsg, ok := fields["error"]; ok {
		entry.Error = errorMsg
	}

	// If we couldn't parse any structured data, try to extract basic info
	if entry.Timestamp == "" {
		// Try to find a timestamp pattern at the beginning
		if len(line) > 19 && (strings.Contains(line[:30], "T") || strings.Contains(line[:30], ":")) {
			parts := strings.Fields(line)
			if len(parts) > 0 {
				entry.Timestamp = parts[0]
			}
		}
	}

	return entry
}

// matchesQuery checks if a log entry matches the query filters
func (h *LogsHandler) matchesQuery(entry *LogEntry, query LogQuery, sinceTime, untilTime time.Time) bool {
	// Level filter
	if query.Level != "" && !strings.EqualFold(entry.Level, query.Level) {
		return false
	}

	// Service filter
	if query.Service != "" && !strings.Contains(strings.ToLower(entry.Service), strings.ToLower(query.Service)) {
		return false
	}

	// Component filter (search in source field)
	if query.Component != "" && !strings.Contains(strings.ToLower(entry.Source), strings.ToLower(query.Component)) {
		return false
	}

	// Search filter (search in message and raw line)
	if query.Search != "" {
		searchLower := strings.ToLower(query.Search)
		if !strings.Contains(strings.ToLower(entry.Message), searchLower) &&
			!strings.Contains(strings.ToLower(entry.RawLine), searchLower) &&
			!strings.Contains(strings.ToLower(entry.Error), searchLower) {
			return false
		}
	}

	// Time range filters
	if !sinceTime.IsZero() || !untilTime.IsZero() {
		entryTime, err := time.Parse(time.RFC3339, entry.Timestamp)
		if err != nil {
			// Try alternative parsing
			if entryTime, err = time.Parse("2006-01-02T15:04:05.000-07:00", entry.Timestamp); err != nil {
				return true // If we can't parse the time, include it
			}
		}

		if !sinceTime.IsZero() && entryTime.Before(sinceTime) {
			return false
		}
		if !untilTime.IsZero() && entryTime.After(untilTime) {
			return false
		}
	}

	return true
}

// getRecentLogsFromBuffer returns recent logs from an in-memory buffer (fallback method)
func (h *LogsHandler) getRecentLogsFromBuffer(query LogQuery) []LogEntry {
	// This is a fallback method when log files are not available
	// In a production system, you might want to implement a circular buffer
	// or use a logging library that supports log retrieval

	// For now, return some sample recent logs to demonstrate the API
	now := time.Now()

	sampleLogs := []LogEntry{
		{
			Timestamp: now.Add(-5 * time.Minute).Format(time.RFC3339),
			Level:     "INFO",
			Message:   "Dataset import initiated successfully - running in background",
			Service:   "annotationtool-microservice",
			Version:   "1.0.0",
			Env:       "development",
			RawLine:   fmt.Sprintf("time=%s level=INFO msg=\"Dataset import initiated successfully - running in background\" service=annotationtool-microservice version=1.0.0 environment=development", now.Add(-5*time.Minute).Format(time.RFC3339)),
		},
		{
			Timestamp: now.Add(-10 * time.Minute).Format(time.RFC3339),
			Level:     "INFO",
			Message:   "Starting data insertion into local table - this may take several minutes for large datasets",
			Service:   "annotationtool-microservice",
			Version:   "1.0.0",
			Env:       "development",
			RawLine:   fmt.Sprintf("time=%s level=INFO msg=\"Starting data insertion into local table - this may take several minutes for large datasets\" service=annotationtool-microservice version=1.0.0 environment=development", now.Add(-10*time.Minute).Format(time.RFC3339)),
		},
		{
			Timestamp: now.Add(-15 * time.Minute).Format(time.RFC3339),
			Level:     "INFO",
			Message:   "Successfully connected to ClickHouse",
			Service:   "annotationtool-microservice",
			Version:   "1.0.0",
			Env:       "development",
			RawLine:   fmt.Sprintf("time=%s level=INFO msg=\"Successfully connected to ClickHouse\" service=annotationtool-microservice version=1.0.0 environment=development", now.Add(-15*time.Minute).Format(time.RFC3339)),
		},
	}

	// Apply filters
	var filteredLogs []LogEntry
	for _, log := range sampleLogs {
		if h.matchesQuery(&log, query, time.Time{}, time.Time{}) {
			filteredLogs = append(filteredLogs, log)
		}
	}

	// Limit results
	if len(filteredLogs) > query.Limit {
		filteredLogs = filteredLogs[:query.Limit]
	}

	return filteredLogs
}

// RegisterRoutes registers all logs routes
func (h *LogsHandler) RegisterRoutes(router *gin.RouterGroup) {
	logs := router.Group("/logs")
	{
		logs.GET("", h.GetLogs)
		logs.GET("/recent", h.GetRecentLogs)
	}
}
