package api

import (
	"time"

	"annotationtool-microservice/internal/container"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes registers all API routes and middleware
func RegisterRoutes(router *gin.Engine, container *container.Container) {
	// Apply global middleware
	router.Use(RequestIDMiddleware())
	router.Use(CORSMiddleware())
	router.Use(RateLimitMiddleware(container.Logger(), container.DatasetMetrics()))
	router.Use(LoggingMiddleware(container.Logger()))
	router.Use(ErrorHandlingMiddleware(container.Logger()))
	router.Use(TimeoutMiddleware(30 * time.Second))
	router.Use(SecurityHeadersMiddleware())

	// Health check endpoint (no authentication required)
	router.GET("/health", func(c *gin.Context) {
		Success(c, map[string]interface{}{
			"status":    "healthy",
			"service":   "annotationtool-microservice",
			"version":   container.Config().Version,
			"timestamp": time.Now().UTC(),
		})
	})

	// Protected API routes group with authentication middleware
	api := router.Group("/api/v1")
	api.Use(AuthenticationMiddleware(container.Config().AccountServiceURL, container.Logger()))

	// Create and register operation handler
	operationHandler := NewOperationHandler(
		container.OperationService(),
		container.PublishingService(),
		container.Logger(),
		container.Config().KafkaEnabled, // Flag for kafka publishing
	)
	operationHandler.RegisterRoutes(api)

	// Create and register labellers handler
	labellersHandler := NewLabellersHandler(
		container.LabellersService(),
		container.Logger(),
	)
	labellersHandler.RegisterRoutes(api)

	// Create and register ecg handler
	ecgHandler := NewECGHandler(container.ECGService(), container.Logger(), container.DatasetMetrics(), container.AuditLogger())
	ecgHandler.RegisterRoutes(api) // Use the protected API group, not the main router

	// Create and register dataset handler
	datasetHandler := NewDatasetHandler(
		container.DatasetService(),
		container.Publisher(),
		container.Logger(),
		container.Config().KafkaEnabled, // Flag for kafka publishing
	)
	datasetHandler.RegisterRoutes(api) // Pass the protected group, not the router

	// Create and register logs handler
	logsHandler := NewLogsHandler(container.Logger())
	logsHandler.RegisterRoutes(api)

	notebookHandler := NewNotebookHandler(
		container.NotebookService(),
		container.Logger(),
	)

	notebookapi := router.Group("/api/v1")
	notebookHandler.RegisterRoutes(notebookapi)
}
