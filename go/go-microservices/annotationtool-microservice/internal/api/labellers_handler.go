package api

import (
	"log/slog"

	"annotationtool-microservice/internal/app"

	"github.com/gin-gonic/gin"
)

// OperationHandler handles HTTP requests for operations
type LabellersHandler struct {
	labellersService *app.LabellersService
	logger           *slog.Logger
}

type GetLabellersRequest struct {
	Catalog string `form:"catalog"`
}

// NewOperationHandler creates a new OperationHandler
func NewLabellersHandler(
	labellersService *app.LabellersService,
	logger *slog.Logger,
) *LabellersHandler {
	return &LabellersHandler{
		labellersService: labellersService,
		logger:           logger,
	}
}

// RegisterRoutes registers the labellers routes
func (h *LabellersHandler) RegisterRoutes(api *gin.RouterGroup) {
	api.GET("/labellers", h.GetLabellers)
	api.GET("/labelling-history/episode/:catalog/:episode_id", h.GetEpisodeLabellingHistory)
	api.GET("/labelling-history/strip/:catalog/:strip_id", h.GetStripLabellingHistory)
	api.GET("/labelling-stats", h.GetLabellingStats)
	api.GET("/labellers/dump", h.GetLabellersDump)
}

func (h *LabellersHandler) GetLabellers(c *gin.Context) {
	var req GetLabellersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Failed to bind query", "error", err)
		BadRequest(c, "Failed to bind query")
		return
	}

	labellers, err := h.labellersService.GetLabellers(c.Request.Context(), req.Catalog)
	if err != nil {
		h.logger.Warn("Failed to get labellers", "error", err)
		InternalError(c, "Failed to get labellers")
		return
	}

	Success(c, labellers)
}

func (h *LabellersHandler) GetEpisodeLabellingHistory(c *gin.Context) {
	episode_id := c.Param("episode_id")
	catalog := c.Param("catalog")
	labelling_history, err := h.labellersService.GetEpisodeLabellingHistory(c.Request.Context(), episode_id, catalog)
	if err != nil {
		h.logger.Warn("Failed to get episode labelling history", "error", err)
		InternalError(c, "Failed to get episode labelling history")
		return
	}

	Success(c, labelling_history)
}

func (h *LabellersHandler) GetStripLabellingHistory(c *gin.Context) {
	strip_id := c.Param("strip_id")
	catalog := c.Param("catalog")
	labelling_history, err := h.labellersService.GetStripLabellingHistory(c.Request.Context(), strip_id, catalog)
	if err != nil {
		h.logger.Warn("Failed to get strip labelling history", "error", err)
		InternalError(c, "Failed to get strip labelling history")
		return
	}

	Success(c, labelling_history)
}

func (h *LabellersHandler) GetLabellingStats(c *gin.Context) {
	catalog := c.Query("catalog")

	stats, err := h.labellersService.GetLabellingStats(c.Request.Context(), catalog)
	if err != nil {
		h.logger.Warn("Failed to get labelling stats", "error", err)
		InternalError(c, "Failed to get labelling stats")
		return
	}

	Success(c, stats)
}

func (h *LabellersHandler) GetLabellersDump(c *gin.Context) {
	labellers, err := h.labellersService.GetLabellersDump(c.Request.Context())
	if err != nil {
		h.logger.Warn("Failed to get labellers", "error", err)
		InternalError(c, "Failed to get labellers")
		return
	}

	Success(c, labellers)
}
