package api

import (
	"bytes"
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"annotationtool-microservice/internal/app"
	"annotationtool-microservice/internal/domain"
	"annotationtool-microservice/internal/infra"
)

// Shared test instances to avoid Prometheus metric registration conflicts
var (
	testMetrics     = infra.NewDatasetMetrics()
	testLogger      = slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelError}))
	testAuditLogger = infra.NewAuditLogger(testLogger)
)

// MockECGService is a mock implementation of ECGService
type MockECGService struct {
	mock.Mock
}

func (m *MockECGService) QueryECGData(ctx context.Context, req domain.ECGQueryRequest) (*domain.ECGQueryResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*domain.ECGQueryResponse), args.Error(1)
}

func (m *MockECGService) GetConflictingEpisodes(ctx context.Context, req domain.ECGQueryRequest) (*domain.ECGQueryResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*domain.ECGQueryResponse), args.Error(1)
}

func (m *MockECGService) GetNumConflictingEpisodes(ctx context.Context, req domain.ECGNumConflictingEpisodesRequest) (*domain.ECGNumConflictingEpisodesResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*domain.ECGNumConflictingEpisodesResponse), args.Error(1)
}

func (m *MockECGService) GetECGAnalytics(ctx context.Context, req domain.ECGAnalyticsRequest) (*domain.ECGAnalyticsResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*domain.ECGAnalyticsResponse), args.Error(1)
}

func (m *MockECGService) GetEpisodeDetails(ctx context.Context, episodeID string, catalog string) (*domain.ECGEpisodeSummary, error) {
	args := m.Called(ctx, episodeID, catalog)
	return args.Get(0).(*domain.ECGEpisodeSummary), args.Error(1)
}

func (m *MockECGService) GetClinics(ctx context.Context, req domain.ECGClinicRequest) ([]domain.Clinic, error) {
	args := m.Called(ctx, req)
	return args.Get(0).([]domain.Clinic), args.Error(1)
}

func (m *MockECGService) GetPatients(ctx context.Context, req domain.ECGPatientRequest) ([]domain.Patient, error) {
	args := m.Called(ctx, req)
	return args.Get(0).([]domain.Patient), args.Error(1)
}

func (m *MockECGService) GetManufacturers(ctx context.Context, req domain.ECGManufacturerRequest) ([]string, error) {
	args := m.Called(ctx, req)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockECGService) GetDeviceModels(ctx context.Context, req domain.ECGDeviceModelRequest) ([]string, error) {
	args := m.Called(ctx, req)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockECGService) GetECGDashboard(ctx context.Context, req domain.ECGDashboardRequest, rawQueryParams string) (*domain.ECGDashboardResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*domain.ECGDashboardResponse), args.Error(1)
}

func TestECGHandler_QueryECGData(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		requestBody    domain.ECGQueryRequest
		mockResponse   *domain.ECGQueryResponse
		mockError      error
		expectedStatus int
	}{
		{
			name: "successful query",
			requestBody: domain.ECGQueryRequest{
				PatientIDs: []string{"patient_123"},
				Catalog:    "hive.default.episodes_v1_0_3",
				Limit:      10,
			},
			mockResponse: &domain.ECGQueryResponse{
				Episodes: []domain.ECGEpisodeSummary{
					{
						EpisodeID:   "ep_001",
						PatientID:   "patient_123",
						PatientName: "John Doe",
						ClinicID:    "clinic_001",
						ClinicName:  "Test Clinic",
						ReportTime:  time.Now().Format(time.RFC3339),
						Strips: []domain.ECGStripData{
							{
								StripID:    "strip_001",
								Timestamps: []float32{0.0, 0.1, 0.2},
								Voltages:   []float32{1.0, 1.1, 1.2},
							},
						},
					},
				},
				TotalCount: 1,
				Pagination: domain.PaginationInfo{
					Page:       1,
					Limit:      10,
					TotalRows:  1,
					TotalPages: 1,
					HasNext:    false,
					HasPrev:    false,
				},
				QueryTime: time.Millisecond * 100,
				Version:   "hive.default.episodes_v1_0_3",
				Timestamp: time.Now(),
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
		},
		{
			name: "validation error",
			requestBody: domain.ECGQueryRequest{
				Limit: -1, // Invalid limit
			},
			mockResponse:   nil,
			mockError:      app.NewValidationError("Invalid limit", nil),
			expectedStatus: http.StatusUnprocessableEntity,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := new(MockECGService)
			if tt.mockError == nil {
				mockService.On("QueryECGData", mock.Anything, mock.MatchedBy(func(req domain.ECGQueryRequest) bool {
					return req.Catalog == tt.requestBody.Catalog
				})).Return(tt.mockResponse, tt.mockError)
			} else {
				mockService.On("QueryECGData", mock.Anything, mock.Anything).Return((*domain.ECGQueryResponse)(nil), tt.mockError)
			}

			// Create handler with mock service
			handler := &ECGHandler{
				ecgService:  mockService,
				logger:      testLogger,
				metrics:     testMetrics,
				auditLogger: testAuditLogger,
			}

			// Create request
			requestBody, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/api/v1/ecg/query", bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			w := httptest.NewRecorder()

			// Create Gin context
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("user_id", "test_user")

			// Call handler
			handler.QueryECGData(c)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response APIResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
				assert.NotNil(t, response.Data)
			}

			mockService.AssertExpectations(t)
		})
	}
}

func TestECGHandler_GetEpisodeDetails(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name            string
		episodeID       string
		catalog         string
		mockResponse    *domain.ECGEpisodeSummary
		mockError       error
		expectedStatus  int
	}{
		{
			name:            "successful get episode",
			episodeID:       "ep_001",
			catalog:         "hive.default.episodes_v1_0_3",
			mockResponse: &domain.ECGEpisodeSummary{
				EpisodeID:   "ep_001",
				PatientID:   "patient_123",
				PatientName: "John Doe",
				ClinicID:    "clinic_001",
				ClinicName:  "Test Clinic",
				ReportTime:  time.Now().Format(time.RFC3339),
				Strips: []domain.ECGStripData{
					{
						StripID:    "strip_001",
						Timestamps: []float32{0.0, 0.1, 0.2},
						Voltages:   []float32{1.0, 1.1, 1.2},
					},
				},
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
		},
		{
			name:            "episode not found",
			episodeID:       "nonexistent",
			catalog:         "hive.default.episodes_v1_0_3",
			mockResponse:    nil,
			mockError:       app.NewNotFoundError("Episode not found", nil),
			expectedStatus:  http.StatusNotFound,
		},
		{
			name:            "missing episode ID",
			episodeID:       "",
			catalog:         "hive.default.episodes_v1_0_3",
			mockResponse:    nil,
			mockError:       nil,
			expectedStatus:  http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock service
			mockService := new(MockECGService)
			if tt.episodeID != "" {
				if tt.mockError == nil {
					mockService.On("GetEpisodeDetails", mock.Anything, tt.episodeID, tt.catalog).Return(tt.mockResponse, tt.mockError)
				} else {
					mockService.On("GetEpisodeDetails", mock.Anything, tt.episodeID, tt.catalog).Return((*domain.ECGEpisodeSummary)(nil), tt.mockError)
				}
			}

			// Create handler with mock service
			handler := &ECGHandler{
				ecgService:  mockService,
				logger:      testLogger,
				metrics:     testMetrics,
				auditLogger: testAuditLogger,
			}

			// Create request
			url := "/api/v1/ecg/episodes/" + tt.catalog + "/" + tt.episodeID
			req := httptest.NewRequest(http.MethodGet, url, nil)

			// Create response recorder
			w := httptest.NewRecorder()

			// Create Gin context
			c, _ := gin.CreateTestContext(w)
			c.Request = req
			c.Set("user_id", "test_user")
			c.Params = gin.Params{{Key: "episode_id", Value: tt.episodeID}}

			// Call handler
			handler.GetEpisodeDetails(c)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response APIResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.True(t, response.Success)
				assert.NotNil(t, response.Data)
			}

			if tt.episodeID != "" {
				mockService.AssertExpectations(t)
			}
		})
	}
}

func TestECGHandler_GetECGAnalytics(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create mock service
	mockService := new(MockECGService)

	analyticsResponse := &domain.ECGAnalyticsResponse{
		TotalEpisodes: 100,
		TotalStrips:   500,
		ByClinic: []domain.ECGClinicStats{
			{
				ClinicID:        "clinic_001",
				ClinicName:      "Test Clinic",
				EpisodeCount:    50,
				StripCount:      250,
				LabelledCount:   30,
				UnlabelledCount: 20,
			},
		},
		Timestamp: time.Now(),
	}

	mockService.On("GetECGAnalytics", mock.Anything, mock.Anything).Return(analyticsResponse, nil)

	// Create handler
	handler := &ECGHandler{
		ecgService:  mockService,
		logger:      testLogger,
		metrics:     testMetrics,
		auditLogger: testAuditLogger,
	}

	// Create request
	requestBody := domain.ECGAnalyticsRequest{
		GroupBy: []string{"clinic"},
	}
	body, _ := json.Marshal(requestBody)
	req := httptest.NewRequest(http.MethodPost, "/api/v1/ecg/analytics", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Set("user_id", "test_user")

	// Call handler
	handler.GetECGAnalytics(c)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.NotNil(t, response.Data)

	mockService.AssertExpectations(t)
}
