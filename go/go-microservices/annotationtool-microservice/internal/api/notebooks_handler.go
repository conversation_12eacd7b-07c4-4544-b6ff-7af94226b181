package api

import (
	"annotationtool-microservice/internal/app"
	"log/slog"

	"github.com/gin-gonic/gin"
)

type NotebookHandler struct {
	notebookService *app.NotebookService
	logger          *slog.Logger
}

func NewNotebookHandler(
	notebookService *app.NotebookService,
	logger *slog.Logger,
) *NotebookHandler {
	return &NotebookHandler{
		notebookService: notebookService,
		logger:          logger,
	}
}

func (h *NotebookHandler) GetNotebooks(c *gin.Context) {
	var query app.GetNotebooksQuery

	if err := c.ShouldBindUri(&query); err != nil {
		BadRequest(c, "Invalid nameSpace parameter")
		return
	}

	notebooks, err := h.notebookService.GetNotebooks(c.Request.Context(), query)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			NotFound(c, err.Error())
		default:
			h.logger.Error("Failed to get notebooks", "nameSpace", query.NameSpace, "error", err)
			InternalError(c, "Failed to retrieve notebooks")
		}
		return
	}

	Success(c, notebooks)
}

func (h *NotebookHandler) CreateNotebook(c *gin.Context) {
	// Path param
	var pathQuery app.GetNotebooksQuery
	if err := c.ShouldBindUri(&pathQuery); err != nil {
		BadRequest(c, "Invalid nameSpace parameter")
		return
	}

	// Bind JSON body
	var cmd app.CreateNotebookCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	notebook, err := h.notebookService.CreateNotebook(c.Request.Context(), pathQuery.NameSpace, cmd)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		default:
			h.logger.Error("Failed to create notebook", "namespace", pathQuery.NameSpace, "error", err)
			InternalError(c, "Failed to create notebook")
		}
		return
	}

	Created(c, notebook)
}

func (h *NotebookHandler) UpdateNotebook(c *gin.Context) {
	var path struct {
		NameSpace string `uri:"nameSpace" binding:"required"`
		Name      string `uri:"name" binding:"required"`
	}
	if err := c.ShouldBindUri(&path); err != nil {
		BadRequest(c, "Invalid parameters")
		return
	}

	var cmd app.UpdateNotebookCommand
	if err := c.ShouldBindJSON(&cmd); err != nil {
		BadRequest(c, "Invalid request body: "+err.Error())
		return
	}
	// Ensure name from path
	cmd.Name = path.Name

	nb, err := h.notebookService.UpdateNotebook(c.Request.Context(), path.NameSpace, cmd)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		default:
			h.logger.Error("Failed to update notebook", "namespace", path.NameSpace, "name", path.Name, "error", err)
			InternalError(c, "Failed to update notebook")
		}
		return
	}
	Success(c, nb)
}

func (h *NotebookHandler) DeleteNotebook(c *gin.Context) {
	var path struct {
		NameSpace string `uri:"nameSpace" binding:"required"`
		Name      string `uri:"name" binding:"required"`
	}
	if err := c.ShouldBindUri(&path); err != nil {
		BadRequest(c, "Invalid parameters")
		return
	}
	if err := h.notebookService.DeleteNotebook(c.Request.Context(), path.NameSpace, path.Name); err != nil {
		h.logger.Error("Failed to delete notebook", "namespace", path.NameSpace, "name", path.Name, "error", err)
		InternalError(c, "Failed to delete notebook")
		return
	}
	Success(c, gin.H{"deleted": true})
}

func (h *NotebookHandler) StartNotebook(c *gin.Context) {
	var path struct {
		NameSpace string `uri:"nameSpace" binding:"required"`
		Name      string `uri:"name" binding:"required"`
	}
	if err := c.ShouldBindUri(&path); err != nil {
		BadRequest(c, "Invalid parameters")
		return
	}
	if err := h.notebookService.StartNotebook(c.Request.Context(), path.NameSpace, path.Name); err != nil {
		h.logger.Error("Failed to start notebook", "namespace", path.NameSpace, "name", path.Name, "error", err)
		InternalError(c, "Failed to start notebook")
		return
	}
	Success(c, gin.H{"started": true})
}

func (h *NotebookHandler) StopNotebook(c *gin.Context) {
	var path struct {
		NameSpace string `uri:"nameSpace" binding:"required"`
		Name      string `uri:"name" binding:"required"`
	}
	if err := c.ShouldBindUri(&path); err != nil {
		BadRequest(c, "Invalid parameters")
		return
	}
	if err := h.notebookService.StopNotebook(c.Request.Context(), path.NameSpace, path.Name); err != nil {
		h.logger.Error("Failed to stop notebook", "namespace", path.NameSpace, "name", path.Name, "error", err)
		InternalError(c, "Failed to stop notebook")
		return
	}
	Success(c, gin.H{"stopped": true})
}

func (h *NotebookHandler) RestartNotebook(c *gin.Context) {
	var path struct {
		NameSpace string `uri:"nameSpace" binding:"required"`
		Name      string `uri:"name" binding:"required"`
	}
	if err := c.ShouldBindUri(&path); err != nil {
		BadRequest(c, "Invalid parameters")
		return
	}
	if err := h.notebookService.RestartNotebook(c.Request.Context(), path.NameSpace, path.Name); err != nil {
		h.logger.Error("Failed to restart notebook", "namespace", path.NameSpace, "name", path.Name, "error", err)
		InternalError(c, "Failed to restart notebook")
		return
	}
	Success(c, gin.H{"restarted": true})
}
func (h *NotebookHandler) RegisterRoutes(router *gin.RouterGroup) {
	notebooks := router.Group("/notebooks")
	{
		notebooks.GET("/namespaces/:nameSpace/notebooks", h.GetNotebooks)
		notebooks.POST("/namespaces/:nameSpace/notebooks", h.CreateNotebook)
		notebooks.PUT("/namespaces/:nameSpace/notebooks/:name", h.UpdateNotebook)
		notebooks.PATCH("/namespaces/:nameSpace/notebooks/:name", h.UpdateNotebook)
		notebooks.DELETE("/namespaces/:nameSpace/notebooks/:name", h.DeleteNotebook)
		notebooks.POST("/namespaces/:nameSpace/notebooks/:name/start", h.StartNotebook)
		notebooks.POST("/namespaces/:nameSpace/notebooks/:name/stop", h.StopNotebook)
		notebooks.POST("/namespaces/:nameSpace/notebooks/:name/restart", h.RestartNotebook)
	}
}
