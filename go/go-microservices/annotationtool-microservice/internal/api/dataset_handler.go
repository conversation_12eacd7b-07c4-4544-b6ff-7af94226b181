package api

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"annotationtool-microservice/internal/app"
	"annotationtool-microservice/internal/domain"

	"github.com/gin-gonic/gin"
)

// DatasetHandler handles HTTP requests for dataset operations
type DatasetHandler struct {
	datasetService       *app.DatasetService
	publisher            app.Publisher // service that publishes commands to topic
	logger               *slog.Logger
	shouldPublishCommand bool
}

// NewDatasetHandler creates a new DatasetHandler
func NewDatasetHandler(
	datasetService *app.DatasetService,
	publisher app.Publisher,
	logger *slog.Logger,
	shouldPublishCommand bool,
) *DatasetHandler {
	return &DatasetHandler{
		datasetService:       datasetService,
		publisher:            publisher,
		logger:               logger,
		shouldPublishCommand: shouldPublishCommand,
	}
}

// GetProjects handles GET /api/v1/datasets/projects
func (h *DatasetHandler) GetProjects(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON><PERSON>("page_size", "50"))

	projects, err := h.datasetService.GetProjects(c.Request.Context(), page, pageSize)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		default:
			h.logger.Error("Failed to get projects", "error", err)
			InternalError(c, "Failed to retrieve projects")
		}
		return
	}

	Success(c, projects)
}

// GetProjectEnvironments handles GET /api/v1/datasets/projects/{project}/environments
func (h *DatasetHandler) GetProjectEnvironments(c *gin.Context) {
	projectName := c.Param("project")
	if projectName == "" {
		BadRequest(c, "Project name is required")
		return
	}

	environments, err := h.datasetService.GetProjectEnvironments(c.Request.Context(), projectName)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			NotFound(c, err.Error())
		default:
			h.logger.Error("Failed to get project environments", "project", projectName, "error", err)
			InternalError(c, "Failed to retrieve project environments")
		}
		return
	}

	Success(c, map[string]interface{}{
		"project":      projectName,
		"environments": environments,
	})
}

// GetDatasets handles GET /api/v1/datasets/projects/{project}/environments/{env}/datasets
func (h *DatasetHandler) GetDatasets(c *gin.Context) {
	projectName := c.Param("project")
	envStr := c.Param("env")

	if projectName == "" {
		BadRequest(c, "Project name is required")
		return
	}

	if envStr == "" {
		BadRequest(c, "Environment is required")
		return
	}

	env := domain.DatasetEnvironment(envStr)
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))

	datasets, err := h.datasetService.GetDatasets(c.Request.Context(), projectName, env, page, pageSize)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			NotFound(c, err.Error())
		default:
			h.logger.Error("Failed to get datasets", "project", projectName, "environment", env, "error", err)
			InternalError(c, "Failed to retrieve datasets")
		}
		return
	}

	Success(c, datasets)
}

// SearchDatasets handles GET /api/v1/datasets/search
func (h *DatasetHandler) SearchDatasets(c *gin.Context) {
	var params domain.DatasetSearchParams

	// Parse query parameters
	params.Project = c.Query("project")
	params.Environment = domain.DatasetEnvironment(c.Query("environment"))
	params.Keyword = c.Query("keyword")

	// Parse tags (comma-separated)
	if tagsStr := c.Query("tags"); tagsStr != "" {
		// Split the comma-separated string into individual tags and trim whitespace
		tags := strings.Split(tagsStr, ",")
		for i := range tags {
			tags[i] = strings.TrimSpace(tags[i])
		}
		params.Tags = tags
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))
	params.Page = page
	params.PageSize = pageSize

	datasets, err := h.datasetService.SearchDatasets(c.Request.Context(), params)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		default:
			h.logger.Error("Failed to search datasets", "params", params, "error", err)
			InternalError(c, "Failed to search datasets")
		}
		return
	}

	Success(c, datasets)
}

// GetDatasetVersions handles GET /api/v1/datasets/projects/{project}/environments/{env}/datasets/{dataset}/versions
func (h *DatasetHandler) GetDatasetVersions(c *gin.Context) {
	projectName := c.Param("project")
	envStr := c.Param("env")
	datasetName := c.Param("dataset")

	if projectName == "" {
		BadRequest(c, "Project name is required")
		return
	}

	if envStr == "" {
		BadRequest(c, "Environment is required")
		return
	}

	if datasetName == "" {
		BadRequest(c, "Dataset name is required")
		return
	}

	env := domain.DatasetEnvironment(envStr)

	versions, err := h.datasetService.GetDatasetVersions(c.Request.Context(), projectName, env, datasetName)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			NotFound(c, err.Error())
		default:
			h.logger.Error("Failed to get dataset versions", "project", projectName, "environment", env, "dataset", datasetName, "error", err)
			InternalError(c, "Failed to retrieve dataset versions")
		}
		return
	}

	Success(c, map[string]interface{}{
		"project":     projectName,
		"environment": env,
		"dataset":     datasetName,
		"versions":    versions,
	})
}

// GetDataset handles GET /api/v1/datasets/projects/{project}/environments/{env}/datasets/{dataset}
func (h *DatasetHandler) GetDataset(c *gin.Context) {
	projectName := c.Param("project")
	envStr := c.Param("env")
	datasetName := c.Param("dataset")

	if projectName == "" {
		BadRequest(c, "Project name is required")
		return
	}

	if envStr == "" {
		BadRequest(c, "Environment is required")
		return
	}

	if datasetName == "" {
		BadRequest(c, "Dataset name is required")
		return
	}

	env := domain.DatasetEnvironment(envStr)

	dataset, err := h.datasetService.GetDatasetByPath(c.Request.Context(), projectName, env, datasetName)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			NotFound(c, err.Error())
		default:
			h.logger.Error("Failed to get dataset", "project", projectName, "environment", env, "dataset", datasetName, "error", err)
			InternalError(c, "Failed to retrieve dataset")
		}
		return
	}

	Success(c, dataset)
}

// ExportDataset handles GET /api/v1/datasets/projects/{project}/environments/{env}/datasets/{dataset}/export?format=csv|json
func (h *DatasetHandler) ExportDataset(c *gin.Context) {
	projectName := c.Param("project")
	envStr := c.Param("env")
	datasetName := c.Param("dataset")

	if projectName == "" || envStr == "" || datasetName == "" {
		BadRequest(c, "Project, environment and dataset are required")
		return
	}

	env := domain.DatasetEnvironment(envStr)

	dataset, err := h.datasetService.GetDatasetByPath(c.Request.Context(), projectName, env, datasetName)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			NotFound(c, err.Error())
		default:
			h.logger.Error("Failed to export dataset", "error", err)
			InternalError(c, "Failed to export dataset")
		}
		return
	}

	format := c.DefaultQuery("format", "json")

	if format == "csv" {
		var buf bytes.Buffer
		writer := csv.NewWriter(&buf)

		// Write header
		_ = writer.Write([]string{"version", "path", "last_modified", "size_bytes"})
		for _, v := range dataset.Versions {
			_ = writer.Write([]string{
				v.Version,
				v.Path,
				v.LastModified.Format(time.RFC3339),
				strconv.FormatInt(v.SizeBytes, 10),
			})
		}
		writer.Flush()

		c.Header("Content-Type", "text/csv")
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s_metadata.csv\"", datasetName))
		c.String(200, buf.String())
		return
	}

	// default JSON response
	Success(c, dataset)
}

// CreateDataset handles POST /api/v1/datasets/projects/{project}/environments/{env}/datasets
func (h *DatasetHandler) CreateDataset(c *gin.Context) {
	projectName := c.Param("project")
	envStr := c.Param("env")

	if projectName == "" || envStr == "" {
		BadRequest(c, "Project and environment are required")
		return
	}
	env := domain.DatasetEnvironment(envStr)

	var req struct {
		DatasetName string                    `json:"dataset_name" binding:"required"`
		Version     string                    `json:"version" binding:"required"`
		Columns     []domain.ColumnDefinition `json:"columns,omitempty"`
		RequestedBy string                    `json:"requested_by" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		BadRequest(c, err.Error())
		return
	}

	cmd := app.CreateDatasetCommand{
		ProjectName: projectName,
		Environment: env,
		DatasetName: req.DatasetName,
		Version:     req.Version,
		Columns:     req.Columns,
		RequestedBy: req.RequestedBy,
	}

	if h.shouldPublishCommand {
		// Process via Kafka
		if h.publisher == nil {
			InternalError(c, "Kafka processing enabled but publisher is not available")
			return
		}

		if asyncErr := h.publisher.PublishCommand(c.Request.Context(), cmd); asyncErr != nil {
			h.logger.Error("Failed to queue command", "Command", cmd, "error", asyncErr)
			InternalError(c, "Failed to queue dataset for processing.")
			return
		}

		Success(c, gin.H{"message": "Dataset creation has been queued for processing."})
		return
	}

	// Process directly via service layer
	if err := h.datasetService.CreateDataset(c.Request.Context(), cmd); err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		default:
			h.logger.Error("Failed to create dataset", "error", err)
			InternalError(c, "Failed to create dataset")
		}
		return
	}

	Success(c, gin.H{"message": "Dataset created successfully"})
}

// CreateDatasetVersion handles POST /api/v1/datasets/projects/{project}/environments/{env}/datasets/{dataset}/versions
func (h *DatasetHandler) CreateDatasetVersion(c *gin.Context) {
	projectName := c.Param("project")
	envStr := c.Param("env")
	datasetName := c.Param("dataset")

	if projectName == "" || envStr == "" || datasetName == "" {
		BadRequest(c, "Project, environment and dataset are required")
		return
	}
	env := domain.DatasetEnvironment(envStr)

	var req struct {
		Version     string                    `json:"version" binding:"required"`
		Columns     []domain.ColumnDefinition `json:"columns,omitempty"`
		RequestedBy string                    `json:"requested_by" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		BadRequest(c, err.Error())
		return
	}

	cmd := app.CreateDatasetVersionCommand{
		ProjectName: projectName,
		Environment: env,
		DatasetName: datasetName,
		Version:     req.Version,
		Columns:     req.Columns,
		RequestedBy: req.RequestedBy,
	}

	if h.shouldPublishCommand {
		// Process via Kafka
		if h.publisher == nil {
			InternalError(c, "Kafka processing enabled but publisher is not available")
			return
		}

		if asyncErr := h.publisher.PublishCommand(c.Request.Context(), cmd); asyncErr != nil {
			h.logger.Error("Failed to queue command", "Command", cmd, "error", asyncErr)
			InternalError(c, "Failed to queue dataset version for processing.")
			return
		}

		Success(c, gin.H{"message": "Dataset version creation has been queued for processing."})
		return
	}

	// Process directly via service layer
	if err := h.datasetService.CreateDatasetVersion(c.Request.Context(), cmd); err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		default:
			h.logger.Error("Failed to create dataset version", "error", err)
			InternalError(c, "Failed to create dataset version")
		}
		return
	}

	Success(c, gin.H{"message": "Dataset version created successfully"})
}

// ImportDatasetToClickhouse handles POST /api/v1/datasets/projects/{project}/environments/{env}/datasets/{dataset}/import-to-clickhouse
func (h *DatasetHandler) ImportDatasetToClickhouse(c *gin.Context) {
	datasetPath := c.Query("datasetPath")
	clickhouseTableName := c.Query("clickhouseTableName")

	if datasetPath == "" || clickhouseTableName == "" {
		BadRequest(c, "datasetPath and clickhouseTableName are required")
		return
	}

	if err := h.datasetService.ImportDatasetToClickhouse(c.Request.Context(), datasetPath, clickhouseTableName); err != nil {
		InternalError(c, "Failed to initiate dataset import to clickhouse")
		return
	}

	Success(c, gin.H{
		"message":    "Dataset import initiated successfully - running in background",
		"table_name": clickhouseTableName,
		"status":     "in_progress",
		"note":       "Check server logs for import progress and completion status",
	})
}

// GetDatasetStats handles
// - GET /api/v1/datasets/:catalog/stats
// - GET /api/v1/datasets/stats?catalog=<catalog>
func (h *DatasetHandler) GetDatasetStats(c *gin.Context) {
	catalog := c.Param("catalog")
	if catalog == "" {
		catalog = c.Query("catalog")
	}
	if catalog == "" {
		BadRequest(c, "catalog is required")
		return
	}

	stats, err := h.datasetService.GetDatasetStats(c.Request.Context(), catalog)
	if err != nil {
		switch {
		case app.IsValidationError(err):
			ValidationError(c, err.Error())
		case app.IsNotFoundError(err):
			NotFound(c, err.Error())
		default:
			h.logger.Error("Failed to get dataset stats", "catalog", catalog, "error", err)
			InternalError(c, "Failed to retrieve dataset stats")
		}
		return
	}

	Success(c, stats)
}

// RegisterRoutes registers all dataset routes
func (h *DatasetHandler) RegisterRoutes(router *gin.RouterGroup) {
	datasets := router.Group("/datasets")
	{
		// Project routes
		datasets.GET("/projects", h.GetProjects)
		datasets.GET("/projects/:project/environments", h.GetProjectEnvironments)

		// Dataset routes
		datasets.GET("/projects/:project/environments/:env/datasets", h.GetDatasets)
		datasets.GET("/projects/:project/environments/:env/datasets/:dataset", h.GetDataset)
		datasets.GET("/projects/:project/environments/:env/datasets/:dataset/versions", h.GetDatasetVersions)
		datasets.GET("/projects/:project/environments/:env/datasets/:dataset/export", h.ExportDataset)
		datasets.POST("/projects/:project/environments/:env/datasets", h.CreateDataset)                          // Publishes to pubsub for async processing
		datasets.POST("/projects/:project/environments/:env/datasets/:dataset/versions", h.CreateDatasetVersion) // Publishes to pubsub for async processing

		// Search route
		datasets.GET("/search", h.SearchDatasets)

		// Clickhouse dataset import
		datasets.POST("/import-to-clickhouse", h.ImportDatasetToClickhouse)

		// Dataset stats (supports both path and query param styles)
		datasets.GET(":catalog/stats", h.GetDatasetStats)
		datasets.GET("/stats", h.GetDatasetStats)
	}
}
