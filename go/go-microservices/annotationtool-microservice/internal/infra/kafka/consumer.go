// internal/infra/kafka/consumer.go
package kafka

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"time"

	"annotationtool-microservice/internal/infra/kafka/message"
	"log/slog"

	"github.com/IBM/sarama"
)

type RetryConfig struct {
	BaseDelay     time.Duration
	MaxDelay      time.Duration
	JitterEnabled bool
}

type Consumer struct {
	group       sarama.ConsumerGroup
	topic       string
	retryCount  int
	retryConfig RetryConfig
	dlqTopic    string
	dlqProducer sarama.SyncProducer
	processor   message.Processor
	logger      *slog.Logger
}

func NewConsumer(
	brokers []string,
	groupID, topic string,
	retryCount int,
	retryConfig RetryConfig,
	dlqTopic string,
	processor message.Processor,
	logger *slog.Logger,
) (*Consumer, error) {
	config := sarama.NewConfig()
	config.Version = sarama.V2_5_0_0
	config.Consumer.Return.Errors = true
	config.Consumer.Offsets.Initial = sarama.OffsetOldest

	// Configure producer settings for DLQ
	config.Producer.Return.Successes = true
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 3
	config.Producer.Retry.Backoff = 250 * time.Millisecond

	group, err := sarama.NewConsumerGroup(brokers, groupID, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create consumer group: %w", err)
	}

	// Create DLQ producer
	dlqProducer, err := sarama.NewSyncProducer(brokers, config)
	if err != nil {
		group.Close() // Clean up consumer group if producer creation fails
		return nil, fmt.Errorf("failed to create DLQ producer: %w", err)
	}

	return &Consumer{
		group:       group,
		topic:       topic,
		retryCount:  retryCount,
		retryConfig: retryConfig,
		dlqTopic:    dlqTopic,
		dlqProducer: dlqProducer,
		processor:   processor,
		logger:      logger,
	}, nil
}

func (c *Consumer) Start(ctx context.Context) error {
	for {
		if err := c.group.Consume(ctx, []string{c.topic}, c); err != nil {
			return fmt.Errorf("error from consumer: %w", err)
		}
		if ctx.Err() != nil {
			return ctx.Err()
		}
	}
}

func (c *Consumer) Setup(sarama.ConsumerGroupSession) error   { return nil }
func (c *Consumer) Cleanup(sarama.ConsumerGroupSession) error { return nil }

func (c *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for msg := range claim.Messages() {
		var envelope message.Envelope
		if err := json.Unmarshal(msg.Value, &envelope); err != nil {
			c.logger.Error("Invalid message format", "error", err, "offset", msg.Offset)
			// session.MarkMessage(msg, "") // Dont mark as processed
			continue
		}

		if err := c.processWithRetry(session.Context(), &envelope); err != nil {
			c.logger.Error("Processing failed after all retries, sending to DLQ",
				"error", err,
				"offset", msg.Offset,
				"attempts", c.retryCount)

			if dlqErr := c.sendToDLQ(session.Context(), msg, err); dlqErr != nil {
				c.logger.Error("Failed to send message to DLQ",
					"dlq_error", dlqErr,
					"original_error", err,
					"offset", msg.Offset)
				// Could choose to not mark message as processed here to cause redelivery
				// For now, we'll mark it to prevent infinite loops
			}
		}

		session.MarkMessage(msg, "")
	}
	return nil
}

func (c *Consumer) processWithRetry(ctx context.Context, envelope *message.Envelope) error {
	var lastErr error

	for i := 0; i < c.retryCount; i++ {
		// Check for context cancellation before each attempt
		if ctx.Err() != nil {
			return ctx.Err()
		}

		if err := c.processor.Process(ctx, envelope); err != nil {
			lastErr = err

			if i < c.retryCount-1 { // Don't sleep on the last iteration
				delay := c.calculateBackoffDelay(i)

				c.logger.Warn("Processing failed, retrying with exponential backoff",
					"attempt", i+1,
					"max_attempts", c.retryCount,
					"delay", delay,
					"error", err)

				select {
				case <-time.After(delay):
					// Continue to next attempt
				case <-ctx.Done():
					return ctx.Err()
				}
			}
		} else {
			// Success
			if i > 0 {
				c.logger.Info("Processing succeeded after retries", "attempt", i+1)
			}
			return nil
		}
	}

	return lastErr
}

func (c *Consumer) calculateBackoffDelay(attempt int) time.Duration {
	// Exponential backoff: baseDelay * 2^attempt
	delay := time.Duration(float64(c.retryConfig.BaseDelay) * math.Pow(2, float64(attempt)))

	// Cap at maximum delay
	if delay > c.retryConfig.MaxDelay {
		delay = c.retryConfig.MaxDelay
	}

	// Add jitter if enabled (±25% randomization)
	if c.retryConfig.JitterEnabled {
		jitterRange := float64(delay) * 0.25
		jitter := time.Duration(jitterRange * (2*rand.Float64() - 1))
		delay += jitter

		// Ensure delay doesn't go negative
		if delay < 0 {
			delay = c.retryConfig.BaseDelay
		}
	}

	return delay
}

func (c *Consumer) sendToDLQ(ctx context.Context, originalMsg *sarama.ConsumerMessage, processingError error) error {
	// Create DLQ message with comprehensive metadata
	dlqMessage := map[string]interface{}{
		"original_topic":     originalMsg.Topic,
		"original_partition": originalMsg.Partition,
		"original_offset":    originalMsg.Offset,
		"original_timestamp": originalMsg.Timestamp,
		"original_key":       string(originalMsg.Key),
		"original_value":     string(originalMsg.Value),
		"processing_error":   processingError.Error(),
		"retry_count":        c.retryCount,
		"dlq_timestamp":      time.Now().UTC(),
		"consumer_group":     "annotationtool-consumer-group", // Could be made configurable
	}

	// Add original headers if they exist
	if len(originalMsg.Headers) > 0 {
		headers := make(map[string]string)
		for _, header := range originalMsg.Headers {
			headers[string(header.Key)] = string(header.Value)
		}
		dlqMessage["original_headers"] = headers
	}

	dlqBytes, err := json.Marshal(dlqMessage)
	if err != nil {
		return fmt.Errorf("failed to marshal DLQ message: %w", err)
	}

	// Create producer message for DLQ
	producerMsg := &sarama.ProducerMessage{
		Topic: c.dlqTopic,
		Key:   sarama.ByteEncoder(originalMsg.Key), // Preserve original key for partitioning consistency
		Value: sarama.ByteEncoder(dlqBytes),
		Headers: []sarama.RecordHeader{
			{Key: []byte("dlq-reason"), Value: []byte("processing-failed")},
			{Key: []byte("original-topic"), Value: []byte(originalMsg.Topic)},
			{Key: []byte("retry-count"), Value: []byte(fmt.Sprintf("%d", c.retryCount))},
			{Key: []byte("dlq-timestamp"), Value: []byte(time.Now().UTC().Format(time.RFC3339))},
		},
	}

	// Send to DLQ topic with timeout
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	partition, offset, err := c.dlqProducer.SendMessage(producerMsg)
	if err != nil {
		return fmt.Errorf("failed to send message to DLQ: %w", err)
	}

	c.logger.Info("Message successfully sent to DLQ",
		"dlq_topic", c.dlqTopic,
		"dlq_partition", partition,
		"dlq_offset", offset,
		"original_topic", originalMsg.Topic,
		"original_partition", originalMsg.Partition,
		"original_offset", originalMsg.Offset,
		"error", processingError.Error())

	return nil
}

func (c *Consumer) Close() error {
	var errs []error

	// Close consumer group
	if c.group != nil {
		if err := c.group.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close consumer group: %w", err))
		}
	}

	// Close DLQ producer
	if c.dlqProducer != nil {
		if err := c.dlqProducer.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close DLQ producer: %w", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors during close: %v", errs)
	}

	c.logger.Info("Kafka consumer and DLQ producer closed successfully")
	return nil
}
