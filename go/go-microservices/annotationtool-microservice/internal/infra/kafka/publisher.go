// internal/infra/kafka/publisher.go
package kafka

import (
	"context"
	"encoding/json"
	"fmt"

	"annotationtool-microservice/internal/infra/kafka/message"
	"log/slog"

	"github.com/IBM/sarama"
)

type Publisher struct {
	producer sarama.SyncProducer
	topic    string
	logger   *slog.Logger
}

func NewPublisher(brokers []string, topic string, logger *slog.Logger) (*Publisher, error) {
	config := sarama.NewConfig()
	config.Version = sarama.V2_5_0_0
	config.Producer.Return.Successes = true
	config.Producer.RequiredAcks = sarama.WaitForAll
	config.Producer.Retry.Max = 5

	producer, err := sarama.NewSyncProducer(brokers, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create producer: %w", err)
	}

	return &Publisher{
		producer: producer,
		topic:    topic,
		logger:   logger,
	}, nil
}

func (p *Publisher) Publish(ctx context.Context, msg message.Message) error {
	envelope := message.NewEnvelope(msg)
	bytes, err := json.Marshal(envelope)
	if err != nil {
		return fmt.Errorf("failed to marshal message envelope: %w", err)
	}

	partition, offset, err := p.producer.SendMessage(&sarama.ProducerMessage{
		Topic: p.topic,
		Key:   sarama.StringEncoder(envelope.Type),
		Value: sarama.ByteEncoder(bytes),
	})
	if err != nil {
		return fmt.Errorf("failed to publish message to Kafka: %w", err)
	}

	p.logger.Info("Message published to Kafka", "topic", p.topic, "partition", partition, "offset", offset)
	return nil
}

func (p *Publisher) PublishCommand(ctx context.Context, cmd any) error {
	return p.Publish(ctx, cmd)
}

func (p *Publisher) Close() error {
	if p.producer != nil {
		return p.producer.Close()
	}
	return nil
}
