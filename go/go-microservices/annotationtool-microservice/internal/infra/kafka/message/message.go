// internal/infra/kafka/message/envelope.go
package message

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
)

type Message interface{}

// Processor defines the interface for processing Kafka message envelopes
type Processor interface {
	Process(ctx context.Context, envelope *Envelope) error
}

type Envelope struct {
	Type    string          `json:"type"`
	Payload json.RawMessage `json:"payload"`
}

func NewEnvelope(msg Message) Envelope {
	typeName := reflect.TypeOf(msg).String()
	payload, _ := json.Marshal(msg)
	return Envelope{
		Type:    typeName,
		Payload: payload,
	}
}

func (e *Envelope) Decode(targets map[string]func() Message) (Message, error) {
	ctor, ok := targets[e.Type]
	if !ok {
		return nil, fmt.Errorf("unknown message type: %s", e.Type)
	}

	msg := ctor()
	if err := json.Unmarshal(e.Payload, msg); err != nil {
		return nil, fmt.Erro<PERSON>("failed to unmarshal payload: %w", err)
	}
	return msg, nil
}
