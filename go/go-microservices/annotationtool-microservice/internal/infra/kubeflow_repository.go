package infra

import (
	"annotationtool-microservice/internal/app"
	"annotationtool-microservice/internal/domain"
	"context"
	"fmt"
	"log/slog"
	"net/url"
	"strings"
)

type KubeflowRepository struct {
	kubeflowClient *KubeflowClient
	logger         *slog.Logger
}

// NewKubeflowRepository creates a new Kubeflow repository
func NewKubeflowRepository(kubeflowClient *KubeflowClient, logger *slog.Logger) (*KubeflowRepository, error) {
	if kubeflowClient == nil {
		return nil, fmt.Errorf("kubeflow client is required")
	}

	repo := &KubeflowRepository{
		kubeflowClient: kubeflowClient,
		logger:         logger,
	}

	logger.Info("Kubeflow repository initialized successfully")

	return repo, nil
}

// GetNotebooks returns all notebooks for a given nameSpace
func (r *KubeflowRepository) GetNotebooks(ctx context.Context, nameSpace string) (*domain.NotebookSearchResult, error) {
	r.logger.Info("Listing notebooks with namespace", "namespace", nameSpace)

	// Get the list of notebooks from the Kubeflow API
	notebooks, err := r.kubeflowClient.GetNotebooks(ctx, nameSpace)
	if err != nil {
		return nil, err
	}

	notebooksMap := make(map[string]*domain.Notebook)
	for _, nb := range notebooks {
		if nb == nil {
			continue
		}

		// Determine if notebook is running/ready
		state := strings.ToLower(strings.TrimSpace(nb.Status.State))
		phase := strings.ToLower(strings.TrimSpace(nb.Status.Phase))
		isRunning := state == "running" || state == "ready" || phase == "running" || phase == "ready"

		if isRunning {
			// Resolve namespace and construct base Kubeflow path
			ns := nb.Namespace
			if ns == "" {
				ns = nb.Metadata.Namespace
			}
			basePath := "/notebook/" + url.PathEscape(ns) + "/" + url.PathEscape(nb.Name)

			// Append annotation rewrite suffix if meaningful
			rewrite := strings.TrimSpace(nb.Metadata.Annotations.HTTPRewriteURI)
			uri := basePath
			if rewrite != "" && rewrite != "/" {
				if !strings.HasPrefix(rewrite, "/") {
					rewrite = "/" + rewrite
				}
				uri += rewrite
			} else {
				// Ensure trailing slash when no specific rewrite is provided
				if !strings.HasSuffix(uri, "/") {
					uri += "/"
				}
			}

			// Determine server type with fallbacks
			serverType := strings.ToLower(strings.TrimSpace(nb.ServerType))
			if serverType == "" {
				serverType = strings.ToLower(strings.TrimSpace(nb.Metadata.Annotations.ServerType))
			}

			// Special-case Jupyter to land on JupyterLab if not already targeting a specific UI
			if serverType == "jupyter" {
				if !strings.Contains(uri, "/lab") && !strings.Contains(uri, "/tree") && !strings.Contains(uri, "/nbclassic") {
					if strings.HasSuffix(uri, "/") {
						uri += "lab"
					} else {
						uri += "/lab"
					}
				}
			} else {
				// Detect VS Code servers (codeserver images or explicit vscode type)
				imageLower := strings.ToLower(nb.Image)
				shortLower := strings.ToLower(nb.ShortImage)
				isVSCode := serverType == "vscode" || strings.Contains(imageLower, "codeserver") || strings.Contains(imageLower, "code-server") || strings.Contains(shortLower, "codeserver") || strings.Contains(shortLower, "code-server")
				if isVSCode {
					// Ensure trailing slash then append default folder param if missing
					if !strings.Contains(uri, "?") && !strings.HasSuffix(uri, "/") {
						uri += "/"
					}
					if !strings.Contains(uri, "folder=") {
						if strings.Contains(uri, "?") {
							uri += "&folder=/home/<USER>"
						} else {
							uri += "?folder=/home/<USER>"
						}
					}
				}
			}

			nb.URL = r.kubeflowClient.BaseURL() + uri
		}

		notebooksMap[nb.Name] = nb
	}

	return &domain.NotebookSearchResult{
		Total:     len(notebooksMap),
		Notebooks: notebooksMap,
	}, nil
}

// CreateNotebook creates a notebook via the Kubeflow client
func (r *KubeflowRepository) CreateNotebook(ctx context.Context, nameSpace string, cmd app.CreateNotebookCommand) (*domain.Notebook, error) {
	r.logger.Info("Creating notebook in Kubeflow", "namespace", nameSpace, "name", cmd.Name, "creator", cmd.CreatorEmail)

	// Build request for Kubeflow Jupyter Web App
	createReq := CreateNotebookRequest{
		Name:       cmd.Name,
		Image:      cmd.Image,
		CPU:        cmd.CPU,
		Memory:     cmd.Memory,
		GPUs:       cmd.GPUCount,
		ServerType: cmd.ServerType,
		ImagePullPolicy: func() string {
			if cmd.ImagePullPolicy == "" {
				return "IfNotPresent"
			}
			return cmd.ImagePullPolicy
		}(),
		Annotations: map[string]string{
			"notebooks.kubeflow.org/creator":      cmd.CreatorEmail,
			"notebooks.kubeflow.org/creator-name": cmd.CreatorName,
			// Respect user's choice of server type (e.g., jupyter or vscode)
			"notebooks.kubeflow.org/server-type": cmd.ServerType,
		},
		Labels: map[string]string{
			"app": "notebook",
		},
	}

	nb, err := r.kubeflowClient.CreateNotebook(ctx, nameSpace, createReq)
	if err != nil {
		return nil, err
	}

	return nb, nil
}

// UpdateNotebook updates a notebook via the Kubeflow client
func (r *KubeflowRepository) UpdateNotebook(ctx context.Context, nameSpace string, cmd app.UpdateNotebookCommand) (*domain.Notebook, error) {
	r.logger.Info("Updating notebook in Kubeflow", "namespace", nameSpace, "name", cmd.Name)

	annotations := cmd.Annotations
	if annotations == nil {
		annotations = map[string]string{}
	}
	if cmd.ServerType != "" {
		annotations["notebooks.kubeflow.org/server-type"] = cmd.ServerType
	}

	updateReq := UpdateNotebookRequest{
		Image:           cmd.Image,
		CPU:             cmd.CPU,
		Memory:          cmd.Memory,
		GPUs:            cmd.GPUCount,
		ServerType:      cmd.ServerType,
		ImagePullPolicy: cmd.ImagePullPolicy,
		Annotations:     annotations,
		Labels:          cmd.Labels,
	}

	return r.kubeflowClient.UpdateNotebook(ctx, nameSpace, cmd.Name, updateReq)
}

// DeleteNotebook deletes a notebook via the Kubeflow client
func (r *KubeflowRepository) DeleteNotebook(ctx context.Context, nameSpace, name string) error {
	r.logger.Info("Deleting notebook in Kubeflow", "namespace", nameSpace, "name", name)
	return r.kubeflowClient.DeleteNotebook(ctx, nameSpace, name)
}

// StartNotebook starts a notebook via the Kubeflow client
func (r *KubeflowRepository) StartNotebook(ctx context.Context, nameSpace, name string) error {
	r.logger.Info("Starting notebook in Kubeflow", "namespace", nameSpace, "name", name)
	return r.kubeflowClient.StartNotebook(ctx, nameSpace, name)
}

// StopNotebook stops a notebook via the Kubeflow client
func (r *KubeflowRepository) StopNotebook(ctx context.Context, nameSpace, name string) error {
	r.logger.Info("Stopping notebook in Kubeflow", "namespace", nameSpace, "name", name)
	return r.kubeflowClient.StopNotebook(ctx, nameSpace, name)
}

// RestartNotebook restarts a notebook via the Kubeflow client
func (r *KubeflowRepository) RestartNotebook(ctx context.Context, nameSpace, name string) error {
	r.logger.Info("Restarting notebook in Kubeflow", "namespace", nameSpace, "name", name)
	return r.kubeflowClient.RestartNotebook(ctx, nameSpace, name)
}

func (r *KubeflowRepository) Close() error {
	r.logger.Info("Kubeflow repository closed")
	return nil
}
