package infra

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"

	"annotationtool-microservice/internal/domain"

	"github.com/ClickHouse/clickhouse-go/v2"
)

func joinStringValues(values []string) string {
	result := ""
	for i, value := range values {
		if i > 0 {
			result += ", "
		}
		result += fmt.Sprintf("'%s'", value)
	}
	return result
}

// ClickHouseLabellerRepository implements LabellerRepository with ClickHouse storage
type ClickHouseLabellersRepository struct {
	db     *sql.DB
	logger *slog.Logger
}

// NewClickHouseLabellersRepository creates a new ClickHouse labeller repository
func NewClickHouseLabellersRepository(dsn string, logger *slog.Logger) (*ClickHouseLabellersRepository, error) {
	database := getEnv("CLICKHOUSE_DATABASE", "annotationtool")
	username := getEnv("CLICKHOUSE_USERNAME", "default")
	password := getEnv("CLICKHOUSE_PASSWORD", "")

	// Avoid printing credentials; log minimal connection info
	logger.Info("Initializing ClickHouse client", "database", database, "username", username)

	conn := clickhouse.OpenDB(&clickhouse.Options{
		Addr: []string{dsn},
		Auth: clickhouse.Auth{
			Database: database,
			Username: username,
			Password: password,
		},
	})

	// Test the connection
	if err := conn.Ping(); err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %w", err)
	}

	logger.Info("Successfully connected to ClickHouse", "dsn", dsn)

	return &ClickHouseLabellersRepository{
		db:     conn,
		logger: logger,
	}, nil
}

// GetLabellers gets all labellers from ClickHouse
func (r *ClickHouseLabellersRepository) GetLabellers(ctx context.Context, catalog string) ([]domain.Labeller, error) {
	query := fmt.Sprintf(`
		SELECT DISTINCT(submitted_by) as labeller_id FROM annotationtool.dataset_annotations WHERE dataset_id = '%s'
	`, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get labellers", "error", err)
		return nil, fmt.Errorf("failed to get labellers: %w", err)
	}

	labellers := []domain.Labeller{}

	for rows.Next() {
		var labeller domain.Labeller
		err := rows.Scan(&labeller.LabellerID)
		if err != nil {
			r.logger.Error("Failed to scan labeller", "error", err)
			return nil, fmt.Errorf("failed to scan labeller: %w", err)
		}
		labellers = append(labellers, labeller)
	}

	return labellers, nil
}

// GetLabelledEpisodeIds gets all distinct episode ids for a labeller
func (r *ClickHouseLabellersRepository) GetLabelledEpisodeIds(ctx context.Context, catalog string, labels, userIds []string, maxLabellingTime *string) ([]string, error) {
	whereClause := fmt.Sprintf("dataset_id = '%s'", catalog)

	whereClause += fmt.Sprintf(" AND label IN  (%s)", joinStringValues(labels))

	whereClause += fmt.Sprintf(" AND submitted_by IN (%s)", joinStringValues(userIds))

	if maxLabellingTime != nil {
		whereClause += fmt.Sprintf(" AND label_timestamp <= %s", *maxLabellingTime)
	}

	query := fmt.Sprintf(`
		SELECT episode_id, SUM(
					CASE 
						WHEN operation_type = 'addition' THEN 1
						WHEN operation_type = 'removal' THEN -1
						ELSE 0
					END
				) as operation_sum
		FROM annotationtool.dataset_annotations 
		WHERE %s
		GROUP BY episode_id
		HAVING operation_sum >= 1
	`, whereClause)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get labelled episode ids", "error", err)
		return nil, fmt.Errorf("failed to get labelled episode ids: %w", err)
	}
	defer rows.Close()

	episodeIds := []string{}

	for rows.Next() {
		var episodeId string
		var operationSum int
		err := rows.Scan(&episodeId, &operationSum)
		if err != nil {
			r.logger.Error("Failed to scan episode id", "error", err)
			return nil, fmt.Errorf("failed to scan episode id: %w", err)
		}
		episodeIds = append(episodeIds, episodeId)
	}

	// keep only distinct episodeIds
	episodeIdSet := make(map[string]struct{})
	distinctEpisodeIds := []string{}
	for _, id := range episodeIds {
		if _, exists := episodeIdSet[id]; !exists {
			episodeIdSet[id] = struct{}{}
			distinctEpisodeIds = append(distinctEpisodeIds, id)
		}
	}
	episodeIds = distinctEpisodeIds

	return episodeIds, nil
}

// GetLabelledStripIds gets all distinct strip ids that have been labelled
func (r *ClickHouseLabellersRepository) GetLabelledStripIds(ctx context.Context, catalog string) ([]string, error) {
	query := fmt.Sprintf(`
		SELECT DISTINCT(strip_id) as strip_id FROM annotationtool.dataset_annotations WHERE label != 'isInterestingFlag' AND dataset_id = '%s'
	`, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get labelled strip ids", "error", err)
		return nil, fmt.Errorf("failed to get labelled strip ids: %w", err)
	}

	stripIds := []string{}

	for rows.Next() {
		var stripId string
		err := rows.Scan(&stripId)
		if err != nil {
			r.logger.Error("Failed to scan strip id", "error", err)
			return nil, fmt.Errorf("failed to scan strip id: %w", err)
		}
		stripIds = append(stripIds, stripId)
	}

	return stripIds, nil
}

// AnnotationEntry represents an annotation entry from the database
type AnnotationEntry struct {
	EpisodeID      string  `json:"episode_id"`
	StripID        string  `json:"strip_id"`
	Label          string  `json:"label"`
	LabelTimestamp float64 `json:"label_timestamp"`
	OperationType  string  `json:"operation_type"`
	SubmittedBy    string  `json:"submitted_by"`
}

// GetAnnotationsByEpisodeIDs gets all annotation entries that match any of the provided episode IDs
func (r *ClickHouseLabellersRepository) GetAnnotationsByEpisodeIDs(ctx context.Context, episodeIDs []string, catalog string) ([]domain.AnnotationEntry, error) {
	if len(episodeIDs) == 0 {
		return []domain.AnnotationEntry{}, nil
	}

	// Build IN clause with episode IDs
	inClause := ""
	for i, episodeID := range episodeIDs {
		if i > 0 {
			inClause += ", "
		}
		inClause += fmt.Sprintf("'%s'", episodeID)
	}

	query := fmt.Sprintf(`
		SELECT 
			episode_id,
			strip_id,
			label,
			start,
			end,
			label_timestamp,
			operation_type,
			submitted_by,
			concat(label, '|', ifNull(toString(start), '0'), '|', ifNull(toString(end), '0')) as segment_key
		FROM annotationtool.dataset_annotations 
		WHERE episode_id IN (%s) AND dataset_id = '%s'
		AND concat(label, '|', ifNull(toString(start), '0'), '|', ifNull(toString(end), '0')) IN (
			SELECT segment_key
			FROM (
				SELECT 
					concat(label, '|', ifNull(toString(start), '0'), '|', ifNull(toString(end), '0')) as segment_key,
					SUM(
						CASE 
							WHEN operation_type = 'addition' THEN 1
							WHEN operation_type = 'removal' THEN -1
							ELSE 0
						END
					) as operation_sum
				FROM annotationtool.dataset_annotations
				WHERE episode_id IN (%s) AND dataset_id = '%s'
				GROUP BY segment_key
				HAVING operation_sum >= 1
			)
		)
		ORDER BY episode_id, strip_id, label_timestamp
	`, inClause, catalog, inClause, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get annotations by episode IDs", "error", err, "episode_count", len(episodeIDs))
		return nil, fmt.Errorf("failed to get annotations by episode IDs: %w", err)
	}
	defer rows.Close()

	annotations := []domain.AnnotationEntry{}

	for rows.Next() {
		var annotation domain.AnnotationEntry
		// The 9th key (segment_key) can be ignored in Scan by assigning to a blank identifier.
		err := rows.Scan(
			&annotation.EpisodeID,
			&annotation.StripID,
			&annotation.Label,
			&annotation.Start,
			&annotation.End,
			&annotation.LabelTimestamp,
			&annotation.OperationType,
			&annotation.SubmittedBy,
			new(interface{}), // ignore segment_key
		)
		if err != nil {
			r.logger.Error("Failed to scan annotation entry", "error", err)
			return nil, fmt.Errorf("failed to scan annotation entry: %w", err)
		}
		annotations = append(annotations, annotation)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over annotation rows", "error", err)
		return nil, fmt.Errorf("error iterating over annotation rows: %w", err)
	}

	r.logger.Info("Retrieved annotations by episode IDs",
		"episode_count", len(episodeIDs),
		"annotation_count", len(annotations))

	return annotations, nil
}

// GetEpisodeIdsAnnotatedByUserIDs gets all episode IDs annotated by any of the provided user IDs
func (r *ClickHouseLabellersRepository) GetEpisodeIdsAnnotatedByUserIDs(ctx context.Context, userIDs []string, catalog string) ([]string, error) {
	if len(userIDs) == 0 {
		return []string{}, nil
	}

	// Build IN clause with episode IDs
	inClause := ""
	for i, userID := range userIDs {
		if i > 0 {
			inClause += ", "
		}
		inClause += fmt.Sprintf("'%s'", userID)
	}

	query := fmt.Sprintf(`
		SELECT 
			episode_id
		FROM annotationtool.dataset_annotations 
		WHERE strip_id IS NULL AND submitted_by IN (%s) AND dataset_id = '%s'
		ORDER BY episode_id
	`, inClause, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get episode IDs annotated by user IDs", "error", err)
		return nil, fmt.Errorf("failed to get episode IDs annotated by user IDs: %w", err)
	}
	defer rows.Close()

	episodeIds := []string{}

	for rows.Next() {
		var episodeId string
		err := rows.Scan(
			&episodeId,
		)
		if err != nil {
			r.logger.Error("Failed to scan episode ID", "error", err)
			return nil, fmt.Errorf("failed to scan episode ID: %w", err)
		}
		episodeIds = append(episodeIds, episodeId)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over episode ID rows", "error", err)
		return nil, fmt.Errorf("error iterating over episode ID rows: %w", err)
	}

	r.logger.Info("Retrieved episode IDs annotated by user IDs",
		"user_count", len(userIDs),
		"episode_count", len(episodeIds))

	return episodeIds, nil
}

// GetAllAnnotations gets all annotations
func (r *ClickHouseLabellersRepository) GetAllStripAnnotators(ctx context.Context, catalog string) ([]domain.StripAnnotatorEntry, error) {
	query := fmt.Sprintf(`
		SELECT strip_id, arrayStringConcat(groupArray(DISTINCT submitted_by), ', ') as submitted_by 
		FROM annotationtool.dataset_annotations 
		WHERE strip_id IS NOT NULL AND label != 'isInterestingFlag' AND dataset_id = '%s'
		GROUP BY strip_id
	`, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get all annotations", "error", err)
		return nil, fmt.Errorf("failed to get all annotations: %w", err)
	}

	annotations := []domain.StripAnnotatorEntry{}

	for rows.Next() {
		var annotation domain.StripAnnotatorEntry
		err := rows.Scan(&annotation.StripID, &annotation.SubmittedBy)
		if err != nil {
			r.logger.Error("Failed to scan annotation entry", "error", err)
			return nil, fmt.Errorf("failed to scan annotation entry: %w", err)
		}
		annotations = append(annotations, annotation)
	}

	return annotations, nil
}

func (r *ClickHouseLabellersRepository) GetUnsureEpisodeIds(ctx context.Context, catalog string) ([]string, error) {
	query := fmt.Sprintf(`
		SELECT 
			episode_id
		FROM (
			SELECT 
				episode_id,
				SUM(
					CASE 
						WHEN operation_type = 'addition' THEN 1
						WHEN operation_type = 'removal' THEN -1
						ELSE 0
					END
				) as operation_sum
			FROM annotationtool.dataset_annotations
			WHERE label='unsure' AND strip_id IS NOT NULL AND dataset_id = '%s'
			GROUP BY episode_id
			HAVING operation_sum >= 1
		)
	`, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get unsure episode ids", "error", err)
		return nil, fmt.Errorf("failed to get unsure episode ids: %w", err)
	}
	defer rows.Close()

	episodeIds := []string{}

	for rows.Next() {
		var episodeId string
		err := rows.Scan(&episodeId)
		if err != nil {
			r.logger.Error("Failed to scan episode id", "error", err)
			return nil, fmt.Errorf("failed to scan episode id: %w", err)
		}
		episodeIds = append(episodeIds, episodeId)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over episode id rows", "error", err)
		return nil, fmt.Errorf("error iterating over episode id rows: %w", err)
	}

	r.logger.Info("Retrieved unsure episode ids",
		"episode_count", len(episodeIds))

	return episodeIds, nil
}

func (r *ClickHouseLabellersRepository) GetConflictingEpisodeIds(ctx context.Context, catalog string) ([]string, error) {
	query := fmt.Sprintf(`
		WITH active_labels AS (
			-- Calculate net operations and keep only active labels (sum > 0)
			SELECT 
				episode_id,
				strip_id,
				label,
				submitted_by
			FROM (
				SELECT 
					episode_id,
					strip_id,
					label,
					submitted_by,
					SUM(CASE 
						WHEN operation_type = 'addition' THEN 1 
						WHEN operation_type = 'removal' THEN -1 
						ELSE 0 
					END) as label_sum
				FROM annotationtool.dataset_annotations
				WHERE label != 'isInterestingFlag' AND label != 'isDoneFlag' AND dataset_id = '%s'
				GROUP BY episode_id, strip_id, label, submitted_by
			)
			WHERE label_sum > 0
		),
		submitter_label_sets AS (
			-- Create sorted label sets for each submitter on each episode-strip
			SELECT 
				episode_id,
				strip_id,
				submitted_by,
				arraySort(groupArray(label)) as label_set
			FROM active_labels
			GROUP BY episode_id, strip_id, submitted_by
		),
		conflicts AS (
			-- Find episode-strip combinations with conflicting label sets
			SELECT 
				episode_id,
				strip_id
			FROM submitter_label_sets
			GROUP BY episode_id, strip_id
			HAVING COUNT(DISTINCT submitted_by) >= 2 
				AND COUNT(DISTINCT label_set) > 1
		)
		-- Return distinct conflicting episodes
		SELECT DISTINCT episode_id
		FROM conflicts
		ORDER BY episode_id
	`, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get conflicting episode ids", "error", err)
		return nil, fmt.Errorf("failed to get conflicting episode ids: %w", err)
	}
	defer rows.Close()

	episodeIds := []string{}

	for rows.Next() {
		var episodeId string
		err := rows.Scan(&episodeId)
		if err != nil {
			r.logger.Error("Failed to scan episode id", "error", err)
			return nil, fmt.Errorf("failed to scan episode id: %w", err)
		}
		episodeIds = append(episodeIds, episodeId)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over episode id rows", "error", err)
		return nil, fmt.Errorf("error iterating over episode id rows: %w", err)
	}

	r.logger.Info("Retrieved conflicting episode ids",
		"episode_count", len(episodeIds))

	return episodeIds, nil
}

func (r *ClickHouseLabellersRepository) GetEpisodeLabellingHistory(ctx context.Context, episode_id string, catalog string) ([]domain.LabellingHistory, error) {
	query := fmt.Sprintf(`
		SELECT 
			episode_id,
			strip_id,
			submitted_by as labeller_id,
			label,
			operation_type as operation,
			label_timestamp as labelled_at	
		FROM annotationtool.dataset_annotations
		WHERE episode_id = '%s' AND dataset_id = '%s'
		ORDER BY label_timestamp
	`, episode_id, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get episode labelling history", "error", err)
		return nil, fmt.Errorf("failed to get episode labelling history: %w", err)
	}
	defer rows.Close()

	labelling_history_entries := []domain.LabellingHistory{}

	for rows.Next() {
		var labelling_history domain.LabellingHistory
		err := rows.Scan(&labelling_history.EpisodeID, &labelling_history.StripID, &labelling_history.LabellerID, &labelling_history.Label, &labelling_history.Operation, &labelling_history.LabelledAt)
		if err != nil {
			r.logger.Error("Failed to scan labelling history", "error", err)
			return nil, fmt.Errorf("failed to scan labelling history: %w", err)
		}
		labelling_history_entries = append(labelling_history_entries, labelling_history)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over labelling history rows", "error", err)
		return nil, fmt.Errorf("error iterating over labelling history rows: %w", err)
	}

	return labelling_history_entries, nil
}

func (r *ClickHouseLabellersRepository) GetStripLabellingHistory(ctx context.Context, strip_id string, catalog string) ([]domain.LabellingHistory, error) {
	query := fmt.Sprintf(`
		SELECT 
			episode_id,
			strip_id,
			submitted_by as labeller_id,
			label,
			operation_type as operation,
			label_timestamp as labelled_at
		FROM annotationtool.dataset_annotations
		WHERE strip_id = '%s' AND dataset_id = '%s'
		ORDER BY label_timestamp
	`, strip_id, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get strip labelling history", "error", err)
		return nil, fmt.Errorf("failed to get strip labelling history: %w", err)
	}
	defer rows.Close()

	labelling_history_entries := []domain.LabellingHistory{}

	for rows.Next() {
		var labelling_history domain.LabellingHistory
		err := rows.Scan(&labelling_history.EpisodeID, &labelling_history.StripID, &labelling_history.LabellerID, &labelling_history.Label, &labelling_history.Operation, &labelling_history.LabelledAt)
		if err != nil {
			r.logger.Error("Failed to scan labelling history", "error", err)
			return nil, fmt.Errorf("failed to scan labelling history: %w", err)
		}
		labelling_history_entries = append(labelling_history_entries, labelling_history)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over labelling history rows", "error", err)
		return nil, fmt.Errorf("error iterating over labelling history rows: %w", err)
	}

	return labelling_history_entries, nil
}

func (r *ClickHouseLabellersRepository) GetLabellersDump(ctx context.Context) ([]domain.Dump, error) {
	query := `
		SELECT 
			episode_id,
			strip_id,
			label,
			label_timestamp,
			operation_type,
			submitted_by,
			dataset_id,
			start,
			end
		FROM annotationtool.dataset_annotations
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get labellers dump", "error", err)
		return nil, fmt.Errorf("failed to get labellers dump: %w", err)
	}
	defer rows.Close()

	annotations := []domain.Dump{}

	for rows.Next() {
		var annotation domain.Dump
		err := rows.Scan(&annotation.EpisodeID, &annotation.StripID, &annotation.Label, &annotation.LabelTimestamp, &annotation.OperationType, &annotation.SubmittedBy, &annotation.DatasetID, &annotation.Start, &annotation.End)
		if err != nil {
			r.logger.Error("Failed to scan annotation entry", "error", err)
			return nil, fmt.Errorf("failed to scan annotation entry: %w", err)
		}
		annotations = append(annotations, annotation)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over annotation rows", "error", err)
		return nil, fmt.Errorf("error iterating over annotation rows: %w", err)
	}

	return annotations, nil
}

func (r *ClickHouseLabellersRepository) GetConflictResolvedEpisodeIds(ctx context.Context, catalog string) ([]string, error) {
	query := fmt.Sprintf(`
		SELECT 
			episode_id
		FROM (
			SELECT 
				episode_id,
				SUM(
					CASE 
						WHEN operation_type = 'approval' THEN 1
						WHEN operation_type = 'disapproval' THEN -1
						ELSE 0
					END
				) as operation_sum
			FROM annotationtool.dataset_annotations
			WHERE label='isConflictResolvedFlag' AND strip_id IS NULL AND dataset_id = '%s'
			GROUP BY episode_id
			HAVING operation_sum >= 1
		)
	`, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get conflict resolved episode ids", "error", err)
		return nil, fmt.Errorf("failed to get conflict resolved episode ids: %w", err)
	}
	defer rows.Close()

	episodeIds := []string{}

	for rows.Next() {
		var episodeId string
		err := rows.Scan(&episodeId)
		if err != nil {
			r.logger.Error("Failed to scan episode id", "error", err)
			return nil, fmt.Errorf("failed to scan episode id: %w", err)
		}
		episodeIds = append(episodeIds, episodeId)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over episode id rows", "error", err)
		return nil, fmt.Errorf("error iterating over episode id rows: %w", err)
	}

	r.logger.Info("Retrieved conflict resolved episode ids",
		"episode_count", len(episodeIds))

	return episodeIds, nil
}

func (r *ClickHouseLabellersRepository) GetLabellingStats(ctx context.Context, catalog string) ([]domain.LabellingStats, error) {
	query := fmt.Sprintf(`
		SELECT 
			submitted_by as labeller_id,
			toDate(toDateTime(label_timestamp / 1000)) as day,
			COUNT(DISTINCT strip_id) as strip_count
		FROM annotationtool.dataset_annotations
		WHERE strip_id IS NOT NULL AND dataset_id = '%s'
		GROUP BY 
			labeller_id,
			day
		ORDER BY 
			labeller_id,
			day
	`, catalog)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to get labelling stats", "error", err)
		return nil, fmt.Errorf("failed to get labelling stats: %w", err)
	}
	defer rows.Close()

	stats := []domain.LabellingStats{}

	for rows.Next() {
		var stat domain.LabellingStats
		err := rows.Scan(&stat.LabellerID, &stat.Day, &stat.StripCount)
		if err != nil {
			r.logger.Error("Failed to scan labelling stats", "error", err)
			return nil, fmt.Errorf("failed to scan labelling stats: %w", err)
		}
		stats = append(stats, stat)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error("Error iterating over labelling stats rows", "error", err)
		return nil, fmt.Errorf("error iterating over labelling stats rows: %w", err)
	}

	return stats, nil
}

func (r *ClickHouseLabellersRepository) Close() error {
	if r.db != nil {
		return r.db.Close()
	}
	return nil
}
