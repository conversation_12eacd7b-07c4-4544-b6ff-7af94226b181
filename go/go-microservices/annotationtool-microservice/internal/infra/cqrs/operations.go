package cqrs

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"

	"annotationtool-microservice/internal/app"
	"annotationtool-microservice/internal/infra/kafka/message"
)

// CQRSProcessor implements the processor interface for Kafka consumer
// It bridges Kafka messages to the CQRS registry
type CQRSProcessor struct {
	registry *Registry
	logger   *slog.Logger
}

// NewCQRSProcessor creates a new CQRS processor
func NewCQRSProcessor(registry *Registry, logger *slog.Logger) *CQRSProcessor {
	return &CQRSProcessor{
		registry: registry,
		logger:   logger,
	}
}

// Process implements the processor interface for Kafka consumer
func (p *CQRSProcessor) Process(ctx context.Context, envelope *message.Envelope) error {
	p.logger.Debug("Processing Kafka message", "type", envelope.Type)

	// Create a mapping of message types to command constructors
	commandTypes := map[string]func() any{
		"app.CreateDatasetCommand":        func() any { return &app.CreateDatasetCommand{} },
		"app.CreateDatasetVersionCommand": func() any { return &app.CreateDatasetVersionCommand{} },
		"app.CreateOperationCommand":      func() any { return &app.CreateOperationCommand{} },
		// Add more command types as needed
	}

	// Get the constructor for this message type
	constructor, exists := commandTypes[envelope.Type]
	if !exists {
		return fmt.Errorf("unknown command type: %s", envelope.Type)
	}

	// Unmarshal the payload into the appropriate command type
	cmd := constructor()
	if err := json.Unmarshal(envelope.Payload, cmd); err != nil {
		return fmt.Errorf("failed to unmarshal command payload: %w", err)
	}

	// Process through CQRS registry
	if err := p.registry.ProcessCommand(ctx, envelope.Type, cmd); err != nil {
		return fmt.Errorf("failed to process command: %w", err)
	}

	p.logger.Info("Successfully processed command", "type", envelope.Type)
	return nil
}
