package cqrs

import (
	"context"
	"fmt"
	"log/slog"
	"reflect"
	"sync"

	"annotationtool-microservice/internal/app"
)

// Type of operation (Command or Query)
type OperationType string

const (
	CommandType OperationType = "command"
	QueryType   OperationType = "query"
)

// CommandHandler defines a function that handles commands of type T.
type CommandHandler[T any] func(ctx context.Context, command T) error

// QueryHandler defines a function that handles queries of type T.
type QueryHandler[T any] func(ctx context.Context, query T) error

// operationProcessor wraps a typed handler into a generic processor interface.
type operationProcessor interface {
	Process(ctx context.Context, data any) error
}

// typedProcessor is a generic operation processor for a specific operation type.
type typedProcessor[T any] struct {
	handler func(ctx context.Context, input T) error
	logger  *slog.Logger
	typ     reflect.Type
}

func (p *typedProcessor[T]) Process(ctx context.Context, data any) error {
	value, ok := data.(T)
	if !ok {
		return fmt.Errorf("expected %s but got %T", p.typ, data)
	}
	p.logger.Debug("Processing message", "type", p.typ.Name())
	return p.handler(ctx, value)
}

// Registry maintains handlers for commands and queries.
type Registry struct {
	mu       sync.RWMutex
	commands map[string]operationProcessor
	queries  map[string]operationProcessor
	logger   *slog.Logger
}

// NewRegistry returns a new CQRS handler registry.
func NewRegistry(logger *slog.Logger) *Registry {
	return &Registry{
		commands: make(map[string]operationProcessor),
		queries:  make(map[string]operationProcessor),
		logger:   logger,
	}
}

// RegisterCommand registers a command handler.
func RegisterCommand[T any](r *Registry, name string, handler CommandHandler[T]) {
	var zero T
	r.mu.Lock()
	defer r.mu.Unlock()
	r.commands[name] = &typedProcessor[T]{
		handler: handler,
		typ:     reflect.TypeOf(zero),
		logger:  r.logger,
	}
	r.logger.Info("Registered command handler", "type", name)
}

// RegisterQuery registers a query handler.
func RegisterQuery[T any](r *Registry, name string, handler QueryHandler[T]) {
	var zero T
	r.mu.Lock()
	defer r.mu.Unlock()
	r.queries[name] = &typedProcessor[T]{
		handler: handler,
		typ:     reflect.TypeOf(zero),
		logger:  r.logger,
	}
	r.logger.Info("Registered query handler", "type", name)
}

// ProcessCommand executes a registered command handler.
func (r *Registry) ProcessCommand(ctx context.Context, name string, command any) error {
	r.mu.RLock()
	handler, ok := r.commands[name]
	r.mu.RUnlock()
	if !ok {
		return fmt.Errorf("no command handler registered for type: %s", name)
	}
	return handler.Process(ctx, command)
}

// ProcessQuery executes a registered query handler.
func (r *Registry) ProcessQuery(ctx context.Context, name string, query any) error {
	r.mu.RLock()
	handler, ok := r.queries[name]
	r.mu.RUnlock()
	if !ok {
		return fmt.Errorf("no query handler registered for type: %s", name)
	}
	return handler.Process(ctx, query)
}

// ServiceRegistry provides dynamic service registration and lookup using generics
type ServiceRegistry struct {
	services map[reflect.Type]any
	mu       sync.RWMutex
}

// NewServiceRegistry creates a new service registry
func NewServiceRegistry() *ServiceRegistry {
	return &ServiceRegistry{
		services: make(map[reflect.Type]any),
	}
}

// Register registers a service of type T in the registry
func Register[T any](sr *ServiceRegistry, service T) {
	sr.mu.Lock()
	defer sr.mu.Unlock()
	sr.services[reflect.TypeOf(service)] = service
}

// Get retrieves a service of type T from the registry
func Get[T any](sr *ServiceRegistry) (T, bool) {
	var zero T
	sr.mu.RLock()
	defer sr.mu.RUnlock()
	service, ok := sr.services[reflect.TypeOf(zero)]
	if !ok {
		return zero, false
	}
	return service.(T), true
}

// MustGet retrieves a service of type T from the registry, panics if not found
func MustGet[T any](sr *ServiceRegistry) T {
	service, ok := Get[T](sr)
	if !ok {
		panic(fmt.Sprintf("service not found: %T", *new(T)))
	}
	return service
}

// Registrar is responsible for registering all CQRS handlers.
type Registrar struct {
	registry        *Registry
	serviceRegistry *ServiceRegistry
	logger          *slog.Logger
}

// NewRegistrar creates a new Registrar instance.
func NewRegistrar(registry *Registry, serviceRegistry *ServiceRegistry, logger *slog.Logger) *Registrar {
	return &Registrar{
		registry:        registry,
		serviceRegistry: serviceRegistry,
		logger:          logger,
	}
}

// RegisterAll sets up all commands and queries with their handlers.
func (r *Registrar) RegisterAll() {
	// Dataset commands
	RegisterCommand(r.registry, "app.CreateDatasetCommand", func(ctx context.Context, cmd *app.CreateDatasetCommand) error {
		r.logger.Info("Handling CreateDatasetCommand", "cmd", cmd)
		datasetService := MustGet[*app.DatasetService](r.serviceRegistry)
		return datasetService.CreateDataset(ctx, *cmd)
	})

	RegisterCommand(r.registry, "app.CreateDatasetVersionCommand", func(ctx context.Context, cmd *app.CreateDatasetVersionCommand) error {
		r.logger.Info("Handling CreateDatasetVersionCommand", "cmd", cmd)
		datasetService := MustGet[*app.DatasetService](r.serviceRegistry)
		return datasetService.CreateDatasetVersion(ctx, *cmd)
	})

	// ECG and dataset query commands
	RegisterCommand(r.registry, "app.CreateOperationCommand", func(ctx context.Context, cmd *app.CreateOperationCommand) error {
		r.logger.Info("Handling CreateOperationCommand", "cmd", cmd)
		operationService := MustGet[*app.OperationService](r.serviceRegistry)
		_, err := operationService.CreateOperation(ctx, *cmd)
		return err
	})

	// Add more command/query registrations below...
	// Example for other services:
	// RegisterCommand(r.registry, "app.CreateAnnotationCommand", func(ctx context.Context, cmd *app.CreateAnnotationCommand) error {
	//     annotationService := MustGet[*app.AnnotationService](r.serviceRegistry)
	//     return annotationService.CreateAnnotation(ctx, *cmd)
	// })
}
