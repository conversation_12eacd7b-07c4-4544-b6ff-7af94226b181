package infra

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"annotationtool-microservice/internal/domain"

	"github.com/ClickHouse/clickhouse-go/v2"
)

// ClickHouseEpisodeRepository provides persistence for episodes in ClickHouse.
// NOTE: The current implementation covers only the functionality that is
// required by the service today. Additional query capabilities can be added
// incrementally as the API surface grows.
type ClickHouseEpisodeRepository struct {
	db     *sql.DB
	logger *slog.Logger
}

// NewClickHouseEpisodeRepository establishes a connection to ClickHouse and
// returns a repository instance that satisfies app.EpisodeRepository.
func NewClickHouseEpisodeRepository(dsn string, logger *slog.Logger) (*ClickHouseEpisodeRepository, error) {
	database := getEnv("CLICKHOUSE_DATABASE", "annotationtool")
	username := getEnv("CLICKHOUSE_USERNAME", "default")
	password := getEnv("CLICKHOUSE_PASSWORD", "")

	conn := clickhouse.OpenDB(&clickhouse.Options{
		Addr: []string{dsn},
		Auth: clickhouse.Auth{
			Database: database,
			Username: username,
			Password: password,
		},
	})

	if err := conn.Ping(); err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %w", err)
	}

	logger.Info("Successfully connected to ClickHouse (episode repo)", "dsn", dsn)

	return &ClickHouseEpisodeRepository{
		db:     conn,
		logger: logger,
	}, nil
}

func (r *ClickHouseEpisodeRepository) GetEpisodes(ctx context.Context, query string) (*domain.EpisodeQueryResult, error) {
	r.logger.Info("Executing episode query", "sql", query)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to execute episode query", "error", err)
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	// Get column information to detect if total_count_all_rows column is present
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	episodes := []domain.ClickhouseStripRow{}
	var actualTotalRows int64 = 0

	for rows.Next() {
		// Create slice to hold all column values (including potential total_count_all_rows)
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// Scan all columns
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		// Convert to map to check for total_count_all_rows
		row := make(map[string]interface{})
		for i, col := range columns {
			row[col] = values[i]
		}

		// Extract total count if present
		if totalCountVal, exists := row["total_count_all_rows"]; exists {
			if count, ok := totalCountVal.(int64); ok {
				actualTotalRows = count
			} else if count, ok := totalCountVal.(int); ok {
				actualTotalRows = int64(count)
			} else if count, ok := totalCountVal.(uint64); ok {
				actualTotalRows = int64(count)
			}
		}

		// Map the row data to ClickhouseStripRow struct
		// We need to maintain the original scan order for the expected fields
		var episode domain.ClickhouseStripRow

		// Find indices of expected columns (excluding total_count_all_rows)
		stripIDIdx := findColumnIndex(columns, "e.strip_id", "strip_id")
		episodeIDIdx := findColumnIndex(columns, "e.episodeid", "episodeid")
		patientIDIdx := findColumnIndex(columns, "e.patientid", "patientid")
		patientNameIdx := findColumnIndex(columns, "e.patientname", "patientname")
		clinicIDIdx := findColumnIndex(columns, "e.clinicid", "clinicid")
		clinicNameIdx := findColumnIndex(columns, "e.clinicname", "clinicname")
		manufacturerIdx := findColumnIndex(columns, "e.manufacturer", "manufacturer")
		deviceLabelIdx := findColumnIndex(columns, "e.devicelabel", "devicelabel")
		deviceModelIdx := findColumnIndex(columns, "e.devicemodel", "devicemodel")
		episodeLengthIdx := findColumnIndex(columns, "e.episodelength", "episodelength")
		reportTimeIdx := findColumnIndex(columns, "e.reporttime", "reporttime")
		timestampsIdx := findColumnIndex(columns, "e.ecgstrip_timestamps", "ecgstrip_timestamps")
		voltagesIdx := findColumnIndex(columns, "e.ecgstrip_voltages", "ecgstrip_voltages")

		// Safely assign values if columns exist
		if stripIDIdx >= 0 && values[stripIDIdx] != nil {
			episode.StripID = values[stripIDIdx].(string)
		}
		if episodeIDIdx >= 0 && values[episodeIDIdx] != nil {
			episode.EpisodeID = values[episodeIDIdx].(string)
		}
		if patientIDIdx >= 0 && values[patientIDIdx] != nil {
			episode.PatientID = values[patientIDIdx].(string)
		}
		if patientNameIdx >= 0 && values[patientNameIdx] != nil {
			episode.PatientName = values[patientNameIdx].(string)
		}
		if clinicIDIdx >= 0 && values[clinicIDIdx] != nil {
			episode.ClinicID = values[clinicIDIdx].(string)
		}
		if clinicNameIdx >= 0 && values[clinicNameIdx] != nil {
			episode.ClinicName = values[clinicNameIdx].(string)
		}
		if manufacturerIdx >= 0 && values[manufacturerIdx] != nil {
			episode.Manufacturer = values[manufacturerIdx].(string)
		}
		if deviceLabelIdx >= 0 && values[deviceLabelIdx] != nil {
			episode.DeviceLabel = values[deviceLabelIdx].(string)
		}
		if deviceModelIdx >= 0 && values[deviceModelIdx] != nil {
			episode.DeviceModel = values[deviceModelIdx].(string)
		}
		if episodeLengthIdx >= 0 && values[episodeLengthIdx] != nil {
			episode.EpisodeLength = values[episodeLengthIdx].(string)
		}
		if reportTimeIdx >= 0 && values[reportTimeIdx] != nil {
			switch v := values[reportTimeIdx].(type) {
			case time.Time:
				episode.ReportTime = v
			case []byte:
				if ts, ok := parseReportTime(string(v)); ok {
					episode.ReportTime = ts
				}
			case string:
				if ts, ok := parseReportTime(v); ok {
					episode.ReportTime = ts
				}
			}
		}
		if timestampsIdx >= 0 && values[timestampsIdx] != nil {
			switch ts := values[timestampsIdx].(type) {
			case []float32:
				episode.Timestamps = ts
			case []*float32:
				converted := make([]float32, len(ts))
				for i, v := range ts {
					if v != nil {
						converted[i] = *v
					} else {
						converted[i] = 0 // or handle nil as needed
					}
				}
				episode.Timestamps = converted
			}
		}
		if voltagesIdx >= 0 && values[voltagesIdx] != nil {
			switch vs := values[voltagesIdx].(type) {
			case []float32:
				episode.Voltages = vs
			case []*float32:
				converted := make([]float32, len(vs))
				for i, v := range vs {
					if v != nil {
						converted[i] = *v
					} else {
						converted[i] = 0 // or handle nil as needed
					}
				}
				episode.Voltages = converted
			}
		}

		episodes = append(episodes, episode)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error reading rows: %w", err)
	}

	// Use the extracted total count if available, otherwise fall back to row count
	totalRows := actualTotalRows
	if totalRows == 0 {
		totalRows = int64(len(episodes))
	}

	r.logger.Info("Episode query executed successfully",
		"rows_returned", len(episodes),
		"total_rows", totalRows,
	)

	return &domain.EpisodeQueryResult{
		Episodes:  episodes,
		TotalRows: totalRows,
	}, nil
}

func (r *ClickHouseEpisodeRepository) GetDashboardData(ctx context.Context, query string) (*domain.DashboardDataQueryResult, error) {
	r.logger.Info("Executing dashboard data query", "sql", query)

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		r.logger.Error("Failed to execute dashboard data query", "error", err)
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	// Get column information to detect if total_count_all_rows column is present
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	dashboardData := []domain.ClickhouseDashboardStripRow{}
	var actualTotalRows int64 = 0

	for rows.Next() {
		// Create slice to hold all column values (including potential total_count_all_rows)
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// Scan all columns
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		// Convert to map to check for total_count_all_rows
		row := make(map[string]interface{})
		for i, col := range columns {
			row[col] = values[i]
		}

		// Extract total count if present
		if totalCountVal, exists := row["total_count_all_rows"]; exists {
			if count, ok := totalCountVal.(int64); ok {
				actualTotalRows = count
			} else if count, ok := totalCountVal.(int); ok {
				actualTotalRows = int64(count)
			} else if count, ok := totalCountVal.(uint64); ok {
				actualTotalRows = int64(count)
			}
		}

		// Map the row data to ClickhouseDashboardStripRow struct
		var dashboardRow domain.ClickhouseDashboardStripRow

		// Find indices of base ClickhouseStripRow columns
		stripIDIdx := findColumnIndex(columns, "e.strip_id", "strip_id")
		episodeIDIdx := findColumnIndex(columns, "e.episodeid", "episodeid")
		patientIDIdx := findColumnIndex(columns, "e.patientid", "patientid")
		patientNameIdx := findColumnIndex(columns, "e.patientname", "patientname")
		clinicIDIdx := findColumnIndex(columns, "e.clinicid", "clinicid")
		clinicNameIdx := findColumnIndex(columns, "e.clinicname", "clinicname")
		manufacturerIdx := findColumnIndex(columns, "e.manufacturer", "manufacturer")
		deviceLabelIdx := findColumnIndex(columns, "e.devicelabel", "devicelabel")
		deviceModelIdx := findColumnIndex(columns, "e.devicemodel", "devicemodel")
		episodeLengthIdx := findColumnIndex(columns, "e.episodelength", "episodelength")
		reportTimeIdx := findColumnIndex(columns, "e.reporttime", "reporttime")
		timestampsIdx := findColumnIndex(columns, "e.ecgstrip_timestamps", "ecgstrip_timestamps")
		voltagesIdx := findColumnIndex(columns, "e.ecgstrip_voltages", "ecgstrip_voltages")

		// Find indices of additional dashboard columns
		patientCountIdx := findColumnIndex(columns, "patientcount", "patientcount")
		episodeCountIdx := findColumnIndex(columns, "episodecount", "episodecount")
		ecgStripsCountIdx := findColumnIndex(columns, "ecgstripscount", "ecgstripscount")
		episodeLengthMinutesIdx := findColumnIndex(columns, "episodelengthminutes", "episodelengthminutes")
		isLabelledIdx := findColumnIndex(columns, "is_labelled", "is_labelled")
		hierarichicalDoctorLabelIdx := findColumnIndex(columns, "hierarichical-doctorLabel", "hierarichical-doctorLabel")
		leftColumnDistributorEpisodeLengthIdx := findColumnIndex(columns, "leftColumnDistributor-episodeLength", "leftColumnDistributor-episodeLength")
		leftColumnDistributorDeviceLabelIdx := findColumnIndex(columns, "leftColumnDistributor-deviceLabel", "leftColumnDistributor-deviceLabel")
		leftColumnDistributorDoctorLabelIdx := findColumnIndex(columns, "leftColumnDistributor-doctorLabel", "leftColumnDistributor-doctorLabel")

		// Safely assign base values if columns exist
		if stripIDIdx >= 0 && values[stripIDIdx] != nil {
			dashboardRow.StripID = values[stripIDIdx].(string)
		}
		if episodeIDIdx >= 0 && values[episodeIDIdx] != nil {
			dashboardRow.EpisodeID = values[episodeIDIdx].(string)
		}
		if patientIDIdx >= 0 && values[patientIDIdx] != nil {
			dashboardRow.PatientID = values[patientIDIdx].(string)
		}
		if patientNameIdx >= 0 && values[patientNameIdx] != nil {
			dashboardRow.PatientName = values[patientNameIdx].(string)
		}
		if clinicIDIdx >= 0 && values[clinicIDIdx] != nil {
			dashboardRow.ClinicID = values[clinicIDIdx].(string)
		}
		if clinicNameIdx >= 0 && values[clinicNameIdx] != nil {
			dashboardRow.ClinicName = values[clinicNameIdx].(string)
		}
		if manufacturerIdx >= 0 && values[manufacturerIdx] != nil {
			dashboardRow.Manufacturer = values[manufacturerIdx].(string)
		}
		if deviceLabelIdx >= 0 && values[deviceLabelIdx] != nil {
			dashboardRow.DeviceLabel = values[deviceLabelIdx].(string)
		}
		if deviceModelIdx >= 0 && values[deviceModelIdx] != nil {
			dashboardRow.DeviceModel = values[deviceModelIdx].(string)
		}
		if episodeLengthIdx >= 0 && values[episodeLengthIdx] != nil {
			dashboardRow.EpisodeLength = values[episodeLengthIdx].(string)
		}
		if reportTimeIdx >= 0 && values[reportTimeIdx] != nil {
			switch v := values[reportTimeIdx].(type) {
			case time.Time:
				dashboardRow.ReportTime = v
			case []byte:
				if ts, ok := parseReportTime(string(v)); ok {
					dashboardRow.ReportTime = ts
				}
			case string:
				if ts, ok := parseReportTime(v); ok {
					dashboardRow.ReportTime = ts
				}
			}
		}
		if timestampsIdx >= 0 && values[timestampsIdx] != nil {
			if timestamps, ok := values[timestampsIdx].([]float32); ok {
				dashboardRow.Timestamps = timestamps
			}
		}
		if voltagesIdx >= 0 && values[voltagesIdx] != nil {
			if voltages, ok := values[voltagesIdx].([]float32); ok {
				dashboardRow.Voltages = voltages
			}
		}

		// Safely assign additional dashboard values if columns exist
		if patientCountIdx >= 0 && values[patientCountIdx] != nil {
			if count, ok := values[patientCountIdx].(int64); ok {
				dashboardRow.PatientCount = count
			} else if count, ok := values[patientCountIdx].(int); ok {
				dashboardRow.PatientCount = int64(count)
			} else if count, ok := values[patientCountIdx].(uint64); ok {
				dashboardRow.PatientCount = int64(count)
			}
		}
		if episodeCountIdx >= 0 && values[episodeCountIdx] != nil {
			if count, ok := values[episodeCountIdx].(int64); ok {
				dashboardRow.EpisodeCount = count
			} else if count, ok := values[episodeCountIdx].(int); ok {
				dashboardRow.EpisodeCount = int64(count)
			} else if count, ok := values[episodeCountIdx].(uint64); ok {
				dashboardRow.EpisodeCount = int64(count)
			}
		}
		if ecgStripsCountIdx >= 0 && values[ecgStripsCountIdx] != nil {
			if count, ok := values[ecgStripsCountIdx].(int64); ok {
				dashboardRow.ECGStripsCount = count
			} else if count, ok := values[ecgStripsCountIdx].(int); ok {
				dashboardRow.ECGStripsCount = int64(count)
			} else if count, ok := values[ecgStripsCountIdx].(uint64); ok {
				dashboardRow.ECGStripsCount = int64(count)
			}
		}
		if episodeLengthMinutesIdx >= 0 && values[episodeLengthMinutesIdx] != nil {
			if length, ok := values[episodeLengthMinutesIdx].(float64); ok {
				dashboardRow.EpisodeLengthMinutes = &length
			} else if length, ok := values[episodeLengthMinutesIdx].(float32); ok {
				lengthFloat64 := float64(length)
				dashboardRow.EpisodeLengthMinutes = &lengthFloat64
			}
		}
		if isLabelledIdx >= 0 && values[isLabelledIdx] != nil {
			if isLabelled, ok := values[isLabelledIdx].(bool); ok {
				dashboardRow.IsLabelled = &isLabelled
			}
		}

		if hierarichicalDoctorLabelIdx >= 0 && values[hierarichicalDoctorLabelIdx] != nil {
			dashboardRow.HierarichicalDoctorLabel = values[hierarichicalDoctorLabelIdx].(string)
		}
		if leftColumnDistributorEpisodeLengthIdx >= 0 && values[leftColumnDistributorEpisodeLengthIdx] != nil {
			dashboardRow.LeftColumnDistributorEpisodeLength = values[leftColumnDistributorEpisodeLengthIdx].(string)
		}
		if leftColumnDistributorDeviceLabelIdx >= 0 && values[leftColumnDistributorDeviceLabelIdx] != nil {
			dashboardRow.LeftColumnDistributorDeviceLabel = values[leftColumnDistributorDeviceLabelIdx].(string)
		}
		if leftColumnDistributorDoctorLabelIdx >= 0 && values[leftColumnDistributorDoctorLabelIdx] != nil {
			dashboardRow.LeftColumnDistributorDoctorLabel = values[leftColumnDistributorDoctorLabelIdx].(string)
		}

		dashboardData = append(dashboardData, dashboardRow)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error reading rows: %w", err)
	}

	// Use the extracted total count if available, otherwise fall back to row count
	totalRows := actualTotalRows
	if totalRows == 0 {
		totalRows = int64(len(dashboardData))
	}

	r.logger.Info("Dashboard data query executed successfully",
		"rows_returned", len(dashboardData),
		"total_rows", totalRows,
	)

	return &domain.DashboardDataQueryResult{
		Data:      dashboardData,
		TotalRows: totalRows,
	}, nil
}

// findColumnIndex finds the index of a column by name (case-insensitive)
func findColumnIndex(columns []string, name string, fallbackName string) int {
	for i, col := range columns {
		if strings.EqualFold(col, name) || strings.EqualFold(col, fallbackName) {
			return i
		}
	}
	return -1
}

// parseReportTime attempts to parse timestamps that may come back as strings/bytes
// from ClickHouse in a variety of common formats.
func parseReportTime(s string) (time.Time, bool) {
	s = strings.TrimSpace(s)
	if s == "" {
		return time.Time{}, false
	}

	// Try a set of common formats
	layouts := []string{
		time.RFC3339Nano,
		time.RFC3339,
		"2006-01-02 15:04:05.999999 -0700 MST",
		"2006-01-02 15:04:05 -0700 MST",
		"2006-01-02 15:04:05.999999 MST",
		"2006-01-02 15:04:05 MST",
		"2006-01-02 15:04:05.999999",
		"2006-01-02 15:04:05",
	}

	// Some sources append literal " UTC" without offset; handle that case explicitly
	if strings.HasSuffix(s, " UTC") {
		candidate := strings.TrimSuffix(s, " UTC") + " +0000 UTC"
		if ts, err := time.Parse("2006-01-02 15:04:05.999999 -0700 MST", candidate); err == nil {
			return ts.UTC(), true
		}
		if ts, err := time.Parse("2006-01-02 15:04:05 -0700 MST", candidate); err == nil {
			return ts.UTC(), true
		}
	}

	for _, layout := range layouts {
		if ts, err := time.Parse(layout, s); err == nil {
			return ts.UTC(), true
		}
	}

	// As a last resort, try parsing in UTC without zone using the microseconds layout
	if ts, err := time.ParseInLocation("2006-01-02 15:04:05.999999", s, time.UTC); err == nil {
		return ts.UTC(), true
	}
	if ts, err := time.ParseInLocation("2006-01-02 15:04:05", s, time.UTC); err == nil {
		return ts.UTC(), true
	}

	return time.Time{}, false
}
