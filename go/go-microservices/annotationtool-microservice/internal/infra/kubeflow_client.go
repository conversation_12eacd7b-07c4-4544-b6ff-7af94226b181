package infra

import (
	"annotationtool-microservice/internal/domain"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type KubeflowClient struct {
	baseURL    string
	cookie     string
	httpClient *http.Client
	logger     *slog.Logger
}

// BaseURL returns the configured Kubeflow base URL (with trailing slash trimmed)
func (c *KubeflowClient) BaseURL() string {
	return strings.TrimSuffix(c.baseURL, "/")
}

func NewKubeflowClient(baseURL, cookie string, logger *slog.Logger) (*KubeflowClient, error) {
	if baseURL == "" {
		return nil, fmt.Errorf("kubeflow URL is required")
	}

	if !strings.HasSuffix(baseURL, "/") {
		baseURL += "/"
	}

	client := &KubeflowClient{
		baseURL: baseURL,
		cookie:  cookie,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}

	logger.Info("Kubeflow client initialized", "baseURL", baseURL)

	return client, nil
}

func (c *KubeflowClient) GetNotebooks(ctx context.Context, namespace string) ([]*domain.Notebook, error) {

	if namespace == "" {
		return nil, fmt.Errorf("namespace is required")
	}

	endpoint := fmt.Sprintf("%sjupyter/api/namespaces/%s/notebooks",
		c.baseURL, url.PathEscape(namespace))

	c.logger.Info("Fetching notebooks from Kubeflow",
		"endpoint", endpoint,
		"namespace", namespace)

	req, err := c.buildRequest(ctx, "GET", endpoint, nil, false)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		c.logger.Error("Kubeflow API returned error status",
			"status", resp.StatusCode,
			"body", string(body))
		return nil, fmt.Errorf("kubeflow API returned status %d: %s", resp.StatusCode, string(body))
	}

	var apiResponse domain.NotebooksApiResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Convert to domain objects (ensure unique pointers per element)
	notebooks := make([]*domain.Notebook, len(apiResponse.Notebooks))
	for i := range apiResponse.Notebooks {
		nb := apiResponse.Notebooks[i]
		notebooks[i] = &nb
	}

	c.logger.Info("Successfully fetched notebooks",
		"count", len(notebooks),
		"namespace", namespace)

	return notebooks, nil
}

// CreateNotebookRequest represents the JSON payload expected by Kubeflow Jupyter Web App
type CreateNotebookRequest struct {
	Name            string            `json:"name"`
	Namespace       string            `json:"namespace"`
	Image           string            `json:"image,omitempty"`
	CPU             string            `json:"cpu,omitempty"`
	Memory          string            `json:"memory,omitempty"`
	GPUs            int               `json:"gpus"`
	ImagePullPolicy string            `json:"imagePullPolicy,omitempty"`
	ServerType      string            `json:"serverType,omitempty"`
	Annotations     map[string]string `json:"annotations,omitempty"`
	Labels          map[string]string `json:"labels,omitempty"`
}

func (c *KubeflowClient) CreateNotebook(ctx context.Context, namespace string, req CreateNotebookRequest) (*domain.Notebook, error) {
	if namespace == "" {
		return nil, fmt.Errorf("namespace is required")
	}
	if req.Name == "" {
		return nil, fmt.Errorf("name is required")
	}

	endpoint := fmt.Sprintf("%sjupyter/api/namespaces/%s/notebooks", c.baseURL, url.PathEscape(namespace))

	c.logger.Info("Creating Kubeflow notebook",
		"endpoint", endpoint,
		"namespace", namespace,
		"name", req.Name,
	)

	// Ensure namespace in payload
	req.Namespace = namespace

	payload, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to encode request: %w", err)
	}

	httpReq, err := c.buildRequest(ctx, "POST", endpoint, strings.NewReader(string(payload)), true)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		c.logger.Error("Kubeflow API returned error status",
			"status", resp.StatusCode,
			"body", string(body))
		return nil, fmt.Errorf("kubeflow API returned status %d: %s", resp.StatusCode, string(body))
	}

	var nb domain.Notebook
	if err := json.Unmarshal(body, &nb); err != nil {
		// Some Kubeflow setups may not return the full notebook; tolerate empty body
		c.logger.Warn("Failed to parse notebook creation response, returning basic object", "error", err)
		nb = domain.Notebook{Metadata: domain.Metadata{Name: req.Name, Namespace: namespace}, Name: req.Name, Namespace: namespace}
	}

	return &nb, nil
}

// UpdateNotebookRequest supports partial updates; fields omitted are left unchanged
type UpdateNotebookRequest struct {
	Image           string            `json:"image,omitempty"`
	CPU             string            `json:"cpu,omitempty"`
	Memory          string            `json:"memory,omitempty"`
	GPUs            int               `json:"gpus,omitempty"`
	ImagePullPolicy string            `json:"imagePullPolicy,omitempty"`
	ServerType      string            `json:"serverType,omitempty"`
	Annotations     map[string]string `json:"annotations,omitempty"`
	Labels          map[string]string `json:"labels,omitempty"`
}

func (c *KubeflowClient) UpdateNotebook(ctx context.Context, namespace, name string, req UpdateNotebookRequest) (*domain.Notebook, error) {
	if namespace == "" || name == "" {
		return nil, fmt.Errorf("namespace and name are required")
	}
	endpoint := fmt.Sprintf("%sjupyter/api/namespaces/%s/notebooks/%s", c.baseURL, url.PathEscape(namespace), url.PathEscape(name))
	payload, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to encode request: %w", err)
	}
	httpReq, err := c.buildRequest(ctx, "PATCH", endpoint, strings.NewReader(string(payload)), true)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("kubeflow API returned status %d: %s", resp.StatusCode, string(body))
	}
	var nb domain.Notebook
	if err := json.Unmarshal(body, &nb); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}
	return &nb, nil
}

func (c *KubeflowClient) DeleteNotebook(ctx context.Context, namespace, name string) error {
	if namespace == "" || name == "" {
		return fmt.Errorf("namespace and name are required")
	}
	endpoint := fmt.Sprintf("%sjupyter/api/namespaces/%s/notebooks/%s", c.baseURL, url.PathEscape(namespace), url.PathEscape(name))
	httpReq, err := c.buildRequest(ctx, "DELETE", endpoint, nil, false)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("kubeflow API returned status %d: %s", resp.StatusCode, string(body))
	}
	return nil
}

func (c *KubeflowClient) StartNotebook(ctx context.Context, namespace, name string) error {
	if namespace == "" || name == "" {
		return fmt.Errorf("namespace and name are required")
	}
	endpoint := fmt.Sprintf("%sjupyter/api/namespaces/%s/notebooks/%s/start", c.baseURL, url.PathEscape(namespace), url.PathEscape(name))
	httpReq, err := c.buildRequest(ctx, "POST", endpoint, nil, false)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusAccepted {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("kubeflow API returned status %d: %s", resp.StatusCode, string(body))
	}
	return nil
}

func (c *KubeflowClient) StopNotebook(ctx context.Context, namespace, name string) error {
	if namespace == "" || name == "" {
		return fmt.Errorf("namespace and name are required")
	}
	endpoint := fmt.Sprintf("%sjupyter/api/namespaces/%s/notebooks/%s/stop", c.baseURL, url.PathEscape(namespace), url.PathEscape(name))
	httpReq, err := c.buildRequest(ctx, "POST", endpoint, nil, false)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusAccepted {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("kubeflow API returned status %d: %s", resp.StatusCode, string(body))
	}
	return nil
}

func (c *KubeflowClient) RestartNotebook(ctx context.Context, namespace, name string) error {
	if namespace == "" || name == "" {
		return fmt.Errorf("namespace and name are required")
	}
	endpoint := fmt.Sprintf("%sjupyter/api/namespaces/%s/notebooks/%s/restart", c.baseURL, url.PathEscape(namespace), url.PathEscape(name))
	httpReq, err := c.buildRequest(ctx, "POST", endpoint, nil, false)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusAccepted {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("kubeflow API returned status %d: %s", resp.StatusCode, string(body))
	}
	return nil
}

// setCommonHeaders sets headers required by Kubeflow Jupyter Web App including CSRF protection
func (c *KubeflowClient) setCommonHeaders(req *http.Request, contentTypeJSON bool) {
	if contentTypeJSON {
		req.Header.Set("Content-Type", "application/json")
	}
	// Always pass the cookie string as provided via configuration
	if c.cookie != "" {
		req.Header.Set("Cookie", c.cookie)
		// If the cookie string contains an XSRF token cookie, mirror it in the header
		if xsrf := extractCookieValue(c.cookie, "XSRF-TOKEN"); xsrf != "" {
			req.Header.Set("X-XSRF-TOKEN", xsrf)
		}
	}
	// Many Kubeflow setups expect this header for API requests triggered via JS
	req.Header.Set("X-Requested-With", "XMLHttpRequest")
	req.Header.Set("Accept", "application/json, text/plain, */*")

	// Set Origin and Referer to satisfy CSRF checks (use base URL's origin)
	if u, err := url.Parse(c.baseURL); err == nil {
		origin := u.Scheme + "://" + u.Host
		req.Header.Set("Origin", origin)
		// Referer should typically point to the Jupyter Web App page
		referer := strings.TrimSuffix(c.baseURL, "/") + "/jupyter/"
		req.Header.Set("Referer", referer)
	}
}

// buildRequest constructs an HTTP request using the provided context
// so cancellations/timeouts from the caller are respected.
func (c *KubeflowClient) buildRequest(ctx context.Context, method, endpoint string, body io.Reader, contentTypeJSON bool) (*http.Request, error) {
	req, err := http.NewRequestWithContext(ctx, method, endpoint, body)
	if err != nil {
		return nil, err
	}
	c.setCommonHeaders(req, contentTypeJSON)
	return req, nil
}

// extractCookieValue parses a raw Cookie header string and returns the value of the given cookie name
func extractCookieValue(cookieHeader, name string) string {
	// cookieHeader format (Cookie header): "k=v; other=val; XSRF-TOKEN=abc%3D%3D"
	// Parse more robustly: split on ';', then splitN on first '='; trim, handle quotes and URL-decoding.
	parts := strings.Split(cookieHeader, ";")
	for _, part := range parts {
		p := strings.TrimSpace(part)
		if p == "" {
			continue
		}
		kv := strings.SplitN(p, "=", 2)
		if len(kv) != 2 {
			continue
		}
		key := strings.TrimSpace(kv[0])
		if key != name {
			continue
		}
		val := strings.TrimSpace(kv[1])
		// Trim surrounding quotes if present
		if len(val) >= 2 && ((val[0] == '"' && val[len(val)-1] == '"') || (val[0] == '\'' && val[len(val)-1] == '\'')) {
			val = val[1 : len(val)-1]
		}
		if decoded, err := url.QueryUnescape(val); err == nil {
			return decoded
		}
		return val
	}
	return ""
}
