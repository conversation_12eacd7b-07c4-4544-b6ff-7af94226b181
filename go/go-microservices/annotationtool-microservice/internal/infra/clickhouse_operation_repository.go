package infra

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"os"
	"strings"

	"annotationtool-microservice/internal/domain"

	"github.com/ClickHouse/clickhouse-go/v2"
)

// ClickHouseOperationRepository implements OperationRepository with ClickHouse storage
type ClickHouseOperationRepository struct {
	db     *sql.DB
	logger *slog.Logger
}

// NewClickHouseOperationRepository creates a new ClickHouse operation repository
func NewClickHouseOperationRepository(dsn string, logger *slog.Logger) (*ClickHouseOperationRepository, error) {
	database := getEnv("CLICKHOUSE_DATABASE", "annotationtool")
	username := getEnv("CLICKHOUSE_USERNAME", "default")
	password := getEnv("CLICKHOUSE_PASSWORD", "")

	// Avoid printing credentials; log minimal connection info
	logger.Info("Initializing ClickHouse client", "database", database, "username", username)

	conn := clickhouse.OpenDB(&clickhouse.Options{
		Addr: []string{dsn},
		Auth: clickhouse.Auth{
			Database: database,
			Username: username,
			Password: password,
		},
	})

	// Test the connection
	if err := conn.Ping(); err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %w", err)
	}

	logger.Info("Successfully connected to ClickHouse", "dsn", dsn)

	return &ClickHouseOperationRepository{
		db:     conn,
		logger: logger,
	}, nil
}

// CreateOperation creates a new operation in ClickHouse
func (r *ClickHouseOperationRepository) CreateOperation(ctx context.Context, operation *domain.Operation) error {
	query := `
		INSERT INTO annotationtool.dataset_annotations 
		(id, dataset_id, episode_id, strip_id, label, label_timestamp, operation_type, submitted_by, start, end, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	r.logger.Debug("Inserting operation into ClickHouse",
		"operation_id", operation.ID,
		"dataset_id", operation.DatasetID,
		"episode_id", operation.EpisodeID,
	)

	// ClickHouse's Nullable(String) maps cleanly from sql.NullString; using it
	// avoids any ambiguity in the driver when we want to insert NULL for an
	// empty strip ID.
	stripID := sql.NullString{String: operation.StripID, Valid: operation.StripID != ""}

	_, err := r.db.ExecContext(ctx, query,
		operation.ID,
		operation.DatasetID,
		operation.EpisodeID,
		stripID,
		operation.Label,
		operation.LabelTimestamp,
		string(operation.OperationType),
		operation.SubmittedBy,
		operation.Start,
		operation.End,
		operation.CreatedAt,
		operation.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to insert operation", "error", err, "operation_id", operation.ID)
		return fmt.Errorf("failed to insert operation: %w", err)
	}

	r.logger.Debug("Operation inserted successfully", "operation_id", operation.ID)

	return nil
}

func (r *ClickHouseOperationRepository) CreateDataset(ctx context.Context, datasetPath string, tableName string) error {
	minioAccessKey := getEnv("MINIO_ACCESS_KEY", "")
	minioSecretKey := getEnv("MINIO_SECRET_KEY", "")

	// Clean the dataset path by removing /browser/ if present (MinIO console UI path)
	cleanedPath := datasetPath
	if strings.Contains(datasetPath, "/browser/") {
		cleanedPath = strings.Replace(datasetPath, "/browser/", "/", 1)
	}

	// Extract the filetype from the datasetPath (e.g., "s3://bucket/path/file.csv" -> "csv")
	filetype := "" // default
	if idx := strings.LastIndex(cleanedPath, "."); idx != -1 && idx < len(cleanedPath)-1 {
		ext := strings.ToLower(cleanedPath[idx+1:])
		switch ext {
		case "csv":
			filetype = "CSV"
		case "tsv":
			filetype = "TSV"
		case "parquet":
			filetype = "Parquet"
		case "json":
			filetype = "JSONEachRow"
		}
	}

	if filetype == "" {
		return fmt.Errorf("unsupported filetype: %s", cleanedPath)
	}

	tableName = strings.ReplaceAll(tableName, "-", "_")
	tableName = strings.ReplaceAll(tableName, ".", "_")

	r.logger.Info("Creating dataset from S3",
		"original_path", datasetPath,
		"cleaned_path", cleanedPath,
		"table", tableName,
		"filetype", filetype)

	// Execute queries separately to avoid multi-statement issues

	// First, get the schema of the S3 file
	var createLocalQuery string

	// Get the schema first
	describeQuery := fmt.Sprintf(`
        DESCRIBE s3(
            '%s',
            '%s',
            '%s',
            '%s'
        )
    `, cleanedPath, minioAccessKey, minioSecretKey, filetype)

	r.logger.Debug("Getting schema from S3 file", "query", describeQuery)
	rows, err := r.db.QueryContext(ctx, describeQuery)
	if err != nil {
		return fmt.Errorf("failed to describe S3 file structure: %w", err)
	}
	defer rows.Close()

	// Create table with proper ORDER BY clause and settings to allow nullable keys
	if filetype == "Parquet" {
		createLocalQuery = fmt.Sprintf(`
            CREATE TABLE annotationtool.%s_local ON CLUSTER 'default'
            ENGINE = MergeTree()
            ORDER BY (episodeid, patientid, reporttime)
            SETTINGS allow_nullable_key = 1
            AS SELECT * FROM s3(
                '%s',
                '%s',
                '%s',
                '%s'
            ) LIMIT 0
        `, tableName, cleanedPath, minioAccessKey, minioSecretKey, filetype)
	} else {
		createLocalQuery = fmt.Sprintf(`
            CREATE TABLE annotationtool.%s_local ON CLUSTER 'default'
            ENGINE = MergeTree()
            ORDER BY (episodeid, patientid, reporttime)
            SETTINGS allow_nullable_key = 1
            AS SELECT * FROM s3(
                '%s',
                '%s',
                '%s',
                '%s'
            ) LIMIT 0
        `, tableName, cleanedPath, minioAccessKey, minioSecretKey, filetype)
	}

	r.logger.Debug("Creating local table structure", "table", tableName+"_local")
	_, err = r.db.ExecContext(ctx, createLocalQuery)
	if err != nil {
		// If standard approach fails, try s3Cluster as fallback
		r.logger.Warn("Standard table creation failed, trying s3Cluster approach", "error", err)

		altQuery := fmt.Sprintf(`
            CREATE TABLE annotationtool.%s_local ON CLUSTER 'default'
            ENGINE = MergeTree()
            ORDER BY (episodeid, patientid, reporttime)
            SETTINGS allow_nullable_key = 1
            AS SELECT * FROM s3Cluster(
                'default',
                '%s',
                '%s',
                '%s',
                '%s'
            ) LIMIT 0
        `, tableName, cleanedPath, minioAccessKey, minioSecretKey, filetype)

		r.logger.Debug("Trying alternative table creation with s3Cluster", "query", altQuery)
		_, altErr := r.db.ExecContext(ctx, altQuery)
		if altErr != nil {
			return fmt.Errorf("failed to create local table structure with both standard and cluster approaches. Original error: %w, Alternative error: %v", err, altErr)
		}
	}

	// 2. Create distributed table
	createDistributedQuery := fmt.Sprintf(`
        CREATE TABLE annotationtool.%s ON CLUSTER 'default'
        AS annotationtool.%s_local
        ENGINE = Distributed('default', 'annotationtool', '%s_local', rand())
    `, tableName, tableName, tableName)

	r.logger.Debug("Creating distributed table", "table", tableName)
	_, err = r.db.ExecContext(ctx, createDistributedQuery)
	if err != nil {
		return fmt.Errorf("failed to create distributed table: %w", err)
	}

	// 3. Insert data into distributed table
	insertQuery := fmt.Sprintf(`
        INSERT INTO annotationtool.%s
        SELECT * FROM s3(
            '%s',
            '%s',
            '%s',
            '%s'
        )
    `, tableName, cleanedPath, minioAccessKey, minioSecretKey, filetype)

	r.logger.Info("Starting data insertion into distributed table - this may take several minutes for large datasets",
		"table", tableName,
		"source", cleanedPath)

	_, err = r.db.ExecContext(ctx, insertQuery)
	if err != nil {
		// If insert fails for Parquet, try s3Cluster approach
		if filetype == "Parquet" {
			r.logger.Warn("Standard Parquet insert failed, trying s3Cluster approach", "error", err)

			altInsertQuery := fmt.Sprintf(`
                INSERT INTO annotationtool.%s
                SELECT * FROM s3Cluster(
                    'default',
                    '%s',
                    '%s',
                    '%s',
                    '%s'
                )
            `, tableName, cleanedPath, minioAccessKey, minioSecretKey, filetype)

			r.logger.Info("Retrying data insertion with s3Cluster approach", "table", tableName)
			_, altErr := r.db.ExecContext(ctx, altInsertQuery)
			if altErr != nil {
				return fmt.Errorf("failed to insert data with both standard and cluster approaches. Original error: %w, Alternative error: %v", err, altErr)
			}
			r.logger.Info("Data insertion completed successfully using s3Cluster approach", "table", tableName)
		} else {
			return fmt.Errorf("failed to insert data into distributed table: %w", err)
		}
	} else {
		r.logger.Info("Data insertion completed successfully", "table", tableName)
	}

	r.logger.Info("Successfully created dataset tables", "table", tableName)
	return nil
}

// GetDatasetStats aggregates statistics for a given dataset in ClickHouse
func (r *ClickHouseOperationRepository) GetDatasetStats(ctx context.Context, datasetID string) (*domain.DatasetStats, error) {
	// total annotations, unique labellers, labelled strips, episodes touched, unsure episodes, finished/unfinished
	// "Finished" is defined as episodes which have active isDoneFlag at episode level (strip_id IS NULL) with net +1 (approval/disapproval logic not applied here)
	// "Unfinished" are episodes that were touched but have no active isDoneFlag.

	query := fmt.Sprintf(`
        WITH
        annotations AS (
            SELECT *
            FROM annotationtool.dataset_annotations
            WHERE dataset_id = '%s'
        ),
        active_labels AS (
            SELECT episode_id, strip_id, label, submitted_by
            FROM (
                SELECT episode_id, strip_id, label, submitted_by,
                       SUM(CASE WHEN operation_type = 'addition' THEN 1 WHEN operation_type = 'removal' THEN -1 ELSE 0 END) AS op_sum
                FROM annotations
                WHERE strip_id IS NOT NULL AND label != 'isInterestingFlag'
                GROUP BY episode_id, strip_id, label, submitted_by
            )
            WHERE op_sum > 0
        ),
        labelled_episodes AS (
            SELECT DISTINCT episode_id FROM active_labels
        ),
        active_done AS (
            SELECT episode_id
            FROM (
                SELECT episode_id,
                       SUM(CASE WHEN operation_type = 'addition' THEN 1 WHEN operation_type = 'removal' THEN -1 ELSE 0 END) AS op_sum
                FROM annotations
                WHERE label = 'isDoneFlag' AND strip_id IS NULL
                GROUP BY episode_id
            )
            WHERE op_sum > 0
        ),
        unsure_episodes AS (
            SELECT episode_id
            FROM (
                SELECT episode_id,
                       SUM(CASE WHEN operation_type = 'addition' THEN 1 WHEN operation_type = 'removal' THEN -1 ELSE 0 END) AS op_sum
                FROM annotations
                WHERE label = 'unsure' AND strip_id IS NOT NULL
                GROUP BY episode_id
            )
            WHERE op_sum > 0
        )
        SELECT
            (SELECT COUNT(*) FROM annotations) AS total_annotations,
            (SELECT COUNT(DISTINCT submitted_by) FROM annotations) AS unique_labellers,
            (SELECT COUNT(DISTINCT strip_id) FROM annotations WHERE strip_id IS NOT NULL AND label != 'isInterestingFlag') AS labelled_strips,
            (SELECT COUNT(DISTINCT episode_id) FROM annotations) AS episodes_touched,
            (SELECT COUNT() FROM labelled_episodes) AS labelled_episodes,
            (SELECT COUNT() FROM active_done) AS finished_episodes,
            (SELECT COUNT(DISTINCT episode_id) FROM annotations WHERE episode_id NOT IN (SELECT episode_id FROM active_done)) AS unfinished_episodes,
            (SELECT COUNT() FROM unsure_episodes) AS unsure_episodes,
            (SELECT toDateTime64(max(label_timestamp)/1000, 3) FROM annotations) AS last_activity_at
    `, datasetID)

	row := r.db.QueryRowContext(ctx, query)
	var stats domain.DatasetStats
	stats.DatasetID = datasetID

	var lastActivity sql.NullTime
	if err := row.Scan(
		&stats.TotalAnnotations,
		&stats.UniqueLabellers,
		&stats.LabelledStrips,
		&stats.EpisodesTouched,
		&stats.LabelledEpisodes,
		&stats.FinishedEpisodes,
		&stats.UnfinishedEpisodes,
		&stats.UnsureEpisodes,
		&lastActivity,
	); err != nil {
		r.logger.Error("Failed to get dataset stats", "error", err)
		return nil, fmt.Errorf("failed to get dataset stats: %w", err)
	}

	if lastActivity.Valid {
		t := lastActivity.Time
		stats.LastActivityAt = &t
	}

	return &stats, nil
}

// Close closes the database connection
func (r *ClickHouseOperationRepository) Close() error {
	if r.db != nil {
		return r.db.Close()
	}
	return nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
