package container

import (
	"context"
	"fmt"
	"log/slog"
	"os"

	"annotationtool-microservice/internal/app"
	"annotationtool-microservice/internal/config"
	"annotationtool-microservice/internal/infra"
	"annotationtool-microservice/internal/infra/cqrs"
	"annotationtool-microservice/internal/infra/kafka"

	"github.com/lmittmann/tint"
)

// Container holds all dependencies for the annotation tool microservice
type Container struct {
	config *config.Config
	logger *slog.Logger

	// Repositories
	episodeRepo    app.EpisodeRepository
	annotationRepo app.AnnotationRepository
	refDataRepo    app.ReferenceDataRepository
	operationRepo  app.OperationRepository
	datasetRepo    app.DatasetRepository
	notebookRepo   app.NotebookRepository
	clickhouseRepo app.ClickHouseOperationRepository // For cleanup
	minioRepo      *infra.MinioDatasetRepository     // For cleanup (optional, may be nil if using composite repo)
	eventPublisher app.EventPublisher

	// Services
	annotationService *app.AnnotationService
	operationService  *app.OperationService
	datasetService    *app.DatasetService
	notebookService   *app.NotebookService
	ecgService        app.ECGServiceInterface
	labellersService  *app.LabellersService

	// Infrastructure
	datasetMetrics *infra.DatasetMetrics
	auditLogger    *infra.AuditLogger

	// CQRS and Messaging
	cqrsRegistry    *cqrs.Registry
	serviceRegistry *cqrs.ServiceRegistry
	publisher       *kafka.Publisher
	consumer        *kafka.Consumer
}

// New creates a new container with all dependencies
func New() (*Container, error) {
	// Load configuration
	cfg, err := config.New()
	if err != nil {
		return nil, err
	}

	// Initialize logger
	logger := initLogger(cfg)

	// Resolve ClickHouse connection details once
	clickhouseDSN := getEnv("CLICKHOUSE_DSN", "localhost:9002")

	// Initialize repositories (ClickHouse-based)
	episodeRepo, err := infra.NewClickHouseEpisodeRepository(clickhouseDSN, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize ClickHouse episode repository: %w", err)
	}

	annotationRepo, err := infra.NewClickHouseAnnotationRepository(clickhouseDSN, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize ClickHouse annotation repository: %w", err)
	}

	refDataRepo, err := infra.NewClickHouseReferenceDataRepository(clickhouseDSN, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize ClickHouse reference data repository: %w", err)
	}

	// Event publisher that simply logs for now
	eventPublisher := infra.NewLoggingEventPublisher(logger)

	// Initialize ClickHouse operation repository
	logger.Info("Configuring ClickHouse connection", "dsn", clickhouseDSN)
	clickhouseRepo, err := infra.NewClickHouseOperationRepository(clickhouseDSN, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize ClickHouse operation repository: %w", err)
	}

	logger.Info("Using ClickHouse operation repository")

	// Initialize composite dataset repository (MinIO + Trino)
	datasetRepo, err := infra.NewCompositeDatasetRepositoryFromConfig(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize composite dataset repository: %w", err)
	}

	logger.Info("Using composite dataset repository (MinIO + Trino)")

	// Initialize infrastructure components
	datasetMetrics := infra.NewDatasetMetrics()
	auditLogger := infra.NewAuditLogger(logger)

	// Initialize labellers repository and service
	labellersRepo, err := infra.NewClickHouseLabellersRepository(clickhouseDSN, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize ClickHouse labellers repository: %w", err)
	}

	// Initialize CQRS registry
	cqrsRegistry := cqrs.NewRegistry(logger)

	// Initialize CQRS processor that bridges Kafka to CQRS
	cqrsProcessor := cqrs.NewCQRSProcessor(cqrsRegistry, logger)

	// Initialize Kafka publisher/consumer with graceful fallback
	var publisher *kafka.Publisher
	var consumer *kafka.Consumer

	if !cfg.KafkaEnabled {
		logger.Info("Kafka disabled by configuration; starting in direct-processing mode", "enabled", cfg.KafkaEnabled)
	} else {
		logger.Info("Kafka enabled by configuration; starting in Kafka mode", "enabled", cfg.KafkaEnabled)
		// Try to create producer
		p, err := kafka.NewPublisher(cfg.KafkaBrokers, cfg.KafkaTopic, logger)
		if err != nil {
			logger.Warn("Kafka unavailable, falling back to direct-processing mode",
				"error", err, "brokers", cfg.KafkaBrokers, "topic", cfg.KafkaTopic)
			cfg.KafkaEnabled = false
		} else {
			publisher = p

			// Only create consumer if producer succeeded
			retryConfig := kafka.RetryConfig{
				BaseDelay:     cfg.KafkaRetryBaseDelay,
				MaxDelay:      cfg.KafkaRetryMaxDelay,
				JitterEnabled: cfg.KafkaRetryJitterEnabled,
			}

			c, err := kafka.NewConsumer(
				cfg.KafkaBrokers,
				cfg.KafkaConsumerGroup,
				cfg.KafkaTopic,
				cfg.KafkaRetryCount,
				retryConfig,
				cfg.KafkaDeadLetterTopic,
				cqrsProcessor,
				logger,
			)
			if err != nil {
				logger.Warn("Kafka consumer init failed; disabling Kafka and falling back to direct-processing",
					"error", err)
				_ = publisher.Close()
				publisher = nil
				cfg.KafkaEnabled = false
			} else {
				consumer = c
				// Start consumer in background
				go func() {
					logger.Info("Starting Kafka consumer...")
					if err := consumer.Start(context.Background()); err != nil {
						logger.Error("Kafka consumer failed", "error", err)
					}
				}()
				logger.Info("Kafka consumer started in background")
			}
		}
	}

	labellersService := app.NewLabellersService(
		labellersRepo,
		eventPublisher,
		logger,
	)

	// Initialize Kubeflow client and notebook repository
	kubeflowClient, err := infra.NewKubeflowClient(cfg.KubeflowURL, cfg.KubeflowCookie, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Kubeflow client: %w", err)
	}

	notebookRepo, err := infra.NewKubeflowRepository(kubeflowClient, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Kubeflow notebook repository: %w", err)
	}

	notebookService := app.NewNotebookService(
		notebookRepo,
		logger,
	)

	// Initialize services
	annotationService := app.NewAnnotationService(
		episodeRepo,
		annotationRepo,
		refDataRepo,
		eventPublisher,
		logger,
	)

	operationService := app.NewOperationService(
		clickhouseRepo,
		logger,
	)

	datasetService := app.NewDatasetService(
		datasetRepo,
		clickhouseRepo,
		logger,
	)

	ecgService := app.NewECGService(
		datasetRepo,
		episodeRepo,
		labellersService,
		logger,
		cfg.TrinoCatalog,
		cfg.TrinoSchema,
	)

	// Create service registry and register all services
	serviceRegistry := cqrs.NewServiceRegistry()
	cqrs.Register(serviceRegistry, datasetService)
	cqrs.Register(serviceRegistry, annotationService)
	cqrs.Register(serviceRegistry, operationService)
	cqrs.Register(serviceRegistry, ecgService)
	cqrs.Register(serviceRegistry, labellersService)
	// Add more services as needed...

	container := &Container{
		config:            cfg,
		logger:            logger,
		episodeRepo:       episodeRepo,
		annotationRepo:    annotationRepo,
		refDataRepo:       refDataRepo,
		operationRepo:     clickhouseRepo,
		datasetRepo:       datasetRepo,
		notebookRepo:      notebookRepo,
		clickhouseRepo:    clickhouseRepo,
		minioRepo:         nil, // No longer using direct MinIO repo
		eventPublisher:    eventPublisher,
		annotationService: annotationService,
		operationService:  operationService,
		datasetService:    datasetService,
		notebookService:   notebookService,
		ecgService:        ecgService,
		labellersService:  labellersService,
		datasetMetrics:    datasetMetrics,
		auditLogger:       auditLogger,
		cqrsRegistry:      cqrsRegistry,
		serviceRegistry:   serviceRegistry,
		publisher:         publisher,
		consumer:          consumer,
	}

	// Register all CQRS handlers with the service registry
	registrar := cqrs.NewRegistrar(cqrsRegistry, serviceRegistry, logger)
	registrar.RegisterAll()

	logger.Info("Container initialized successfully")

	return container, nil
}

// Config returns the configuration
func (c *Container) Config() *config.Config {
	return c.config
}

// Logger returns the logger
func (c *Container) Logger() *slog.Logger {
	return c.logger
}

// AnnotationService returns the annotation service
func (c *Container) AnnotationService() *app.AnnotationService {
	return c.annotationService
}

// OperationService returns the operation service
func (c *Container) OperationService() *app.OperationService {
	return c.operationService
}

// DatasetService returns the dataset service
func (c *Container) DatasetService() *app.DatasetService {
	return c.datasetService
}

// EpisodeRepository returns the episode repository
func (c *Container) EpisodeRepository() app.EpisodeRepository {
	return c.episodeRepo
}

// AnnotationRepository returns the annotation repository
func (c *Container) AnnotationRepository() app.AnnotationRepository {
	return c.annotationRepo
}

// OperationRepository returns the operation repository
func (c *Container) OperationRepository() app.OperationRepository {
	return c.operationRepo
}

// ReferenceDataRepository returns the reference data repository
func (c *Container) ReferenceDataRepository() app.ReferenceDataRepository {
	return c.refDataRepo
}

// EventPublisher returns the event publisher
func (c *Container) EventPublisher() app.EventPublisher {
	return c.eventPublisher
}

// ECGService returns the ECG service
func (c *Container) ECGService() app.ECGServiceInterface {
	return c.ecgService
}

// LabellersService returns the labellers service
func (c *Container) LabellersService() *app.LabellersService {
	return c.labellersService
}

// NotebookService returns the notebook service
func (c *Container) NotebookService() *app.NotebookService {
	return c.notebookService
}

// DatasetMetrics returns the dataset metrics
func (c *Container) DatasetMetrics() *infra.DatasetMetrics {
	return c.datasetMetrics
}

// AuditLogger returns the audit logger
func (c *Container) AuditLogger() *infra.AuditLogger {
	return c.auditLogger
}

// PublishingService returns the publishing service for the API layer
func (c *Container) PublishingService() app.Publisher {
	return c.publisher
}

func (c *Container) Publisher() *kafka.Publisher {
	return c.publisher
}

func (c *Container) Consumer() *kafka.Consumer {
	return c.consumer
}

// Close performs cleanup when the container is shut down
func (c *Container) Close(ctx context.Context) error {
	c.logger.Info("Shutting down container...")

	// Close ClickHouse connection if it exists
	if c.clickhouseRepo != nil {
		if err := c.clickhouseRepo.Close(); err != nil {
			c.logger.Error("Failed to close ClickHouse connection", "error", err)
		} else {
			c.logger.Info("ClickHouse connection closed successfully")
		}
	}

	// Close MinIO connection if it exists
	if c.minioRepo != nil {
		if err := c.minioRepo.Close(); err != nil {
			c.logger.Error("Failed to close MinIO connection", "error", err)
		} else {
			c.logger.Info("MinIO connection closed successfully")
		}
	}

	// Close dataset repository (composite repo will handle both MinIO and Trino)
	if c.datasetRepo != nil {
		if err := c.datasetRepo.Close(); err != nil {
			c.logger.Error("Failed to close dataset repository", "error", err)
		} else {
			c.logger.Info("Dataset repository closed successfully")
		}
	}

	// Close kafka publisher
	if c.publisher != nil {
		if err := c.publisher.Close(); err != nil {
			c.logger.Error("Failed to close Kafka Publisher", "error", err)
		} else {
			c.logger.Info("Kafka Publisher closed successfully")
		}
	}

	// Close kafka consumer
	if c.consumer != nil {
		if err := c.consumer.Close(); err != nil {
			c.logger.Error("Failed to close Kafka Consumer", "error", err)
		} else {
			c.logger.Info("Kafka Consumer closed successfully")
		}
	}

	c.logger.Info("Container shutdown complete")
	return nil
}

// initLogger initializes the structured logger based on configuration
func initLogger(cfg *config.Config) *slog.Logger {
	var level slog.Level
	switch cfg.LogLevel {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: cfg.Environment == "development",
	}

	var handler slog.Handler
	if cfg.Environment == "development" {
		// Colorized, human-friendly logs in development
		handler = tint.NewHandler(os.Stdout, &tint.Options{
			Level:     level,
			AddSource: cfg.Environment == "development",
		})
	} else {
		handler = slog.NewJSONHandler(os.Stdout, opts)
	}

	logger := slog.New(handler)

	return logger
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
