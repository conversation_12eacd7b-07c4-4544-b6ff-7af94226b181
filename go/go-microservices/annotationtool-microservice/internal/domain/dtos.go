package domain

// Strip<PERSON>ithLabels represents an ECG strip with its latest annotations
type StripWithLabels struct {
	*ECGStrip
	Labels map[string][]string `json:"labels"` // userID -> labels array
}

// EpisodeWithAnnotations represents an episode with embedded strip and annotation data
type EpisodeWithAnnotations struct {
	*Episode
	LabelledBy []string          `json:"labelled_by"`
	Strips     []StripWithLabels `json:"strips"`
}

// PaginatedEpisodes represents paginated episode results
type PaginatedEpisodes struct {
	Episodes   []EpisodeWithAnnotations `json:"episodes"`
	TotalCount int                      `json:"total_count"`
	Page       int                      `json:"page"`
	Limit      int                      `json:"limit"`
	TotalPages int                      `json:"total_pages"`
}

// Clinic represents a clinic entity
type Clinic struct {
	ClinicID   string `json:"clinic_id"`
	ClinicName string `json:"clinic_name"`
}

// Patient represents a patient entity
type Patient struct {
	PatientID   string `json:"patient_id"`
	PatientName string `json:"patient_name"`
}

// Labeller represents a user who can label episodes
type Labeller struct {
	LabellerID string `json:"labeller_id"`
}

// LabellingHistory represents the labelling history for an episode or strip
type LabellingHistory struct {
	EpisodeID  string  `json:"episode_id"`
	StripID    *string `json:"strip_id,omitempty"`
	LabellerID string  `json:"labeller_id"`
	LabelledAt float64 `json:"labelled_at"`
	Label      string  `json:"label"`
	Operation  string  `json:"operation"`
}

type Group struct {
	LabellerID string             `json:"labeller_id"`
	Entries    []LabellingHistory `json:"entries"`
	StartAt    float64            `json:"start_at"`
	EndAt      float64            `json:"end_at"`
}

type LabellingStats struct {
	LabellerID string `json:"labeller_id"`
	Day        string `json:"day"`
	StripCount int    `json:"strip_count"`
}

type Dump struct {
	EpisodeID      string   `json:"episode_id"`
	StripID        *string  `json:"strip_id,omitempty"`
	Label          string   `json:"label"`
	LabelTimestamp float64  `json:"label_timestamp"`
	OperationType  string   `json:"operation_type"`
	SubmittedBy    string   `json:"submitted_by"`
	DatasetID      string   `json:"dataset_id"`
	Start          *float64 `json:"start,omitempty"` // Start position for time-based annotations
	End            *float64 `json:"end,omitempty"`   // End position for time-based annotations
}
