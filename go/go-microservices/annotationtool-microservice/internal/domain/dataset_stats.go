package domain

import "time"

// DatasetStats represents aggregated statistics for a dataset in ClickHouse
type DatasetStats struct {
	DatasetID          string     `json:"dataset_id"`
	TotalAnnotations   int64      `json:"total_annotations"`
	UniqueLabellers    int64      `json:"unique_labellers"`
	LabelledStrips     int64      `json:"labelled_strips"`
	EpisodesTouched    int64      `json:"episodes_touched"`
	LabelledEpisodes   int64      `json:"labelled_episodes"`
	FinishedEpisodes   int64      `json:"finished_episodes"`
	UnfinishedEpisodes int64      `json:"unfinished_episodes"`
	UnsureEpisodes     int64      `json:"unsure_episodes"`
	LastActivityAt     *time.Time `json:"last_activity_at,omitempty"`
}
