package domain

import (
	"time"

	"github.com/google/uuid"
)

// OperationType represents the type of operation performed
type OperationType string

const (
	OperationTypeAddition    OperationType = "addition"
	OperationTypeRemoval     OperationType = "removal"
	OperationTypeApproval    OperationType = "approval"
	OperationTypeDisapproval OperationType = "disapproval"
)

// Operation represents a labelling operation performed on an ECG strip
type Operation struct {
	ID             string        `json:"id"`
	DatasetID      string        `json:"dataset_id"`
	EpisodeID      string        `json:"episode_id"`
	StripID        string        `json:"strip_id"`
	Label          string        `json:"label"`
	LabelTimestamp float64       `json:"label_timestamp"`
	OperationType  OperationType `json:"operation_type"`
	SubmittedBy    string        `json:"submitted_by"`
	Start          *float64      `json:"start,omitempty"` // Start position for time-based annotations
	End            *float64      `json:"end,omitempty"`   // End position for time-based annotations
	CreatedAt      time.Time     `json:"created_at"`
	UpdatedAt      time.Time     `json:"updated_at"`
}

// NewOperation creates a new Operation instance
func NewOperation(datasetID, episodeID, stripID, label string, labelTimestamp float64, operationType OperationType, submittedBy string, start, end *float64) *Operation {
	now := time.Now()
	return &Operation{
		ID:             uuid.New().String(),
		DatasetID:      datasetID,
		EpisodeID:      episodeID,
		StripID:        stripID,
		Label:          label,
		LabelTimestamp: labelTimestamp,
		OperationType:  operationType,
		SubmittedBy:    submittedBy,
		Start:          start,
		End:            end,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
}

// IsValid checks if the operation has valid values
func (o *Operation) IsValid() bool {
	if o.DatasetID == "" || o.EpisodeID == "" || o.Label == "" || o.SubmittedBy == "" {
		return false
	}

	switch o.OperationType {
	case OperationTypeAddition, OperationTypeRemoval, OperationTypeApproval, OperationTypeDisapproval:
		return true
	default:
		return false
	}
}
