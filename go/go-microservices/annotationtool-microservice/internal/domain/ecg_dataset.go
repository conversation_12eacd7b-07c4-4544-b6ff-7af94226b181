package domain

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

// Label constants
const (
	LabelIsDoneFlag = "isDoneFlag"
	LabelUnsure     = "unsure"
)

// Labelling status constants
const (
	LabellingStatusUnlabeledEpisodes = "unlabeledEpisodes"
	LabellingStatusLabeledEpisodes   = "labeledEpisodes"
	LabellingStatusUnsureEpisodes    = "unsureEpisodes"
)

// Conflict status constants
const (
	ConflictStatusConflicting = "conflicting"
	ConflictStatusResolved    = "resolved"
)

// Order direction constants
const (
	OrderDirectionAsc  = "asc"
	OrderDirectionDesc = "desc"
)

// Default values
const (
	DefaultLimit    = 2
	MaxLimit        = 10
	DefaultOrderBy  = "reporttime"
	DefaultOrderDir = "desc"
)

// Valid order by fields
var ValidOrderByFields = []string{"reporttime", "reportTime", "episodeid", "stripNumber", "patientId"}

// Default values for unknown data
const (
	DefaultUnknownClinic        = "Unknown Clinic"
	DefaultUnknownManufacturer  = "Unknown Manufacturer"
	DefaultUnknownDeviceModel   = "Unknown Device Model"
	DefaultUnknownLength        = "Unknown Length"
	DefaultUnknownEpisodeLength = "Unknown Episode Length"
)

// Labelling status values
const (
	LabellingStatusYes = "yes"
	LabellingStatusNo  = "no"
)

// SQL fragments
const (
	SQLEmptyResult = "SELECT * FROM (SELECT NULL as episodeid) WHERE 1=0"
)

// Column names
const (
	ColumnPatientCount   = "patientcount"
	ColumnEpisodeCount   = "episodecount"
	ColumnECGStripsCount = "ecgstripscount"
)

// Episode length categories
const (
	EpisodeLengthLessThan1Min = "Less than 1 min"
	EpisodeLength1To3Min      = "1-3 min"
	EpisodeLength3To5Min      = "3-5 min"
	EpisodeLength5To10Min     = "5-10 min"
	EpisodeLength10To30Min    = "10-30 min"
	EpisodeLength30To60Min    = "30-60 min"
	EpisodeLength1To2Hrs      = "1-2 hrs"
	EpisodeLength2To6Hrs      = "2-6 hrs"
	EpisodeLength6To12Hrs     = "6-12 hrs"
	EpisodeLength12To24Hrs    = "12-24 hrs"
	EpisodeLength24PlusHrs    = "24+ hrs"
	EpisodeLengthOther        = "Other"
)

// ProcessedLabel represents a processed label with timestamp and optional time-based positions
type ProcessedLabel struct {
	Label     string   `json:"label"`
	Timestamp float64  `json:"timestamp"`
	Start     *float64 `json:"start,omitempty"` // Start position for time-based annotations
	End       *float64 `json:"end,omitempty"`   // End position for time-based annotations
}

type AnnotationEntry struct {
	EpisodeID      string   `json:"episode_id"`
	StripID        *string  `json:"strip_id"`
	Label          string   `json:"label"`
	LabelTimestamp float64  `json:"label_timestamp"`
	OperationType  string   `json:"operation_type"`
	SubmittedBy    string   `json:"submitted_by"`
	Start          *float64 `json:"start,omitempty"` // Start position for time-based annotations
	End            *float64 `json:"end,omitempty"`   // End position for time-based annotations
}

type StripAnnotatorEntry struct {
	StripID     string `json:"strip_id"`
	SubmittedBy string `json:"submitted_by"`
}

// LabellersServiceInterface defines the interface for accessing labellers service
type LabellersServiceInterface interface {
	GetLabellers(ctx context.Context, catalog string) ([]Labeller, error)
	GetLabelledEpisodeIds(ctx context.Context, catalog string, labels, userIds []string, maxLabellingTime *string) ([]string, error)
	GetLabelledStripIds(ctx context.Context, catalog string) ([]string, error)
	GetAnnotationsByEpisodeIDs(ctx context.Context, episodeIDs []string, catalog string) ([]AnnotationEntry, error)
	GetEpisodeIdsAnnotatedByUserIDs(ctx context.Context, userIDs []string, catalog string) ([]string, error)
	GetAllStripAnnotators(ctx context.Context, catalog string) ([]StripAnnotatorEntry, error)
	GetUnsureEpisodeIds(ctx context.Context, catalog string) ([]string, error)
	GetConflictingEpisodeIds(ctx context.Context, catalog string) ([]string, error)
	GetConflictResolvedEpisodeIds(ctx context.Context, catalog string) ([]string, error)
	GetEpisodeLabellingHistory(ctx context.Context, episode_id string, catalog string) ([]Group, error)
	GetStripLabellingHistory(ctx context.Context, strip_id string, catalog string) ([]Group, error)
	GetLabellersDump(ctx context.Context) ([]Dump, error)
}

// ECGDataset represents ECG data stored in Trino/MinIO
type ECGDataset struct {
	// Episode information
	EpisodeID     string    `json:"episode_id"`
	PatientID     string    `json:"patient_id"`
	PatientName   string    `json:"patient_name"`
	ClinicID      string    `json:"clinic_id"`
	ClinicName    string    `json:"clinic_name"`
	Manufacturer  string    `json:"manufacturer"`
	DeviceLabel   string    `json:"device_label"`
	DeviceModel   string    `json:"device_model"`
	ReportTime    time.Time `json:"report_time"`
	EpisodeLength string    `json:"episode_length"`

	// ECG Strip information
	StripID     string    `json:"strip_id"`
	StripNumber int       `json:"strip_number"`
	Timestamps  []float32 `json:"timestamps"`
	Voltages    []float32 `json:"voltages"`

	// Metadata
	DataLayer string    `json:"data_layer"` // bronze, silver, gold
	FilePath  string    `json:"file_path"`  // MinIO path
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ECGQueryRequest represents a request to query ECG data
type ECGQueryRequest struct {
	// Episode filters
	AnnotatorIDs    []string `json:"annotator_ids,omitempty"`
	EpisodeIDs      []string `json:"episode_ids,omitempty"`
	PatientIDs      []string `json:"patient_ids,omitempty"`
	ClinicIDs       []string `json:"clinic_ids,omitempty"`
	Manufacturers   []string `json:"manufacturers,omitempty"`
	DeviceModels    []string `json:"device_models,omitempty"`
	LabellingStatus []string `json:"labelling_status,omitempty"`
	ConflictStatus  []string `json:"conflict_status,omitempty"`
	LabelledBy      []string `json:"labelled_by,omitempty"`
	NotLabelledBy   []string `json:"not_labelled_by,omitempty"`

	// Labelling time filters
	MaxLabellingTime *string `json:"max_labelling_time,omitempty"`

	// Time range filters
	ReportTimeFrom *time.Time `json:"report_time_from,omitempty"`
	ReportTimeTo   *time.Time `json:"report_time_to,omitempty"`

	// ECG Strip filters
	StripNumbers []int    `json:"strip_numbers,omitempty"`
	MinVoltage   *float64 `json:"min_voltage,omitempty"`
	MaxVoltage   *float64 `json:"max_voltage,omitempty"`

	// Current user ID
	CurrentUser string `json:"current_user,omitempty"`

	// MinIO path selection (replaces data_layer)
	Catalog string `json:"catalog,omitempty"`

	// Query options
	Limit    int    `json:"limit,omitempty"`
	Offset   int    `json:"offset,omitempty"`
	OrderBy  string `json:"order_by,omitempty"`  // report_time, episode_id, strip_number
	OrderDir string `json:"order_dir,omitempty"` // asc, desc
}

type ECGNumConflictingEpisodesRequest struct {
	Catalog string `json:"catalog,omitempty" form:"catalog"`
}

type ECGDashboardRequest struct {
	HierarichicalClinic                *int16  `json:"hierarichical-clinic,omitempty" form:"hierarichical-clinic"`
	HierarichicalManufacturer          *int16  `json:"hierarichical-manufacturer,omitempty" form:"hierarichical-manufacturer"`
	HierarichicalDeviceModel           *int16  `json:"hierarichical-deviceModel,omitempty" form:"hierarichical-deviceModel"`
	HierarichicalDeviceLabel           *int16  `json:"hierarichical-deviceLabel,omitempty" form:"hierarichical-deviceLabel"`
	HierarichicalDoctorLabel           *int16  `json:"hierarichical-doctorLabel,omitempty" form:"hierarichical-doctorLabel"`
	LeftColumnDistributorEpisodeLength *int16  `json:"leftColumnDistributor-episodeLength,omitempty" form:"leftColumnDistributor-episodeLength"`
	LeftColumnDistributorDeviceLabel   *int16  `json:"leftColumnDistributor-deviceLabel,omitempty" form:"leftColumnDistributor-deviceLabel"`
	LeftColumnDistributorDoctorLabel   *int16  `json:"leftColumnDistributor-doctorLabel,omitempty" form:"leftColumnDistributor-doctorLabel"`
	ClinicID                           *string `json:"clinic_id,omitempty" form:"clinic_id"`
	Manufacturer                       *string `json:"manufacturer,omitempty" form:"manufacturer"`
	EpisodeLengthFrom                  *int16  `json:"episodeLengthFrom,omitempty" form:"episodeLengthFrom"`
	EpisodeLengthTo                    *int16  `json:"episodeLengthTo,omitempty" form:"episodeLengthTo"`
	LabelledOnly                       *bool   `json:"labelledOnly,omitempty" form:"labelledOnly"`

	// MinIO path selection (replaces data_layer)
	Catalog string `json:"catalog,omitempty" form:"catalog"`
}

type ECGClinicRequest struct {
	Catalog string `json:"catalog,omitempty" form:"catalog"`
}

type ECGPatientRequest struct {
	ClinicID string `json:"clinic_id,omitempty" form:"clinic_id"`
	Catalog  string `json:"catalog,omitempty" form:"catalog"`
}

type ECGManufacturerRequest struct {
	Catalog string `json:"catalog,omitempty" form:"catalog"`
}

type ECGDeviceModelRequest struct {
	Manufacturer string `json:"manufacturer,omitempty" form:"manufacturer"`
	Catalog      string `json:"catalog,omitempty" form:"catalog"`
}

// Validate validates the ECG query request
func (r *ECGQueryRequest) Validate() error {
	// Set defaults
	if r.Limit <= 0 {
		r.Limit = DefaultLimit
	}
	if r.Limit > MaxLimit {
		return fmt.Errorf("limit cannot exceed %d", MaxLimit)
	}

	if r.Offset < 0 {
		r.Offset = 0
	}

	if r.OrderBy == "" {
		r.OrderBy = DefaultOrderBy
	}

	if r.OrderDir == "" {
		r.OrderDir = DefaultOrderDir
	}

	// Validate order by field
	valid := false
	for _, field := range ValidOrderByFields {
		if r.OrderBy == field {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("invalid order_by field: %s", r.OrderBy)
	}

	// Validate order direction
	if r.OrderDir != OrderDirectionAsc && r.OrderDir != OrderDirectionDesc {
		return fmt.Errorf("order_dir must be '%s' or '%s'", OrderDirectionAsc, OrderDirectionDesc)
	}

	return nil
}

func (r *ECGDashboardRequest) Validate() error {
	// Validate that if any of the dashboard hierarchical or left column distributor fields are present, their value is 1
	if r.HierarichicalClinic != nil && *r.HierarichicalClinic != 1 {
		return fmt.Errorf("hierarichical-clinic must be 1 if present")
	}
	if r.HierarichicalManufacturer != nil && *r.HierarichicalManufacturer != 1 {
		return fmt.Errorf("hierarichical-manufacturer must be 1 if present")
	}
	if r.HierarichicalDeviceModel != nil && *r.HierarichicalDeviceModel != 1 {
		return fmt.Errorf("hierarichical-deviceModel must be 1 if present")
	}
	if r.HierarichicalDeviceLabel != nil && *r.HierarichicalDeviceLabel != 1 {
		return fmt.Errorf("hierarichical-deviceLabel must be 1 if present")
	}
	if r.HierarichicalDoctorLabel != nil && *r.HierarichicalDoctorLabel != 1 {
		return fmt.Errorf("hierarichical-doctorLabel must be 1 if present")
	}
	if r.LeftColumnDistributorEpisodeLength != nil && *r.LeftColumnDistributorEpisodeLength != 1 {
		return fmt.Errorf("leftColumnDistributor-episodeLength must be 1 if present")
	}
	if r.LeftColumnDistributorDeviceLabel != nil && *r.LeftColumnDistributorDeviceLabel != 1 {
		return fmt.Errorf("leftColumnDistributor-deviceLabel must be 1 if present")
	}
	if r.LeftColumnDistributorDoctorLabel != nil && *r.LeftColumnDistributorDoctorLabel != 1 {
		return fmt.Errorf("leftColumnDistributor-doctorLabel must be 1 if present")
	}

	allowedEpisodeLengths := map[int16]bool{0: true, 1: true, 3: true, 5: true}
	if r.EpisodeLengthFrom != nil {
		if !allowedEpisodeLengths[*r.EpisodeLengthFrom] {
			return fmt.Errorf("episodeLengthFrom must be one of 0, 1, 3, or 5 if present")
		}
	}
	if r.EpisodeLengthTo != nil {
		if !allowedEpisodeLengths[*r.EpisodeLengthTo] {
			return fmt.Errorf("episodeLengthTo must be one of 0, 1, 3, or 5 if present")
		}
	}

	return nil
}

// ECGQueryResponse represents the response for ECG data queries
type ECGQueryResponse struct {
	Episodes   []ECGEpisodeSummary `json:"episodes"`
	TotalCount int64               `json:"total_count"`
	Pagination PaginationInfo      `json:"pagination"`
	QueryTime  time.Duration       `json:"query_time_ms"`
	Version    string              `json:"version"`
	Timestamp  time.Time           `json:"timestamp"`
}

type ECGNumConflictingEpisodesResponse struct {
	NumConflictingEpisodes int64         `json:"num_conflicting_episodes"`
	QueryTime              time.Duration `json:"query_time_ms"`
}

type ECGDashboardResponse struct {
	Data        [][]string    `json:"data"`
	ColumnOrder []string      `json:"column_order"`
	QueryTime   time.Duration `json:"query_time_ms"`
	Version     string        `json:"version"`
	Timestamp   time.Time     `json:"timestamp"`
}

// ECGEpisodeSummary represents a summary of an ECG episode with its strips
type ECGEpisodeSummary struct {
	EpisodeID         string                      `json:"episode_id"`
	PatientID         string                      `json:"patient_id"`
	PatientName       string                      `json:"patient_name"`
	ClinicID          string                      `json:"clinic_id"`
	ClinicName        string                      `json:"clinic_name"`
	Manufacturer      string                      `json:"manufacturer"`
	DeviceLabel       string                      `json:"device_label"`
	DeviceModel       string                      `json:"device_model"`
	ReportTime        string                      `json:"report_time"`
	EpisodeLength     string                      `json:"episode_length"`
	Assessment        string                      `json:"assessment"`
	Labels            map[string][]ProcessedLabel `json:"labels,omitempty"`
	ApprovedLabels    []ProcessedLabel            `json:"approved_labels,omitempty"`
	DisapprovedLabels []ProcessedLabel            `json:"disapproved_labels,omitempty"`
	Strips            []ECGStripData              `json:"strips"`
}

// ECGStripData represents ECG strip data
type ECGStripData struct {
	StripID           string                      `json:"id"`
	Timestamps        []float32                   `json:"timestamps,omitempty"`
	Voltages          []float32                   `json:"voltages,omitempty"`
	Labels            map[string][]ProcessedLabel `json:"labels,omitempty"`
	ApprovedLabels    []ProcessedLabel            `json:"approved_labels,omitempty"`
	DisapprovedLabels []ProcessedLabel            `json:"disapproved_labels,omitempty"`
	Triage            *string                     `json:"triage,omitempty"`
}

// ECGAnalyticsRequest represents a request for ECG analytics
type ECGAnalyticsRequest struct {
	// Time range
	DateFrom *time.Time `json:"date_from,omitempty"`
	DateTo   *time.Time `json:"date_to,omitempty"`

	// Grouping
	GroupBy []string `json:"group_by,omitempty"` // clinic, manufacturer, device_model, labelling_status

	// Filters
	ClinicIDs       []string `json:"clinic_ids,omitempty"`
	Manufacturers   []string `json:"manufacturers,omitempty"`
	LabellingStatus []string `json:"labelling_status,omitempty"`
}

// ECGAnalyticsResponse represents analytics data for ECG episodes
type ECGAnalyticsResponse struct {
	TotalEpisodes     int64                  `json:"total_episodes"`
	TotalStrips       int64                  `json:"total_strips"`
	ByClinic          []ECGClinicStats       `json:"by_clinic,omitempty"`
	ByManufacturer    []ECGManufacturerStats `json:"by_manufacturer,omitempty"`
	ByLabellingStatus []ECGLabellingStats    `json:"by_labelling_status,omitempty"`
	TimeRange         ECGTimeRangeStats      `json:"time_range"`
	Timestamp         time.Time              `json:"timestamp"`
}

// ECGClinicStats represents statistics by clinic
type ECGClinicStats struct {
	ClinicID        string `json:"clinic_id"`
	ClinicName      string `json:"clinic_name"`
	EpisodeCount    int64  `json:"episode_count"`
	StripCount      int64  `json:"strip_count"`
	LabelledCount   int64  `json:"labelled_count"`
	UnlabelledCount int64  `json:"unlabelled_count"`
}

// ECGManufacturerStats represents statistics by manufacturer
type ECGManufacturerStats struct {
	Manufacturer string   `json:"manufacturer"`
	EpisodeCount int64    `json:"episode_count"`
	StripCount   int64    `json:"strip_count"`
	DeviceModels []string `json:"device_models"`
}

// ECGLabellingStats represents statistics by labelling status
type ECGLabellingStats struct {
	Status       string  `json:"status"`
	EpisodeCount int64   `json:"episode_count"`
	StripCount   int64   `json:"strip_count"`
	Percentage   float64 `json:"percentage"`
}

// ECGTimeRangeStats represents time-based statistics
type ECGTimeRangeStats struct {
	EarliestEpisode time.Time `json:"earliest_episode"`
	LatestEpisode   time.Time `json:"latest_episode"`
	DaysCovered     int       `json:"days_covered"`
	AvgPerDay       float64   `json:"avg_episodes_per_day"`
}

// BuildTrinoQueryWithLabellers builds a Trino SQL query with access to labellers service
func (r *ECGQueryRequest) BuildTrinoQueryWithLabellers(labellersService LabellersServiceInterface) string {
	// Debug prints removed to use structured logging upstream

	catalog := r.Catalog

	// Base query
	selectFields := []string{
		"e.episodeid", "e.id as strip_id", "e.patientid", "e.patientname", "e.clinicid", "e.clinicname",
		"e.manufacturer", "e.devicelabel", "e.devicemodel", "e.reporttime",
		"e.episodelength", "e.ecgstrip_timestamps", "e.ecgstrip_voltages",
	}

	// Build WHERE conditions first to use in both the main query and count
	var conditions []string

	// Episode filters
	if len(r.PatientIDs) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.patientid IN (%s)", joinStringValues(r.PatientIDs)))
	}
	if len(r.ClinicIDs) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.clinicid IN (%s)", joinStringValues(r.ClinicIDs)))
	}
	if len(r.Manufacturers) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.manufacturer IN (%s)", joinStringValues(r.Manufacturers)))
	}
	if len(r.DeviceModels) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.devicemodel IN (%s)", joinStringValues(r.DeviceModels)))
	}

	if len(r.LabellingStatus) > 0 && labellersService != nil {
		userIds := append([]string(nil), r.AnnotatorIDs...)
		if len(userIds) == 0 {
			userIds = []string{r.CurrentUser}
		}

		var labels []string
		var conditionFormat string

		switch {
		case containsString(r.LabellingStatus, LabellingStatusUnlabeledEpisodes):
			labels = []string{LabelIsDoneFlag}
			conditionFormat = "e.episodeid NOT IN (%s)"
		case containsString(r.LabellingStatus, LabellingStatusLabeledEpisodes):
			labels = []string{LabelIsDoneFlag}
			conditionFormat = "e.episodeid IN (%s)"
		case containsString(r.LabellingStatus, LabellingStatusUnsureEpisodes):
			labels = []string{LabelUnsure}
			conditionFormat = "e.episodeid IN (%s)"
		default:
		}

		if doneEpisodeIds, err := labellersService.GetLabelledEpisodeIds(context.Background(), catalog, labels, userIds, r.MaxLabellingTime); err == nil && len(doneEpisodeIds) > 0 {
			conditions = append(conditions, fmt.Sprintf(conditionFormat, joinStringValues(doneEpisodeIds)))
		} else {
			falseEpisodeIds := []string{"0000-0000-0000-000000000000"}
			conditions = append(conditions, fmt.Sprintf(conditionFormat, joinStringValues(falseEpisodeIds)))
		}
	}

	if len(r.LabelledBy) > 0 {
		episodeIds, err := labellersService.GetEpisodeIdsAnnotatedByUserIDs(context.Background(), r.LabelledBy, catalog)
		if err != nil {
			return ""
		}
		conditions = append(conditions, fmt.Sprintf("e.episodeid IN (%s)", joinStringValues(episodeIds)))
	}

	if len(r.NotLabelledBy) > 0 {
		episodeIds, err := labellersService.GetEpisodeIdsAnnotatedByUserIDs(context.Background(), r.NotLabelledBy, catalog)
		if err != nil {
			return ""
		}
		conditions = append(conditions, fmt.Sprintf("e.episodeid NOT IN (%s)", joinStringValues(episodeIds)))
	}

	// Time range filters
	if r.ReportTimeFrom != nil {
		conditions = append(conditions, fmt.Sprintf("e.reporttime >= TIMESTAMP '%s'", r.ReportTimeFrom.Format("2006-01-02 15:04:05")))
	}
	if r.ReportTimeTo != nil {
		conditions = append(conditions, fmt.Sprintf("e.reporttime <= TIMESTAMP '%s'", r.ReportTimeTo.Format("2006-01-02 15:04:05")))
	}

	// Strip filters
	if len(r.StripNumbers) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.stripnumber IN (%s)", joinIntValues(r.StripNumbers)))
	}

	// Build the WHERE clause
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + joinConditions(conditions)
	}

	// Build the ORDER BY and pagination clauses for the new query structure
	orderByClause := fmt.Sprintf("ORDER BY e.patientid ASC, e.%s %s", strings.ToLower(r.OrderBy), strings.ToUpper(r.OrderDir))

	// Now using offset and fetch first, instead of rankedWhereClause
	paginationClause := ""
	if r.Limit > 0 {
		paginationClause = fmt.Sprintf("OFFSET %d ROWS FETCH FIRST %d ROWS ONLY", r.Offset, r.Limit)
	}

	cteQuery := "WITH "

	if len(r.EpisodeIDs) > 0 {
		specificWhereClause := whereClause

		if len(conditions) > 0 {
			specificWhereClause += " AND e.patientid IN (SELECT patientid FROM patient)"
		} else {
			specificWhereClause = " WHERE e.patientid IN (SELECT patientid FROM patient)"
		}

		specificOrderByClause := fmt.Sprintf("ORDER BY if(e.episodeid IN (%s), 0, 1), e.patientid ASC, e.%s %s", joinStringValues(r.EpisodeIDs), strings.ToLower(r.OrderBy), strings.ToUpper(r.OrderDir))

		cteQuery += fmt.Sprintf(`
			patient AS (
				SELECT patientid
				FROM %s AS e
				WHERE e.episodeid IN (%s)
				LIMIT 1
			),
			episodes AS (
				SELECT DISTINCT e.episodeid
				FROM %s AS e, patient AS p
				%s
				%s
				%s
			)`,
			catalog,
			joinStringValues(r.EpisodeIDs),
			catalog,
			specificWhereClause,
			specificOrderByClause,
			paginationClause,
		)
	} else {
		cteQuery += fmt.Sprintf(`
			episodes AS (
				SELECT DISTINCT e.episodeid
				FROM %s AS e
				%s
				%s
				%s
			)`,
			catalog,
			whereClause,
			orderByClause,
			paginationClause,
		)
	}

	totalEpisodesQuery := ""
	if len(r.EpisodeIDs) > 0 {
		specificWhereClause := whereClause

		if len(conditions) > 0 {
			specificWhereClause += " AND e.patientid IN (SELECT patientid FROM patient)"
		} else {
			specificWhereClause = " WHERE e.patientid IN (SELECT patientid FROM patient)"
		}

		totalEpisodesQuery += fmt.Sprintf(`
		total_episodes AS (
    		SELECT COUNT(DISTINCT episodeid) AS total_count 
			FROM %s AS e
			%s
		)`,
			catalog,
			specificWhereClause,
		)

	} else {
		totalEpisodesQuery += fmt.Sprintf(`
		total_episodes AS (
    		SELECT COUNT(DISTINCT episodeid) AS total_count 
			FROM %s AS e
			%s
		)`,
			catalog,
			whereClause,
		)
	}

	selectQuery := fmt.Sprintf(`
		SELECT %s, (SELECT total_count FROM total_episodes) AS total_count_all_rows 
		FROM %s AS e
		WHERE e.episodeid IN (SELECT episodeid FROM episodes)
		%s
		`,
		strings.Join(selectFields, ", "),
		catalog,
		orderByClause,
	)

	query := cteQuery + ",\n" + totalEpisodesQuery + "\n" + selectQuery

	return query
}

// BuildTrinoQueryForConflictingEpisodes builds a Trino SQL query for conflicting episodes
func (r *ECGQueryRequest) BuildTrinoQueryForConflictingEpisodes(labellersService LabellersServiceInterface) string {
	catalog := r.Catalog

	// Extract conflicting episodes from ClickHouse
	conflictingEpisodeIds, err := labellersService.GetConflictingEpisodeIds(context.Background(), catalog)
	if err != nil {
		return ""
	}

	if len(conflictingEpisodeIds) == 0 {
		return ""
	}

	// Base query
	selectFields := []string{
		"e.episodeid", "e.id as strip_id", "e.patientid", "e.patientname", "e.clinicid", "e.clinicname",
		"e.manufacturer", "e.devicelabel", "e.devicemodel", "e.reporttime",
		"e.episodelength", "e.ecgstrip_timestamps", "e.ecgstrip_voltages",
	}

	// Build WHERE conditions first to use in both the main query and count
	var conditions []string

	// Filter to only conflicting episodes
	if len(conflictingEpisodeIds) > 0 {

		conditions = append(conditions, fmt.Sprintf("e.episodeid IN (%s)", joinStringValues(conflictingEpisodeIds)))
	} else {
		// If no conflicting episodes found, return empty result
		return SQLEmptyResult
	}

	// Episode filters
	if len(r.PatientIDs) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.patientid IN (%s)", joinStringValues(r.PatientIDs)))
	}
	if len(r.ClinicIDs) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.clinicid IN (%s)", joinStringValues(r.ClinicIDs)))
	}
	if len(r.Manufacturers) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.manufacturer IN (%s)", joinStringValues(r.Manufacturers)))
	}
	if len(r.DeviceModels) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.devicemodel IN (%s)", joinStringValues(r.DeviceModels)))
	}
	if len(r.LabelledBy) > 0 {
		episodeIds, err := labellersService.GetEpisodeIdsAnnotatedByUserIDs(context.Background(), r.LabelledBy, catalog)
		if err != nil {
			return ""
		}
		conditions = append(conditions, fmt.Sprintf("e.episodeid IN (%s)", joinStringValues(episodeIds)))
	}
	if len(r.NotLabelledBy) > 0 {
		episodeIds, err := labellersService.GetEpisodeIdsAnnotatedByUserIDs(context.Background(), r.NotLabelledBy, catalog)
		if err != nil {
			return ""
		}
		conditions = append(conditions, fmt.Sprintf("e.episodeid NOT IN (%s)", joinStringValues(episodeIds)))
	}
	if len(r.ConflictStatus) > 0 {
		if containsString(r.ConflictStatus, ConflictStatusConflicting) {
			conflictResolvedEpisodeIds, err := labellersService.GetConflictResolvedEpisodeIds(context.Background(), catalog)
			if err != nil {
				return ""
			}

			conditions = append(conditions, fmt.Sprintf("e.episodeid NOT IN (%s)", joinStringValues(conflictResolvedEpisodeIds)))
		} else if containsString(r.ConflictStatus, ConflictStatusResolved) {
			conflictResolvedEpisodeIds, err := labellersService.GetConflictResolvedEpisodeIds(context.Background(), catalog)
			if err != nil {
				return ""
			}

			conditions = append(conditions, fmt.Sprintf("e.episodeid IN (%s)", joinStringValues(conflictResolvedEpisodeIds)))
		}
	}

	// Time range filters
	if r.ReportTimeFrom != nil {
		conditions = append(conditions, fmt.Sprintf("e.reporttime >= TIMESTAMP '%s'", r.ReportTimeFrom.Format("2006-01-02 15:04:05")))
	}
	if r.ReportTimeTo != nil {
		conditions = append(conditions, fmt.Sprintf("e.reporttime <= TIMESTAMP '%s'", r.ReportTimeTo.Format("2006-01-02 15:04:05")))
	}

	// Strip filters
	if len(r.StripNumbers) > 0 {
		conditions = append(conditions, fmt.Sprintf("e.stripnumber IN (%s)", joinIntValues(r.StripNumbers)))
	}

	// Episode ID filter
	if len(r.EpisodeIDs) > 0 {
		conditions = append(conditions, "e.patientid IN (SELECT patientid FROM patient)")
	}

	// Build the WHERE clause
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + joinConditions(conditions)
	}

	rankedWhereClause := ""
	if r.Limit > 0 {
		rankedWhereClause = fmt.Sprintf("WHERE rn > %d AND rn <= %d", r.Offset, r.Offset+r.Limit)
	}

	orderByClause := ""
	// Add ORDER BY
	orderByClause += fmt.Sprintf("ORDER BY e.patientid ASC, e.%s %s", strings.ToLower(r.OrderBy), r.OrderDir)

	query := "WITH "

	if len(r.EpisodeIDs) > 0 {
		query += fmt.Sprintf(`
			patient AS (
				SELECT patientid
				FROM %s AS e
				WHERE e.episodeid IN (%s)
				LIMIT 1
			),`,
			catalog,
			joinStringValues(r.EpisodeIDs),
		)
	}

	// Build the query with total count included as a window function
	// This allows us to get both the paginated results and the total count in a single query
	query += fmt.Sprintf(`
		ranked_episodes AS (
			SELECT 
				episodeid,
				patientid,
				MIN(%s) AS min_report_time,
				ROW_NUMBER() OVER (ORDER BY patientid ASC, MIN(%s) %s) AS rn
			FROM %s e
			%s
			GROUP BY episodeid, patientid
			ORDER BY patientid ASC, min_report_time %s
		),  
		filtered_episodes AS (
			SELECT episodeid
			FROM ranked_episodes
			%s
		)
		SELECT %s, totals.total_groups as total_count_all_rows
		FROM %s e
		INNER JOIN filtered_episodes fe
			ON e.episodeid = fe.episodeid
		CROSS JOIN (
			SELECT COUNT(DISTINCT episodeid) AS total_groups
			FROM %s e
			%s
		) totals 
		%s %s`,
		strings.ToLower(r.OrderBy), strings.ToLower(r.OrderBy), r.OrderDir, catalog, whereClause, r.OrderDir, rankedWhereClause, joinFields(selectFields), catalog, catalog, whereClause, whereClause, orderByClause)

	return query
}

func (r *ECGDashboardRequest) BuildTrinoQueryForDashboard(queryParams [][2]string, labellersService LabellersServiceInterface) (string, int, []string) {
	hasDoctorLabel := false
	for _, param := range queryParams {
		if param[0] == "leftColumnDistributor-doctorLabel" || param[0] == "hierarichical-doctorLabel" {
			hasDoctorLabel = true
			break
		}
	}

	catalog := r.Catalog

	conditions := []string{}
	if r.ClinicID != nil {
		conditions = append(conditions, fmt.Sprintf("clinicid = '%s'", *r.ClinicID))
	}
	if r.Manufacturer != nil {
		conditions = append(conditions, fmt.Sprintf("manufacturer = '%s'", *r.Manufacturer))
	}
	if r.EpisodeLengthFrom != nil {
		conditions = append(conditions, fmt.Sprintf("episodelengthminutes >= %d", *r.EpisodeLengthFrom))
	}
	if r.EpisodeLengthTo != nil {
		conditions = append(conditions, fmt.Sprintf("episodelengthminutes <= %d", *r.EpisodeLengthTo))
	}
	if r.LabelledOnly != nil && *r.LabelledOnly {
		stripAnnotatorArray := make([]string, 0)
		result, err := labellersService.GetAllStripAnnotators(context.Background(), catalog)
		if err != nil {
			return "", 0, []string{}
		}
		for _, entry := range result {
			stripID := entry.StripID
			if stripID == "" {
				continue
			}
			stripAnnotatorArray = append(stripAnnotatorArray, stripID)
		}

		conditions = append(conditions, fmt.Sprintf("strip_id IN (%s)", joinStringValues(stripAnnotatorArray)))
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + joinConditions(conditions)
	}

	selectClauseCte := ""
	numberOfParams := 0
	columnOrder := []string{}
	// Generate the select clause for the CTE based on the queryParams
	for _, param := range queryParams {
		if _, ok := myMap[param[0]]; !ok {
			continue
		}

		numberOfParams++

		if param[0] == "leftColumnDistributor-episodeLength" {
			selectClauseCte += fmt.Sprintf(`
				CASE
					WHEN (episodelengthminutes >= 0 AND episodelengthminutes < 1) THEN '%s'
					WHEN (episodelengthminutes >= 1 AND episodelengthminutes < 3) THEN '%s'
					WHEN (episodelengthminutes >= 3 AND episodelengthminutes < 5) THEN '%s'
					WHEN (episodelengthminutes >= 5 AND episodelengthminutes < 10) THEN '%s'
					WHEN (episodelengthminutes >= 10 AND episodelengthminutes < 30) THEN '%s'
					WHEN (episodelengthminutes >= 30 AND episodelengthminutes < 60) THEN '%s'
					WHEN (episodelengthminutes >= 60 AND episodelengthminutes < 120) THEN '%s'
					WHEN (episodelengthminutes >= 120 AND episodelengthminutes < 360) THEN '%s'
					WHEN (episodelengthminutes >= 360 AND episodelengthminutes < 720) THEN '%s'
					WHEN (episodelengthminutes >= 720 AND episodelengthminutes < 1440) THEN '%s'
					WHEN (episodelengthminutes >= 1440) THEN '%s'
					WHEN episodelengthminutes IS NULL THEN '%s'
				ELSE '%s'
				END AS episodelength,
			`, EpisodeLengthLessThan1Min, EpisodeLength1To3Min, EpisodeLength3To5Min, EpisodeLength5To10Min,
				EpisodeLength10To30Min, EpisodeLength30To60Min, EpisodeLength1To2Hrs, EpisodeLength2To6Hrs,
				EpisodeLength6To12Hrs, EpisodeLength12To24Hrs, EpisodeLength24PlusHrs, DefaultUnknownLength, EpisodeLengthOther)
		} else {
			selectClauseCte += fmt.Sprintf("COALESCE(FilteredData.%s, %s) as %s,", myMap[param[0]][0], myMap[param[0]][1], myMap[param[0]][2])
		}

		columnOrder = append(columnOrder, myMap[param[0]][2])
	}

	columnOrder = append(columnOrder, ColumnPatientCount, ColumnEpisodeCount, ColumnECGStripsCount)

	groupByClause := ""
	// Generate the group by clause for the CTE based on the queryParams
	if numberOfParams > 0 {
		// Generate an array from 1 to numberOfParams, e.g., if numberOfParams=4, arr=[1,2,3,4]
		groupByIndices := make([]string, numberOfParams)
		for i := 0; i < numberOfParams; i++ {
			groupByIndices[i] = fmt.Sprintf("%d", i+1)
		}
		groupByClause = fmt.Sprintf("GROUP BY %s", strings.Join(groupByIndices, ", "))
	}

	orderByClause := ""
	if r.LeftColumnDistributorEpisodeLength != nil {
		orderByClause = fmt.Sprintf(`
			ORDER BY 
				CASE episodelength
					WHEN '%s' THEN 1
					WHEN '%s' THEN 2
					WHEN '%s' THEN 3
					WHEN '%s' THEN 4
					WHEN '%s' THEN 5
					WHEN '%s' THEN 6
					WHEN '%s' THEN 7
					WHEN '%s' THEN 8
					WHEN '%s' THEN 9
					WHEN '%s' THEN 10
					WHEN '%s' THEN 11
					WHEN '%s' THEN 12
					ELSE 13
				END
		`, EpisodeLengthLessThan1Min, EpisodeLength1To3Min, EpisodeLength3To5Min, EpisodeLength5To10Min,
			EpisodeLength10To30Min, EpisodeLength30To60Min, EpisodeLength1To2Hrs, EpisodeLength2To6Hrs,
			EpisodeLength6To12Hrs, EpisodeLength12To24Hrs, EpisodeLength24PlusHrs, DefaultUnknownLength)
	}

	labellersCastCTE := "WITH "
	labellersJoinCTE := fmt.Sprintf(`FROM %s`, catalog)
	isLabelledCTE := ""
	if hasDoctorLabel {
		stripAnnotatorArray := make([][]string, 0)
		result, err := labellersService.GetAllStripAnnotators(context.Background(), catalog)
		if err != nil {
			return "", 0, []string{}
		}
		for _, entry := range result {
			stripID := entry.StripID
			submittedBy := entry.SubmittedBy
			if stripID == "" {
				continue
			}
			stripAnnotatorArray = append(stripAnnotatorArray, []string{stripID, submittedBy})
		}

		stripAnnotatorArrayJSON, err := json.Marshal(stripAnnotatorArray)
		if err != nil {
			// Swallow error and fall back to null; logging handled upstream
			stripAnnotatorArrayJSON = []byte("null")
		}

		labellersCastCTE = fmt.Sprintf(`
		WITH external_json AS (
			SELECT 
				JSONExtract(json_data, 'Array(Array(String))')[idx][1] as annotation_strip_id,
				JSONExtract(json_data, 'Array(Array(String))')[idx][2] as annotation_submitted_by
			FROM (
				SELECT '%s' as json_data
			)
			ARRAY JOIN arrayEnumerate(JSONExtract(json_data, 'Array(Array(String))')) AS idx
		),
		`, string(stripAnnotatorArrayJSON))

		labellersJoinCTE = fmt.Sprintf(`
			FROM external_json as e
			RIGHT JOIN %s AS s
				ON s.id = e.annotation_strip_id
		`, catalog)

		isLabelledCTE = fmt.Sprintf(`
			annotation_submitted_by,
			CASE
				WHEN annotation_submitted_by IS NOT NULL AND annotation_submitted_by != '' THEN '%s'
				ELSE '%s'
			END as is_labelled,
		`, LabellingStatusYes, LabellingStatusNo)
	}

	trinoQuery := fmt.Sprintf(` 
			%s
			BaseData AS (
                SELECT
                    episodeid,
					id as strip_id,
                    patientid,
                    clinicid,
                    COALESCE(clinicname, '%s') as clinicname,
                    COALESCE(manufacturer, '%s') as manufacturer,
                    devicemodel,
                    devicelabel,
                    reporttime,
					%s
					CASE 
						WHEN ecgstrip_timestamps IS NULL OR length(ecgstrip_timestamps) <= 1 THEN NULL
						ELSE (ecgstrip_timestamps[length(ecgstrip_timestamps)] - ecgstrip_timestamps[1]) / 60.0
					END AS episodelengthminutes
                %s
            ),
            FilteredData AS (
                SELECT *
                FROM BaseData
				%s
            )
                SELECT
					%s
                    COALESCE(uniqExact(patientid), 0) AS %s,
                    COALESCE(uniqExact(episodeid), 0) AS %s,
                    COALESCE(COUNT(*), 0) AS %s
                FROM FilteredData
				%s
				%s
	`, labellersCastCTE, DefaultUnknownClinic, DefaultUnknownManufacturer, isLabelledCTE, labellersJoinCTE, whereClause, selectClauseCte, ColumnPatientCount, ColumnEpisodeCount, ColumnECGStripsCount, groupByClause, orderByClause)

	// Debug prints removed to use structured logging upstream

	return trinoQuery, numberOfParams, columnOrder
}

// Helper functions for query building
func joinFields(fields []string) string {
	result := ""
	for i, field := range fields {
		if i > 0 {
			result += ", "
		}
		result += field
	}
	return result
}

func joinStringValues(values []string) string {
	result := ""
	for i, value := range values {
		if i > 0 {
			result += ", "
		}
		result += fmt.Sprintf("'%s'", value)
	}
	return result
}

func joinIntValues(values []int) string {
	result := ""
	for i, value := range values {
		if i > 0 {
			result += ", "
		}
		result += fmt.Sprintf("%d", value)
	}
	return result
}

func joinConditions(conditions []string) string {
	result := ""
	for i, condition := range conditions {
		if i > 0 {
			result += " AND "
		}
		result += condition
	}
	return result
}

func containsString(slice []string, target string) bool {
	for _, item := range slice {
		if item == target {
			return true
		}
	}
	return false
}

// create a map[string]int as an example
var myMap = map[string][3]string{
	"hierarichical-clinic":                {"clinicname", fmt.Sprintf("'%s'", DefaultUnknownClinic), "clinicname"},
	"hierarichical-manufacturer":          {"manufacturer", fmt.Sprintf("'%s'", DefaultUnknownManufacturer), "manufacturer"},
	"hierarichical-deviceModel":           {"devicemodel", fmt.Sprintf("'%s'", DefaultUnknownDeviceModel), "devicemodel"},
	"hierarichical-deviceLabel":           {"devicelabel", "''", "devicelabel"},
	"hierarichical-doctorLabel":           {"annotation_submitted_by", "''", "\"hierarichical-doctorLabel\""},
	"leftColumnDistributor-episodeLength": {"episodelength", fmt.Sprintf("'%s'", DefaultUnknownEpisodeLength), "\"leftColumnDistributor-episodeLength\""},
	"leftColumnDistributor-deviceLabel":   {"devicelabel", "''", "\"leftColumnDistributor-deviceLabel\""},
	"leftColumnDistributor-doctorLabel":   {"annotation_submitted_by", "''", "\"leftColumnDistributor-doctorLabel\""},
}
