package domain

import (
	"time"

	"github.com/google/uuid"
)

// Episode represents an ECG episode in the annotation tool domain
type ClickhouseStripRow struct {
	StripID       string    `json:"id"`
	EpisodeID     string    `json:"episode_id"`
	PatientID     string    `json:"patient_id"`
	PatientName   string    `json:"patient_name"`
	ClinicID      string    `json:"clinic_id"`
	ClinicName    string    `json:"clinic_name"`
	Manufacturer  string    `json:"manufacturer"`
	DeviceLabel   string    `json:"device_label"`
	DeviceModel   string    `json:"device_model"`
	ReportTime    time.Time `json:"report_time"`
	EpisodeLength string    `json:"episode_length"`
	Assessment    string    `json:"assessment"`
	Timestamps    []float32 `json:"timestamps"`
	Voltages      []float32 `json:"voltages"`
}

// ExtendedClickhouseStripRow extends ClickhouseStripRow with additional fields if needed
type ClickhouseDashboardStripRow struct {
	ClickhouseStripRow
	PatientCount                       int64    `json:"patientcount"`
	EpisodeCount                       int64    `json:"episodecount"`
	ECGStripsCount                     int64    `json:"ecgstripscount"`
	EpisodeLengthMinutes               *float64 `json:"episodelengthminutes"`
	IsLabelled                         *bool    `json:"is_labelled"`
	HierarichicalDoctorLabel           string   `json:"hierarichical-doctorLabel"`
	LeftColumnDistributorEpisodeLength string   `json:"leftColumnDistributor-episodeLength"`
	LeftColumnDistributorDeviceLabel   string   `json:"leftColumnDistributor-deviceLabel"`
	LeftColumnDistributorDoctorLabel   string   `json:"leftColumnDistributor-doctorLabel"`
}

// Episode represents an ECG episode in the annotation tool domain
type Episode struct {
	ID              string    `json:"id"`
	EpisodeID       string    `json:"episode_id"`
	PatientID       string    `json:"patient_id"`
	PatientName     string    `json:"patient_name"`
	ClinicID        string    `json:"clinic_id"`
	ClinicName      string    `json:"clinic_name"`
	Manufacturer    string    `json:"manufacturer"`
	DeviceLabel     string    `json:"device_label"`
	DeviceModel     string    `json:"device_model"`
	ReportTime      time.Time `json:"report_time"`
	EpisodeLength   string    `json:"episode_length"`
	Assessment      string    `json:"assessment"`
	LabellingStatus string    `json:"labelling_status"`
	IsInteresting   bool      `json:"is_interesting"`
	InterestingDesc string    `json:"interesting_description"`
	ECGStripIDs     []string  `json:"ecg_strip_ids"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// NewEpisode creates a new Episode instance
func NewEpisode(episodeID, patientID, patientName, clinicID, clinicName, manufacturer, deviceLabel, deviceModel string, reportTime time.Time, episodeLength, assessment string) *Episode {
	now := time.Now()
	return &Episode{
		ID:              uuid.New().String(),
		EpisodeID:       episodeID,
		PatientID:       patientID,
		PatientName:     patientName,
		ClinicID:        clinicID,
		ClinicName:      clinicName,
		Manufacturer:    manufacturer,
		DeviceLabel:     deviceLabel,
		DeviceModel:     deviceModel,
		ReportTime:      reportTime,
		EpisodeLength:   episodeLength,
		Assessment:      assessment,
		LabellingStatus: "unlabelled",
		IsInteresting:   false,
		InterestingDesc: "",
		ECGStripIDs:     []string{},
		CreatedAt:       now,
		UpdatedAt:       now,
	}
}

// UpdateStatus updates the episode's labelling status
func (e *Episode) UpdateStatus(status, notes string) {
	e.LabellingStatus = status
	e.UpdatedAt = time.Now()
}

// SetInteresting marks the episode as interesting with description
func (e *Episode) SetInteresting(isInteresting bool, description string) {
	e.IsInteresting = isInteresting
	e.InterestingDesc = description
	e.UpdatedAt = time.Now()
}

// AddECGStrip adds an ECG strip ID to the episode
func (e *Episode) AddECGStrip(stripID string) {
	e.ECGStripIDs = append(e.ECGStripIDs, stripID)
	e.UpdatedAt = time.Now()
}

// EpisodeQueryResult represents the result of an episode query with pagination support
type EpisodeQueryResult struct {
	Episodes  []ClickhouseStripRow `json:"episodes"`
	TotalRows int64                `json:"total_rows"`
}

// DashboardDataQueryResult represents the result of a dashboard data query with pagination support
type DashboardDataQueryResult struct {
	Data      []ClickhouseDashboardStripRow `json:"data"`
	TotalRows int64                         `json:"total_rows"`
}
