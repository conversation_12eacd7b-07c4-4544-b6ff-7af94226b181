package domain

type GPUs struct {
	Count   int    `json:"count"`
	Message string `json:"message"`
}

type ManagedField struct {
	APIVersion  string  `json:"apiVersion"`
	FieldsType  string  `json:"fieldsType"`
	FieldsV1    any     `json:"fieldsV1"`
	Manager     string  `json:"manager"`
	Operation   string  `json:"operation"`
	Time        string  `json:"time"`
	Subresource *string `json:"subresource,omitempty"`
}

type Annotations struct {
	Creator        string            `json:"notebooks.kubeflow.org/creator"`
	HTTPRewriteURI string            `json:"notebooks.kubeflow.org/http-rewrite-uri"`
	ServerType     string            `json:"notebooks.kubeflow.org/server-type"`
	Additional     map[string]string `json:"-"`
}

type Labels struct {
	App        string            `json:"app"`
	Additional map[string]string `json:"-"`
}

type Metadata struct {
	Annotations       Annotations    `json:"annotations"`
	CreationTimestamp string         `json:"creationTimestamp"`
	Generation        int            `json:"generation"`
	Labels            Labels         `json:"labels"`
	ManagedFields     []ManagedField `json:"managedFields"`
	Name              string         `json:"name"`
	Namespace         string         `json:"namespace"`
	ResourceVersion   string         `json:"resourceVersion"`
	UID               string         `json:"uid"`
}

type Status struct {
	Message string `json:"message"`
	Phase   string `json:"phase"`
	State   string `json:"state"`
}

type Notebook struct {
	Age          string   `json:"age"`
	CPU          string   `json:"cpu"`
	GPUs         GPUs     `json:"gpus"`
	Image        string   `json:"image"`
	LastActivity string   `json:"last_activity"`
	Memory       string   `json:"memory"`
	Metadata     Metadata `json:"metadata"`
	Name         string   `json:"name"`
	Namespace    string   `json:"namespace"`
	ServerType   string   `json:"serverType"`
	ShortImage   string   `json:"shortImage"`
	Status       Status   `json:"status"`
	Volumes      []string `json:"volumes"`
	URL          string   `json:"url,omitempty"`
}

type NotebooksApiResponse struct {
	Notebooks []Notebook `json:"notebooks"`
	Status    int        `json:"status"`
	Success   bool       `json:"success"`
	User      string     `json:"user"`
}

type NotebookSearchResult struct {
	Total     int                  `json:"total"`
	Notebooks map[string]*Notebook `json:"notebooks"`
}
