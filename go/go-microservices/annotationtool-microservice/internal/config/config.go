package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// Config holds all configuration for the annotation tool microservice
type Config struct {
	// Application settings
	Version     string
	Environment string
	HTTPPort    string
	LogLevel    string

	// Database settings (for future use)
	DatabaseURL      string
	DatabaseName     string
	ConnectionString string

	// Trino settings
	TrinoHost               string
	TrinoPort               int
	TrinoCatalog            string
	TrinoSchema             string
	TrinoUser               string
	TrinoPassword           string
	TrinoTimeout            int // Query timeout in seconds
	TrinoAccessToken        string
	TrinoKerberosConfigPath string
	TrinoKerberosPrincipal  string

	// External services
	DaprAppID         string
	DaprPort          string
	EventBroker       string
	AccountServiceURL string

	// Kubeflow settings
	KubeflowURL    string
	KubeflowCookie string

	// MinIO settings for dataset storage
	MinioEndpoint        string
	MinioAccessKey       string
	MinioSecretKey       string
	MinioBucket          string
	MinioUseSSL          bool
	MinioRootPrefix      string // e.g., "gold/rnd"
	MinioDefaultPageSize int

	// Kafka settings for async processing
	KafkaBrokers         []string
	KafkaTopic           string
	KafkaConsumerGroup   string
	KafkaEnabled         bool
	KafkaRetryCount      int
	KafkaRetryTopic      string
	KafkaDeadLetterTopic string

	// Kafka retry configuration for exponential backoff
	KafkaRetryBaseDelay     time.Duration
	KafkaRetryMaxDelay      time.Duration
	KafkaRetryJitterEnabled bool

	// Feature flags
	PrometheusEnabled bool
	TracingEnabled    bool

	// Timeouts and limits
	RequestTimeoutSeconds int
	MaxPageSize           int
}

// New creates a new configuration from environment variables and YAML
func New() (*Config, error) {
	// Load .env file if it exists
	loadEnvFile(".env")

	// Try to load YAML config for Kafka settings
	kafkaConfig := loadKafkaFromYAML()

	config := &Config{
		// Application defaults
		Version:     getEnv("VERSION", "1.0.0"),
		Environment: getEnv("ENVIRONMENT", "development"),
		HTTPPort:    getEnv("HTTP_PORT", "2021"),
		LogLevel:    getEnv("LOG_LEVEL", "info"),

		// Database settings (for future use)
		DatabaseURL:      getEnv("DATABASE_URL", ""),
		DatabaseName:     getEnv("DATABASE_NAME", "annotationtool"),
		ConnectionString: getEnv("CONNECTION_STRING", ""),

		// Trino settings
		TrinoHost:               getEnv("TRINO_HOST", "localhost"),
		TrinoPort:               getIntEnv("TRINO_PORT", 8080),
		TrinoCatalog:            getEnv("TRINO_CATALOG", "minio"),
		TrinoSchema:             getEnv("TRINO_SCHEMA", "default"),
		TrinoUser:               getEnv("TRINO_USER", "admin"),
		TrinoPassword:           getEnv("TRINO_PASSWORD", ""),
		TrinoTimeout:            getIntEnv("TRINO_TIMEOUT", 30),
		TrinoAccessToken:        getEnv("TRINO_ACCESS_TOKEN", ""),
		TrinoKerberosConfigPath: getEnv("TRINO_KERBEROS_CONFIG_PATH", ""),
		TrinoKerberosPrincipal:  getEnv("TRINO_KERBEROS_PRINCIPAL", ""),

		// External services
		DaprAppID:         getEnv("DAPR_APP_ID", "annotationtool-microservice"),
		DaprPort:          getEnv("DAPR_HTTP_PORT", "3500"),
		EventBroker:       getEnv("EVENT_BROKER", "pubsub"),
		AccountServiceURL: getEnv("ACCOUNT_SERVICE_URL", "http://localhost:3000"),

		// Kubeflow settings
		KubeflowURL:    getEnv("KUBEFLOW_URL", "http://**************"),
		KubeflowCookie: getEnv("KUBEFLOW_COOKIE", ""),

		// MinIO settings
		MinioEndpoint:        getEnv("MINIO_ENDPOINT", "localhost:9000"),
		MinioAccessKey:       getEnv("MINIO_ACCESS_KEY", "minioadmin"),
		MinioSecretKey:       getEnv("MINIO_SECRET_KEY", "minioadmin"),
		MinioBucket:          getEnv("MINIO_BUCKET", "datasets"),
		MinioUseSSL:          getBoolEnv("MINIO_USE_SSL", false),
		MinioRootPrefix:      getEnv("MINIO_ROOT_PREFIX", "gold/rnd"),
		MinioDefaultPageSize: getIntEnv("MINIO_DEFAULT_PAGE_SIZE", 50),

		// Kafka settings (YAML first, then env vars)
		KafkaBrokers:         getStringSliceEnvOrYAML("KAFKA_BROKERS", kafkaConfig.Brokers, []string{"kafka:9092"}),
		KafkaTopic:           getEnvOrYAML("KAFKA_TOPIC", kafkaConfig.Topic, "annotationtool-kafka-bus"),
		KafkaConsumerGroup:   getEnvOrYAML("KAFKA_CONSUMER_GROUP", kafkaConfig.ConsumerGroup, "annotationtool-consumer-group"),
		KafkaEnabled:         getBoolEnvOrYAML("KAFKA_ENABLED", kafkaConfig.Enabled, true),
		KafkaRetryCount:      getIntEnvOrYAML("KAFKA_RETRY_COUNT", kafkaConfig.RetryCount, 3),
		KafkaRetryTopic:      getEnvOrYAML("KAFKA_RETRY_TOPIC", kafkaConfig.RetryTopic, "annotationtool-retry"),
		KafkaDeadLetterTopic: getEnvOrYAML("KAFKA_DEAD_LETTER_TOPIC", kafkaConfig.DeadLetterTopic, "annotationtool-dlq"),

		// Kafka retry configuration for exponential backoff
		KafkaRetryBaseDelay:     getDurationEnvOrYAML("KAFKA_RETRY_BASE_DELAY", kafkaConfig.RetryBaseDelay, 1*time.Second),
		KafkaRetryMaxDelay:      getDurationEnvOrYAML("KAFKA_RETRY_MAX_DELAY", kafkaConfig.RetryMaxDelay, 30*time.Second),
		KafkaRetryJitterEnabled: getBoolEnvOrYAML("KAFKA_RETRY_JITTER_ENABLED", kafkaConfig.RetryJitterEnabled, true),

		// Feature flags
		PrometheusEnabled: getBoolEnv("PROMETHEUS_ENABLED", true),
		TracingEnabled:    getBoolEnv("TRACING_ENABLED", true),

		// Timeouts and limits
		RequestTimeoutSeconds: getIntEnv("REQUEST_TIMEOUT_SECONDS", 30),
		MaxPageSize:           getIntEnv("MAX_PAGE_SIZE", 100),
	}

	if err := config.validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return config, nil
}

// validate checks if the configuration is valid
func (c *Config) validate() error {
	if c.HTTPPort == "" {
		return fmt.Errorf("HTTP_PORT is required")
	}

	if c.DaprAppID == "" {
		return fmt.Errorf("DAPR_APP_ID is required")
	}

	if c.AccountServiceURL == "" {
		return fmt.Errorf("ACCOUNT_SERVICE_URL is required")
	}

	if c.MinioEndpoint == "" {
		return fmt.Errorf("MINIO_ENDPOINT is required")
	}

	if c.MinioAccessKey == "" {
		return fmt.Errorf("MINIO_ACCESS_KEY is required")
	}

	if c.MinioSecretKey == "" {
		return fmt.Errorf("MINIO_SECRET_KEY is required")
	}

	if c.MinioBucket == "" {
		return fmt.Errorf("MINIO_BUCKET is required")
	}

	if c.RequestTimeoutSeconds <= 0 {
		return fmt.Errorf("REQUEST_TIMEOUT_SECONDS must be positive")
	}

	if c.MaxPageSize <= 0 {
		return fmt.Errorf("MAX_PAGE_SIZE must be positive")
	}

	if c.MinioDefaultPageSize <= 0 {
		return fmt.Errorf("MINIO_DEFAULT_PAGE_SIZE must be positive")
	}

	// Validate Kafka retry configuration
	if c.KafkaRetryBaseDelay <= 0 {
		return fmt.Errorf("KAFKA_RETRY_BASE_DELAY must be positive")
	}

	if c.KafkaRetryMaxDelay <= 0 {
		return fmt.Errorf("KAFKA_RETRY_MAX_DELAY must be positive")
	}

	if c.KafkaRetryBaseDelay > c.KafkaRetryMaxDelay {
		return fmt.Errorf("KAFKA_RETRY_BASE_DELAY cannot be greater than KAFKA_RETRY_MAX_DELAY")
	}

	return nil
}

// Helper functions for environment variable parsing
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getBoolEnv(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getIntEnv(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.Atoi(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getDurationEnv(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if parsed, err := time.ParseDuration(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getStringSliceEnv(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		// Split by comma and trim whitespace
		parts := strings.Split(value, ",")
		result := make([]string, len(parts))
		for i, part := range parts {
			result[i] = strings.TrimSpace(part)
		}
		return result
	}
	return defaultValue
}

// KafkaYAMLConfig represents Kafka config from YAML
type KafkaYAMLConfig struct {
	Enabled            bool          `yaml:"enabled"`
	Brokers            []string      `yaml:"brokers"`
	Topic              string        `yaml:"topic"`
	ConsumerGroup      string        `yaml:"consumer_group"`
	RetryCount         int           `yaml:"retry_count"`
	RetryTopic         string        `yaml:"retry_topic"`
	DeadLetterTopic    string        `yaml:"dead_letter_topic"`
	RetryBaseDelay     time.Duration `yaml:"retry_base_delay"`
	RetryMaxDelay      time.Duration `yaml:"retry_max_delay"`
	RetryJitterEnabled bool          `yaml:"retry_jitter_enabled"`
}

type YAMLConfig struct {
	Kafka KafkaYAMLConfig `yaml:"kafka"`
}

// loadKafkaFromYAML loads Kafka configuration from YAML file
func loadKafkaFromYAML() KafkaYAMLConfig {

	var yamlConfig YAMLConfig

	// Try to read config.yaml
	configPath := getEnv("CONFIG_PATH", "configs/config.yaml")
	if content, err := os.ReadFile(configPath); err == nil {
		// Basic YAML parsing for Kafka section only
		if err := parseYAMLSimple(content, &yamlConfig); err == nil {
			return yamlConfig.Kafka
		}
	}

	// Return empty config if file doesn't exist or can't be parsed
	return KafkaYAMLConfig{}
}

// parseYAMLSimple is a simple YAML parser for the Kafka section
func parseYAMLSimple(content []byte, yamlConfig interface{}) error {
	config := yamlConfig.(*YAMLConfig)
	lines := strings.Split(string(content), "\n")
	inKafkaSection := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		if line == "kafka:" {
			inKafkaSection = true
			continue
		}

		if !inKafkaSection {
			continue
		}

		// Exit Kafka section if we hit another top-level section
		if !strings.HasPrefix(line, " ") && !strings.HasPrefix(line, "\t") && strings.Contains(line, ":") {
			break
		}

		// Parse Kafka properties
		if strings.HasPrefix(line, "  enabled:") {
			config.Kafka.Enabled = strings.Contains(line, "true")
		} else if strings.HasPrefix(line, "  topic:") {
			config.Kafka.Topic = strings.Trim(strings.Split(line, ":")[1], " \"")
		} else if strings.HasPrefix(line, "  consumer_group:") {
			config.Kafka.ConsumerGroup = strings.Trim(strings.Split(line, ":")[1], " \"")
		} else if strings.HasPrefix(line, "  retry_count:") {
			if count, err := strconv.Atoi(strings.TrimSpace(strings.Split(line, ":")[1])); err == nil {
				config.Kafka.RetryCount = count
			}
		} else if strings.HasPrefix(line, "  retry_topic:") {
			config.Kafka.RetryTopic = strings.Trim(strings.Split(line, ":")[1], " \"")
		} else if strings.HasPrefix(line, "  dead_letter_topic:") {
			config.Kafka.DeadLetterTopic = strings.Trim(strings.Split(line, ":")[1], " \"")
		} else if strings.HasPrefix(line, "  retry_base_delay:") {
			durationStr := strings.Trim(strings.Split(line, ":")[1], " \"")
			if duration, err := time.ParseDuration(durationStr); err == nil {
				config.Kafka.RetryBaseDelay = duration
			}
		} else if strings.HasPrefix(line, "  retry_max_delay:") {
			durationStr := strings.Trim(strings.Split(line, ":")[1], " \"")
			if duration, err := time.ParseDuration(durationStr); err == nil {
				config.Kafka.RetryMaxDelay = duration
			}
		} else if strings.HasPrefix(line, "  retry_jitter_enabled:") {
			config.Kafka.RetryJitterEnabled = strings.Contains(line, "true")
		} else if strings.HasPrefix(line, "  brokers:") {
			// Start collecting brokers array
			continue
		} else if strings.HasPrefix(line, "    -") {
			// Broker array item
			broker := strings.Trim(strings.TrimPrefix(line, "    -"), " \"")
			config.Kafka.Brokers = append(config.Kafka.Brokers, broker)
		}
	}

	return nil
}

// Helper functions that check prefixed env vars, then unprefixed, then YAML, then defaults
func getEnvOrYAML(key, yamlValue, defaultValue string) string {
	// 1. Check prefixed env var first (highest priority)
	if envValue := os.Getenv("ANNOTATIONTOOL_" + key); envValue != "" {
		return envValue
	}
	// 2. Check unprefixed env var
	if envValue := os.Getenv(key); envValue != "" {
		return envValue
	}
	// 3. Check YAML value
	if yamlValue != "" {
		return yamlValue
	}
	// 4. Use default
	return defaultValue
}

func getBoolEnvOrYAML(key string, yamlValue, defaultValue bool) bool {
	// 1. Check prefixed env var first (highest priority)
	if envValue := os.Getenv("ANNOTATIONTOOL_" + key); envValue != "" {
		if parsed, err := strconv.ParseBool(envValue); err == nil {
			return parsed
		}
	}
	// 2. Check unprefixed env var
	if envValue := os.Getenv(key); envValue != "" {
		if parsed, err := strconv.ParseBool(envValue); err == nil {
			return parsed
		}
	}
	// 3. Check YAML value
	if yamlValue {
		return yamlValue
	}
	// 4. Use default
	return defaultValue
}

func getIntEnvOrYAML(key string, yamlValue, defaultValue int) int {
	// 1. Check prefixed env var first (highest priority)
	if envValue := os.Getenv("ANNOTATIONTOOL_" + key); envValue != "" {
		if parsed, err := strconv.Atoi(envValue); err == nil {
			return parsed
		}
	}
	// 2. Check unprefixed env var
	if envValue := os.Getenv(key); envValue != "" {
		if parsed, err := strconv.Atoi(envValue); err == nil {
			return parsed
		}
	}
	// 3. Check YAML value
	if yamlValue != 0 {
		return yamlValue
	}
	// 4. Use default
	return defaultValue
}

func getDurationEnvOrYAML(key string, yamlValue, defaultValue time.Duration) time.Duration {
	// 1. Check prefixed env var first (highest priority)
	if envValue := os.Getenv("ANNOTATIONTOOL_" + key); envValue != "" {
		if parsed, err := time.ParseDuration(envValue); err == nil {
			return parsed
		}
	}
	// 2. Check unprefixed env var
	if envValue := os.Getenv(key); envValue != "" {
		if parsed, err := time.ParseDuration(envValue); err == nil {
			return parsed
		}
	}
	// 3. Check YAML value
	if yamlValue != 0 {
		return yamlValue
	}
	// 4. Use default
	return defaultValue
}

func getStringSliceEnvOrYAML(key string, yamlValue []string, defaultValue []string) []string {
	// 1. Check prefixed env var first (highest priority)
	if envValue := os.Getenv("ANNOTATIONTOOL_" + key); envValue != "" {
		return parseStringSlice(envValue)
	}
	// 2. Check unprefixed env var
	if envValue := os.Getenv(key); envValue != "" {
		return parseStringSlice(envValue)
	}
	// 3. Check YAML value
	if len(yamlValue) > 0 {
		return yamlValue
	}
	// 4. Use default
	return defaultValue
}

// parseStringSlice splits a comma-separated string into a slice
func parseStringSlice(value string) []string {
	parts := strings.Split(value, ",")
	result := make([]string, len(parts))
	for i, part := range parts {
		result[i] = strings.TrimSpace(part)
	}
	return result
}

// loadEnvFile loads environment variables from a .env file if it exists
func loadEnvFile(filename string) {
	// This is a simple implementation - in production you might want to use a library like godotenv
	// For now, we'll just ignore if the file doesn't exist
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return
	}

	// Read the file
	content, err := os.ReadFile(filename)
	if err != nil {
		return
	}

	// Parse simple KEY=VALUE lines
	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			// Remove quotes if present
			if (strings.HasPrefix(value, "\"") && strings.HasSuffix(value, "\"")) ||
				(strings.HasPrefix(value, "'") && strings.HasSuffix(value, "'")) {
				value = value[1 : len(value)-1]
			}
			os.Setenv(key, value)
		}
	}
}
