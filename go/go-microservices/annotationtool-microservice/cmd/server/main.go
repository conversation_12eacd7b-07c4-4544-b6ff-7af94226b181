package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"annotationtool-microservice/internal/api"
	"annotationtool-microservice/internal/container"
)

func main() {
	// Create context that will be canceled on shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create container with all dependencies
	container, err := container.New()
	if err != nil {
		panic(fmt.Sprintf("Failed to create container: %v", err))
	}
	defer container.Close(ctx)

	// Force consistent logging style from Gin by disabling debug mode output
	gin.SetMode(gin.ReleaseMode)

	// Initialize router
	router := gin.New()
	api.RegisterRoutes(router, container)

	// Add observability endpoints
	if container.Config().PrometheusEnabled {
		router.GET("/metrics", gin.WrapH(promhttp.Handler()))
	}

	// Start server
	addr := fmt.Sprintf(":%s", container.Config().HTTPPort)
	container.Logger().Info("Starting annotation tool microservice",
		"address", addr,
	)

	// Handle graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		if err := router.Run(addr); err != nil {
			container.Logger().Error("Failed to start server", "error", err)
			cancel()
		}
	}()

	<-quit
	container.Logger().Info("Shutting down annotation tool microservice...")

	// Give outstanding requests 30 seconds to complete
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Perform graceful shutdown
	if err := container.Close(shutdownCtx); err != nil {
		container.Logger().Error("Error during shutdown", "error", err)
	}

	container.Logger().Info("Annotation tool microservice shutdown complete")
}
