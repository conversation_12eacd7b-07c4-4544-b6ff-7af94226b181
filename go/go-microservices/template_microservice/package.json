{"name": "@hcengineering/go-template-microservice", "version": "0.1.0", "author": "Matics", "license": "EPL-2.0", "scripts": {"_phase:docker-build": "rushx docker:build", "_phase:docker-staging": "rushx docker:staging", "docker:build": "cd ../.. && DOCKER_EXTRA=\"-f go-microservices/template-microservice/Dockerfile\" ../common/scripts/docker_build.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-template-microservice", "docker:staging": "../../../common/scripts/docker_tag.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-template-microservice staging", "docker:push": "../../../common/scripts/docker_tag.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-template-microservice", "docker:tbuild": "docker build -t us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-template-microservice . --platform=linux/amd64 && ../../../common/scripts/docker_tag_push.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-template-microservice", "docker:abuild": "docker build -t us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-template-microservice . --platform=linux/arm64 && ../../../common/scripts/docker_tag_push.sh us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-template-microservice"}}