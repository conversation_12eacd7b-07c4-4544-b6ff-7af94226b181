#!/bin/bash

set -e


echo "🔧 Building Go microservice Docker images...(1/10)"

cd ../go/go-microservices/webhook-microservice
npm run docker:build

cd ../analytics-microservice
npm run docker:build

cd ../annotationtool-microservice
npm run docker:build

echo "🏷️ Tagging Docker images...(2/10)"

docker tag us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-webhook-microservice:latest hardcoreeng/go-webhook-microservice:latest
docker tag us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-analytics-microservice:latest hardcoreeng/go-analytics-microservice:latest
docker tag us-east1-docker.pkg.dev/ninetyone-devops/hplus-devops-docker/huly/go-annotationtool-microservice:latest hardcoreeng/go-annotationtool-microservice:latest

echo "🚀 Starting ClickHouse...(3/10)"

cd ../../../dev
docker-compose up -d clickhouse

echo "⏳ Waiting for ClickHouse to be ready..."
sleep 10

echo "🛠️ Running ClickHouse migrations for annotation tool...(4/10)"
docker exec -i dev-clickhouse-1 clickhouse-client --multiquery < ../go/go-microservices/annotationtool-microservice/migrations/0001_create_tables.sql

echo "🚀 Starting MinIO...(5/10)"
docker-compose up -d minio

echo "⏳ Waiting for MinIO to be ready..."
sleep 5

echo "📦 Creating MinIO bucket...(6/10)"
docker exec dev-minio-1 mc alias set local http://localhost:9000 minioadmin minioadmin
docker exec dev-minio-1 mc mb local/datasets || true
docker exec dev-minio-1 mc ls local

echo "📦 Starting remaining services...(7/10)"
docker-compose up -d

echo "⛔ Stopping transactor service...(8/10)"
docker-compose stop server

echo "🏁 Starting transactor service manually...(10/10)"
cd ../pods/server
npm run run-local

echo "✅ Setup complete. All services running as expected!"
